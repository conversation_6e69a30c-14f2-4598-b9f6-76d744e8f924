<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير العملاء</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #1f2937;
            margin: 0;
            font-size: 28px;
        }
        
        .header p {
            color: #6b7280;
            margin: 10px 0 0 0;
            font-size: 16px;
        }
        
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .summary-card h3 {
            margin: 0 0 10px 0;
            color: #1f2937;
            font-size: 24px;
        }
        
        .summary-card p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }
        
        th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }
        
        td {
            color: #1f2937;
            font-size: 14px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status-inactive {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .status-vip {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
        }
        
        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            
            .header, .table-container, .summary-card {
                box-shadow: none;
                border: 1px solid #e5e7eb;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>تقرير العملاء</h1>
        <p>تم إنشاؤه في {{ now()->format('Y-m-d H:i') }}</p>
    </div>

    <div class="summary">
        <div class="summary-card">
            <h3>{{ number_format($customers->count()) }}</h3>
            <p>إجمالي العملاء</p>
        </div>
        
        <div class="summary-card">
            <h3>{{ number_format($customers->where('is_active', true)->count()) }}</h3>
            <p>عملاء نشطون</p>
        </div>
        
        <div class="summary-card">
            <h3>{{ number_format($customers->where('is_vip', true)->count()) }}</h3>
            <p>عملاء VIP</p>
        </div>
        
        <div class="summary-card">
            <h3>{{ number_format($customers->where('type', 'business')->count()) }}</h3>
            <p>عملاء تجاريون</p>
        </div>
    </div>

    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>رقم العميل</th>
                    <th>الاسم</th>
                    <th>النوع</th>
                    <th>البريد الإلكتروني</th>
                    <th>الجوال</th>
                    <th>المدينة</th>
                    <th>طلبات الصيانة</th>
                    <th>إجمالي الإنفاق</th>
                    <th>الحالة</th>
                    <th>تاريخ التسجيل</th>
                </tr>
            </thead>
            <tbody>
                @foreach($customers as $customer)
                <tr>
                    <td>{{ $customer->customer_number }}</td>
                    <td>
                        {{ $customer->display_name }}
                        @if($customer->is_vip)
                            <span class="status-badge status-vip">VIP</span>
                        @endif
                    </td>
                    <td>{{ $customer->type == 'individual' ? 'فرد' : 'شركة' }}</td>
                    <td>{{ $customer->email ?? '-' }}</td>
                    <td>{{ $customer->mobile }}</td>
                    <td>{{ $customer->city ?? '-' }}</td>
                    <td>{{ $customer->repairs_count ?? 0 }}</td>
                    <td>{{ number_format($customer->total_spent ?? 0, 2) }} ر.س</td>
                    <td>
                        <span class="status-badge {{ $customer->is_active ? 'status-active' : 'status-inactive' }}">
                            {{ $customer->is_active ? 'نشط' : 'غير نشط' }}
                        </span>
                    </td>
                    <td>{{ $customer->created_at->format('Y-m-d') }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="footer">
        <p>نظام إدارة مراكز الصيانة - تقرير العملاء</p>
        <p>تم إنشاء هذا التقرير تلقائياً في {{ now()->format('Y-m-d H:i:s') }}</p>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
