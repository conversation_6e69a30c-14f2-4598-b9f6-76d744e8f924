/**
 * مدير إمكانية الوصول
 * Accessibility Manager
 */

class AccessibilityManager {
    constructor() {
        this.preferences = this.loadPreferences();
        this.announcer = null;
        this.focusManager = null;
        this.keyboardManager = null;
        this.init();
    }

    init() {
        this.setupAnnouncer();
        this.setupFocusManagement();
        this.setupKeyboardNavigation();
        this.setupScreenReaderSupport();
        this.setupColorBlindSupport();
        this.setupMotionPreferences();
        this.setupTextSizeControl();
        this.setupContrastControl();
        this.applyPreferences();
    }

    // إعداد المعلن للرسائل الحية
    setupAnnouncer() {
        this.announcer = document.createElement('div');
        this.announcer.setAttribute('aria-live', 'polite');
        this.announcer.setAttribute('aria-atomic', 'true');
        this.announcer.className = 'sr-only live-region polite';
        this.announcer.id = 'accessibility-announcer';
        document.body.appendChild(this.announcer);

        // معلن للرسائل العاجلة
        this.urgentAnnouncer = document.createElement('div');
        this.urgentAnnouncer.setAttribute('aria-live', 'assertive');
        this.urgentAnnouncer.setAttribute('aria-atomic', 'true');
        this.urgentAnnouncer.className = 'sr-only live-region assertive';
        this.urgentAnnouncer.id = 'accessibility-urgent-announcer';
        document.body.appendChild(this.urgentAnnouncer);
    }

    // إعلان رسالة لقارئ الشاشة
    announce(message, urgent = false) {
        const announcer = urgent ? this.urgentAnnouncer : this.announcer;
        announcer.textContent = '';
        
        // تأخير قصير للتأكد من قراءة الرسالة
        setTimeout(() => {
            announcer.textContent = message;
        }, 100);
    }

    // إدارة التركيز
    setupFocusManagement() {
        this.focusManager = new FocusManager();
        
        // تتبع التركيز
        document.addEventListener('focusin', (e) => {
            this.focusManager.updateFocus(e.target);
        });

        // إدارة التركيز في النوافذ المنبثقة
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.focusManager.handleTabNavigation(e);
            }
        });
    }

    // التنقل بلوحة المفاتيح
    setupKeyboardNavigation() {
        this.keyboardManager = new KeyboardNavigationManager();
        
        document.addEventListener('keydown', (e) => {
            this.keyboardManager.handleKeydown(e);
        });
    }

    // دعم قارئ الشاشة
    setupScreenReaderSupport() {
        // إضافة تسميات ARIA للعناصر
        this.addAriaLabels();
        
        // إضافة أوصاف للعناصر التفاعلية
        this.addAriaDescriptions();
        
        // إدارة الحالات الديناميكية
        this.setupDynamicStates();
    }

    addAriaLabels() {
        // إضافة تسميات للأزرار بدون نص
        document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])').forEach(button => {
            const icon = button.querySelector('svg, i');
            if (icon && !button.textContent.trim()) {
                const label = this.getButtonLabel(button);
                if (label) {
                    button.setAttribute('aria-label', label);
                }
            }
        });

        // إضافة تسميات للروابط
        document.querySelectorAll('a:not([aria-label]):not([aria-labelledby])').forEach(link => {
            if (!link.textContent.trim()) {
                const label = this.getLinkLabel(link);
                if (label) {
                    link.setAttribute('aria-label', label);
                }
            }
        });

        // إضافة تسميات للنماذج
        document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])').forEach(input => {
            const label = input.previousElementSibling;
            if (label && label.tagName === 'LABEL') {
                const labelId = 'label-' + Math.random().toString(36).substr(2, 9);
                label.id = labelId;
                input.setAttribute('aria-labelledby', labelId);
            }
        });
    }

    getButtonLabel(button) {
        const actions = {
            'save': 'حفظ',
            'edit': 'تعديل',
            'delete': 'حذف',
            'close': 'إغلاق',
            'menu': 'قائمة',
            'search': 'بحث',
            'filter': 'فلتر',
            'export': 'تصدير',
            'import': 'استيراد',
            'refresh': 'تحديث',
            'add': 'إضافة',
            'remove': 'إزالة'
        };

        const className = button.className.toLowerCase();
        for (const [key, value] of Object.entries(actions)) {
            if (className.includes(key)) {
                return value;
            }
        }

        return null;
    }

    getLinkLabel(link) {
        const href = link.getAttribute('href');
        if (href) {
            if (href.includes('edit')) return 'تعديل';
            if (href.includes('delete')) return 'حذف';
            if (href.includes('view')) return 'عرض';
            if (href.includes('download')) return 'تحميل';
        }
        return null;
    }

    addAriaDescriptions() {
        // إضافة أوصاف للعناصر المعقدة
        document.querySelectorAll('.chart, .graph').forEach(element => {
            if (!element.getAttribute('aria-describedby')) {
                const description = this.createChartDescription(element);
                if (description) {
                    const descId = 'desc-' + Math.random().toString(36).substr(2, 9);
                    const descElement = document.createElement('div');
                    descElement.id = descId;
                    descElement.className = 'sr-only';
                    descElement.textContent = description;
                    element.appendChild(descElement);
                    element.setAttribute('aria-describedby', descId);
                }
            }
        });
    }

    createChartDescription(chart) {
        // إنشاء وصف للرسم البياني
        return 'رسم بياني تفاعلي يعرض البيانات الإحصائية';
    }

    setupDynamicStates() {
        // مراقبة التغييرات في الحالات
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.type === 'attributes') {
                    this.handleStateChange(mutation.target, mutation.attributeName);
                }
            });
        });

        observer.observe(document.body, {
            attributes: true,
            subtree: true,
            attributeFilter: ['class', 'aria-expanded', 'aria-selected', 'aria-checked']
        });
    }

    handleStateChange(element, attribute) {
        if (attribute === 'aria-expanded') {
            const expanded = element.getAttribute('aria-expanded') === 'true';
            this.announce(expanded ? 'تم توسيع القائمة' : 'تم طي القائمة');
        }
    }

    // دعم عمى الألوان
    setupColorBlindSupport() {
        // إضافة أنماط ودلائل بديلة للألوان
        document.querySelectorAll('.status-indicator').forEach(indicator => {
            this.addColorBlindSupport(indicator);
        });
    }

    addColorBlindSupport(element) {
        const status = element.textContent.toLowerCase();
        const patterns = {
            'نشط': '●',
            'غير نشط': '○',
            'معلق': '◐',
            'مكتمل': '✓',
            'ملغي': '✗'
        };

        if (patterns[status]) {
            element.setAttribute('data-pattern', patterns[status]);
        }
    }

    // تفضيلات الحركة
    setupMotionPreferences() {
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
        
        if (prefersReducedMotion.matches) {
            document.body.classList.add('respect-motion');
        }

        prefersReducedMotion.addEventListener('change', (e) => {
            if (e.matches) {
                document.body.classList.add('respect-motion');
                this.announce('تم تقليل الحركة والرسوم المتحركة');
            } else {
                document.body.classList.remove('respect-motion');
            }
        });
    }

    // التحكم في حجم النص
    setupTextSizeControl() {
        // إنشاء التحكم بدون إضافته مباشرة للصفحة
        const controls = document.createElement('div');
        controls.className = 'accessible-size-control';
        controls.innerHTML = `
            <button class="accessible-size-button" data-size="small" aria-label="نص صغير" title="نص صغير">A-</button>
            <button class="accessible-size-button active" data-size="normal" aria-label="نص عادي" title="نص عادي">A</button>
            <button class="accessible-size-button" data-size="large" aria-label="نص كبير" title="نص كبير">A+</button>
            <button class="accessible-size-button" data-size="extra-large" aria-label="نص كبير جداً" title="نص كبير جداً">A++</button>
        `;

        controls.addEventListener('click', (e) => {
            if (e.target.matches('[data-size]')) {
                this.changeTextSize(e.target.dataset.size);
                this.updateActiveButton(controls, e.target);
            }
        });

        // إضافة التحكم إلى body بدلاً من header لتجنب الازدحام
        document.body.appendChild(controls);

        // إضافة اختصار لوحة مفاتيح لإظهار/إخفاء التحكم
        this.setupAccessibilityToggle();
    }

    // إعداد تبديل أدوات إمكانية الوصول
    setupAccessibilityToggle() {
        // إضافة اختصار لوحة المفاتيح Alt+A لإظهار/إخفاء أدوات إمكانية الوصول
        document.addEventListener('keydown', (e) => {
            if (e.altKey && e.key.toLowerCase() === 'a') {
                e.preventDefault();
                this.toggleAccessibilityControls();
            }
        });

        // إضافة زر صغير لإظهار أدوات إمكانية الوصول
        const toggleButton = document.createElement('button');
        toggleButton.className = 'accessibility-toggle-btn';
        toggleButton.innerHTML = '♿';
        toggleButton.title = 'إظهار/إخفاء أدوات إمكانية الوصول (Alt+A)';
        toggleButton.setAttribute('aria-label', 'إظهار/إخفاء أدوات إمكانية الوصول');

        toggleButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--primary-color, #0066cc);
            color: white;
            border: none;
            font-size: 20px;
            cursor: pointer;
            z-index: 9998;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        toggleButton.addEventListener('click', () => {
            this.toggleAccessibilityControls();
        });

        toggleButton.addEventListener('mouseenter', () => {
            toggleButton.style.transform = 'scale(1.1)';
        });

        toggleButton.addEventListener('mouseleave', () => {
            toggleButton.style.transform = 'scale(1)';
        });

        document.body.appendChild(toggleButton);
    }

    // تبديل إظهار/إخفاء أدوات إمكانية الوصول
    toggleAccessibilityControls() {
        const isVisible = document.body.classList.contains('show-accessibility-controls');

        if (isVisible) {
            document.body.classList.remove('show-accessibility-controls');
            this.announce('تم إخفاء أدوات إمكانية الوصول');
        } else {
            document.body.classList.add('show-accessibility-controls');
            this.announce('تم إظهار أدوات إمكانية الوصول. استخدم Alt+A للتبديل');
        }
    }

    changeTextSize(size) {
        document.body.classList.remove('large-text', 'extra-large-text');
        
        if (size === 'large') {
            document.body.classList.add('large-text');
        } else if (size === 'extra-large') {
            document.body.classList.add('extra-large-text');
        }

        this.preferences.textSize = size;
        this.savePreferences();
        this.announce(`تم تغيير حجم النص إلى ${this.getSizeLabel(size)}`);
    }

    getSizeLabel(size) {
        const labels = {
            'small': 'صغير',
            'normal': 'عادي',
            'large': 'كبير',
            'extra-large': 'كبير جداً'
        };
        return labels[size] || 'عادي';
    }

    // التحكم في التباين
    setupContrastControl() {
        const controls = document.createElement('div');
        controls.className = 'accessible-contrast-control';
        controls.innerHTML = `
            <button class="accessible-contrast-button active" data-contrast="normal" aria-label="تباين عادي" title="تباين عادي">
                <span>◐</span>
            </button>
            <button class="accessible-contrast-button" data-contrast="high" aria-label="تباين عالي" title="تباين عالي">
                <span>●</span>
            </button>
        `;

        controls.addEventListener('click', (e) => {
            if (e.target.matches('[data-contrast]') || e.target.parentElement.matches('[data-contrast]')) {
                const button = e.target.matches('[data-contrast]') ? e.target : e.target.parentElement;
                this.changeContrast(button.dataset.contrast);
                this.updateActiveButton(controls, button);
            }
        });

        // إضافة التحكم إلى body بدلاً من header
        document.body.appendChild(controls);
    }

    changeContrast(contrast) {
        document.body.classList.remove('high-contrast');
        
        if (contrast === 'high') {
            document.body.classList.add('high-contrast');
        }

        this.preferences.contrast = contrast;
        this.savePreferences();
        this.announce(`تم تغيير التباين إلى ${contrast === 'high' ? 'عالي' : 'عادي'}`);
    }

    updateActiveButton(container, activeButton) {
        container.querySelectorAll('button').forEach(btn => {
            btn.classList.remove('active');
        });
        activeButton.classList.add('active');
    }

    // تحميل التفضيلات
    loadPreferences() {
        const saved = localStorage.getItem('accessibility-preferences');
        return saved ? JSON.parse(saved) : {
            textSize: 'normal',
            contrast: 'normal',
            reducedMotion: false,
            screenReader: false
        };
    }

    // حفظ التفضيلات
    savePreferences() {
        localStorage.setItem('accessibility-preferences', JSON.stringify(this.preferences));
    }

    // تطبيق التفضيلات
    applyPreferences() {
        this.changeTextSize(this.preferences.textSize);
        this.changeContrast(this.preferences.contrast);
        
        if (this.preferences.reducedMotion) {
            document.body.classList.add('respect-motion');
        }
    }

    // إضافة دعم لوحة المفاتيح للعناصر المخصصة
    addKeyboardSupport(element) {
        if (!element.hasAttribute('tabindex')) {
            element.setAttribute('tabindex', '0');
        }

        element.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                element.click();
            }
        });
    }

    // إضافة دعم ARIA للعناصر الديناميكية
    addAriaSupport(element, role, properties = {}) {
        element.setAttribute('role', role);
        
        Object.entries(properties).forEach(([key, value]) => {
            element.setAttribute(`aria-${key}`, value);
        });
    }

    // تنظيف الموارد
    cleanup() {
        if (this.announcer && this.announcer.parentNode) {
            this.announcer.parentNode.removeChild(this.announcer);
        }
        
        if (this.urgentAnnouncer && this.urgentAnnouncer.parentNode) {
            this.urgentAnnouncer.parentNode.removeChild(this.urgentAnnouncer);
        }
    }
}

// مدير التركيز
class FocusManager {
    constructor() {
        this.focusHistory = [];
        this.trapStack = [];
    }

    updateFocus(element) {
        this.focusHistory.push(element);
        if (this.focusHistory.length > 10) {
            this.focusHistory.shift();
        }
    }

    trapFocus(container) {
        const focusableElements = this.getFocusableElements(container);
        if (focusableElements.length === 0) return;

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        const trapHandler = (e) => {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    if (document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    }
                } else {
                    if (document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            }
        };

        container.addEventListener('keydown', trapHandler);
        this.trapStack.push({ container, handler: trapHandler });
        
        firstElement.focus();
    }

    releaseFocus() {
        const trap = this.trapStack.pop();
        if (trap) {
            trap.container.removeEventListener('keydown', trap.handler);
        }
    }

    getFocusableElements(container) {
        const selector = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
        return Array.from(container.querySelectorAll(selector)).filter(el => {
            return !el.disabled && !el.hidden && el.offsetParent !== null;
        });
    }

    handleTabNavigation(e) {
        const modal = document.querySelector('.modal.show');
        if (modal) {
            const focusableElements = this.getFocusableElements(modal);
            if (focusableElements.length > 0) {
                const firstElement = focusableElements[0];
                const lastElement = focusableElements[focusableElements.length - 1];

                if (e.shiftKey && document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                } else if (!e.shiftKey && document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }
        }
    }
}

// مدير التنقل بلوحة المفاتيح
class KeyboardNavigationManager {
    constructor() {
        this.shortcuts = new Map();
        this.setupDefaultShortcuts();
    }

    setupDefaultShortcuts() {
        this.shortcuts.set('alt+1', () => this.focusMainContent());
        this.shortcuts.set('alt+2', () => this.focusNavigation());
        this.shortcuts.set('alt+3', () => this.focusSearch());
        this.shortcuts.set('alt+s', () => this.skipToContent());
        this.shortcuts.set('escape', () => this.closeModal());
    }

    handleKeydown(e) {
        const key = this.getKeyString(e);
        const handler = this.shortcuts.get(key);
        
        if (handler) {
            e.preventDefault();
            handler();
        }
    }

    getKeyString(e) {
        const parts = [];
        if (e.altKey) parts.push('alt');
        if (e.ctrlKey) parts.push('ctrl');
        if (e.shiftKey) parts.push('shift');
        parts.push(e.key.toLowerCase());
        return parts.join('+');
    }

    focusMainContent() {
        const main = document.querySelector('main, [role="main"], .main-content');
        if (main) {
            main.focus();
            main.scrollIntoView();
        }
    }

    focusNavigation() {
        const nav = document.querySelector('nav, [role="navigation"], .navigation');
        if (nav) {
            const firstLink = nav.querySelector('a, button');
            if (firstLink) {
                firstLink.focus();
            }
        }
    }

    focusSearch() {
        const search = document.querySelector('input[type="search"], .search-input');
        if (search) {
            search.focus();
        }
    }

    skipToContent() {
        const skipLink = document.querySelector('.skip-to-content');
        if (skipLink) {
            skipLink.click();
        }
    }

    closeModal() {
        const modal = document.querySelector('.modal.show');
        if (modal) {
            const closeButton = modal.querySelector('.modal-close, [data-dismiss="modal"]');
            if (closeButton) {
                closeButton.click();
            }
        }
    }
}

// تهيئة مدير إمكانية الوصول
document.addEventListener('DOMContentLoaded', () => {
    window.accessibilityManager = new AccessibilityManager();
});

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    if (window.accessibilityManager) {
        window.accessibilityManager.cleanup();
    }
});

// تصدير للاستخدام العام
window.AccessibilityManager = AccessibilityManager;
