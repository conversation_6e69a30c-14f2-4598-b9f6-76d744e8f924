<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Part;

class QuickPartsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample parts if none exist
        if (Part::count() === 0) {
            $parts = [
                [
                    'part_number' => 'PART-001',
                    'name' => 'شاشة iPhone 13',
                    'description' => 'شاشة أصلية لجهاز iPhone 13',
                    'category' => 'screens',
                    'brand' => 'Apple',
                    'model' => 'iPhone 13',
                    'price' => 450.00,
                    'cost_price' => 350.00,
                    'stock_quantity' => 25,
                    'min_stock_level' => 5,
                    'max_stock_level' => 100,
                    'unit' => 'piece',
                    'location' => 'A1-01',
                    'supplier_id' => null,
                    'is_active' => true,
                ],
                [
                    'part_number' => 'PART-002',
                    'name' => 'بطارية iPhone 12',
                    'description' => 'بطارية أصلية لجهاز iPhone 12',
                    'category' => 'batteries',
                    'brand' => 'Apple',
                    'model' => 'iPhone 12',
                    'price' => 120.00,
                    'cost_price' => 80.00,
                    'stock_quantity' => 40,
                    'min_stock_level' => 10,
                    'max_stock_level' => 100,
                    'unit' => 'piece',
                    'location' => 'B2-05',
                    'supplier_id' => null,
                    'is_active' => true,
                ],
                [
                    'part_number' => 'PART-003',
                    'name' => 'شاشة Samsung A52',
                    'description' => 'شاشة أصلية لجهاز Samsung Galaxy A52',
                    'category' => 'screens',
                    'brand' => 'Samsung',
                    'model' => 'Galaxy A52',
                    'price' => 280.00,
                    'cost_price' => 200.00,
                    'stock_quantity' => 15,
                    'min_stock_level' => 5,
                    'max_stock_level' => 50,
                    'unit' => 'piece',
                    'location' => 'A1-15',
                    'supplier_id' => null,
                    'is_active' => true,
                ],
                [
                    'part_number' => 'ACC-001',
                    'name' => 'كابل USB-C',
                    'description' => 'كابل شحن USB-C عالي الجودة',
                    'category' => 'cables',
                    'brand' => 'Generic',
                    'model' => 'Universal',
                    'price' => 25.00,
                    'cost_price' => 15.00,
                    'stock_quantity' => 100,
                    'min_stock_level' => 20,
                    'max_stock_level' => 200,
                    'unit' => 'piece',
                    'location' => 'C3-10',
                    'supplier_id' => null,
                    'is_active' => true,
                ],
                [
                    'part_number' => 'ACC-002',
                    'name' => 'شاحن سريع 20W',
                    'description' => 'شاحن سريع 20 واط متوافق مع iPhone',
                    'category' => 'chargers',
                    'brand' => 'Apple',
                    'model' => 'Universal',
                    'price' => 85.00,
                    'cost_price' => 60.00,
                    'stock_quantity' => 30,
                    'min_stock_level' => 10,
                    'max_stock_level' => 100,
                    'unit' => 'piece',
                    'location' => 'C3-15',
                    'supplier_id' => null,
                    'is_active' => true,
                ],
                [
                    'part_number' => 'ACC-003',
                    'name' => 'جراب iPhone 13 Pro',
                    'description' => 'جراب حماية شفاف لجهاز iPhone 13 Pro',
                    'category' => 'cases',
                    'brand' => 'Generic',
                    'model' => 'iPhone 13 Pro',
                    'price' => 35.00,
                    'cost_price' => 20.00,
                    'stock_quantity' => 50,
                    'min_stock_level' => 15,
                    'max_stock_level' => 150,
                    'unit' => 'piece',
                    'location' => 'D4-20',
                    'supplier_id' => null,
                    'is_active' => true,
                ],
                [
                    'part_number' => 'ACC-004',
                    'name' => 'سماعات AirPods',
                    'description' => 'سماعات AirPods الجيل الثالث',
                    'category' => 'accessories',
                    'brand' => 'Apple',
                    'model' => 'AirPods 3rd Gen',
                    'price' => 650.00,
                    'cost_price' => 500.00,
                    'stock_quantity' => 12,
                    'min_stock_level' => 3,
                    'max_stock_level' => 30,
                    'unit' => 'piece',
                    'location' => 'E5-01',
                    'supplier_id' => null,
                    'is_active' => true,
                ],
                [
                    'part_number' => 'PART-004',
                    'name' => 'كاميرا خلفية iPhone 12',
                    'description' => 'كاميرا خلفية أصلية لجهاز iPhone 12',
                    'category' => 'cameras',
                    'brand' => 'Apple',
                    'model' => 'iPhone 12',
                    'price' => 180.00,
                    'cost_price' => 130.00,
                    'stock_quantity' => 8,
                    'min_stock_level' => 3,
                    'max_stock_level' => 25,
                    'unit' => 'piece',
                    'location' => 'B2-20',
                    'supplier_id' => null,
                    'is_active' => true,
                ],
            ];

            foreach ($parts as $partData) {
                Part::create($partData);
            }

            $this->command->info('تم إنشاء ' . count($parts) . ' قطعة تجريبية');
        } else {
            $this->command->info('القطع موجودة بالفعل');
        }
    }
}
