@extends('layouts.main')

@section('title', 'مدفوعات العميل')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">مدفوعات العميل</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $customer->display_name }} - {{ $customer->customer_number }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('customers.show', $customer) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة لتفاصيل العميل
            </a>
            <button onclick="showAddPaymentModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                إضافة دفعة
            </button>
        </div>
    </div>

    <!-- Payment Summary -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ number_format($paymentStats['total_paid'], 2) }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">إجمالي المدفوع (ر.س)</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ number_format($paymentStats['pending_payments'], 2) }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">مدفوعات معلقة (ر.س)</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ number_format($paymentStats['outstanding_balance'], 2) }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">الرصيد المستحق (ر.س)</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($paymentStats['credit_limit'], 2) }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">حد الائتمان (ر.س)</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ number_format($paymentStats['available_credit'], 2) }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">الائتمان المتاح (ر.س)</div>
            </div>
        </div>
    </div>

    <!-- Credit Status Alert -->
    @if($paymentStats['outstanding_balance'] > 0)
        <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <div class="mr-3">
                    <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                        تنبيه: يوجد رصيد مستحق
                    </h3>
                    <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                        <p>العميل لديه رصيد مستحق بقيمة {{ number_format($paymentStats['outstanding_balance'], 2) }} ر.س</p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if($paymentStats['credit_limit'] > 0 && $paymentStats['available_credit'] < ($paymentStats['credit_limit'] * 0.2))
        <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <div class="mr-3">
                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        تحذير: حد الائتمان منخفض
                    </h3>
                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                        <p>الائتمان المتاح أقل من 20% من الحد الأقصى</p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Payments List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">سجل المدفوعات</h3>
        </div>

        @if($payments->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            رقم الدفعة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            طلب الصيانة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المبلغ
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            طريقة الدفع
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            التاريخ
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($payments as $payment)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                #{{ $payment->id }}
                            </div>
                            @if($payment->payment_number)
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $payment->payment_number }}
                                </div>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($payment->repair)
                                <div class="text-sm text-gray-900 dark:text-gray-100">
                                    <a href="{{ route('repairs.show', $payment->repair) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200">
                                        طلب #{{ $payment->repair->id }}
                                    </a>
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $payment->repair->title ?? 'طلب صيانة' }}
                                </div>
                            @else
                                <span class="text-gray-500 dark:text-gray-400">دفعة عامة</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {{ number_format($payment->amount, 2) }} ر.س
                            </div>
                            @if($payment->currency && $payment->currency != 'SAR')
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $payment->currency }}
                                </div>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            @switch($payment->payment_method)
                                @case('cash') نقداً @break
                                @case('card') بطاقة @break
                                @case('bank_transfer') تحويل بنكي @break
                                @case('check') شيك @break
                                @case('credit') ائتمان @break
                                @default {{ $payment->payment_method }}
                            @endswitch
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                @switch($payment->status)
                                    @case('pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 @break
                                    @case('completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @break
                                    @case('failed') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @break
                                    @case('cancelled') bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @break
                                    @default bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                @endswitch
                            ">
                                @switch($payment->status)
                                    @case('pending') معلق @break
                                    @case('completed') مكتمل @break
                                    @case('failed') فاشل @break
                                    @case('cancelled') ملغي @break
                                    @default {{ $payment->status }}
                                @endswitch
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <div>{{ $payment->created_at->format('Y-m-d') }}</div>
                            <div class="text-xs text-gray-500">{{ $payment->created_at->format('H:i') }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{{ route('payments.show', $payment) }}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200">
                                    عرض
                                </a>
                                @if($payment->status == 'pending')
                                    <button onclick="confirmPayment({{ $payment->id }})" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200">
                                        تأكيد
                                    </button>
                                    <button onclick="cancelPayment({{ $payment->id }})" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-200">
                                        إلغاء
                                    </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            {{ $payments->links() }}
        </div>
        @else
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد مدفوعات</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لم يتم تسجيل أي مدفوعات لهذا العميل بعد.</p>
            <div class="mt-6">
                <button onclick="showAddPaymentModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <svg class="-mr-1 ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة دفعة
                </button>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Add Payment Modal -->
<div id="addPaymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إضافة دفعة جديدة</h3>
            
            <form action="{{ route('payments.store') }}" method="POST" class="space-y-4">
                @csrf
                <input type="hidden" name="customer_id" value="{{ $customer->id }}">
                
                <div>
                    <label for="repair_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طلب الصيانة</label>
                    <select name="repair_id" id="repair_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="">دفعة عامة</option>
                        @foreach($customer->repairs()->whereIn('payment_status', ['pending', 'partial'])->get() as $repair)
                            <option value="{{ $repair->id }}">
                                طلب #{{ $repair->id }} - {{ number_format($repair->total_cost, 2) }} ر.س
                            </option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ *</label>
                    <input type="number" name="amount" id="amount" step="0.01" min="0" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة الدفع *</label>
                    <select name="payment_method" id="payment_method" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="">اختر طريقة الدفع</option>
                        <option value="cash">نقداً</option>
                        <option value="card">بطاقة</option>
                        <option value="bank_transfer">تحويل بنكي</option>
                        <option value="check">شيك</option>
                        <option value="credit">ائتمان</option>
                    </select>
                </div>

                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات</label>
                    <textarea name="notes" id="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
                </div>

                <div class="flex justify-end space-x-2 space-x-reverse pt-4">
                    <button type="button" onclick="hideAddPaymentModal()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        حفظ الدفعة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showAddPaymentModal() {
    document.getElementById('addPaymentModal').classList.remove('hidden');
}

function hideAddPaymentModal() {
    document.getElementById('addPaymentModal').classList.add('hidden');
}

function confirmPayment(paymentId) {
    if (confirm('هل أنت متأكد من تأكيد هذه الدفعة؟')) {
        // Implementation for confirming payment
        alert('تأكيد الدفعة #' + paymentId);
    }
}

function cancelPayment(paymentId) {
    if (confirm('هل أنت متأكد من إلغاء هذه الدفعة؟')) {
        // Implementation for cancelling payment
        alert('إلغاء الدفعة #' + paymentId);
    }
}

// Close modal when clicking outside
document.getElementById('addPaymentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideAddPaymentModal();
    }
});
</script>
@endsection
