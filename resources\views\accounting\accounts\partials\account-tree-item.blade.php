@php
    $hasChildren = $account->children->count() > 0;
    $indentClass = 'mr-' . ($level * 6);
    $typeColors = [
        'asset' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        'liability' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        'equity' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
        'revenue' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        'expense' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
    ];
@endphp

<div class="account-item {{ $indentClass }}">
    <!-- Account Row -->
    <div class="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg group">
        <div class="flex items-center flex-1">
            <!-- Toggle But<PERSON> for Children -->
            @if($hasChildren)
                <button onclick="toggleChildren({{ $account->id }})" 
                        class="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-2">
                    <i id="toggle-{{ $account->id }}" class="fas fa-plus text-xs"></i>
                </button>
            @else
                <div class="w-6 h-6 mr-2"></div>
            @endif

            <!-- Account Info -->
            <div class="flex items-center flex-1">
                <div class="flex-1">
                    <div class="flex items-center gap-3">
                        <span class="font-mono text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                            {{ $account->account_code }}
                        </span>
                        
                        <span class="font-medium text-gray-900 dark:text-gray-100">
                            {{ $account->account_name }}
                        </span>
                        
                        @if($account->account_name_en)
                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                ({{ $account->account_name_en }})
                            </span>
                        @endif

                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $typeColors[$account->account_type] ?? 'bg-gray-100 text-gray-800' }}">
                            {{ $account->getAccountTypeLabel() }}
                        </span>

                        @if(!$account->is_active)
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                غير نشط
                            </span>
                        @endif

                        @if($account->is_system)
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                نظام
                            </span>
                        @endif
                    </div>
                    
                    @if($account->description)
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 mr-8">
                            {{ $account->description }}
                        </p>
                    @endif
                </div>

                <!-- Balance -->
                <div class="text-left mr-4">
                    <span class="font-medium text-gray-900 dark:text-gray-100">
                        {{ number_format($account->current_balance, 2) }} ريال
                    </span>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                        {{ $account->getBalanceTypeLabel() }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button onclick="showAccountDetails({{ $account->id }})" 
                    class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 p-1"
                    title="عرض التفاصيل">
                <i class="fas fa-eye"></i>
            </button>

            @if(!$account->is_system)
                <button onclick="editAccount({{ $account->id }})" 
                        class="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 p-1"
                        title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
            @endif

            <button onclick="addSubAccount({{ $account->id }})" 
                    class="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 p-1"
                    title="إضافة حساب فرعي">
                <i class="fas fa-plus"></i>
            </button>

            @if(!$account->is_system)
                <button onclick="toggleAccountStatus({{ $account->id }})" 
                        class="text-yellow-600 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300 p-1"
                        title="{{ $account->is_active ? 'تعطيل' : 'تفعيل' }}">
                    <i class="fas fa-{{ $account->is_active ? 'pause' : 'play' }}"></i>
                </button>

                @if($account->canBeDeleted())
                    <button onclick="deleteAccount({{ $account->id }})" 
                            class="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1"
                            title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                @endif
            @endif
        </div>
    </div>

    <!-- Children -->
    @if($hasChildren)
        <div id="children-{{ $account->id }}" class="hidden mr-6">
            @foreach($account->children as $child)
                @include('accounting.accounts.partials.account-tree-item', ['account' => $child, 'level' => $level + 1])
            @endforeach
        </div>
    @endif
</div>
