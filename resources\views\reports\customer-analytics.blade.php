@extends('layouts.main')

@section('title', 'تحليلات العملاء')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تحليلات العملاء</h1>
            <p class="text-gray-600 dark:text-gray-400">تحليل شامل لسلوك العملاء والإحصائيات</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('reports.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة للتقارير
            </a>
            
            <button onclick="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير التقرير
            </button>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" name="start_date" value="{{ $startDate->format('Y-m-d') }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" name="end_date" value="{{ $endDate->format('Y-m-d') }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                <i class="fas fa-filter mr-2"></i>
                تطبيق الفلتر
            </button>
        </form>
    </div>

    <!-- Customer Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100">إجمالي العملاء</p>
                    <p class="text-3xl font-bold">{{ $customerInsights['total_customers'] ?? 0 }}</p>
                    <p class="text-blue-100 text-sm">عميل مسجل</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100">عملاء نشطين</p>
                    <p class="text-3xl font-bold">{{ $customerInsights['active_customers'] ?? 0 }}</p>
                    <p class="text-green-100 text-sm">خلال الفترة</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100">عملاء جدد</p>
                    <p class="text-3xl font-bold">{{ $customerInsights['new_customers'] ?? 0 }}</p>
                    <p class="text-purple-100 text-sm">خلال الفترة</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-user-plus"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100">متوسط القيمة</p>
                    <p class="text-3xl font-bold">{{ number_format($customerInsights['avg_order_value'] ?? 0, 0) }}</p>
                    <p class="text-orange-100 text-sm">ريال سعودي</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">أفضل العملاء</h3>
            <p class="text-gray-600 dark:text-gray-400">العملاء الأكثر إنفاقاً خلال الفترة المحددة</p>
        </div>
        <div class="p-6">
            @if(isset($customerInsights['top_customers']) && $customerInsights['top_customers']->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">عدد الطلبات</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">إجمالي الإنفاق</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">متوسط الطلب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">آخر طلب</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($customerInsights['top_customers'] as $customer)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                                                    {{ substr($customer->first_name ?? 'ع', 0, 1) }}
                                                </div>
                                            </div>
                                            <div class="mr-4">
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ $customer->first_name }} {{ $customer->last_name }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $customer->phone }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        {{ $customer->repairs_count ?? 0 }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                        {{ number_format($customer->total_spent ?? 0, 2) }} ريال
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        {{ number_format(($customer->total_spent ?? 0) / max($customer->repairs_count ?? 1, 1), 2) }} ريال
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {{ $customer->last_repair_date ? \Carbon\Carbon::parse($customer->last_repair_date)->diffForHumans() : 'لا يوجد' }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد بيانات عملاء للفترة المحددة</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Customer Satisfaction -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">رضا العملاء</h3>
                <p class="text-gray-600 dark:text-gray-400">متوسط تقييم العملاء للخدمة</p>
            </div>
            <div class="p-6">
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                        {{ number_format($customerInsights['customer_satisfaction']->avg_rating ?? 4.5, 1) }}
                    </div>
                    <div class="flex justify-center mb-2">
                        @for($i = 1; $i <= 5; $i++)
                            <i class="fas fa-star {{ $i <= ($customerInsights['customer_satisfaction']->avg_rating ?? 4.5) ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                        @endfor
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">
                        من {{ $customerInsights['customer_satisfaction']->total_ratings ?? 0 }} تقييم
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">التوزيع الجغرافي</h3>
                <p class="text-gray-600 dark:text-gray-400">توزيع العملاء حسب المدن</p>
            </div>
            <div class="p-6">
                @if(isset($customerInsights['geographic_distribution']) && $customerInsights['geographic_distribution']->count() > 0)
                    <div class="space-y-4">
                        @foreach($customerInsights['geographic_distribution'] as $location)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-map-marker-alt text-blue-500 mr-3"></i>
                                    <span class="text-gray-900 dark:text-gray-100">{{ $location->city ?? 'غير محدد' }}</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-gray-600 dark:text-gray-400 mr-2">{{ $location->customer_count }}</span>
                                    <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: {{ ($location->customer_count / $customerInsights['geographic_distribution']->max('customer_count')) * 100 }}%"></div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-map text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400">لا توجد بيانات جغرافية</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function exportReport() {
    // يمكن تطوير هذه الوظيفة لاحقاً
    alert('ميزة التصدير قيد التطوير');
}
</script>
@endpush
@endsection
