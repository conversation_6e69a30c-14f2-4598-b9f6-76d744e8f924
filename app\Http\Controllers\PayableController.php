<?php

namespace App\Http\Controllers;

use App\Models\Payable;
use App\Models\Supplier;
use App\Services\AccountingService;
use Illuminate\Http\Request;

class PayableController extends Controller
{
    protected $accountingService;

    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }

    /**
     * Display payables
     */
    public function index(Request $request)
    {
        $query = Payable::with(['supplier']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by supplier
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // Filter by due date
        if ($request->filled('due_date_from')) {
            $query->where('due_date', '>=', $request->due_date_from);
        }
        if ($request->filled('due_date_to')) {
            $query->where('due_date', '<=', $request->due_date_to);
        }

        // Filter overdue
        if ($request->get('overdue') === '1') {
            $query->overdue();
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('our_reference', 'like', "%{$search}%")
                  ->orWhereHas('supplier', function ($supplierQuery) use ($search) {
                      $supplierQuery->where('company_name', 'like', "%{$search}%");
                  });
            });
        }

        $payables = $query->orderBy('due_date', 'asc')
                         ->paginate(20);

        // Summary statistics
        $totalPending = Payable::whereIn('status', ['pending', 'partial'])->sum('remaining_amount');
        $totalOverdue = Payable::overdue()->sum('remaining_amount');
        $totalPaid = Payable::where('status', 'paid')->sum('original_amount');

        $suppliers = Supplier::where('is_active', true)->orderBy('company_name')->get();

        return view('accounting.payables.index', compact(
            'payables', 'totalPending', 'totalOverdue', 'totalPaid', 'suppliers'
        ));
    }

    /**
     * Show create form
     */
    public function create()
    {
        $suppliers = Supplier::where('is_active', true)->orderBy('company_name')->get();
        
        return view('accounting.payables.create', compact('suppliers'));
    }

    /**
     * Store new payable
     */
    public function store(Request $request)
    {
        $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'invoice_number' => 'required|string|max:50',
            'our_reference' => 'nullable|string|max:50',
            'invoice_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:invoice_date',
            'original_amount' => 'required|numeric|min:0.01',
            'notes' => 'nullable|string',
        ]);

        $payable = Payable::create([
            'supplier_id' => $request->supplier_id,
            'invoice_number' => $request->invoice_number,
            'our_reference' => $request->our_reference,
            'invoice_date' => $request->invoice_date,
            'due_date' => $request->due_date,
            'original_amount' => $request->original_amount,
            'paid_amount' => 0,
            'remaining_amount' => $request->original_amount,
            'status' => 'pending',
            'notes' => $request->notes,
        ]);

        // Create accounting entry for the payable
        $this->accountingService->createPurchaseEntry(
            $request->original_amount,
            $request->supplier_id,
            [],
            'credit' // This creates a payable
        );

        return redirect()->route('accounting.payables.show', $payable)
                        ->with('success', 'تم إنشاء الذمة الدائنة بنجاح');
    }

    /**
     * Show payable details
     */
    public function show(Payable $payable)
    {
        $payable->load(['supplier']);
        
        return view('accounting.payables.show', compact('payable'));
    }

    /**
     * Record payment for payable
     */
    public function recordPayment(Request $request, Payable $payable)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . $payable->remaining_amount,
            'payment_method' => 'required|in:cash,card,bank_transfer',
            'notes' => 'nullable|string|max:255',
        ]);

        try {
            // Record payment in payable
            $payable->recordPayment(
                $request->amount,
                $request->payment_method,
                $request->notes
            );

            // Create accounting entry for payment
            // This would be a debit to payables and credit to cash
            // Implementation would be similar to payment received but reversed

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل الدفعة بنجاح',
                'payable' => [
                    'paid_amount' => $payable->fresh()->paid_amount,
                    'remaining_amount' => $payable->fresh()->remaining_amount,
                    'status' => $payable->fresh()->status,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
