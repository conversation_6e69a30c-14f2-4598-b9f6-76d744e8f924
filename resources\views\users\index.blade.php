@extends('layouts.main')

@section('title', 'إدارة المستخدمين')

@section('content')
<div class="content-wrapper animate-fade-in-up" x-data="usersManager()">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">إدارة المستخدمين</h1>
                    <p class="page-subtitle">إدارة حسابات المستخدمين والصلاحيات والأدوار بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="exportUsers()" class="btn btn-success hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        تصدير
                    </button>
                    <button @click="manageRoles()" class="btn btn-secondary hover-lift ripple" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        إدارة الأدوار
                    </button>
                    <button @click="addUser()" class="btn btn-primary hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        إضافة مستخدم جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card hover-lift animate-scale-in animate-delay-100">
            <div class="stat-value" style="color: var(--accent-color);" x-text="stats.totalUsers">4</div>
            <div class="stat-label">إجمالي المستخدمين</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                فريق متنوع
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-200">
            <div class="stat-value" style="color: var(--success-color);" x-text="stats.activeUsers">4</div>
            <div class="stat-label">مستخدمين نشطين</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                جميعهم نشطين
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-300">
            <div class="stat-value" style="color: var(--warning-color);" x-text="stats.onlineUsers">2</div>
            <div class="stat-label">متصلين الآن</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                نشاط حالي
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-500">
            <div class="stat-value" style="color: var(--accent-color);" x-text="stats.totalRoles">4</div>
            <div class="stat-label">أدوار النظام</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                صلاحيات متنوعة
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card animate-fade-in-up animate-delay-300">
        <div class="card-header">
            <h3 class="card-title">فلترة وبحث المستخدمين</h3>
            <button @click="clearFilters()" class="btn btn-ghost btn-sm hover-scale">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                مسح الفلاتر
            </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="form-group">
                <label class="form-label">البحث</label>
                <div class="search-box">
                    <input type="text" x-model="filters.search" @input="filterUsers()"
                           placeholder="البحث في المستخدمين..."
                           class="search-input focus-glow">
                    <svg class="search-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">الدور</label>
                <select x-model="filters.role" @change="filterUsers()" class="form-select focus-glow">
                    <option value="">جميع الأدوار</option>
                    <option value="مدير النظام">مدير النظام</option>
                    <option value="مدير">مدير</option>
                    <option value="فني">فني</option>
                    <option value="كاشير">كاشير</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">الحالة</label>
                <select x-model="filters.status" @change="filterUsers()" class="form-select focus-glow">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            <div class="flex items-end">
                <button @click="applyFilters()" class="btn btn-secondary w-full hover-lift ripple">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"></path>
                    </svg>
                    تطبيق الفلاتر
                </button>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="table-container animate-fade-in-up animate-delay-500">
        <div class="table-header">
            <h3 class="table-title">قائمة المستخدمين</h3>
        </div>

        <div class="overflow-x-auto">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>المستخدم</th>
                        <th>الدور</th>
                        <th>القسم</th>
                        <th>تاريخ التوظيف</th>
                        <th>آخر دخول</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Sample Data -->
                    <tr>
                        <td>
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 rounded-full overflow-hidden">
                                    <img class="w-full h-full object-cover" src="https://ui-avatars.com/api/?name=أحمد+محمد&background=3B82F6&color=fff&size=40" alt="أحمد محمد علي">
                                </div>
                                <div>
                                    <div class="font-medium">أحمد محمد علي</div>
                                    <div class="text-sm opacity-70"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge badge-danger">
                                مدير النظام
                            </span>
                        </td>
                        <td>الإدارة</td>
                        <td>2023-01-15</td>
                        <td>منذ 5 دقائق</td>
                        <td>
                            <span class="badge badge-success">
                                نشط
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn" @click="viewUser(1)" title="عرض">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                                <button class="action-btn" @click="editUser(1)" title="تعديل">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button class="action-btn" @click="managePermissions(1)" title="صلاحيات">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=سارة+أحمد&background=10B981&color=fff&size=40" alt="">
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">سارة أحمد خالد</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                مدير
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">الإدارة</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">2023-02-01</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">منذ ساعة</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                نشط
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400">عرض</button>
                                <button class="text-green-600 hover:text-green-900 dark:text-green-400">تعديل</button>
                                <button class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400">صلاحيات</button>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=محمد+عبدالله&background=F59E0B&color=fff&size=40" alt="">
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">محمد عبدالله حسن</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                كاشير
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">المبيعات</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">2023-03-10</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">أمس</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                نشط
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400">عرض</button>
                                <button class="text-green-600 hover:text-green-900 dark:text-green-400">تعديل</button>
                                <button class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400">صلاحيات</button>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=علي+حسين&background=8B5CF6&color=fff&size=40" alt="">
                                </div>
                                <div class="mr-4">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">علي حسين محمد</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400"><EMAIL></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                فني
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">الصيانة</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">2023-04-05</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">منذ 3 ساعات</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                نشط
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400">عرض</button>
                                <button class="text-green-600 hover:text-green-900 dark:text-green-400">تعديل</button>
                                <button class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400">صلاحيات</button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
function usersManager() {
    return {
        users: [
            {
                id: 1,
                name: 'أحمد محمد علي',
                email: '<EMAIL>',
                role: 'مدير النظام',
                department: 'الإدارة',
                created_at: '2023-01-15',
                last_login: 'منذ 30 دقيقة',
                status: 'active',
                avatar: 'https://ui-avatars.com/api/?name=أحمد+محمد&background=3B82F6&color=fff&size=40'
            },
            {
                id: 2,
                name: 'سارة أحمد خالد',
                email: '<EMAIL>',
                role: 'محاسب',
                department: 'المحاسبة',
                created_at: '2023-02-01',
                last_login: 'منذ ساعة',
                status: 'active',
                avatar: 'https://ui-avatars.com/api/?name=سارة+أحمد&background=10B981&color=fff&size=40'
            },
            {
                id: 3,
                name: 'محمد عبدالله حسن',
                email: '<EMAIL>',
                role: 'فني',
                department: 'الصيانة',
                created_at: '2023-03-10',
                last_login: 'منذ يومين',
                status: 'inactive',
                avatar: 'https://ui-avatars.com/api/?name=محمد+عبدالله&background=F59E0B&color=fff&size=40'
            },
            {
                id: 4,
                name: 'فاطمة حسين علي',
                email: '<EMAIL>',
                role: 'موظف مبيعات',
                department: 'المبيعات',
                created_at: '2023-04-05',
                last_login: 'منذ أسبوع',
                status: 'active',
                avatar: 'https://ui-avatars.com/api/?name=فاطمة+حسين&background=8B5CF6&color=fff&size=40'
            }
        ],
        filteredUsers: [],
        filters: {
            search: '',
            role: '',
            department: '',
            status: ''
        },
        stats: {
            totalUsers: 4,
            activeUsers: 4,
            onlineUsers: 2,
            totalRoles: 4
        },

        init() {
            this.filteredUsers = [...this.users];
        },

        filterUsers() {
            this.filteredUsers = this.users.filter(user => {
                const matchesSearch = !this.filters.search ||
                    user.name.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    user.email.toLowerCase().includes(this.filters.search.toLowerCase());

                const matchesRole = !this.filters.role || user.role === this.filters.role;
                const matchesDepartment = !this.filters.department || user.department === this.filters.department;
                const matchesStatus = !this.filters.status || user.status === this.filters.status;

                return matchesSearch && matchesRole && matchesDepartment && matchesStatus;
            });
        },

        addUser() {
            if (window.showNotification) {
                window.showNotification('سيتم إضافة نافذة إنشاء مستخدم جديد قريباً', 'info');
            }
        },

        editUser(id) {
            if (window.showNotification) {
                window.showNotification(`سيتم فتح نافذة تعديل المستخدم رقم ${id}`, 'info');
            }
        },

        viewUser(id) {
            if (window.showNotification) {
                window.showNotification(`سيتم عرض تفاصيل المستخدم رقم ${id}`, 'info');
            }
        },

        managePermissions(id) {
            if (window.showNotification) {
                window.showNotification(`سيتم فتح نافذة إدارة صلاحيات المستخدم رقم ${id}`, 'info');
            }
        },

        toggleUserStatus(id) {
            const user = this.users.find(u => u.id === id);
            if (user) {
                user.status = user.status === 'active' ? 'inactive' : 'active';
                this.filterUsers();
            }
        },

        deleteUser(id) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                this.users = this.users.filter(user => user.id !== id);
                this.filteredUsers = [...this.users];

                if (window.showNotification) {
                    window.showNotification('تم حذف المستخدم بنجاح', 'success');
                }
            }
        },

        manageRoles() {
            if (window.showNotification) {
                window.showNotification('سيتم فتح نافذة إدارة الأدوار قريباً', 'info');
            }
        },

        exportUsers() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري التصدير...
            `;
            button.disabled = true;

            // محاكاة عملية التصدير
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                if (window.showNotification) {
                    window.showNotification('تم تصدير بيانات المستخدمين إلى Excel بنجاح!', 'success');
                }
            }, 2000);
        },

        clearFilters() {
            this.filters = {
                search: '',
                role: '',
                status: ''
            };
            this.filteredUsers = [...this.users];

            if (window.showNotification) {
                window.showNotification('تم مسح جميع الفلاتر', 'info');
            }
        },

        applyFilters() {
            this.filterUsers();
            if (window.showNotification) {
                window.showNotification('تم تطبيق الفلاتر', 'info');
            }
        },

        getStatusText(status) {
            return status === 'active' ? 'نشط' : 'غير نشط';
        },

        getStatusClass(status) {
            return status === 'active'
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
        }
    }
}
</script>
@endpush
@endsection
