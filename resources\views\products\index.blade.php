@extends('layouts.main')

@section('page-title', 'إدارة المنتجات')
@section('page-description', 'إدارة المنتجات والمخزون')

@section('content')
<div class="content-wrapper animate-fade-in-up" x-data="productsManager()">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">إدارة المنتجات</h1>
                    <p class="page-subtitle">إدارة المنتجات والمخزون والتصنيفات بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="showAddModal = true" class="btn btn-primary hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        إضافة منتج جديد
                    </button>
                    <button @click="exportProducts()" class="btn btn-success hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        تصدير Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card hover-lift animate-scale-in animate-delay-100">
            <div class="stat-value" style="color: var(--accent-color);" x-text="products.length">6</div>
            <div class="stat-label">إجمالي المنتجات</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                تنوع جيد
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-200">
            <div class="stat-value" style="color: var(--success-color);" x-text="products.filter(p => p.stock > p.min_stock).length">3</div>
            <div class="stat-label">منتجات متوفرة</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                حالة جيدة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-300">
            <div class="stat-value" style="color: var(--warning-color);" x-text="products.filter(p => p.stock <= p.min_stock && p.stock > 0).length">2</div>
            <div class="stat-label">مخزون منخفض</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                يحتاج تجديد
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-500">
            <div class="stat-value" style="color: var(--danger-color);" x-text="products.filter(p => p.stock === 0).length">1</div>
            <div class="stat-label">نفد المخزون</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                يحتاج طلب فوري
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card animate-fade-in-up animate-delay-300">
        <div class="card-header">
            <h3 class="card-title">فلترة وبحث المنتجات</h3>
            <button @click="clearFilters()" class="btn btn-ghost btn-sm hover-scale">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                مسح الفلاتر
            </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div class="md:col-span-2 form-group">
                <label class="form-label">البحث</label>
                <div class="search-box">
                    <input type="text"
                           x-model="searchQuery"
                           placeholder="البحث بالاسم أو الباركود..."
                           class="search-input focus-glow">
                    <svg class="search-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>

            <!-- Category Filter -->
            <div class="form-group">
                <label class="form-label">التصنيف</label>
                <select x-model="selectedCategory" class="form-select focus-glow">
                    <option value="">جميع التصنيفات</option>
                    <option value="accessories">إكسسوارات</option>
                    <option value="cables">كابلات</option>
                    <option value="cases">جرابات</option>
                    <option value="chargers">شواحن</option>
                    <option value="parts">قطع غيار</option>
                </select>
            </div>

            <!-- Stock Filter -->
            <div class="form-group">
                <label class="form-label">حالة المخزون</label>
                <select x-model="stockFilter" class="form-select focus-glow">
                    <option value="">جميع المنتجات</option>
                    <option value="in_stock">متوفر</option>
                    <option value="low_stock">مخزون منخفض</option>
                    <option value="out_of_stock">نفد المخزون</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="table-container animate-fade-in-up animate-delay-500">
        <div class="card-header">
            <h3 class="card-title">جدول المنتجات</h3>
            <div class="action-group">
                <button @click="refreshProducts()" class="btn btn-ghost btn-sm hover-rotate">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    تحديث
                </button>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>التصنيف</th>
                        <th>السعر</th>
                        <th>المخزون</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="(product, index) in filteredProducts" :key="product.id">
                        <tr class="hover-lift" :class="'animate-fade-in-up animate-delay-' + (index * 100)">
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="w-12 h-12 rounded-lg overflow-hidden">
                                        <img x-show="product.image"
                                             :src="product.image"
                                             :alt="product.name"
                                             class="w-full h-full object-cover">
                                        <div x-show="!product.image"
                                             class="w-full h-full bg-accent-color/10 flex items-center justify-center">
                                            <svg class="w-6 h-6 text-accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="product.name"></div>
                                        <div class="text-sm text-muted-light dark:text-muted-dark" x-text="'كود: ' + product.barcode"></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-info" x-text="getCategoryName(product.category)"></span>
                            </td>
                            <td class="text-sm font-semibold text-success-color" x-text="formatCurrency(product.price)"></td>
                            <td>
                                <div class="flex items-center gap-2">
                                    <span class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="product.stock"></span>
                                    <div class="progress" style="width: 60px; height: 4px;">
                                        <div class="progress-bar" :class="getStockProgressClass(product)" :style="'width: ' + Math.min((product.stock / (product.min_stock * 2)) * 100, 100) + '%'"></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge" :class="getStockBadgeClass(product)" x-text="getStockStatusText(product)"></span>
                            </td>
                            <td>
                                <div class="action-group">
                                    <button @click="editProduct(product)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        تعديل
                                    </button>
                                    <button @click="deleteProduct(product.id)" class="btn btn-ghost btn-sm hover-scale" style="color: var(--danger-color);">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        حذف
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <!-- Empty State -->
        <div x-show="filteredProducts.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد منتجات</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">ابدأ بإضافة منتج جديد</p>
            <div class="mt-6">
                <button @click="showAddModal = true" class="btn-primary">
                    إضافة منتج جديد
                </button>
            </div>
        </div>
    </div>

    <!-- Add/Edit Product Modal -->
    <div x-show="showAddModal || showEditModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form @submit.prevent="saveProduct()">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4" 
                                    x-text="showAddModal ? 'إضافة منتج جديد' : 'تعديل المنتج'"></h3>
                                
                                <div class="space-y-4">
                                    <!-- Product Name -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم المنتج</label>
                                        <input type="text" x-model="productForm.name" required class="input-field">
                                    </div>

                                    <!-- Category -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التصنيف</label>
                                        <select x-model="productForm.category" required class="input-field">
                                            <option value="">اختر التصنيف</option>
                                            <option value="accessories">إكسسوارات</option>
                                            <option value="cables">كابلات</option>
                                            <option value="cases">جرابات</option>
                                            <option value="chargers">شواحن</option>
                                            <option value="parts">قطع غيار</option>
                                        </select>
                                    </div>

                                    <!-- Price and Stock -->
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">السعر (د.ع)</label>
                                            <input type="number" x-model="productForm.price" required min="0" class="input-field">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الكمية</label>
                                            <input type="number" x-model="productForm.stock" required min="0" class="input-field">
                                        </div>
                                    </div>

                                    <!-- Min Stock and Barcode -->
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحد الأدنى</label>
                                            <input type="number" x-model="productForm.min_stock" required min="0" class="input-field">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الباركود</label>
                                            <input type="text" x-model="productForm.barcode" class="input-field">
                                        </div>
                                    </div>

                                    <!-- Description -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الوصف</label>
                                        <textarea x-model="productForm.description" rows="3" class="input-field"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="btn-primary sm:mr-3 sm:w-auto">
                            <span x-text="showAddModal ? 'إضافة' : 'حفظ التغييرات'"></span>
                        </button>
                        <button type="button" @click="closeModal()" class="btn-secondary sm:w-auto">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function productsManager() {
    return {
        searchQuery: '',
        selectedCategory: '',
        stockFilter: '',
        showAddModal: false,
        showEditModal: false,
        
        productForm: {
            id: null,
            name: '',
            category: '',
            price: '',
            stock: '',
            min_stock: '',
            barcode: '',
            description: '',
            image: null
        },
        
        products: [
            { id: 1, name: 'كابل USB-C', category: 'cables', price: 15000, stock: 50, min_stock: 20, barcode: '123456789', description: 'كابل USB-C عالي الجودة', image: null },
            { id: 2, name: 'شاحن سريع 20W', category: 'chargers', price: 35000, stock: 30, min_stock: 10, barcode: '123456790', description: 'شاحن سريع للهواتف الذكية', image: null },
            { id: 3, name: 'جراب iPhone 13', category: 'cases', price: 25000, stock: 25, min_stock: 15, barcode: '123456791', description: 'جراب حماية للآيفون 13', image: null },
            { id: 4, name: 'سماعات AirPods', category: 'accessories', price: 180000, stock: 15, min_stock: 5, barcode: '123456792', description: 'سماعات لاسلكية عالية الجودة', image: null },
            { id: 5, name: 'شاشة Samsung A52', category: 'parts', price: 120000, stock: 8, min_stock: 10, barcode: '123456793', description: 'شاشة أصلية لجهاز Samsung A52', image: null },
            { id: 6, name: 'بطارية iPhone 12', category: 'parts', price: 85000, stock: 0, min_stock: 5, barcode: '123456794', description: 'بطارية أصلية للآيفون 12', image: null }
        ],
        
        get filteredProducts() {
            return this.products.filter(product => {
                const matchesSearch = product.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                                    product.barcode.includes(this.searchQuery);
                const matchesCategory = !this.selectedCategory || product.category === this.selectedCategory;
                const matchesStock = !this.stockFilter || 
                                   (this.stockFilter === 'in_stock' && product.stock > product.min_stock) ||
                                   (this.stockFilter === 'low_stock' && product.stock <= product.min_stock && product.stock > 0) ||
                                   (this.stockFilter === 'out_of_stock' && product.stock === 0);
                
                return matchesSearch && matchesCategory && matchesStock;
            });
        },
        
        editProduct(product) {
            this.productForm = { ...product };
            this.showEditModal = true;
        },
        
        deleteProduct(productId) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                this.products = this.products.filter(p => p.id !== productId);
                alert('تم حذف المنتج بنجاح');
            }
        },
        
        saveProduct() {
            if (this.showAddModal) {
                // Add new product
                const newId = Math.max(...this.products.map(p => p.id)) + 1;
                this.products.push({
                    ...this.productForm,
                    id: newId,
                    price: parseFloat(this.productForm.price),
                    stock: parseInt(this.productForm.stock),
                    min_stock: parseInt(this.productForm.min_stock)
                });
                alert('تم إضافة المنتج بنجاح');
            } else {
                // Update existing product
                const index = this.products.findIndex(p => p.id === this.productForm.id);
                if (index !== -1) {
                    this.products[index] = {
                        ...this.productForm,
                        price: parseFloat(this.productForm.price),
                        stock: parseInt(this.productForm.stock),
                        min_stock: parseInt(this.productForm.min_stock)
                    };
                    alert('تم تحديث المنتج بنجاح');
                }
            }
            this.closeModal();
        },
        
        closeModal() {
            this.showAddModal = false;
            this.showEditModal = false;
            this.productForm = {
                id: null,
                name: '',
                category: '',
                price: '',
                stock: '',
                min_stock: '',
                barcode: '',
                description: '',
                image: null
            };
        },
        
        exportProducts() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري التصدير...
            `;
            button.disabled = true;

            // محاكاة عملية التصدير
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                if (window.showNotification) {
                    window.showNotification('تم تصدير بيانات المنتجات إلى Excel بنجاح!', 'success');
                }
            }, 2000);
        },

        refreshProducts() {
            // إضافة تأثير التحديث
            const button = event.target;
            button.classList.add('animate-spin');

            // محاكاة تحديث البيانات
            setTimeout(() => {
                button.classList.remove('animate-spin');
                if (window.showNotification) {
                    window.showNotification('تم تحديث بيانات المنتجات', 'info');
                }
            }, 1000);
        },

        clearFilters() {
            this.searchQuery = '';
            this.selectedCategory = '';
            this.stockFilter = '';

            if (window.showNotification) {
                window.showNotification('تم مسح جميع الفلاتر', 'info');
            }
        },

        getStockBadgeClass(product) {
            if (product.stock > product.min_stock) return 'badge-success';
            if (product.stock > 0) return 'badge-warning';
            return 'badge-danger';
        },

        getStockProgressClass(product) {
            if (product.stock > product.min_stock) return 'success';
            if (product.stock > 0) return 'warning';
            return 'danger';
        },

        getStockStatusText(product) {
            if (product.stock > product.min_stock) return 'متوفر';
            if (product.stock > 0) return 'مخزون منخفض';
            return 'نفد المخزون';
        },
        
        formatCurrency(amount) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'currency',
                currency: 'IQD',
                minimumFractionDigits: 0
            }).format(amount);
        },
        
        getCategoryName(category) {
            const categories = {
                'accessories': 'إكسسوارات',
                'cables': 'كابلات',
                'cases': 'جرابات',
                'chargers': 'شواحن',
                'parts': 'قطع غيار'
            };
            return categories[category] || category;
        }
    }
}
</script>
@endpush
@endsection
