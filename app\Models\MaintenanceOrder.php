<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MaintenanceOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'location_id',
        'customer_id',
        'technician_id',
        'order_number',
        'device_type',
        'device_brand',
        'device_model',
        'serial_number',
        'problem_description',
        'diagnosis',
        'solution',
        'status',
        'priority',
        'estimated_cost',
        'actual_cost',
        'received_date',
        'estimated_completion_date',
        'completed_date',
        'notes'
    ];

    protected $casts = [
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'received_date' => 'datetime',
        'estimated_completion_date' => 'datetime',
        'completed_date' => 'datetime'
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع الموقع
     */
    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * العلاقة مع العميل
     */
    public function customer()
    {
        return $this->belongsTo(Contact::class, 'customer_id');
    }

    /**
     * العلاقة مع الفني
     */
    public function technician()
    {
        return $this->belongsTo(User::class, 'technician_id');
    }

    /**
     * الحصول على حالة الطلب مترجمة
     */
    public function getStatusNameAttribute()
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'in_progress' => 'قيد التنفيذ',
            'waiting_parts' => 'في انتظار قطع الغيار',
            'completed' => 'مكتمل',
            'delivered' => 'تم التسليم',
            'cancelled' => 'ملغي'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * الحصول على أولوية الطلب مترجمة
     */
    public function getPriorityNameAttribute()
    {
        $priorities = [
            'low' => 'منخفضة',
            'normal' => 'عادية',
            'high' => 'عالية',
            'urgent' => 'عاجلة'
        ];

        return $priorities[$this->priority] ?? $this->priority;
    }

    /**
     * scope للطلبات المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * scope للطلبات قيد التنفيذ
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * scope للطلبات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * إنشاء رقم طلب صيانة تلقائي
     */
    public static function generateOrderNumber($companyId)
    {
        $prefix = 'MNT';
        $lastOrder = self::where('company_id', $companyId)
            ->orderBy('id', 'desc')
            ->first();

        $number = $lastOrder ? (int) substr($lastOrder->order_number, strlen($prefix)) + 1 : 1;
        
        return $prefix . str_pad($number, 6, '0', STR_PAD_LEFT);
    }
}
