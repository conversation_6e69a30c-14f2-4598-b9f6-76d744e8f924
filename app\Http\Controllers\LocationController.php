<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LocationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * عرض قائمة المواقع
     */
    public function index()
    {
        $user = Auth::user();
        
        if (!$user->hasPermission('locations.view')) {
            abort(403, 'ليس لديك صلاحية لعرض المواقع');
        }

        $locations = Location::where('company_id', $user->company_id)
            ->with(['manager', 'users'])
            ->get();

        return view('locations.index', compact('locations'));
    }

    /**
     * عرض نموذج إنشاء موقع جديد
     */
    public function create()
    {
        $user = Auth::user();
        
        if (!$user->hasPermission('locations.create')) {
            abort(403, 'ليس لديك صلاحية لإنشاء موقع');
        }

        $managers = User::where('company_id', $user->company_id)
            ->whereHas('role', function ($q) {
                $q->where('name_en', 'Manager');
            })
            ->get();

        return view('locations.create', compact('managers'));
    }

    /**
     * حفظ موقع جديد
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->hasPermission('locations.create')) {
            abort(403, 'ليس لديك صلاحية لإنشاء موقع');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'type' => 'required|in:store,warehouse,service_center',
            'code' => 'required|string|max:50|unique:locations,code',
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'manager_id' => 'nullable|exists:users,id'
        ], [
            'name.required' => 'اسم الموقع مطلوب',
            'type.required' => 'نوع الموقع مطلوب',
            'code.required' => 'رمز الموقع مطلوب',
            'code.unique' => 'رمز الموقع موجود مسبقاً',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'manager_id.exists' => 'المدير المحدد غير موجود'
        ]);

        $location = Location::create([
            'company_id' => $user->company_id,
            'name' => $request->name,
            'name_en' => $request->name_en,
            'type' => $request->type,
            'code' => $request->code,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'city' => $request->city,
            'country' => $request->country,
            'postal_code' => $request->postal_code,
            'manager_id' => $request->manager_id,
            'is_active' => $request->boolean('is_active', true),
            'is_default' => $request->boolean('is_default', false),
            'invoice_settings' => [
                'template' => 'default',
                'show_logo' => true,
                'show_company_details' => true,
                'footer_text' => 'شكراً لتعاملكم معنا'
            ],
            'pos_settings' => [
                'default_customer' => 'Walk-In Customer',
                'auto_print' => false,
                'receipt_template' => 'thermal'
            ]
        ]);

        // إذا كان هذا الموقع افتراضي، قم بإلغاء الافتراضية من المواقع الأخرى
        if ($location->is_default) {
            Location::where('company_id', $user->company_id)
                ->where('id', '!=', $location->id)
                ->update(['is_default' => false]);
        }

        $user->logActivity('location.created', "تم إنشاء موقع جديد: {$location->name}", $location);

        return redirect()->route('locations.index')
            ->with('success', 'تم إنشاء الموقع بنجاح');
    }

    /**
     * عرض تفاصيل موقع
     */
    public function show(Location $location)
    {
        $user = Auth::user();
        
        if (!$user->hasPermission('locations.view') || $location->company_id !== $user->company_id) {
            abort(403, 'ليس لديك صلاحية لعرض هذا الموقع');
        }

        $location->load(['manager', 'users', 'inventories.product']);

        return view('locations.show', compact('location'));
    }

    /**
     * عرض نموذج تعديل موقع
     */
    public function edit(Location $location)
    {
        $user = Auth::user();
        
        if (!$user->hasPermission('locations.edit') || $location->company_id !== $user->company_id) {
            abort(403, 'ليس لديك صلاحية لتعديل هذا الموقع');
        }

        $managers = User::where('company_id', $user->company_id)
            ->whereHas('role', function ($q) {
                $q->where('name_en', 'Manager');
            })
            ->get();

        return view('locations.edit', compact('location', 'managers'));
    }

    /**
     * تحديث موقع
     */
    public function update(Request $request, Location $location)
    {
        $user = Auth::user();
        
        if (!$user->hasPermission('locations.edit') || $location->company_id !== $user->company_id) {
            abort(403, 'ليس لديك صلاحية لتعديل هذا الموقع');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'type' => 'required|in:store,warehouse,service_center',
            'code' => 'required|string|max:50|unique:locations,code,' . $location->id,
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'manager_id' => 'nullable|exists:users,id'
        ], [
            'name.required' => 'اسم الموقع مطلوب',
            'type.required' => 'نوع الموقع مطلوب',
            'code.required' => 'رمز الموقع مطلوب',
            'code.unique' => 'رمز الموقع موجود مسبقاً',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'manager_id.exists' => 'المدير المحدد غير موجود'
        ]);

        $location->update([
            'name' => $request->name,
            'name_en' => $request->name_en,
            'type' => $request->type,
            'code' => $request->code,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'city' => $request->city,
            'country' => $request->country,
            'postal_code' => $request->postal_code,
            'manager_id' => $request->manager_id,
            'is_active' => $request->boolean('is_active', true),
            'is_default' => $request->boolean('is_default', false)
        ]);

        // إذا كان هذا الموقع افتراضي، قم بإلغاء الافتراضية من المواقع الأخرى
        if ($location->is_default) {
            Location::where('company_id', $user->company_id)
                ->where('id', '!=', $location->id)
                ->update(['is_default' => false]);
        }

        $user->logActivity('location.updated', "تم تحديث الموقع: {$location->name}", $location);

        return redirect()->route('locations.index')
            ->with('success', 'تم تحديث الموقع بنجاح');
    }

    /**
     * حذف موقع
     */
    public function destroy(Location $location)
    {
        $user = Auth::user();
        
        if (!$user->hasPermission('locations.delete') || $location->company_id !== $user->company_id) {
            abort(403, 'ليس لديك صلاحية لحذف هذا الموقع');
        }

        // التحقق من عدم وجود بيانات مرتبطة
        if ($location->inventories()->count() > 0 || $location->sales()->count() > 0) {
            return redirect()->route('locations.index')
                ->with('error', 'لا يمكن حذف الموقع لوجود بيانات مرتبطة به');
        }

        $locationName = $location->name;
        $location->delete();

        $user->logActivity('location.deleted', "تم حذف الموقع: {$locationName}");

        return redirect()->route('locations.index')
            ->with('success', 'تم حذف الموقع بنجاح');
    }

    /**
     * تحديث إعدادات الفاتورة
     */
    public function updateInvoiceSettings(Request $request, Location $location)
    {
        $user = Auth::user();
        
        if (!$user->hasPermission('locations.edit') || $location->company_id !== $user->company_id) {
            abort(403, 'ليس لديك صلاحية لتعديل إعدادات هذا الموقع');
        }

        $settings = $request->validate([
            'template' => 'required|string',
            'show_logo' => 'boolean',
            'show_company_details' => 'boolean',
            'footer_text' => 'nullable|string'
        ]);

        $location->update(['invoice_settings' => $settings]);

        $user->logActivity('location.invoice_settings_updated', "تم تحديث إعدادات الفاتورة للموقع: {$location->name}", $location);

        return response()->json(['success' => true, 'message' => 'تم تحديث إعدادات الفاتورة بنجاح']);
    }

    /**
     * تحديث إعدادات نقاط البيع
     */
    public function updatePosSettings(Request $request, Location $location)
    {
        $user = Auth::user();
        
        if (!$user->hasPermission('locations.edit') || $location->company_id !== $user->company_id) {
            abort(403, 'ليس لديك صلاحية لتعديل إعدادات هذا الموقع');
        }

        $settings = $request->validate([
            'default_customer' => 'required|string',
            'auto_print' => 'boolean',
            'receipt_template' => 'required|string'
        ]);

        $location->update(['pos_settings' => $settings]);

        $user->logActivity('location.pos_settings_updated', "تم تحديث إعدادات نقاط البيع للموقع: {$location->name}", $location);

        return response()->json(['success' => true, 'message' => 'تم تحديث إعدادات نقاط البيع بنجاح']);
    }
}
