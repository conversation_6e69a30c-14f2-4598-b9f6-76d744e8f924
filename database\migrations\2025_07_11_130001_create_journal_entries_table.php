<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('journal_entries', function (Blueprint $table) {
            $table->id();
            $table->string('entry_number', 20)->unique()->comment('رقم القيد');
            $table->date('entry_date')->comment('تاريخ القيد');
            $table->string('reference_type', 50)->nullable()->comment('نوع المرجع');
            $table->unsignedBigInteger('reference_id')->nullable()->comment('معرف المرجع');
            $table->text('description')->comment('وصف القيد');
            $table->decimal('total_debit', 15, 2)->default(0)->comment('إجمالي المدين');
            $table->decimal('total_credit', 15, 2)->default(0)->comment('إجمالي الدائن');
            $table->enum('status', ['draft', 'posted', 'reversed'])->default('draft')->comment('حالة القيد');
            $table->unsignedBigInteger('created_by')->comment('المستخدم المنشئ');
            $table->unsignedBigInteger('posted_by')->nullable()->comment('المستخدم المرحل');
            $table->timestamp('posted_at')->nullable()->comment('تاريخ الترحيل');
            $table->unsignedBigInteger('reversed_by')->nullable()->comment('المستخدم المعكس');
            $table->timestamp('reversed_at')->nullable()->comment('تاريخ العكس');
            $table->text('reversal_reason')->nullable()->comment('سبب العكس');
            $table->json('metadata')->nullable()->comment('بيانات إضافية');
            $table->timestamps();

            // Indexes
            $table->index(['entry_date', 'status']);
            $table->index(['reference_type', 'reference_id']);
            $table->index(['status']);
            $table->index(['created_by']);
            
            // Foreign Keys
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('posted_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('reversed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('journal_entries');
    }
};
