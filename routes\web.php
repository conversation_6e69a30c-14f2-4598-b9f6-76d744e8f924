<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\JournalEntryController;
use App\Http\Controllers\AccountingReportController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Test Route
Route::get('/test', function () {
    return 'النظام يعمل بشكل صحيح!';
});

// Test Schedule Route
Route::get('/test-schedule', function () {
    return 'Schedule route يعمل بشكل صحيح!';
});

// Test Parts Route
Route::get('/test-parts', function () {
    return 'Parts route يعمل بشكل صحيح!';
});

// Test Customers Route
Route::get('/test-customers', function () {
    return 'Customers route يعمل بشكل صحيح!';
});

// Simple Schedule Test
Route::get('/simple-schedule', function () {
    return view('technicians.schedule', [
        'technicians' => collect([]),
        'unassignedRepairsList' => collect([]),
        'availableTechnicians' => 0,
        'unassignedRepairs' => 0,
        'activeRepairs' => 0,
        'averageWorkload' => 0,
        'availableTechniciansList' => collect([])
    ]);
});

// Test Dashboard
Route::get('/test-dashboard', function () {
    $tests = [
        'basic' => [
            'name' => 'اختبارات أساسية',
            'routes' => [
                '/test' => 'اختبار النظام الأساسي',
                '/test-schedule' => 'اختبار route الجدولة',
                '/simple-schedule' => 'اختبار view الجدولة (فارغ)'
            ]
        ],
        'schedule' => [
            'name' => 'اختبار الجدولة الكاملة',
            'routes' => [
                '/technicians/schedule' => 'جدولة الفنيين (الهدف الرئيسي)'
            ]
        ],
        'technicians' => [
            'name' => 'إدارة الفنيين',
            'routes' => [
                '/technicians' => 'قائمة الفنيين',
                '/technicians/create' => 'إضافة فني جديد'
            ]
        ],
        'repairs' => [
            'name' => 'إدارة طلبات الصيانة',
            'routes' => [
                '/repairs' => 'قائمة طلبات الصيانة',
                '/repairs/create' => 'إضافة طلب جديد'
            ]
        ]
    ];

    return view('test-dashboard', compact('tests'));
});

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login'])->middleware('login.rate.limit')->name('login');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
Route::get('/password/reset', [AuthController::class, 'showForgotPasswordForm'])->name('password.request');
Route::post('/password/email', [AuthController::class, 'sendResetLinkEmail'])->name('password.email');

// Protected Routes
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Locations Management
    Route::resource('locations', App\Http\Controllers\LocationController::class);
    Route::post('/locations/{location}/invoice-settings', [App\Http\Controllers\LocationController::class, 'updateInvoiceSettings'])->name('locations.invoice-settings');
    Route::post('/locations/{location}/pos-settings', [App\Http\Controllers\LocationController::class, 'updatePosSettings'])->name('locations.pos-settings');

    // POS Routes
    Route::prefix('pos')->name('pos.')->group(function () {
        Route::get('/', function () {
            return view('pos.index');
        })->name('index');
    });

    // Maintenance Routes
    Route::prefix('maintenance')->name('maintenance.')->group(function () {
        Route::get('/', function () {
            return view('maintenance.index');
        })->name('index');

        Route::get('/create', function () {
            return view('maintenance.create');
        })->name('create');

        Route::get('/pending', function () {
            return view('maintenance.index', ['filter' => 'pending']);
        })->name('pending');

        Route::get('/completed', function () {
            return view('maintenance.index', ['filter' => 'completed']);
        })->name('completed');
    });

    // Products Routes
    Route::prefix('products')->name('products.')->group(function () {
        Route::get('/', function () {
            return view('products.index');
        })->name('index');

        Route::get('/create', function () {
            return view('products.create');
        })->name('create');

        Route::get('/categories', function () {
            return view('products.categories');
        })->name('categories');

        Route::get('/low-stock', function () {
            return view('products.index', ['filter' => 'low_stock']);
        })->name('low-stock');
    });

    // Inventory Routes
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::get('/', function () {
            return view('inventory.index');
        })->name('index');
    });

    // Customers Routes
    Route::prefix('customers')->name('customers.')->group(function () {
        Route::get('/', function () {
            return view('customers.index');
        })->name('index');
    });

    // Suppliers Routes
    Route::prefix('suppliers')->name('suppliers.')->group(function () {
        Route::get('/', function () {
            return view('suppliers.index');
        })->name('index');
    });

    // Users Routes
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', function () {
            return view('users.index');
        })->name('index');
    });

    // Profile Routes
    Route::get('/profile', function () {
        return view('profile.show');
    })->name('profile.show');

    // Sales Routes
    Route::prefix('sales')->name('sales.')->group(function () {
        Route::get('/', function () {
            return view('sales.index');
        })->name('index');
        Route::get('/create', function () {
            return view('sales.create');
        })->name('create');
        Route::get('/{id}', function ($id) {
            return view('sales.show', compact('id'));
        })->name('show');
        Route::get('/{id}/edit', function ($id) {
            return view('sales.edit', compact('id'));
        })->name('edit');
        Route::get('/{id}/print', function ($id) {
            return view('sales.print', compact('id'));
        })->name('print');
    });

    // Payments Routes
    Route::prefix('payments')->name('payments.')->group(function () {
        Route::get('/', function () {
            return view('payments.index');
        })->name('index');
        Route::get('/{id}', function ($id) {
            return view('payments.show', compact('id'));
        })->name('show');
    });

    // Purchases Routes
    Route::prefix('purchases')->name('purchases.')->group(function () {
        Route::get('/', function () {
            return view('purchases.index');
        })->name('index');
        Route::get('/create', function () {
            return view('purchases.create');
        })->name('create');
        Route::get('/{id}', function ($id) {
            return view('purchases.show', compact('id'));
        })->name('show');
        Route::get('/{id}/edit', function ($id) {
            return view('purchases.edit', compact('id'));
        })->name('edit');
        Route::get('/{id}/print', function ($id) {
            return view('purchases.print', compact('id'));
        })->name('print');
    });

    // Customers Routes (additional)
    Route::prefix('customers')->name('customers.')->group(function () {
        Route::get('/{id}', function ($id) {
            return view('customers.show', compact('id'));
        })->name('show');
        Route::get('/{id}/edit', function ($id) {
            return view('customers.edit', compact('id'));
        })->name('edit');
    });

    // Credit Management Routes
    Route::prefix('credit')->name('credit.')->group(function () {
        Route::get('/', function () {
            return view('credit.index');
        })->name('index');
    });

    // Roles and Permissions Routes
    Route::prefix('roles')->name('roles.')->group(function () {
        Route::get('/', function () {
            return view('roles.index');
        })->name('index');
        Route::get('/create', function () {
            return view('roles.create');
        })->name('create');
        Route::get('/{id}/edit', function ($id) {
            return view('roles.edit', compact('id'));
        })->name('edit');
    });









    // Customer routes - EXPLICIT ORDER TO PREVENT CONFLICTS

    // 1. Static routes FIRST (no parameters)
    Route::get('/customers/search', [App\Http\Controllers\CustomerController::class, 'search'])->name('customers.search');
    Route::get('/customers/export', [App\Http\Controllers\CustomerController::class, 'export'])->name('customers.export');
    Route::get('/customers/create', [App\Http\Controllers\CustomerController::class, 'create'])->name('customers.create');

    // 2. POST routes
    Route::post('/customers', [App\Http\Controllers\CustomerController::class, 'store'])->name('customers.store');
    Route::post('/customers/bulk-update', [App\Http\Controllers\CustomerController::class, 'bulkUpdate'])->name('customers.bulk-update');

    // 3. Standard CRUD routes (excluding create which is already defined)
    Route::get('/customers', [App\Http\Controllers\CustomerController::class, 'index'])->name('customers.index');
    Route::get('/customers/{customer}', [App\Http\Controllers\CustomerController::class, 'show'])->name('customers.show');
    Route::get('/customers/{customer}/edit', [App\Http\Controllers\CustomerController::class, 'edit'])->name('customers.edit');
    Route::put('/customers/{customer}', [App\Http\Controllers\CustomerController::class, 'update'])->name('customers.update');
    Route::patch('/customers/{customer}', [App\Http\Controllers\CustomerController::class, 'update']);
    Route::delete('/customers/{customer}', [App\Http\Controllers\CustomerController::class, 'destroy'])->name('customers.destroy');

    // 4. Customer-specific routes with parameters LAST
    Route::get('/customers/{customer}/repairs', [App\Http\Controllers\CustomerController::class, 'repairs'])->name('customers.repairs');
    Route::get('/customers/{customer}/devices', [App\Http\Controllers\CustomerController::class, 'devices'])->name('customers.devices');
    Route::post('/customers/{customer}/devices', [App\Http\Controllers\CustomerController::class, 'addDevice'])->name('customers.add-device');
    Route::get('/customers/{customer}/payments', [App\Http\Controllers\CustomerController::class, 'payments'])->name('customers.payments');
    Route::get('/customers/{customer}/communications', [App\Http\Controllers\CustomerController::class, 'communications'])->name('customers.communications');
    Route::post('/customers/{customer}/communications', [App\Http\Controllers\CustomerController::class, 'addCommunication'])->name('customers.add-communication');
    Route::get('/customers/{customer}/analytics', [App\Http\Controllers\CustomerController::class, 'analytics'])->name('customers.analytics');

    // Parts Routes
    Route::resource('parts', App\Http\Controllers\PartController::class);
    Route::prefix('parts')->name('parts.')->group(function () {
        Route::post('/{part}/update-stock', [App\Http\Controllers\PartController::class, 'updateStock'])->name('update-stock');
        Route::get('/{part}/stock-movements', [App\Http\Controllers\PartController::class, 'stockMovements'])->name('stock-movements');
        Route::get('/inventory/dashboard', [App\Http\Controllers\PartController::class, 'inventory'])->name('inventory');
        Route::get('/inventory/low-stock', [App\Http\Controllers\PartController::class, 'lowStock'])->name('low-stock');
        Route::get('/inventory/out-of-stock', [App\Http\Controllers\PartController::class, 'outOfStock'])->name('out-of-stock');
        Route::post('/{part}/generate-barcode', [App\Http\Controllers\PartController::class, 'generateBarcode'])->name('generate-barcode');
        Route::post('/print-labels', [App\Http\Controllers\PartController::class, 'printLabels'])->name('print-labels');
        Route::post('/bulk-update', [App\Http\Controllers\PartController::class, 'bulkUpdate'])->name('bulk-update');
        Route::get('/export', [App\Http\Controllers\PartController::class, 'export'])->name('export');
    });

    // Inventory Management Routes
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::get('/', [App\Http\Controllers\InventoryController::class, 'index'])->name('index');
        Route::get('/dashboard', [App\Http\Controllers\InventoryController::class, 'index'])->name('dashboard');
        Route::get('/movements', [App\Http\Controllers\InventoryController::class, 'movements'])->name('movements');
        Route::get('/adjust-stock', [App\Http\Controllers\InventoryController::class, 'adjustStock'])->name('adjust-stock');
        Route::post('/process-adjustment', [App\Http\Controllers\InventoryController::class, 'processAdjustment'])->name('process-adjustment');
        Route::get('/transfer-stock', [App\Http\Controllers\InventoryController::class, 'transferStock'])->name('transfer-stock');
        Route::post('/process-transfer', [App\Http\Controllers\InventoryController::class, 'processTransfer'])->name('process-transfer');
        Route::get('/reports', [App\Http\Controllers\InventoryController::class, 'reports'])->name('reports');
        Route::get('/low-stock', [App\Http\Controllers\InventoryController::class, 'lowStockAlerts'])->name('low-stock');
    });

    // Repairs Routes
    // Define specific routes before resource routes to avoid conflicts
    Route::get('repairs/workflow', [App\Http\Controllers\RepairController::class, 'workflow'])->name('repairs.workflow');
    Route::get('repairs/export', [App\Http\Controllers\RepairController::class, 'export'])->name('repairs.export');

    Route::resource('repairs', App\Http\Controllers\RepairController::class);
    Route::prefix('repairs')->name('repairs.')->group(function () {
        Route::post('/bulk-status-update', [App\Http\Controllers\RepairController::class, 'bulkStatusUpdate'])->name('bulk-status-update');
        Route::post('/{repair}/status', [App\Http\Controllers\RepairController::class, 'updateStatus'])->name('update-status');
        Route::post('/{repair}/assign-technician', [App\Http\Controllers\RepairController::class, 'assignTechnician'])->name('assign-technician');
        Route::post('/{repair}/generate-estimate', [App\Http\Controllers\RepairController::class, 'generateEstimate'])->name('generate-estimate');
        Route::get('/{repair}/timeline', [App\Http\Controllers\RepairController::class, 'timeline'])->name('timeline');
        Route::post('/{repair}/parts', [App\Http\Controllers\RepairController::class, 'addPart'])->name('add-part');
        Route::delete('/{repair}/parts', [App\Http\Controllers\RepairController::class, 'removePart'])->name('remove-part');
        Route::get('/{repair}/print', [App\Http\Controllers\RepairController::class, 'print'])->name('print');
        Route::get('/{repair}/invoice', [App\Http\Controllers\RepairController::class, 'invoice'])->name('invoice');
        Route::get('/{repair}/warranty', [App\Http\Controllers\RepairController::class, 'warranty'])->name('warranty');
        Route::post('/{repair}/duplicate', [App\Http\Controllers\RepairController::class, 'duplicate'])->name('duplicate');
    });

    // Schedule Routes
    Route::prefix('schedule')->name('schedule.')->group(function () {
        Route::get('/', function () {
            return view('schedule.index');
        })->name('index');
        Route::get('/create', function () {
            return view('schedule.create');
        })->name('create');
    });

    // Technicians Routes
    // Define specific routes before resource routes to avoid conflicts
    Route::get('technicians/schedule', [App\Http\Controllers\TechnicianController::class, 'schedule'])->name('technicians.schedule');
    Route::get('technicians/workload', [App\Http\Controllers\TechnicianController::class, 'workload'])->name('technicians.workload');
    Route::get('technicians/scheduling', [App\Http\Controllers\TechnicianController::class, 'scheduling'])->name('technicians.scheduling');
    Route::get('technicians/export', [App\Http\Controllers\TechnicianController::class, 'export'])->name('technicians.export');
    Route::post('technicians/bulk-assign', [App\Http\Controllers\TechnicianController::class, 'bulkAssign'])->name('technicians.bulk-assign');
    Route::post('technicians/auto-assign', [App\Http\Controllers\TechnicianController::class, 'autoAssignRepairs'])->name('technicians.auto-assign');

    Route::resource('technicians', App\Http\Controllers\TechnicianController::class);

    Route::prefix('technicians')->name('technicians.')->group(function () {
        Route::get('/{technician}/analytics', [App\Http\Controllers\TechnicianController::class, 'analytics'])->name('analytics');
        Route::get('/{technician}/skills', [App\Http\Controllers\TechnicianController::class, 'skills'])->name('skills');
        Route::put('/{technician}/skills', [App\Http\Controllers\TechnicianController::class, 'updateSkills'])->name('update-skills');
        Route::get('/{technician}/performance-report', [App\Http\Controllers\TechnicianController::class, 'performanceReport'])->name('performance-report');
        Route::get('/{technician}/workload-data', [App\Http\Controllers\TechnicianController::class, 'getWorkloadData'])->name('workload-data');
        Route::get('/{technician}/performance', [App\Http\Controllers\TechnicianController::class, 'performance'])->name('performance');
        Route::post('/{technician}/schedule', [App\Http\Controllers\TechnicianController::class, 'updateSchedule'])->name('update-schedule');
        Route::post('/{technician}/availability', [App\Http\Controllers\TechnicianController::class, 'updateAvailability'])->name('update-availability');
        Route::post('/{technician}/assign-repair', [App\Http\Controllers\TechnicianController::class, 'assignRepair'])->name('assign-repair');
    });

    // Notifications Routes
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', function () {
            return view('notifications.index');
        })->name('index');
    });



    // Warranty Routes
    Route::prefix('warranty')->name('warranty.')->group(function () {
        Route::get('/', function () {
            return view('warranty.index');
        })->name('index');
        Route::get('/{id}/certificate', function ($id) {
            return view('warranty.certificate', compact('id'));
        })->name('certificate');
    });

    // Maintenance Routes
    Route::prefix('maintenance')->name('maintenance.')->group(function () {
        Route::get('/preventive', function () {
            return view('maintenance.preventive');
        })->name('preventive');
    });

    // Monitoring Routes
    Route::prefix('monitoring')->name('monitoring.')->group(function () {
        Route::get('/performance', function () {
            return view('monitoring.performance');
        })->name('performance');
    });

    // Security Routes
    Route::prefix('security')->name('security.')->group(function () {
        Route::get('/backup', function () {
            return view('security.backup');
        })->name('backup');
    });

    // Advanced Reports Routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/advanced', function () {
            return view('reports.advanced');
        })->name('advanced');
    });

    // Mobile App Routes
    Route::prefix('mobile')->name('mobile.')->group(function () {
        Route::get('/app', function () {
            return view('mobile.app');
        })->name('app');
    });

    // Barcode Routes
    Route::prefix('barcode')->name('barcode.')->group(function () {
        Route::get('/generator', function () {
            return view('barcode.generator');
        })->name('generator');
    });

    // Advanced Notifications Routes
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/advanced', function () {
            return view('notifications.advanced');
        })->name('advanced');
    });

    // Settings Routes
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', function () {
            return view('settings.index');
        })->name('index');
    });

    // Reports Routes (Consolidated)
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/purchases', function () {
            return view('reports.purchases');
        })->name('purchases');
        Route::get('/sales', function () {
            return view('reports.sales');
        })->name('sales');
        Route::get('/financial', function () {
            return view('reports.financial');
        })->name('financial');
        Route::get('/users', function () {
            return view('reports.users');
        })->name('users');
        Route::get('/repairs', function () {
            return view('reports.repairs');
        })->name('repairs');
        Route::get('/inventory', function () {
            return view('reports.inventory');
        })->name('inventory');
        Route::get('/maintenance', function () {
            return view('reports.maintenance');
        })->name('maintenance');
    });

    // POS Routes
    Route::prefix('pos')->name('pos.')->group(function () {
        Route::get('/', [App\Http\Controllers\POSController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\POSController::class, 'create'])->name('create');
        Route::post('/store', [App\Http\Controllers\POSController::class, 'store'])->name('store');
        Route::get('/sales/{sale}', [App\Http\Controllers\POSController::class, 'show'])->name('show');
        Route::post('/sales/{sale}/payment', [App\Http\Controllers\POSController::class, 'processPayment'])->name('process-payment');
        Route::post('/sales/{sale}/cancel', [App\Http\Controllers\POSController::class, 'cancel'])->name('cancel');
        Route::get('/search-products', [App\Http\Controllers\POSController::class, 'searchProducts'])->name('search-products');
        Route::get('/customers/{customer}', [App\Http\Controllers\POSController::class, 'getCustomer'])->name('get-customer');
        Route::post('/set-location', [App\Http\Controllers\POSController::class, 'setLocation'])->name('set-location');
        Route::post('/validate-promotion', [App\Http\Controllers\POSController::class, 'validatePromotion'])->name('validate-promotion');

        // Payment Routes
        Route::prefix('payments')->name('payments.')->group(function () {
            Route::post('/sales/{sale}/cash', [App\Http\Controllers\PaymentController::class, 'processCashPayment'])->name('cash');
            Route::post('/sales/{sale}/card', [App\Http\Controllers\PaymentController::class, 'processCardPayment'])->name('card');
            Route::post('/sales/{sale}/bank-transfer', [App\Http\Controllers\PaymentController::class, 'processBankTransferPayment'])->name('bank-transfer');
            Route::post('/sales/{sale}/installment', [App\Http\Controllers\PaymentController::class, 'processInstallmentPayment'])->name('installment');
            Route::post('/{payment}/confirm', [App\Http\Controllers\PaymentController::class, 'confirmPayment'])->name('confirm');
            Route::post('/{payment}/refund', [App\Http\Controllers\PaymentController::class, 'processRefund'])->name('refund');
            Route::get('/methods', [App\Http\Controllers\PaymentController::class, 'getPaymentMethods'])->name('methods');
        });

        // Receipt Routes
        Route::prefix('receipts')->name('receipts.')->group(function () {
            Route::get('/sales/{sale}', [App\Http\Controllers\ReceiptController::class, 'show'])->name('show');
            Route::get('/sales/{sale}/print', [App\Http\Controllers\ReceiptController::class, 'print'])->name('print');
            Route::get('/sales/{sale}/pdf', [App\Http\Controllers\ReceiptController::class, 'pdf'])->name('pdf');
            Route::post('/sales/{sale}/email', [App\Http\Controllers\ReceiptController::class, 'email'])->name('email');
            Route::post('/sales/{sale}/sms', [App\Http\Controllers\ReceiptController::class, 'sms'])->name('sms');
        });

        // POS Reports Routes
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/dashboard', [App\Http\Controllers\POSReportController::class, 'dashboard'])->name('dashboard');
            Route::get('/sales-performance', [App\Http\Controllers\POSReportController::class, 'salesPerformance'])->name('sales-performance');
            Route::get('/product-performance', [App\Http\Controllers\POSReportController::class, 'productPerformance'])->name('product-performance');
            Route::get('/payment-analysis', [App\Http\Controllers\POSReportController::class, 'paymentAnalysis'])->name('payment-analysis');
            Route::get('/inventory-movement', [App\Http\Controllers\POSReportController::class, 'inventoryMovement'])->name('inventory-movement');
            Route::post('/export-sales', [App\Http\Controllers\POSReportController::class, 'exportSales'])->name('export-sales');
        });

        // Offline Mode Routes
        Route::prefix('offline')->name('offline.')->group(function () {
            Route::get('/prepare-data', [App\Http\Controllers\OfflineModeController::class, 'prepareData'])->name('prepare-data');
            Route::post('/store-sale', [App\Http\Controllers\OfflineModeController::class, 'storeSale'])->name('store-sale');
            Route::post('/sync', [App\Http\Controllers\OfflineModeController::class, 'sync'])->name('sync');
            Route::get('/status', [App\Http\Controllers\OfflineModeController::class, 'status'])->name('status');
            Route::delete('/clear', [App\Http\Controllers\OfflineModeController::class, 'clear'])->name('clear');
        });

        // Inventory Integration Routes
        Route::prefix('inventory')->name('inventory.')->group(function () {
            Route::post('/check-stock', [App\Http\Controllers\POSInventoryController::class, 'checkStock'])->name('check-stock');
            Route::get('/low-stock-alerts', [App\Http\Controllers\POSInventoryController::class, 'lowStockAlerts'])->name('low-stock-alerts');
            Route::get('/movement-summary', [App\Http\Controllers\POSInventoryController::class, 'movementSummary'])->name('movement-summary');
            Route::get('/available-stock', [App\Http\Controllers\POSInventoryController::class, 'getAvailableStock'])->name('available-stock');
        });
    });

    // Reports Routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [App\Http\Controllers\ReportController::class, 'index'])->name('index');
        Route::get('/financial', [App\Http\Controllers\ReportController::class, 'financial'])->name('financial');
        Route::get('/operational', [App\Http\Controllers\ReportController::class, 'operational'])->name('operational');
        Route::get('/customer-analytics', [App\Http\Controllers\ReportController::class, 'customerAnalytics'])->name('customer-analytics');
        Route::get('/inventory', [App\Http\Controllers\ReportController::class, 'inventory'])->name('inventory');
        Route::get('/custom', [App\Http\Controllers\ReportController::class, 'customReport'])->name('custom');
        Route::post('/custom', [App\Http\Controllers\ReportController::class, 'customReport'])->name('custom.generate');
        Route::get('/export', [App\Http\Controllers\ReportController::class, 'export'])->name('export');
    });

    // Expenses Routes
    Route::prefix('expenses')->name('expenses.')->group(function () {
        Route::get('/', function () {
            return view('expenses.index');
        })->name('index');
    });

    // Accounting System Routes
    Route::prefix('accounting')->name('accounting.')->group(function () {

        // Chart of Accounts
        Route::prefix('accounts')->name('accounts.')->group(function () {
            Route::get('/', [AccountController::class, 'index'])->name('index');
            Route::get('/create', [AccountController::class, 'create'])->name('create');
            Route::post('/', [AccountController::class, 'store'])->name('store');
            Route::get('/{account}', [AccountController::class, 'show'])->name('show');
            Route::get('/{account}/edit', [AccountController::class, 'edit'])->name('edit');
            Route::put('/{account}', [AccountController::class, 'update'])->name('update');
            Route::delete('/{account}', [AccountController::class, 'destroy'])->name('destroy');
            Route::post('/{account}/toggle-status', [AccountController::class, 'toggleStatus'])->name('toggle-status');
            Route::get('/api/hierarchy', [AccountController::class, 'hierarchy'])->name('hierarchy');
            Route::get('/{account}/balance', [AccountController::class, 'balance'])->name('balance');
        });

        // Journal Entries
        Route::prefix('journal-entries')->name('journal-entries.')->group(function () {
            Route::get('/', [JournalEntryController::class, 'index'])->name('index');
            Route::get('/create', [JournalEntryController::class, 'create'])->name('create');
            Route::post('/', [JournalEntryController::class, 'store'])->name('store');
            Route::get('/{journalEntry}', [JournalEntryController::class, 'show'])->name('show');
            Route::get('/{journalEntry}/edit', [JournalEntryController::class, 'edit'])->name('edit');
            Route::put('/{journalEntry}', [JournalEntryController::class, 'update'])->name('update');
            Route::delete('/{journalEntry}', [JournalEntryController::class, 'destroy'])->name('destroy');
            Route::post('/{journalEntry}/post', [JournalEntryController::class, 'post'])->name('post');
            Route::post('/{journalEntry}/reverse', [JournalEntryController::class, 'reverse'])->name('reverse');
        });

        // Accounting Reports
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/', [AccountingReportController::class, 'index'])->name('index');
            Route::get('/trial-balance', [AccountingReportController::class, 'trialBalance'])->name('trial-balance');
            Route::get('/balance-sheet', [AccountingReportController::class, 'balanceSheet'])->name('balance-sheet');
            Route::get('/income-statement', [AccountingReportController::class, 'incomeStatement'])->name('income-statement');
            Route::get('/cash-flow', [AccountingReportController::class, 'cashFlow'])->name('cash-flow');
            Route::get('/account-ledger/{account}', [AccountingReportController::class, 'accountLedger'])->name('account-ledger');
        });

        // Receivables Management
        Route::prefix('receivables')->name('receivables.')->group(function () {
            Route::get('/', [App\Http\Controllers\ReceivableController::class, 'index'])->name('index');
            Route::get('/{receivable}', [App\Http\Controllers\ReceivableController::class, 'show'])->name('show');
            Route::post('/{receivable}/payment', [App\Http\Controllers\ReceivableController::class, 'recordPayment'])->name('record-payment');
        });

        // Payables Management
        Route::prefix('payables')->name('payables.')->group(function () {
            Route::get('/', [App\Http\Controllers\PayableController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\PayableController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\PayableController::class, 'store'])->name('store');
            Route::get('/{payable}', [App\Http\Controllers\PayableController::class, 'show'])->name('show');
            Route::post('/{payable}/payment', [App\Http\Controllers\PayableController::class, 'recordPayment'])->name('record-payment');
        });
    });

    // Direct routes for main accounting features (outside accounting prefix)
    Route::resource('accounts', AccountController::class);
    Route::post('/accounts/{account}/toggle-status', [AccountController::class, 'toggleStatus'])->name('accounts.toggle-status');
    Route::get('/accounts/{account}/balance', [AccountController::class, 'balance'])->name('accounts.balance');

    // Journal Entries direct routes
    Route::resource('journal-entries', JournalEntryController::class);
    Route::post('/journal-entries/{journalEntry}/post', [JournalEntryController::class, 'post'])->name('journal-entries.post');
    Route::post('/journal-entries/{journalEntry}/reverse', [JournalEntryController::class, 'reverse'])->name('journal-entries.reverse');

    // Accounting Reports direct route
    Route::get('/accounting-reports', [AccountingReportController::class, 'index'])->name('accounting-reports.index');
});

// Include test routes for debugging
if (app()->environment(['local', 'testing'])) {
    require __DIR__.'/test.php';
}
