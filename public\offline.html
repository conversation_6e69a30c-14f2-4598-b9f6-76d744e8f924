<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - مركز الصيانة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .offline-container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }
        
        .offline-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #fff;
        }
        
        .offline-message {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .offline-features {
            margin-top: 40px;
            text-align: right;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .feature-icon {
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 14px;
        }
        
        .feature-text {
            flex: 1;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .connection-status {
            margin-top: 30px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            font-size: 14px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 8px;
            animation: pulse 2s infinite;
        }
        
        .status-offline {
            background: #ff6b6b;
        }
        
        .status-online {
            background: #51cf66;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        @media (max-width: 480px) {
            .offline-container {
                padding: 30px 20px;
            }
            
            .offline-title {
                font-size: 24px;
            }
            
            .offline-message {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            📡
        </div>
        
        <h1 class="offline-title">غير متصل بالإنترنت</h1>
        
        <p class="offline-message">
            يبدو أنك غير متصل بالإنترنت حالياً. لا تقلق، يمكنك الاستمرار في استخدام بعض ميزات التطبيق.
        </p>
        
        <button class="retry-button" onclick="checkConnection()">
            🔄 إعادة المحاولة
        </button>
        
        <a href="/mobile/app" class="retry-button">
            🏠 العودة للتطبيق
        </a>
        
        <div class="offline-features">
            <h3 style="margin-bottom: 20px; color: rgba(255, 255, 255, 0.9);">الميزات المتاحة بدون إنترنت:</h3>
            
            <div class="feature-item">
                <div class="feature-icon">📋</div>
                <div class="feature-text">عرض طلبات الصيانة المحفوظة مسبقاً</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">👥</div>
                <div class="feature-text">الوصول لبيانات العملاء المخزنة</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">📊</div>
                <div class="feature-text">مراجعة التقارير والإحصائيات السابقة</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">✏️</div>
                <div class="feature-text">إنشاء طلبات جديدة (سيتم مزامنتها لاحقاً)</div>
            </div>
        </div>
        
        <div class="connection-status">
            <span class="status-indicator status-offline" id="statusIndicator"></span>
            <span id="connectionStatus">غير متصل</span>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusIndicator = document.getElementById('statusIndicator');
            const connectionStatus = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusIndicator.className = 'status-indicator status-online';
                connectionStatus.textContent = 'متصل';
            } else {
                statusIndicator.className = 'status-indicator status-offline';
                connectionStatus.textContent = 'غير متصل';
            }
        }
        
        // Check connection and redirect if online
        function checkConnection() {
            if (navigator.onLine) {
                // Try to fetch a small resource to verify connection
                fetch('/manifest.json', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                })
                .then(() => {
                    // Connection is working, redirect to app
                    window.location.href = '/mobile/app';
                })
                .catch(() => {
                    // Still offline
                    alert('لا يزال الاتصال غير متاح. يرجى المحاولة مرة أخرى.');
                });
            } else {
                alert('لا يوجد اتصال بالإنترنت. يرجى التحقق من إعدادات الشبكة.');
            }
        }
        
        // Listen for connection changes
        window.addEventListener('online', () => {
            updateConnectionStatus();
            setTimeout(() => {
                if (confirm('تم استعادة الاتصال! هل تريد العودة للتطبيق؟')) {
                    window.location.href = '/mobile/app';
                }
            }, 1000);
        });
        
        window.addEventListener('offline', () => {
            updateConnectionStatus();
        });
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodic connection check
        setInterval(() => {
            updateConnectionStatus();
        }, 5000);
        
        // Service Worker messaging
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                // Send message to service worker
                registration.active.postMessage({
                    type: 'OFFLINE_PAGE_LOADED'
                });
            });
        }
        
        // Auto-retry when connection is restored
        let retryAttempts = 0;
        const maxRetries = 3;
        
        function autoRetry() {
            if (navigator.onLine && retryAttempts < maxRetries) {
                retryAttempts++;
                checkConnection();
            }
        }
        
        // Try to reconnect every 30 seconds
        setInterval(autoRetry, 30000);
    </script>
</body>
</html>
