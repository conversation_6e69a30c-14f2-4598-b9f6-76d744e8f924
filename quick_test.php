<?php

/**
 * Quick System Test
 * 
 * Fast check of the most critical components
 */

echo "⚡ اختبار سريع للنظام\n";
echo "==================\n\n";

$issues = [];
$success = [];

// Test 1: Critical Files
echo "1️⃣ الملفات الحرجة...\n";

$criticalFiles = [
    'routes/web.php',
    'app/Http/Controllers/TechnicianController.php',
    'resources/views/technicians/schedule.blade.php',
    'app/Http/Kernel.php'
];

foreach ($criticalFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ " . basename($file) . "\n";
        $success[] = basename($file);
    } else {
        echo "   ❌ " . basename($file) . " مفقود\n";
        $issues[] = basename($file) . " مفقود";
    }
}

echo "\n";

// Test 2: Route Order Fix
echo "2️⃣ إصلاح ترتيب Routes...\n";

if (file_exists('routes/web.php')) {
    $routesContent = file_get_contents('routes/web.php');
    
    $schedulePos = strpos($routesContent, "Route::get('technicians/schedule'");
    $resourcePos = strpos($routesContent, "Route::resource('technicians'");
    
    if ($schedulePos !== false && $resourcePos !== false) {
        if ($schedulePos < $resourcePos) {
            echo "   ✅ Schedule route قبل resource route - صحيح!\n";
            $success[] = "Route order صحيح";
        } else {
            echo "   ❌ Schedule route بعد resource route - خطأ!\n";
            $issues[] = "Route order خاطئ";
        }
    } else {
        echo "   ❌ أحد الـ routes مفقود\n";
        $issues[] = "Routes مفقودة";
    }
} else {
    echo "   ❌ ملف routes غير موجود\n";
    $issues[] = "routes/web.php مفقود";
}

echo "\n";

// Test 3: Controller Method
echo "3️⃣ TechnicianController schedule method...\n";

if (file_exists('app/Http/Controllers/TechnicianController.php')) {
    $controllerContent = file_get_contents('app/Http/Controllers/TechnicianController.php');
    
    if (strpos($controllerContent, 'public function schedule()') !== false) {
        echo "   ✅ schedule method موجود\n";
        $success[] = "schedule method موجود";
        
        if (strpos($controllerContent, "return view('technicians.schedule'") !== false) {
            echo "   ✅ يعيد view صحيح\n";
            $success[] = "schedule view return صحيح";
        } else {
            echo "   ❌ لا يعيد view صحيح\n";
            $issues[] = "schedule view return خاطئ";
        }
        
        if (strpos($controllerContent, 'try {') !== false) {
            echo "   ✅ يحتوي على error handling\n";
            $success[] = "error handling موجود";
        } else {
            echo "   ⚠️  لا يحتوي على error handling\n";
        }
    } else {
        echo "   ❌ schedule method مفقود\n";
        $issues[] = "schedule method مفقود";
    }
} else {
    echo "   ❌ TechnicianController مفقود\n";
    $issues[] = "TechnicianController مفقود";
}

echo "\n";

// Test 4: View Structure
echo "4️⃣ Schedule View...\n";

if (file_exists('resources/views/technicians/schedule.blade.php')) {
    $viewContent = file_get_contents('resources/views/technicians/schedule.blade.php');
    
    if (strpos($viewContent, '@extends') !== false) {
        echo "   ✅ يمتد من layout\n";
        $success[] = "schedule view extends layout";
    } else {
        echo "   ❌ لا يمتد من layout\n";
        $issues[] = "schedule view لا يمتد من layout";
    }
    
    if (strpos($viewContent, 'جدولة الفنيين') !== false) {
        echo "   ✅ يحتوي على عنوان عربي\n";
        $success[] = "schedule view عنوان عربي";
    } else {
        echo "   ❌ لا يحتوي على عنوان عربي\n";
        $issues[] = "schedule view بدون عنوان عربي";
    }
    
    if (strpos($viewContent, '$technicians') !== false) {
        echo "   ✅ يستخدم متغير technicians\n";
        $success[] = "schedule view متغيرات صحيحة";
    } else {
        echo "   ❌ لا يستخدم متغير technicians\n";
        $issues[] = "schedule view متغيرات خاطئة";
    }
} else {
    echo "   ❌ schedule.blade.php مفقود\n";
    $issues[] = "schedule.blade.php مفقود";
}

echo "\n";

// Test 5: Middleware Check
echo "5️⃣ Middleware Configuration...\n";

if (file_exists('app/Http/Kernel.php')) {
    $kernelContent = file_get_contents('app/Http/Kernel.php');
    
    if (strpos($kernelContent, "'permission' => \\App\\Http\\Middleware\\CheckPermission::class") !== false) {
        echo "   ✅ Permission middleware مُسجل\n";
        $success[] = "permission middleware مُسجل";
    } else {
        echo "   ❌ Permission middleware غير مُسجل\n";
        $issues[] = "permission middleware غير مُسجل";
    }
} else {
    echo "   ❌ Kernel.php مفقود\n";
    $issues[] = "Kernel.php مفقود";
}

echo "\n";

// Test 6: Test Routes
echo "6️⃣ Test Routes...\n";

if (file_exists('routes/web.php')) {
    $routesContent = file_get_contents('routes/web.php');
    
    if (strpos($routesContent, "Route::get('/test-schedule'") !== false) {
        echo "   ✅ Test schedule route موجود\n";
        $success[] = "test schedule route موجود";
    } else {
        echo "   ⚠️  Test schedule route مفقود\n";
    }
    
    if (strpos($routesContent, "Route::get('/simple-schedule'") !== false) {
        echo "   ✅ Simple schedule route موجود\n";
        $success[] = "simple schedule route موجود";
    } else {
        echo "   ⚠️  Simple schedule route مفقود\n";
    }
}

echo "\n";

// Summary
echo "📊 النتائج\n";
echo "=========\n";

echo "✅ نجح: " . count($success) . " اختبار\n";
echo "❌ فشل: " . count($issues) . " اختبار\n\n";

if (empty($issues)) {
    echo "🎉 النظام جاهز للاختبار!\n\n";
    
    echo "🔗 روابط الاختبار:\n";
    echo "1. اختبار بسيط: http://tareq.test/test-schedule\n";
    echo "2. اختبار View: http://tareq.test/simple-schedule\n";
    echo "3. الجدولة الكاملة: http://tareq.test/technicians/schedule\n\n";
    
    echo "📋 خطوات الاختبار:\n";
    echo "1. امسح cache: php artisan route:clear\n";
    echo "2. أعد تشغيل الخادم: php artisan serve\n";
    echo "3. جرب الروابط أعلاه\n";
} else {
    echo "⚠️  يوجد " . count($issues) . " مشكلة:\n";
    foreach ($issues as $issue) {
        echo "   ❌ $issue\n";
    }
    echo "\n📝 أصلح هذه المشاكل أولاً\n";
}

echo "\n🏁 انتهى الاختبار السريع\n";
