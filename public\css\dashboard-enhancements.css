/* ===================================
   تحسينات خاصة بلوحة التحكم
   Dashboard Enhancements CSS
   =================================== */

/* تحسينات خاصة بالإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

/* تحسينات بطاقات الإحصائيات */
.stat-card {
    background: var(--bg-primary-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-bounce);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.dark .stat-card {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
    box-shadow: var(--shadow-lg);
}

.stat-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-hover-light);
}

.dark .stat-card:hover {
    border-color: var(--border-hover-dark);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--success-color), var(--info-color));
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.stat-value {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--accent-color);
    margin-bottom: var(--spacing-sm);
    font-family: var(--font-family-primary);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary-light);
    font-weight: var(--font-medium);
    margin-bottom: var(--spacing-xs);
}

.dark .stat-label {
    color: var(--text-secondary-dark);
}

.stat-change {
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-weight: var(--font-medium);
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* تحسينات خاصة بالمخططات */
.chart-container {
    background: var(--bg-primary-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.dark .chart-container {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
}

/* تحسينات الأنشطة الأخيرة */
.activity-item {
    display: flex;
    align-items: start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    transition: background-color var(--transition-normal);
}

.activity-item:hover {
    background: var(--bg-tertiary-light);
}

.dark .activity-item:hover {
    background: var(--bg-tertiary-dark);
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary-light);
    margin-bottom: var(--spacing-xs);
}

.dark .activity-title {
    color: var(--text-primary-dark);
}

.activity-time {
    font-size: var(--text-xs);
    color: var(--text-muted-light);
}

.dark .activity-time {
    color: var(--text-muted-dark);
}

/* تحسينات تنبيهات المخزون */
.stock-alert {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-bounce);
}

.stock-alert:hover {
    transform: translateX(-2px);
    box-shadow: var(--shadow-md);
}

.stock-alert-icon {
    width: 32px;
    height: 32px;
    background: rgba(245, 158, 11, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--warning-color);
}

/* تحسينات الإجراءات السريعة */
.quick-action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    width: 100%;
    padding: var(--spacing-md);
    background: transparent;
    border: none;
    border-radius: var(--radius-lg);
    color: var(--text-primary-light);
    text-decoration: none;
    transition: all var(--transition-bounce);
    font-family: var(--font-family-primary);
}

.dark .quick-action-btn {
    color: var(--text-primary-dark);
}

.quick-action-btn:hover {
    background: var(--bg-tertiary-light);
    transform: translateX(-2px);
}

.dark .quick-action-btn:hover {
    background: var(--bg-tertiary-dark);
}

.quick-action-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

/* تحسينات الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* تحسينات الاستجابة للأجهزة المحمولة */
@media (max-width: 1024px) {
    .stat-card {
        padding: var(--spacing-lg);
    }
    
    .stat-value {
        font-size: var(--text-2xl);
    }
    
    .chart-container {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .stat-card {
        padding: var(--spacing-md);
    }
    
    .stat-value {
        font-size: var(--text-xl);
    }
    
    .activity-item {
        padding: var(--spacing-sm);
    }
    
    .quick-action-btn {
        padding: var(--spacing-sm);
    }
}

/* تحسينات خاصة بالوضع المظلم */
.dark .stat-card::before {
    background: linear-gradient(90deg, var(--accent-color), var(--success-color), var(--info-color));
}

.dark .chart-container {
    box-shadow: var(--shadow-lg);
}

.dark .stock-alert {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.08) 100%);
    border-color: rgba(245, 158, 11, 0.3);
}

/* تحسينات إمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
    .stat-card,
    .activity-item,
    .quick-action-btn,
    .stock-alert {
        transition: none;
    }
    
    .animate-fade-in-up,
    .animate-slide-in-right,
    .animate-pulse {
        animation: none;
    }
}

/* تحسينات الطباعة */
@media print {
    .stat-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .chart-container {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .quick-action-btn,
    .activity-item {
        display: none;
    }
}

/* تحسينات خاصة بالتركيز */
.stat-card:focus-within,
.chart-container:focus-within {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* تحسينات التمرير */
.stats-grid {
    scroll-behavior: smooth;
}

/* تحسينات التحميل */
.loading-skeleton {
    background: linear-gradient(90deg, var(--bg-tertiary-light) 25%, var(--bg-secondary-light) 50%, var(--bg-tertiary-light) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--radius-md);
}

.dark .loading-skeleton {
    background: linear-gradient(90deg, var(--bg-tertiary-dark) 25%, var(--bg-secondary-dark) 50%, var(--bg-tertiary-dark) 75%);
    background-size: 200% 100%;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
