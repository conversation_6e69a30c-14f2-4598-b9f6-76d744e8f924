# نظام تسجيل الدخول المتطور

## نظرة عامة

تم تطوير نظام تسجيل دخول متطور ومتكامل لنظام إدارة مراكز الصيانة يتضمن:

- تصميم حديث متجاوب مع دعم RTL
- حماية متقدمة ضد هجمات Brute Force
- دعم الوضع الليلي/النهاري
- تأثيرات Glass Morphism
- تحقق متقدم من صحة البيانات

## الملفات المضافة/المحدثة

### 1. ملفات العرض (Views)
- `resources/views/auth/login.blade.php` - واجهة تسجيل الدخول المحدثة

### 2. ملفات CSS
- `public/css/auth.css` - تصميم واجهة تسجيل الدخول

### 3. ملفات JavaScript
- `public/js/auth.js` - وظائف تفاعلية متقدمة

### 4. ملفات PHP
- `app/Http/Controllers/AuthController.php` - محدث مع حماية متقدمة
- `app/Http/Middleware/LoginRateLimit.php` - حماية Rate Limiting
- `config/auth_security.php` - إعدادات الحماية

## المميزات

### 1. التصميم البصري
- **Glass Morphism**: تأثيرات زجاجية حديثة
- **خلفية متحركة**: تدرجات لونية متحركة
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **دعم RTL**: دعم كامل للغة العربية
- **خطوط عربية**: Cairo و Tajawal

### 2. الوضع الليلي/النهاري
- تبديل سلس بين الأوضاع
- حفظ التفضيل في localStorage
- تأثيرات انتقال سلسة

### 3. الحماية والأمان
- **Rate Limiting**: حماية من هجمات Brute Force
- **CSRF Protection**: حماية من هجمات CSRF
- **Session Security**: إدارة آمنة للجلسات
- **Input Validation**: تحقق متقدم من البيانات

### 4. تجربة المستخدم
- **حسابات تجريبية**: للاختبار السريع
- **اختصارات لوحة المفاتيح**: للتنقل السريع
- **رسائل خطأ واضحة**: باللغة العربية
- **تأثيرات تفاعلية**: hover وانتقالات

## الاستخدام

### الحسابات التجريبية

```
مدير النظام:
Email: <EMAIL>
Password: password123

مدير:
Email: <EMAIL>
Password: password123

فني:
Email: <EMAIL>
Password: password123

كاشير:
Email: <EMAIL>
Password: password123
```

### اختصارات لوحة المفاتيح

- `Alt + T`: تبديل الوضع الليلي/النهاري
- `Alt + P`: إظهار/إخفاء كلمة المرور
- `Enter`: إرسال النموذج

## الإعدادات

### متغيرات البيئة (.env)

```env
# إعدادات Rate Limiting
LOGIN_MAX_ATTEMPTS=5
LOGIN_DECAY_MINUTES=15
LOGIN_RATE_LIMIT_ENABLED=true

# إعدادات كلمة المرور
PASSWORD_MIN_LENGTH=6
PASSWORD_MAX_LENGTH=255

# إعدادات الجلسة
SESSION_TIMEOUT_MINUTES=120
SESSION_REGENERATE_ON_LOGIN=true

# إعدادات تسجيل الأنشطة
ACTIVITY_LOGGING_ENABLED=true
LOG_SUCCESSFUL_LOGINS=true
LOG_FAILED_LOGINS=true

# إعدادات الحماية المتقدمة
FORCE_HTTPS=false
CSRF_PROTECTION=true
```

## التخصيص

### تخصيص الألوان

يمكن تخصيص الألوان من خلال تعديل متغيرات CSS في `public/css/auth.css`:

```css
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    /* ... المزيد من الألوان */
}
```

### تخصيص النصوص

يمكن تخصيص النصوص من خلال تعديل ملف `resources/views/auth/login.blade.php`.

### تخصيص الحماية

يمكن تخصيص إعدادات الحماية من خلال ملف `config/auth_security.php`.

## استكشاف الأخطاء

### مشاكل شائعة

1. **عدم ظهور التصميم بشكل صحيح**
   - تأكد من تحميل ملفات CSS و JS
   - تحقق من مسارات الملفات

2. **عدم عمل Rate Limiting**
   - تأكد من تفعيل Cache في Laravel
   - تحقق من إعدادات البيئة

3. **مشاكل في الخطوط العربية**
   - تأكد من اتصال الإنترنت لتحميل Google Fonts
   - تحقق من إعدادات dir="rtl"

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا النظام مطور خصيصاً لنظام إدارة مراكز الصيانة وجميع الحقوق محفوظة.
