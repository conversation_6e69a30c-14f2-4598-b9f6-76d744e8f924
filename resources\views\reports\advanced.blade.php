@extends('layouts.main')

@section('title', 'التقارير المتقدمة')

@section('content')
<div class="content-wrapper animate-fade-in-up" x-data="advancedReports()">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">التقارير المتقدمة</h1>
                    <p class="page-subtitle">تقارير تفاعلية وتحليلات متقدمة للأعمال بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="scheduleReport()" class="btn btn-secondary hover-lift ripple" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        جدولة تقرير
                    </button>
                    <button @click="exportDashboard()" class="btn btn-success hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        تصدير لوحة التحكم
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Builder -->
    <div class="card animate-fade-in-up animate-delay-100">
        <div class="card-header">
            <h3 class="card-title">منشئ التقارير المخصصة</h3>
            <div class="action-group">
                <button @click="resetBuilder()" class="btn btn-ghost btn-sm hover-scale">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    إعادة تعيين
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Data Sources -->
            <div class="form-group">
                <label class="form-label">مصدر البيانات</label>
                <select x-model="reportBuilder.dataSource" class="form-select focus-glow">
                    <option value="repairs">طلبات الصيانة</option>
                    <option value="sales">المبيعات</option>
                    <option value="customers">العملاء</option>
                    <option value="inventory">المخزون</option>
                    <option value="financial">المالية</option>
                </select>
            </div>

            <!-- Chart Type -->
            <div class="form-group">
                <label class="form-label">نوع الرسم البياني</label>
                <select x-model="reportBuilder.chartType" class="form-select focus-glow">
                    <option value="line">خطي</option>
                    <option value="bar">أعمدة</option>
                    <option value="pie">دائري</option>
                    <option value="area">منطقة</option>
                    <option value="scatter">نقطي</option>
                </select>
            </div>

            <!-- Time Period -->
            <div class="form-group">
                <label class="form-label">الفترة الزمنية</label>
                <select x-model="reportBuilder.timePeriod" class="form-select focus-glow">
                    <option value="7days">آخر 7 أيام</option>
                    <option value="30days">آخر 30 يوم</option>
                    <option value="3months">آخر 3 أشهر</option>
                    <option value="6months">آخر 6 أشهر</option>
                    <option value="1year">آخر سنة</option>
                </select>
            </div>

            <!-- Generate Button -->
            <div class="flex items-end">
                <button @click="generateCustomReport()" class="btn btn-primary w-full hover-lift ripple">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    إنشاء التقرير
                </button>
            </div>
        </div>
    </div>

    <!-- KPI Dashboard -->
    <div class="stats-grid">
        <div class="dashboard-card hover-lift animate-scale-in animate-delay-200" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
            <div class="flex items-center justify-between text-white">
                <div>
                    <p class="text-blue-100 text-sm">إجمالي الإيرادات</p>
                    <p class="text-3xl font-bold" x-text="kpis.totalRevenue">₪125,430</p>
                    <p class="text-blue-200 text-sm mt-1">+18% من الشهر الماضي</p>
                </div>
                <div class="w-16 h-16 bg-blue-400/30 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="dashboard-card hover-lift animate-scale-in animate-delay-300" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="flex items-center justify-between text-white">
                <div>
                    <p class="text-green-100 text-sm">معدل رضا العملاء</p>
                    <p class="text-3xl font-bold" x-text="kpis.customerSatisfaction">94.2%</p>
                    <p class="text-green-200 text-sm mt-1">+2.1% من الشهر الماضي</p>
                </div>
                <div class="w-16 h-16 bg-green-400/30 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="dashboard-card hover-lift animate-scale-in animate-delay-500" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
            <div class="flex items-center justify-between text-white">
                <div>
                    <p class="text-purple-100 text-sm">متوسط وقت الإصلاح</p>
                    <p class="text-3xl font-bold" x-text="kpis.avgRepairTime">2.8 يوم</p>
                    <p class="text-purple-200 text-sm mt-1">-0.5 يوم من الشهر الماضي</p>
                </div>
                <div class="w-16 h-16 bg-purple-400/30 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="dashboard-card hover-lift animate-scale-in animate-delay-700" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div class="flex items-center justify-between text-white">
                <div>
                    <p class="text-orange-100 text-sm">معدل الإنجاز</p>
                    <p class="text-3xl font-bold" x-text="kpis.completionRate">96.7%</p>
                    <p class="text-orange-200 text-sm mt-1">+1.2% من الشهر الماضي</p>
                </div>
                <div class="w-16 h-16 bg-orange-400/30 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Interactive Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Trend -->
        <div class="card animate-fade-in-up animate-delay-500">
            <div class="card-header">
                <h3 class="card-title">اتجاه الإيرادات</h3>
                <div class="action-group">
                    <button @click="chartPeriod = 'week'" :class="chartPeriod === 'week' ? 'btn btn-primary btn-sm' : 'btn btn-ghost btn-sm'" class="hover-scale">أسبوعي</button>
                    <button @click="chartPeriod = 'month'" :class="chartPeriod === 'month' ? 'btn btn-primary btn-sm' : 'btn btn-ghost btn-sm'" class="hover-scale">شهري</button>
                </div>
            </div>
            <div class="chart-container">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-accent-color/10 rounded-full flex items-center justify-center animate-pulse">
                        <svg class="w-8 h-8 text-accent-color" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <p class="text-sm text-muted-light dark:text-muted-dark">رسم بياني تفاعلي للإيرادات</p>
                    <p class="text-xs text-muted-light dark:text-muted-dark mt-1">سيتم تحميل البيانات...</p>
                </div>
            </div>
        </div>

        <!-- Service Distribution -->
        <div class="card animate-fade-in-up animate-delay-700">
            <div class="card-header">
                <h3 class="card-title">توزيع الخدمات</h3>
                <div class="action-group">
                    <button @click="refreshChart('services')" class="btn btn-ghost btn-sm hover-rotate">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        تحديث
                    </button>
                </div>
            </div>
            <div class="chart-container">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-success-color/10 rounded-full flex items-center justify-center animate-pulse">
                        <svg class="w-8 h-8 text-success-color" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                        </svg>
                    </div>
                    <p class="text-sm text-muted-light dark:text-muted-dark">رسم دائري لتوزيع الخدمات</p>
                    <p class="text-xs text-muted-light dark:text-muted-dark mt-1">سيتم تحميل البيانات...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تحليلات مفصلة</h3>
            <div class="flex space-x-2 space-x-reverse">
                <button @click="analyticsView = 'table'" :class="analyticsView === 'table' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'" class="px-3 py-1 rounded text-sm">جدول</button>
                <button @click="analyticsView = 'chart'" :class="analyticsView === 'chart' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'" class="px-3 py-1 rounded text-sm">رسم بياني</button>
            </div>
        </div>

        <!-- Table View -->
        <div x-show="analyticsView === 'table'" class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المؤشر</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">القيمة الحالية</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الشهر الماضي</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التغيير</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الاتجاه</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="metric in detailedMetrics" :key="metric.name">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100" x-text="metric.name"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="metric.current"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="metric.previous"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm" :class="metric.change >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'" x-text="(metric.change >= 0 ? '+' : '') + metric.change + '%'"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <svg class="w-5 h-5" :class="metric.change >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="metric.change >= 0 ? 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6' : 'M13 17h8m0 0V9m0 8l-8-8-4 4-6-6'"></path>
                                </svg>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <!-- Chart View -->
        <div x-show="analyticsView === 'chart'" class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">رسم بياني للتحليلات المفصلة</p>
            </div>
        </div>
    </div>

    <!-- Scheduled Reports -->
    <div class="card animate-fade-in-up animate-delay-1000">
        <div class="card-header">
            <h3 class="card-title">التقارير المجدولة</h3>
            <div class="action-group">
                <span class="badge badge-info" x-text="scheduledReports.length + ' تقرير مجدول'"></span>
                <button @click="showScheduleModal = true" class="btn btn-primary btn-sm hover-lift ripple">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة جدولة جديدة
                </button>
            </div>
        </div>
        
        <div class="space-y-4">
            <template x-for="report in scheduledReports" :key="report.id">
                <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="report.name"></h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400" x-text="report.description"></p>
                            <p class="text-xs text-gray-400 mt-1">التكرار: <span x-text="report.frequency"></span> | آخر تشغيل: <span x-text="report.lastRun"></span></p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="report.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'" x-text="report.status === 'active' ? 'نشط' : 'متوقف'"></span>
                        <button @click="toggleReport(report.id)" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 text-sm">تبديل</button>
                        <button @click="editReport(report.id)" class="text-green-600 hover:text-green-800 dark:text-green-400 text-sm">تعديل</button>
                        <button @click="deleteReport(report.id)" class="text-red-600 hover:text-red-800 dark:text-red-400 text-sm">حذف</button>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

@push('scripts')
<script>
function advancedReports() {
    return {
        reportBuilder: {
            dataSource: 'repairs',
            chartType: 'line',
            timePeriod: '30days'
        },
        kpis: {
            totalRevenue: '₪125,430',
            customerSatisfaction: '94.2%',
            avgRepairTime: '2.8 يوم',
            completionRate: '96.7%'
        },
        chartPeriod: 'month',
        analyticsView: 'table',
        detailedMetrics: [
            { name: 'إجمالي الطلبات', current: '456', previous: '398', change: 14.6 },
            { name: 'الطلبات المكتملة', current: '441', previous: '375', change: 17.6 },
            { name: 'متوسط قيمة الطلب', current: '₪275', previous: '₪245', change: 12.2 },
            { name: 'معدل العودة للعملاء', current: '23%', previous: '28%', change: -17.9 },
            { name: 'رضا العملاء', current: '4.7/5', previous: '4.5/5', change: 4.4 }
        ],
        scheduledReports: [
            {
                id: 1,
                name: 'تقرير الأداء الشهري',
                description: 'تقرير شامل عن أداء المركز خلال الشهر',
                frequency: 'شهرياً',
                lastRun: '2024-07-01',
                status: 'active'
            },
            {
                id: 2,
                name: 'تقرير المبيعات الأسبوعي',
                description: 'ملخص المبيعات والإيرادات الأسبوعية',
                frequency: 'أسبوعياً',
                lastRun: '2024-07-08',
                status: 'active'
            },
            {
                id: 3,
                name: 'تقرير رضا العملاء',
                description: 'تحليل مستوى رضا العملاء والتقييمات',
                frequency: 'ربع سنوي',
                lastRun: '2024-04-01',
                status: 'inactive'
            }
        ],

        generateCustomReport() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري الإنشاء...
            `;
            button.disabled = true;

            // محاكاة عملية إنشاء التقرير
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                if (window.showNotification) {
                    window.showNotification(`تم إنشاء التقرير المخصص بنجاح!\nمصدر البيانات: ${this.reportBuilder.dataSource}\nنوع الرسم: ${this.reportBuilder.chartType}\nالفترة: ${this.reportBuilder.timePeriod}`, 'success');
                }
            }, 3000);
        },

        scheduleReport() {
            if (window.showNotification) {
                window.showNotification('سيتم فتح نافذة جدولة التقرير قريباً', 'info');
            }
        },

        exportDashboard() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري التصدير...
            `;
            button.disabled = true;

            // محاكاة عملية التصدير
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                if (window.showNotification) {
                    window.showNotification('تم تصدير لوحة التحكم بنجاح!', 'success');
                }
            }, 2000);
        },

        toggleReport(id) {
            const report = this.scheduledReports.find(r => r.id === id);
            if (report) {
                report.status = report.status === 'active' ? 'inactive' : 'active';
            }
        },

        editReport(id) {
            alert(`تعديل التقرير ${id}`);
        },

        deleteReport(id) {
            if (confirm('هل أنت متأكد من حذف هذا التقرير المجدول؟')) {
                this.scheduledReports = this.scheduledReports.filter(r => r.id !== id);
            }
        }
    }
}
</script>
@endpush
@endsection
