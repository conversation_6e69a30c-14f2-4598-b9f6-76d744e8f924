<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('suppliers', function (Blueprint $table) {
            $table->id();
            $table->string('supplier_number')->unique();
            $table->string('name');
            $table->string('company_name')->nullable();
            $table->string('contact_person')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->string('fax')->nullable();
            $table->string('website')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->nullable();
            $table->string('tax_number')->nullable();
            $table->string('registration_number')->nullable();
            $table->string('bank_name')->nullable();
            $table->string('bank_account')->nullable();
            $table->string('iban')->nullable();
            $table->string('swift_code')->nullable();
            $table->integer('payment_terms')->default(30); // days
            $table->decimal('credit_limit', 12, 2)->default(0);
            $table->decimal('discount_percentage', 5, 2)->default(0);
            $table->integer('delivery_time')->nullable(); // days
            $table->decimal('minimum_order', 10, 2)->default(0);
            $table->string('currency', 3)->default('USD');
            $table->decimal('rating', 3, 1)->default(0);
            $table->enum('status', ['active', 'inactive', 'suspended', 'blacklisted'])->default('active');
            $table->string('category')->nullable();
            $table->json('specialization')->nullable();
            $table->json('certifications')->nullable();
            $table->text('notes')->nullable();
            $table->date('contract_start_date')->nullable();
            $table->date('contract_end_date')->nullable();
            $table->boolean('is_preferred')->default(false);
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['status']);
            $table->index(['category']);
            $table->index(['is_preferred']);
            $table->index(['is_active']);
            $table->index(['rating']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('suppliers');
    }
};
