<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'name',
        'name_en',
        'description',
        'sku',
        'barcode',
        'type', // simple, variable
        'category_id',
        'brand_id',
        'unit_id',
        'purchase_price',
        'selling_price',
        'profit_margin',
        'tax_rate',
        'min_stock_level',
        'max_stock_level',
        'reorder_level',
        'weight',
        'dimensions',
        'images',
        'is_active',
        'is_service',
        'warranty_period',
        'warranty_type',
        'notes'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_service' => 'boolean',
        'purchase_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'profit_margin' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'weight' => 'decimal:2',
        'dimensions' => 'array',
        'images' => 'array'
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع التصنيف
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * العلاقة مع العلامة التجارية
     */
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * العلاقة مع الوحدة
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * العلاقة مع المتغيرات (للمنتجات المتعددة)
     */
    public function variants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * العلاقة مع المخزون
     */
    public function inventories()
    {
        return $this->hasMany(Inventory::class);
    }

    /**
     * العلاقة مع عناصر المبيعات
     */
    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * العلاقة مع عناصر المشتريات
     */
    public function purchaseItems()
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * الحصول على إجمالي المخزون
     */
    public function getTotalStockAttribute()
    {
        return $this->inventories()->sum('quantity');
    }

    /**
     * الحصول على المخزون في موقع معين
     */
    public function getStockInLocation($locationId)
    {
        return $this->inventories()
            ->where('location_id', $locationId)
            ->sum('quantity');
    }

    /**
     * التحقق من انخفاض المخزون
     */
    public function getIsLowStockAttribute()
    {
        return $this->total_stock <= $this->min_stock_level;
    }

    /**
     * التحقق من نفاد المخزون
     */
    public function getIsOutOfStockAttribute()
    {
        return $this->total_stock <= 0;
    }

    /**
     * حساب سعر البيع بناءً على هامش الربح
     */
    public function calculateSellingPrice()
    {
        if ($this->profit_margin && $this->purchase_price) {
            return $this->purchase_price * (1 + ($this->profit_margin / 100));
        }
        return $this->selling_price;
    }

    /**
     * تحديث سعر البيع تلقائياً
     */
    public function updateSellingPrice()
    {
        $newPrice = $this->calculateSellingPrice();
        if ($newPrice) {
            $this->update(['selling_price' => $newPrice]);
        }
    }

    /**
     * الحصول على الصورة الرئيسية
     */
    public function getMainImageAttribute()
    {
        $images = $this->images ?? [];
        return count($images) > 0 ? $images[0] : null;
    }

    /**
     * إنشاء SKU تلقائي
     */
    public static function generateSku($companyId, $prefix = 'PRD')
    {
        $lastProduct = self::where('company_id', $companyId)
            ->orderBy('id', 'desc')
            ->first();

        $number = $lastProduct ? (int) substr($lastProduct->sku, strlen($prefix)) + 1 : 1;
        
        return $prefix . str_pad($number, 6, '0', STR_PAD_LEFT);
    }

    /**
     * إنشاء باركود تلقائي
     */
    public static function generateBarcode()
    {
        do {
            $barcode = '2' . str_pad(mt_rand(1, 999999999999), 12, '0', STR_PAD_LEFT);
        } while (self::where('barcode', $barcode)->exists());

        return $barcode;
    }

    /**
     * scope للمنتجات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * scope للمنتجات منخفضة المخزون
     */
    public function scopeLowStock($query)
    {
        return $query->whereRaw('(SELECT SUM(quantity) FROM inventories WHERE product_id = products.id) <= min_stock_level');
    }

    /**
     * scope للمنتجات نافدة المخزون
     */
    public function scopeOutOfStock($query)
    {
        return $query->whereRaw('(SELECT SUM(quantity) FROM inventories WHERE product_id = products.id) <= 0');
    }

    /**
     * scope للبحث في المنتجات
     */
    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('name_en', 'like', "%{$term}%")
              ->orWhere('sku', 'like', "%{$term}%")
              ->orWhere('barcode', 'like', "%{$term}%");
        });
    }

    /**
     * scope للمنتجات حسب التصنيف
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * scope للمنتجات حسب العلامة التجارية
     */
    public function scopeByBrand($query, $brandId)
    {
        return $query->where('brand_id', $brandId);
    }
}
