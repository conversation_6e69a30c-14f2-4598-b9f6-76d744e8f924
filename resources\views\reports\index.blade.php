@extends('layouts.main')

@section('title', 'التقارير والتحليلات')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">التقارير والتحليلات</h1>
            <p class="text-gray-600 dark:text-gray-400">تحليلات شاملة لأداء مركز الصيانة</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('reports.custom') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-plus mr-2"></i>
                تقرير مخصص
            </a>
            <div class="relative">
                <button onclick="toggleExportMenu()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-download mr-2"></i>
                    تصدير
                </button>
                <div id="exportMenu" class="hidden absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-10">
                    <a href="{{ route('reports.export', ['type' => 'financial', 'format' => 'pdf']) }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">تقرير مالي PDF</a>
                    <a href="{{ route('reports.export', ['type' => 'operational', 'format' => 'excel']) }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">تقرير تشغيلي Excel</a>
                    <a href="{{ route('reports.export', ['type' => 'customer', 'format' => 'pdf']) }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">تقرير العملاء PDF</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100">إجمالي الطلبات</p>
                    <p class="text-3xl font-bold">{{ number_format($quickStats['total_repairs']) }}</p>
                    <p class="text-blue-100 text-sm">طلب صيانة</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-tools"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100">إجمالي الإيرادات</p>
                    <p class="text-3xl font-bold">{{ number_format($quickStats['total_revenue'], 2) }}</p>
                    <p class="text-green-100 text-sm">ريال سعودي</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100">العملاء النشطون</p>
                    <p class="text-3xl font-bold">{{ number_format($quickStats['active_customers']) }}</p>
                    <p class="text-purple-100 text-sm">عميل نشط</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100">الفنيون</p>
                    <p class="text-3xl font-bold">{{ number_format($quickStats['total_technicians']) }}</p>
                    <p class="text-orange-100 text-sm">فني نشط</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-user-cog"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Categories -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Financial Reports -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-green-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">مالي</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">التقارير المالية</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">تحليل الإيرادات والمصروفات والأرباح</p>
                <a href="{{ route('reports.financial') }}" class="inline-flex items-center text-green-600 hover:text-green-700 font-medium">
                    عرض التقارير
                    <i class="fas fa-arrow-left mr-2"></i>
                </a>
            </div>
        </div>

        <!-- Operational Reports -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cogs text-blue-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">تشغيلي</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">التقارير التشغيلية</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">إحصائيات الصيانة وأداء الفنيين</p>
                <a href="{{ route('reports.operational') }}" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
                    عرض التقارير
                    <i class="fas fa-arrow-left mr-2"></i>
                </a>
            </div>
        </div>

        <!-- Customer Analytics -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-purple-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">عملاء</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">تحليلات العملاء</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">سلوك العملاء ومعدلات الرضا</p>
                <a href="{{ route('reports.customer-analytics') }}" class="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium">
                    عرض التحليلات
                    <i class="fas fa-arrow-left mr-2"></i>
                </a>
            </div>
        </div>

        <!-- Inventory Reports -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-boxes text-orange-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">مخزون</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">تقارير المخزون</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">مستويات المخزون واستخدام القطع</p>
                <a href="{{ route('reports.inventory') }}" class="inline-flex items-center text-orange-600 hover:text-orange-700 font-medium">
                    عرض التقارير
                    <i class="fas fa-arrow-left mr-2"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Trends -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Daily Revenue Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الإيرادات اليومية (آخر 7 أيام)</h3>
            </div>
            <div class="p-6">
                <canvas id="revenueChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">أفضل العملاء</h3>
                    <a href="{{ route('reports.customer-analytics') }}" class="text-blue-600 hover:text-blue-700 text-sm">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    @foreach($recentReports['top_customers'] as $customer)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-sm font-medium text-blue-600">{{ substr($customer->full_name, 0, 1) }}</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $customer->full_name }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $customer->repairs_count }} طلب</p>
                                </div>
                            </div>
                            <div class="text-left">
                                <p class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ number_format($customer->repairs_sum_total_cost, 2) }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">ريال</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Technician Performance -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">أداء الفنيين</h3>
                <a href="{{ route('reports.operational') }}" class="text-blue-600 hover:text-blue-700 text-sm">
                    تقرير مفصل
                </a>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفني</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الطلبات المكتملة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">متوسط التقييم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الأداء</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($recentReports['technician_performance'] as $technician)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-100 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-xs font-medium text-gray-600 dark:text-gray-300">{{ substr($technician->full_name, 0, 1) }}</span>
                                    </div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $technician->full_name }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ $technician->repairs_count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <div class="flex items-center">
                                    <span class="mr-2">{{ number_format($technician->repairs_avg_customer_rating ?? 0, 1) }}</span>
                                    <div class="flex text-yellow-400">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= ($technician->repairs_avg_customer_rating ?? 0))
                                                <i class="fas fa-star text-xs"></i>
                                            @else
                                                <i class="far fa-star text-xs"></i>
                                            @endif
                                        @endfor
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                                        @php
                                            $performance = min(($technician->repairs_count / 10) * 100, 100);
                                        @endphp
                                        <div class="bg-green-500 h-2 rounded-full" style="width: {{ $performance }}%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">{{ round($performance) }}%</span>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueData = @json($recentReports['daily_revenue']);
const ctx = document.getElementById('revenueChart').getContext('2d');

const revenueChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: revenueData.map(item => item.date),
        datasets: [{
            label: 'الإيرادات اليومية',
            data: revenueData.map(item => item.revenue),
            borderColor: 'rgb(34, 197, 94)',
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        }
    }
});

// Export menu toggle
function toggleExportMenu() {
    const menu = document.getElementById('exportMenu');
    menu.classList.toggle('hidden');
}

// Close export menu when clicking outside
document.addEventListener('click', function(event) {
    const menu = document.getElementById('exportMenu');
    const button = event.target.closest('button');
    
    if (!button || !button.onclick || button.onclick.toString().indexOf('toggleExportMenu') === -1) {
        menu.classList.add('hidden');
    }
});
</script>
@endpush
@endsection
