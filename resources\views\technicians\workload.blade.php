@extends('layouts.main')

@section('title', 'إدارة أعباء العمل')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إدارة أعباء العمل</h1>
            <p class="text-gray-600 dark:text-gray-400">توزيع وإدارة أعباء العمل للفنيين</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <button onclick="openBulkAssignModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-users mr-2"></i>
                تعيين جماعي
            </button>
            <a href="{{ route('technicians.scheduling') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-calendar mr-2"></i>
                الجدولة
            </a>
            <a href="{{ route('technicians.export') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير
            </a>
        </div>
    </div>

    <!-- Workload Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي الفنيين</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-gray-100">{{ $workloadData->count() }}</p>
                </div>
                <div class="text-blue-600">
                    <i class="fas fa-users text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">متاح</p>
                    <p class="text-3xl font-bold text-green-600">{{ $workloadData->where('availability', 'available')->count() }}</p>
                </div>
                <div class="text-green-600">
                    <i class="fas fa-check-circle text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">مشغول</p>
                    <p class="text-3xl font-bold text-orange-600">{{ $workloadData->where('availability', 'busy')->count() }}</p>
                </div>
                <div class="text-orange-600">
                    <i class="fas fa-clock text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">غير متاح</p>
                    <p class="text-3xl font-bold text-red-600">{{ $workloadData->where('availability', 'unavailable')->count() }}</p>
                </div>
                <div class="text-red-600">
                    <i class="fas fa-times-circle text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Technicians Workload Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">أعباء العمل الحالية</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفني</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التخصص</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الطلبات الحالية</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">عبء العمل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">وقت الإنجاز المتوقع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($workloadData as $data)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-100 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-sm font-medium text-gray-600 dark:text-gray-300">
                                            {{ substr($data['name'], 0, 1) }}
                                        </span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                            <a href="{{ route('technicians.show', $data['id']) }}" class="hover:text-blue-600">
                                                {{ $data['name'] }}
                                            </a>
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            مستوى المهارة: {{ $data['skill_level'] }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @if(is_array($data['specializations']) && count($data['specializations']) > 0)
                                    {{ implode(', ', array_slice($data['specializations'], 0, 2)) }}
                                    @if(count($data['specializations']) > 2)
                                        <span class="text-gray-500">+{{ count($data['specializations']) - 2 }}</span>
                                    @endif
                                @else
                                    فني عام
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    @if($data['availability'] == 'available') bg-green-100 text-green-800
                                    @elseif($data['availability'] == 'busy') bg-orange-100 text-orange-800
                                    @elseif($data['availability'] == 'on_break') bg-yellow-100 text-yellow-800
                                    @else bg-red-100 text-red-800 @endif">
                                    @switch($data['availability'])
                                        @case('available') متاح @break
                                        @case('busy') مشغول @break
                                        @case('on_break') في استراحة @break
                                        @case('unavailable') غير متاح @break
                                        @default {{ $data['availability'] }}
                                    @endswitch
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $data['current_repairs'] }}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                                        <div class="h-2 rounded-full 
                                            @if($data['workload_percentage'] <= 50) bg-green-500
                                            @elseif($data['workload_percentage'] <= 80) bg-yellow-500
                                            @else bg-red-500 @endif" 
                                            style="width: {{ min($data['workload_percentage'], 100) }}%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">{{ $data['workload_percentage'] }}%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ $data['estimated_completion'] ?? 'غير محدد' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="{{ route('technicians.analytics', $data['id']) }}" 
                                       class="text-blue-600 hover:text-blue-700">
                                        <i class="fas fa-chart-line"></i>
                                    </a>
                                    <button onclick="openAssignModal({{ $data['id'] }}, '{{ $data['name'] }}')" 
                                            class="text-green-600 hover:text-green-700">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <a href="{{ route('technicians.workload-data', $data['id']) }}" 
                                       class="text-purple-600 hover:text-purple-700">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Workload Distribution Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">توزيع أعباء العمل</h3>
        </div>
        <div class="p-6">
            <canvas id="workloadChart" width="400" height="200"></canvas>
        </div>
    </div>
</div>

<!-- Assign Repair Modal -->
<div id="assignModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تعيين طلب صيانة</h3>
                <button onclick="closeAssignModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="assignForm" method="POST">
                @csrf
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفني</label>
                        <input type="text" id="technicianName" readonly 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                        <input type="hidden" id="technicianId" name="technician_id">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طلب الصيانة</label>
                        <select name="repair_id" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            <option value="">اختر طلب الصيانة</option>
                            <!-- Will be populated via AJAX -->
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات</label>
                        <textarea name="notes" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                                  placeholder="ملاحظات إضافية..."></textarea>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-2 space-x-reverse mt-6">
                    <button type="button" onclick="closeAssignModal()" 
                            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        تعيين
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Workload Distribution Chart
const workloadData = @json($workloadData);
const ctx = document.getElementById('workloadChart').getContext('2d');

const workloadChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: workloadData.map(item => item.name),
        datasets: [{
            label: 'عبء العمل (%)',
            data: workloadData.map(item => item.workload_percentage),
            backgroundColor: workloadData.map(item => {
                if (item.workload_percentage <= 50) return 'rgba(34, 197, 94, 0.8)';
                if (item.workload_percentage <= 80) return 'rgba(251, 191, 36, 0.8)';
                return 'rgba(239, 68, 68, 0.8)';
            }),
            borderColor: workloadData.map(item => {
                if (item.workload_percentage <= 50) return 'rgb(34, 197, 94)';
                if (item.workload_percentage <= 80) return 'rgb(251, 191, 36)';
                return 'rgb(239, 68, 68)';
            }),
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            },
            title: {
                display: true,
                text: 'توزيع أعباء العمل بين الفنيين'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});

// Modal functions
function openAssignModal(technicianId, technicianName) {
    document.getElementById('technicianId').value = technicianId;
    document.getElementById('technicianName').value = technicianName;
    document.getElementById('assignForm').action = `/technicians/${technicianId}/assign-repair`;
    document.getElementById('assignModal').classList.remove('hidden');
    
    // Load available repairs via AJAX
    loadAvailableRepairs();
}

function closeAssignModal() {
    document.getElementById('assignModal').classList.add('hidden');
}

function openBulkAssignModal() {
    // Implementation for bulk assign modal
    alert('ميزة التعيين الجماعي قيد التطوير');
}

function loadAvailableRepairs() {
    // AJAX call to load available repairs
    fetch('/api/repairs/available')
        .then(response => response.json())
        .then(data => {
            const select = document.querySelector('select[name="repair_id"]');
            select.innerHTML = '<option value="">اختر طلب الصيانة</option>';
            
            data.forEach(repair => {
                const option = document.createElement('option');
                option.value = repair.id;
                option.textContent = `${repair.repair_number} - ${repair.customer_name}`;
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading repairs:', error);
        });
}
</script>
@endpush
@endsection
