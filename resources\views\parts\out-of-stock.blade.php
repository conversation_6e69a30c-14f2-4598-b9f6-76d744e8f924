@extends('layouts.main')

@section('title', 'قطع الغيار - نفد المخزون')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">قطع الغيار - نفد المخزون</h1>
            <p class="text-gray-600 dark:text-gray-400">قطع الغيار غير المتوفرة حالياً</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('parts.inventory') }}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                لوحة المخزون
            </a>
            <a href="{{ route('parts.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                جميع قطع الغيار
            </a>
        </div>
    </div>

    <!-- Alert -->
    <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <div class="mr-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                    تنبيه نفاد المخزون
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                    <p>هذه القطع نفد مخزونها بالكامل. يجب إعادة طلبها فوراً لتجنب تأخير طلبات الصيانة.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Parts Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                قطع الغيار ({{ $parts->total() }} قطعة)
            </h3>
        </div>

        @if($parts->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            القطعة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الفئة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المورد
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            آخر تحديث
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            كمية الطلب المقترحة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الأولوية
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($parts as $part)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                @if($part->images && count($part->images) > 0)
                                    <img src="{{ Storage::url($part->images[0]) }}" alt="{{ $part->name }}" class="w-10 h-10 rounded-lg object-cover ml-3">
                                @else
                                    <div class="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center ml-3">
                                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"></path>
                                        </svg>
                                    </div>
                                @endif
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        <a href="{{ route('parts.show', $part) }}" class="hover:text-blue-600 dark:hover:text-blue-400">
                                            {{ $part->name }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $part->part_number ?? $part->sku }}
                                    </div>
                                    @if($part->brand)
                                        <div class="text-xs text-gray-400">{{ $part->brand }} {{ $part->model }}</div>
                                    @endif
                                    <div class="flex items-center mt-1">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                            نفد المخزون
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {{ $part->category->name ?? 'غير محدد' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {{ $part->supplier->name ?? 'غير محدد' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ $part->updated_at->diffForHumans() }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-sm font-medium text-green-600 dark:text-green-400">
                                {{ $part->reorder_quantity ?? ($part->reorder_point ? $part->reorder_point * 2 : 20) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @php
                                $daysSinceUpdate = $part->updated_at->diffInDays(now());
                                if ($daysSinceUpdate > 7) {
                                    $priority = ['class' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200', 'text' => 'عاجل'];
                                } elseif ($daysSinceUpdate > 3) {
                                    $priority = ['class' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200', 'text' => 'مهم'];
                                } else {
                                    $priority = ['class' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200', 'text' => 'عادي'];
                                }
                            @endphp
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $priority['class'] }}">
                                {{ $priority['text'] }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{{ route('parts.show', $part) }}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200">
                                    عرض
                                </a>
                                <a href="{{ route('parts.edit', $part) }}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-200">
                                    تعديل
                                </a>
                                <button onclick="addStock({{ $part->id }})" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200">
                                    إضافة مخزون
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            {{ $parts->links() }}
        </div>
        @else
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">جميع القطع متوفرة</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لا توجد قطع غيار نفد مخزونها حالياً.</p>
            <div class="mt-6">
                <a href="{{ route('parts.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <svg class="-mr-1 ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"></path>
                    </svg>
                    عرض جميع قطع الغيار
                </a>
            </div>
        </div>
        @endif
    </div>

    <!-- Emergency Actions -->
    @if($parts->count() > 0)
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border-r-4 border-red-400">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <svg class="w-5 h-5 text-red-500 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            إجراءات طارئة
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button onclick="createUrgentPurchaseOrder()" class="flex items-center justify-center p-4 bg-red-50 dark:bg-red-900 rounded-lg hover:bg-red-100 dark:hover:bg-red-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-red-600 dark:text-red-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-sm font-medium text-red-600 dark:text-red-400">أمر شراء عاجل</p>
                </div>
            </button>

            <button onclick="notifySuppliers()" class="flex items-center justify-center p-4 bg-orange-50 dark:bg-orange-900 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-orange-600 dark:text-orange-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <p class="text-sm font-medium text-orange-600 dark:text-orange-400">إشعار الموردين</p>
                </div>
            </button>

            <button onclick="findAlternatives()" class="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-blue-600 dark:text-blue-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <p class="text-sm font-medium text-blue-600 dark:text-blue-400">البحث عن بدائل</p>
                </div>
            </button>

            <button onclick="exportUrgentList()" class="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900 rounded-lg hover:bg-green-100 dark:hover:bg-green-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-green-600 dark:text-green-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-sm font-medium text-green-600 dark:text-green-400">تصدير قائمة عاجلة</p>
                </div>
            </button>
        </div>
    </div>
    @endif
</div>

<script>
function addStock(partId) {
    // Implementation for adding stock
    alert('سيتم إضافة نافذة إضافة المخزون قريباً');
}

function createUrgentPurchaseOrder() {
    // Implementation for creating urgent purchase order
    alert('سيتم إضافة وظيفة إنشاء أمر الشراء العاجل قريباً');
}

function notifySuppliers() {
    // Implementation for notifying suppliers
    alert('سيتم إضافة وظيفة إشعار الموردين قريباً');
}

function findAlternatives() {
    // Implementation for finding alternatives
    alert('سيتم إضافة وظيفة البحث عن البدائل قريباً');
}

function exportUrgentList() {
    // Implementation for exporting urgent list
    window.location.href = '{{ route("parts.export") }}?stock_status=out_of_stock';
}
</script>
@endsection
