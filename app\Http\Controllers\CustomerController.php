<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\CustomerDevice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CustomerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // TODO: Add permission middleware when role system is implemented
        // $this->middleware('permission:customers.view')->only(['index', 'show']);
        // $this->middleware('permission:customers.create')->only(['create', 'store']);
        // $this->middleware('permission:customers.edit')->only(['edit', 'update']);
        // $this->middleware('permission:customers.delete')->only(['destroy']);
    }

    public function index(Request $request)
    {
        $query = Customer::with(['repairs']);

        // Apply filters
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('first_name', 'like', '%' . $request->search . '%')
                  ->orWhere('last_name', 'like', '%' . $request->search . '%')
                  ->orWhere('company_name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%')
                  ->orWhere('mobile', 'like', '%' . $request->search . '%')
                  ->orWhere('customer_number', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('city')) {
            $query->where('city', $request->city);
        }

        if ($request->filled('is_vip')) {
            $query->where('is_vip', $request->is_vip);
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        if ($request->filled('has_outstanding_balance')) {
            if ($request->has_outstanding_balance == '1') {
                $query->whereHas('repairs', function ($q) {
                    $q->whereIn('payment_status', ['pending', 'partial']);
                });
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        
        if ($sortBy === 'name') {
            $query->orderBy('first_name', $sortDirection)
                  ->orderBy('last_name', $sortDirection);
        } else {
            $query->orderBy($sortBy, $sortDirection);
        }

        $customers = $query->paginate(15)->appends($request->query());

        // Get filter options
        $cities = Customer::distinct()->pluck('city')->filter()->sort()->values();
        $customerTypes = ['individual', 'business'];

        // Statistics
        $stats = [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::where('is_active', true)->count(),
            'vip_customers' => Customer::where('is_vip', true)->count(),
            'customers_with_repairs' => Customer::whereHas('repairs')->count(),
            'total_revenue' => Customer::join('repairs', 'customers.id', '=', 'repairs.customer_id')
                                    ->where('repairs.payment_status', 'paid')
                                    ->sum('repairs.total_cost'),
        ];

        return view('customers.index', compact('customers', 'cities', 'customerTypes', 'stats'));
    }





    public function create()
    {
        $customerTypes = ['individual', 'business'];
        $genders = ['male', 'female'];
        $contactMethods = ['phone', 'email', 'sms', 'whatsapp'];
        $languages = ['ar', 'en'];
        $sources = ['website', 'referral', 'social_media', 'advertisement', 'walk_in', 'other'];

        // Set customer to null to avoid undefined variable errors in shared views
        $customer = null;

        return view('customers.create', compact(
            'customer', 'customerTypes', 'genders', 'contactMethods', 'languages', 'sources'
        ));
    }

    public function store(Request $request)
    {
        $request->validate([
            'type' => 'required|in:individual,business',
            'first_name' => 'required_if:type,individual|string|max:255',
            'last_name' => 'required_if:type,individual|string|max:255',
            'company_name' => 'required_if:type,business|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'required|string|max:20|unique:customers,mobile',
            'whatsapp' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:255',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female',
            'national_id' => 'nullable|string|max:20|unique:customers,national_id',
            'tax_number' => 'nullable|string|max:50|unique:customers,tax_number',
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'preferred_contact_method' => 'nullable|in:phone,email,sms,whatsapp',
            'language_preference' => 'nullable|in:ar,en',
            'notes' => 'nullable|string',
            'tags' => 'nullable|array',
            'source' => 'nullable|string|max:255',
            'referral_source' => 'nullable|string|max:255',
            'marketing_consent' => 'boolean',
            'sms_consent' => 'boolean',
            'email_consent' => 'boolean',
            'is_vip' => 'boolean',
            'is_active' => 'boolean',
        ], [
            'type.required' => 'نوع العميل مطلوب.',
            'type.in' => 'نوع العميل غير صحيح.',
            'first_name.required_if' => 'الاسم الأول مطلوب للعملاء الأفراد.',
            'last_name.required_if' => 'الاسم الأخير مطلوب للعملاء الأفراد.',
            'company_name.required_if' => 'اسم الشركة مطلوب للعملاء التجاريين.',
            'email.email' => 'البريد الإلكتروني غير صحيح.',
            'email.unique' => 'البريد الإلكتروني مستخدم من قبل.',
            'mobile.required' => 'رقم الجوال مطلوب.',
            'mobile.unique' => 'رقم الجوال مستخدم من قبل.',
            'date_of_birth.before' => 'تاريخ الميلاد يجب أن يكون في الماضي.',
            'national_id.unique' => 'رقم الهوية مستخدم من قبل.',
            'tax_number.unique' => 'الرقم الضريبي مستخدم من قبل.',
            'credit_limit.numeric' => 'الحد الائتماني يجب أن يكون رقماً.',
            'credit_limit.min' => 'الحد الائتماني يجب أن يكون أكبر من أو يساوي صفر.',
            'discount_percentage.min' => 'نسبة الخصم يجب أن تكون أكبر من أو تساوي صفر.',
            'discount_percentage.max' => 'نسبة الخصم يجب أن تكون أقل من أو تساوي 100.',
        ]);

        try {
            $data = $request->all();

            // Log the incoming data for debugging
            Log::info('Customer creation attempt', [
                'type' => $data['type'] ?? 'not_set',
                'has_first_name' => !empty($data['first_name']),
                'has_last_name' => !empty($data['last_name']),
                'has_company_name' => !empty($data['company_name']),
                'has_mobile' => !empty($data['mobile']),
                'user_id' => auth()->id()
            ]);

            // Generate customer number
            $data['customer_number'] = $this->generateCustomerNumber();
            $data['created_by'] = auth()->id();

            // Handle boolean fields properly
            $data['is_vip'] = $request->has('is_vip') ? true : false;
            $data['is_active'] = $request->has('is_active') ? true : false;
            $data['marketing_consent'] = $request->has('marketing_consent') ? true : false;
            $data['sms_consent'] = $request->has('sms_consent') ? true : false;
            $data['email_consent'] = $request->has('email_consent') ? true : false;

            $customer = Customer::create($data);

            Log::info('Customer created successfully', ['customer_id' => $customer->id]);

            return redirect()->route('customers.show', $customer)
                ->with('success', 'تم إضافة العميل بنجاح.');

        } catch (\Exception $e) {
            Log::error('Customer creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'input_data' => $request->except(['_token'])
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إضافة العميل: ' . $e->getMessage());
        }
    }

    public function show(Customer $customer)
    {
        $customer->load([
            'repairs' => function ($query) {
                $query->latest()->limit(10);
            },
            'devices' => function ($query) {
                $query->where('is_active', true);
            },
            'payments' => function ($query) {
                $query->latest()->limit(5);
            },
            'communications' => function ($query) {
                $query->latest()->limit(5);
            }
        ]);

        // Calculate statistics
        $stats = [
            'total_repairs' => $customer->repairs()->count(),
            'completed_repairs' => $customer->repairs()->where('status', 'completed')->count(),
            'pending_repairs' => $customer->repairs()->whereIn('status', ['pending', 'in_progress', 'waiting_parts'])->count(),
            'total_spent' => $customer->total_spent,
            'outstanding_balance' => $customer->outstanding_balance,
            'average_repair_cost' => $customer->repairs()->avg('total_cost') ?? 0,
            'last_repair_date' => $customer->last_repair_date,
            'customer_since' => $customer->created_at,
            'total_devices' => 0, // Will be updated when devices table is available
            'active_devices' => 0, // Will be updated when devices table is available
        ];

        // Recent activity
        $recentRepairs = $customer->repairs()
            ->with(['technician', 'parts'])
            ->latest()
            ->limit(5)
            ->get();

        return view('customers.show', compact('customer', 'stats', 'recentRepairs'));
    }

    public function edit(Customer $customer)
    {
        $customerTypes = ['individual', 'business'];
        $genders = ['male', 'female'];
        $contactMethods = ['phone', 'email', 'sms', 'whatsapp'];
        $languages = ['ar', 'en'];
        $sources = ['website', 'referral', 'social_media', 'advertisement', 'walk_in', 'other'];

        return view('customers.edit', compact(
            'customer', 'customerTypes', 'genders', 'contactMethods', 'languages', 'sources'
        ));
    }

    public function update(Request $request, Customer $customer)
    {
        $request->validate([
            'type' => 'required|in:individual,business',
            'first_name' => 'required_if:type,individual|string|max:255',
            'last_name' => 'required_if:type,individual|string|max:255',
            'company_name' => 'required_if:type,business|string|max:255',
            'email' => 'nullable|email|unique:customers,email,' . $customer->id,
            'phone' => 'nullable|string|max:20',
            'mobile' => 'required|string|max:20|unique:customers,mobile,' . $customer->id,
            'whatsapp' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:255',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female',
            'national_id' => 'nullable|string|max:20|unique:customers,national_id,' . $customer->id,
            'tax_number' => 'nullable|string|max:50|unique:customers,tax_number,' . $customer->id,
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'preferred_contact_method' => 'nullable|in:phone,email,sms,whatsapp',
            'language_preference' => 'nullable|in:ar,en',
            'notes' => 'nullable|string',
            'tags' => 'nullable|array',
            'source' => 'nullable|string|max:255',
            'referral_source' => 'nullable|string|max:255',
            'marketing_consent' => 'boolean',
            'sms_consent' => 'boolean',
            'email_consent' => 'boolean',
            'is_vip' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();
        $data['updated_by'] = auth()->id();

        $customer->update($data);

        return redirect()->route('customers.show', $customer)
            ->with('success', 'تم تحديث بيانات العميل بنجاح.');
    }

    public function destroy(Customer $customer)
    {
        // Check if customer has repairs
        if ($customer->repairs()->count() > 0) {
            return redirect()->route('customers.index')
                ->with('error', 'لا يمكن حذف العميل لأنه يحتوي على طلبات صيانة.');
        }

        $customer->delete();

        return redirect()->route('customers.index')
            ->with('success', 'تم حذف العميل بنجاح.');
    }

    public function repairs(Customer $customer)
    {
        $repairs = $customer->repairs()
            ->with(['technician', 'parts'])
            ->latest()
            ->paginate(15);

        return view('customers.repairs', compact('customer', 'repairs'));
    }

    public function devices(Customer $customer)
    {
        // For now, return empty collection until devices table is created
        $devices = collect()->paginate(15);

        return view('customers.devices', compact('customer', 'devices'));
    }

    public function addDevice(Request $request, Customer $customer)
    {
        $request->validate([
            'device_type' => 'required|string|max:255',
            'brand' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'serial_number' => 'nullable|string|max:255|unique:customer_devices,serial_number',
            'imei' => 'nullable|string|max:20|unique:customer_devices,imei',
            'purchase_date' => 'nullable|date|before_or_equal:today',
            'warranty_expires_at' => 'nullable|date|after:today',
            'notes' => 'nullable|string',
        ]);

        $data = $request->all();
        $data['customer_id'] = $customer->id;
        $data['is_active'] = true;
        $data['added_at'] = now();

        // For now, return success message until devices table is created
        return redirect()->route('customers.devices', $customer)
            ->with('info', 'ميزة إضافة الأجهزة ستكون متاحة قريباً');
    }

    public function payments(Customer $customer)
    {
        $payments = $customer->payments()
            ->with(['repair'])
            ->latest()
            ->paginate(15);

        $paymentStats = [
            'total_paid' => $customer->payments()->where('status', 'completed')->sum('amount'),
            'pending_payments' => $customer->payments()->where('status', 'pending')->sum('amount'),
            'outstanding_balance' => $customer->outstanding_balance,
            'credit_limit' => $customer->credit_limit,
            'available_credit' => max(0, ($customer->credit_limit ?? 0) - $customer->outstanding_balance),
        ];

        return view('customers.payments', compact('customer', 'payments', 'paymentStats'));
    }

    public function communications(Customer $customer)
    {
        $communications = $customer->communications()
            ->with(['createdBy'])
            ->latest()
            ->paginate(15);

        return view('customers.communications', compact('customer', 'communications'));
    }

    public function addCommunication(Request $request, Customer $customer)
    {
        $request->validate([
            'type' => 'required|in:call,email,sms,whatsapp,visit,note',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'direction' => 'required|in:inbound,outbound',
            'status' => 'required|in:completed,scheduled,cancelled',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        $data = $request->all();
        $data['customer_id'] = $customer->id;
        $data['created_by'] = auth()->id();

        $customer->communications()->create($data);

        // Update last contact date
        $customer->update(['last_contact_date' => now()]);

        return redirect()->route('customers.communications', $customer)
            ->with('success', 'تم إضافة التواصل بنجاح.');
    }

    public function analytics(Customer $customer)
    {
        // Repair trends
        $repairTrends = $customer->repairs()
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month, COUNT(*) as count, SUM(total_cost) as revenue')
            ->groupBy('month')
            ->orderBy('month')
            ->limit(12)
            ->get();

        // Device breakdown
        $deviceBreakdown = $customer->devices()
            ->selectRaw('device_type, COUNT(*) as count')
            ->groupBy('device_type')
            ->get();

        // Repair status distribution
        $repairStatusDistribution = $customer->repairs()
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        // Average repair time
        $averageRepairTime = $customer->repairs()
            ->whereNotNull('completed_at')
            ->selectRaw('AVG(DATEDIFF(completed_at, created_at)) as avg_days')
            ->first()->avg_days ?? 0;

        // Customer lifetime value
        $lifetimeValue = $customer->total_spent;

        // Satisfaction metrics (if available)
        $satisfactionRating = $customer->repairs()
            ->whereNotNull('customer_rating')
            ->avg('customer_rating') ?? 0;

        return view('customers.analytics', compact(
            'customer',
            'repairTrends',
            'deviceBreakdown',
            'repairStatusDistribution',
            'averageRepairTime',
            'lifetimeValue',
            'satisfactionRating'
        ));
    }

    public function export(Request $request)
    {
        $query = Customer::with(['repairs', 'devices']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('first_name', 'like', '%' . $request->search . '%')
                  ->orWhere('last_name', 'like', '%' . $request->search . '%')
                  ->orWhere('company_name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('city')) {
            $query->where('city', $request->city);
        }

        $customers = $query->get();

        return view('customers.export', compact('customers'));
    }

    public function search(Request $request)
    {
        $query = $request->get('q');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $customers = Customer::where(function ($q) use ($query) {
            $q->where('first_name', 'like', '%' . $query . '%')
              ->orWhere('last_name', 'like', '%' . $query . '%')
              ->orWhere('company_name', 'like', '%' . $query . '%')
              ->orWhere('email', 'like', '%' . $query . '%')
              ->orWhere('phone', 'like', '%' . $query . '%')
              ->orWhere('mobile', 'like', '%' . $query . '%')
              ->orWhere('customer_number', 'like', '%' . $query . '%');
        })
        ->where('is_active', true)
        ->limit(10)
        ->get(['id', 'customer_number', 'first_name', 'last_name', 'company_name', 'email', 'mobile']);

        return response()->json($customers->map(function ($customer) {
            return [
                'id' => $customer->id,
                'text' => $customer->display_name . ' - ' . $customer->mobile,
                'customer_number' => $customer->customer_number,
                'name' => $customer->display_name,
                'mobile' => $customer->mobile,
                'email' => $customer->email,
            ];
        }));
    }

    private function generateCustomerNumber()
    {
        $prefix = 'CUS';
        $year = now()->year;

        $lastCustomer = Customer::whereYear('created_at', $year)
                              ->orderBy('id', 'desc')
                              ->first();

        $sequence = $lastCustomer ? (int)substr($lastCustomer->customer_number, -5) + 1 : 1;

        return $prefix . $year . str_pad($sequence, 5, '0', STR_PAD_LEFT);
    }
}
