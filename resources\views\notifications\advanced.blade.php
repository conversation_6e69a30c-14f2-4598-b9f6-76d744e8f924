@extends('layouts.main')

@section('title', 'نظام الإشعارات المتقدم')

@section('content')
<div class="space-y-6" x-data="advancedNotifications()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">نظام الإشعارات المتقدم</h1>
            <p class="text-gray-600 dark:text-gray-400">إدارة وتخصيص الإشعارات والتنبيهات</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="markAllAsRead()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                تحديد الكل كمقروء
            </button>
            <button @click="showSettings()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                إعدادات الإشعارات
            </button>
        </div>
    </div>

    <!-- Notification Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الإشعارات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.total">156</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">غير مقروءة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.unread">23</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">عاجلة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.urgent">5</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">تم التعامل معها</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.handled">128</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex flex-wrap items-center gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع الإشعار</label>
                <select x-model="filters.type" @change="filterNotifications()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الأنواع</option>
                    <option value="repair">طلبات الصيانة</option>
                    <option value="payment">المدفوعات</option>
                    <option value="system">النظام</option>
                    <option value="reminder">تذكيرات</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الأولوية</label>
                <select x-model="filters.priority" @change="filterNotifications()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الأولويات</option>
                    <option value="high">عالية</option>
                    <option value="medium">متوسطة</option>
                    <option value="low">منخفضة</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                <select x-model="filters.status" @change="filterNotifications()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="unread">غير مقروءة</option>
                    <option value="read">مقروءة</option>
                    <option value="archived">مؤرشفة</option>
                </select>
            </div>

            <div class="flex-1">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البحث</label>
                <input type="text" x-model="filters.search" @input="filterNotifications()" placeholder="البحث في الإشعارات..." class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الإشعارات</h3>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button @click="refreshNotifications()" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 text-sm">
                        تحديث
                    </button>
                    <span class="text-sm text-gray-500 dark:text-gray-400" x-text="filteredNotifications.length + ' إشعار'"></span>
                </div>
            </div>
        </div>

        <div class="divide-y divide-gray-200 dark:divide-gray-700">
            <template x-for="notification in paginatedNotifications" :key="notification.id">
                <div class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200" :class="{'bg-blue-50 dark:bg-blue-900/20': !notification.read}">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center" :class="{
                                'bg-red-100 dark:bg-red-900': notification.priority === 'high',
                                'bg-yellow-100 dark:bg-yellow-900': notification.priority === 'medium',
                                'bg-green-100 dark:bg-green-900': notification.priority === 'low'
                            }">
                                <svg class="w-5 h-5" :class="{
                                    'text-red-600 dark:text-red-400': notification.priority === 'high',
                                    'text-yellow-600 dark:text-yellow-400': notification.priority === 'medium',
                                    'text-green-600 dark:text-green-400': notification.priority === 'low'
                                }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getNotificationIcon(notification.type)"></path>
                                </svg>
                            </div>
                        </div>

                        <div class="mr-4 flex-1">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="notification.title"></h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1" x-text="notification.message"></p>
                                    <div class="flex items-center mt-2 space-x-4 space-x-reverse">
                                        <span class="text-xs text-gray-500 dark:text-gray-400" x-text="notification.timestamp"></span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="{
                                            'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': notification.type === 'repair',
                                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': notification.type === 'payment',
                                            'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200': notification.type === 'system',
                                            'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200': notification.type === 'reminder'
                                        }" x-text="getTypeText(notification.type)"></span>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <button @click="markAsRead(notification.id)" x-show="!notification.read" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 text-sm">
                                        تحديد كمقروء
                                    </button>
                                    <button @click="archiveNotification(notification.id)" class="text-gray-600 hover:text-gray-800 dark:text-gray-400 text-sm">
                                        أرشفة
                                    </button>
                                    <button @click="deleteNotification(notification.id)" class="text-red-600 hover:text-red-800 dark:text-red-400 text-sm">
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    عرض <span x-text="(currentPage - 1) * itemsPerPage + 1"></span> إلى
                    <span x-text="Math.min(currentPage * itemsPerPage, filteredNotifications.length)"></span> من
                    <span x-text="filteredNotifications.length"></span> إشعار
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <button @click="previousPage()" :disabled="currentPage === 1" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm disabled:opacity-50">
                        السابق
                    </button>
                    <button @click="nextPage()" :disabled="currentPage >= totalPages" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm disabled:opacity-50">
                        التالي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Settings Modal -->
    <div x-show="showSettingsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-transition>
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">إعدادات الإشعارات</h3>
                <button @click="showSettingsModal = false" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">إشعارات سطح المكتب</label>
                    <input type="checkbox" x-model="settings.desktop" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>

                <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">إشعارات البريد الإلكتروني</label>
                    <input type="checkbox" x-model="settings.email" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>

                <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">إشعارات SMS</label>
                    <input type="checkbox" x-model="settings.sms" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>

                <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">الأصوات</label>
                    <input type="checkbox" x-model="settings.sound" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>
            </div>

            <div class="mt-6 flex space-x-2 space-x-reverse">
                <button @click="saveSettings()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                    حفظ
                </button>
                <button @click="showSettingsModal = false" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function advancedNotifications() {
    return {
        stats: {
            total: 156,
            unread: 23,
            urgent: 5,
            handled: 128
        },
        filters: {
            type: '',
            priority: '',
            status: '',
            search: ''
        },
        notifications: [
            {
                id: 1,
                type: 'repair',
                priority: 'high',
                title: 'طلب صيانة عاجل',
                message: 'طلب صيانة جديد يتطلب اهتماماً فورياً - iPhone 13 Pro',
                timestamp: '2024-07-09 14:30',
                read: false
            },
            {
                id: 2,
                type: 'payment',
                priority: 'medium',
                title: 'دفعة جديدة مستلمة',
                message: 'تم استلام دفعة بقيمة 500 شيكل من العميل أحمد محمد',
                timestamp: '2024-07-09 13:15',
                read: false
            },
            {
                id: 3,
                type: 'system',
                priority: 'low',
                title: 'تحديث النظام',
                message: 'تم تحديث النظام بنجاح إلى الإصدار 2.1.0',
                timestamp: '2024-07-09 12:00',
                read: true
            },
            {
                id: 4,
                type: 'reminder',
                priority: 'medium',
                title: 'تذكير موعد',
                message: 'موعد صيانة مجدول اليوم في الساعة 16:00',
                timestamp: '2024-07-09 11:45',
                read: false
            }
        ],
        filteredNotifications: [],
        currentPage: 1,
        itemsPerPage: 10,
        showSettingsModal: false,
        settings: {
            desktop: true,
            email: true,
            sms: false,
            sound: true
        },

        init() {
            this.filteredNotifications = [...this.notifications];
            this.requestNotificationPermission();
        },

        get paginatedNotifications() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filteredNotifications.slice(start, end);
        },

        get totalPages() {
            return Math.ceil(this.filteredNotifications.length / this.itemsPerPage);
        },

        filterNotifications() {
            this.filteredNotifications = this.notifications.filter(notification => {
                const matchesType = !this.filters.type || notification.type === this.filters.type;
                const matchesPriority = !this.filters.priority || notification.priority === this.filters.priority;
                const matchesStatus = !this.filters.status ||
                    (this.filters.status === 'read' && notification.read) ||
                    (this.filters.status === 'unread' && !notification.read);
                const matchesSearch = !this.filters.search ||
                    notification.title.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    notification.message.toLowerCase().includes(this.filters.search.toLowerCase());

                return matchesType && matchesPriority && matchesStatus && matchesSearch;
            });
            this.currentPage = 1;
        },

        markAsRead(id) {
            const notification = this.notifications.find(n => n.id === id);
            if (notification) {
                notification.read = true;
                this.stats.unread--;
            }
        },

        markAllAsRead() {
            this.notifications.forEach(notification => {
                if (!notification.read) {
                    notification.read = true;
                }
            });
            this.stats.unread = 0;
        },

        archiveNotification(id) {
            // In a real app, this would move to archived status
            alert(`أرشفة الإشعار ${id}`);
        },

        deleteNotification(id) {
            if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
                const index = this.notifications.findIndex(n => n.id === id);
                if (index !== -1) {
                    this.notifications.splice(index, 1);
                    this.filterNotifications();
                }
            }
        },

        refreshNotifications() {
            // Simulate fetching new notifications
            alert('تم تحديث الإشعارات');
        },

        previousPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },

        showSettings() {
            this.showSettingsModal = true;
        },

        saveSettings() {
            // Save settings to backend
            alert('تم حفظ الإعدادات');
            this.showSettingsModal = false;
        },

        getNotificationIcon(type) {
            const icons = {
                'repair': 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
                'payment': 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
                'system': 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
                'reminder': 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
            };
            return icons[type] || icons['system'];
        },

        getTypeText(type) {
            const types = {
                'repair': 'صيانة',
                'payment': 'دفع',
                'system': 'نظام',
                'reminder': 'تذكير'
            };
            return types[type] || type;
        },

        requestNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission();
            }
        },

        showDesktopNotification(title, message) {
            if ('Notification' in window && Notification.permission === 'granted' && this.settings.desktop) {
                new Notification(title, {
                    body: message,
                    icon: '/icons/icon-192x192.png',
                    dir: 'rtl',
                    lang: 'ar'
                });
            }
        }
    }
}
</script>
@endpush
@endsection