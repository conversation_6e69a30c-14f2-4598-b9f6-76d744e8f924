<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accounts', function (Blueprint $table) {
            $table->id();
            $table->string('account_code', 10)->unique()->comment('رقم الحساب');
            $table->string('account_name')->comment('اسم الحساب');
            $table->string('account_name_en')->nullable()->comment('اسم الحساب بالإنجليزية');
            $table->unsignedBigInteger('parent_id')->nullable()->comment('الحساب الأب');
            $table->enum('account_type', ['asset', 'liability', 'equity', 'revenue', 'expense'])->comment('نوع الحساب');
            $table->string('account_category', 50)->nullable()->comment('فئة الحساب');
            $table->boolean('is_active')->default(true)->comment('نشط/غير نشط');
            $table->boolean('is_system')->default(false)->comment('حساب نظام');
            $table->enum('balance_type', ['debit', 'credit'])->comment('طبيعة الرصيد');
            $table->decimal('opening_balance', 15, 2)->default(0)->comment('الرصيد الافتتاحي');
            $table->decimal('current_balance', 15, 2)->default(0)->comment('الرصيد الحالي');
            $table->text('description')->nullable()->comment('وصف الحساب');
            $table->json('settings')->nullable()->comment('إعدادات إضافية');
            $table->timestamps();

            // Indexes
            $table->index(['account_type', 'is_active']);
            $table->index(['parent_id']);
            $table->index(['account_code']);
            
            // Foreign Keys
            $table->foreign('parent_id')->references('id')->on('accounts')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accounts');
    }
};
