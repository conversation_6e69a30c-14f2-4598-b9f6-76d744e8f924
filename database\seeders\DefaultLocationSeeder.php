<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Location;

class DefaultLocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default location if none exists
        if (Location::count() === 0) {
            Location::create([
                'name' => 'الفرع الرئيسي',
                'code' => 'MAIN',
                'type' => 'store',
                'description' => 'الفرع الرئيسي لمركز الصيانة',
                'email' => '<EMAIL>',
                'phone' => '+966501234567',
                'address' => 'شارع الملك فهد، الرياض',
                'city' => 'الرياض',
                'state' => 'الرياض',
                'country' => 'المملكة العربية السعودية',
                'postal_code' => '12345',
                'is_active' => true,
                'is_default' => true,
                'is_main_branch' => true,
                'business_hours' => [
                    'sunday' => ['open' => '09:00', 'close' => '18:00'],
                    'monday' => ['open' => '09:00', 'close' => '18:00'],
                    'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                    'thursday' => ['open' => '09:00', 'close' => '18:00'],
                    'friday' => ['open' => '14:00', 'close' => '18:00'],
                    'saturday' => ['open' => '09:00', 'close' => '18:00'],
                ],
                'services_offered' => [
                    'mobile_repair',
                    'computer_repair',
                    'tablet_repair',
                    'parts_sales',
                    'accessories_sales'
                ],
                'pos_settings' => [
                    'allow_sales' => true,
                    'tax_rate' => 15,
                    'payment_methods' => ['cash', 'card', 'bank_transfer'],
                    'auto_print_receipt' => true,
                    'require_customer_info' => false,
                ],
                'receipt_header' => 'مركز الصيانة المتقدم',
                'receipt_footer' => 'شكراً لثقتكم - نتطلع لخدمتكم مرة أخرى',
                'tax_number' => '***************',
            ]);

            $this->command->info('تم إنشاء الموقع الافتراضي بنجاح');
        } else {
            $this->command->info('الموقع الافتراضي موجود بالفعل');
        }
    }
}
