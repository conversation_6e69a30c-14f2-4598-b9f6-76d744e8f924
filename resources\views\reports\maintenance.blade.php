@extends('layouts.main')

@section('title', 'تقارير الصيانة الإضافية')

@section('content')
<div class="space-y-6" x-data="maintenanceReports()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تقارير الصيانة الإضافية</h1>
            <p class="text-gray-600 dark:text-gray-400">تحليلات متقدمة لأداء الصيانة والفنيين</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="generateReport()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                إنشاء تقرير مخصص
            </button>
            <button @click="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير
            </button>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm">متوسط وقت الإصلاح</p>
                    <p class="text-3xl font-bold" x-text="metrics.avgRepairTime">2.5 يوم</p>
                    <p class="text-blue-200 text-sm mt-1">-0.3 يوم من الشهر الماضي</p>
                </div>
                <div class="w-16 h-16 bg-blue-400 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm">معدل النجاح</p>
                    <p class="text-3xl font-bold" x-text="metrics.successRate">96.8%</p>
                    <p class="text-green-200 text-sm mt-1">+2.1% من الشهر الماضي</p>
                </div>
                <div class="w-16 h-16 bg-green-400 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm">رضا العملاء</p>
                    <p class="text-3xl font-bold" x-text="metrics.customerSatisfaction">4.7/5</p>
                    <p class="text-purple-200 text-sm mt-1">+0.2 من الشهر الماضي</p>
                </div>
                <div class="w-16 h-16 bg-purple-400 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100 text-sm">إنتاجية الفنيين</p>
                    <p class="text-3xl font-bold" x-text="metrics.technicianProductivity">8.2</p>
                    <p class="text-orange-200 text-sm mt-1">طلب/يوم للفني</p>
                </div>
                <div class="w-16 h-16 bg-orange-400 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">فلاتر التقرير</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفترة الزمنية</label>
                <select x-model="filters.period" @change="updateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="7days">آخر 7 أيام</option>
                    <option value="30days">آخر 30 يوم</option>
                    <option value="3months">آخر 3 أشهر</option>
                    <option value="6months">آخر 6 أشهر</option>
                    <option value="1year">آخر سنة</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الجهاز</label>
                <select x-model="filters.deviceType" @change="updateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الأجهزة</option>
                    <option value="phone">هواتف</option>
                    <option value="tablet">أجهزة لوحية</option>
                    <option value="laptop">أجهزة كمبيوتر محمولة</option>
                    <option value="other">أخرى</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفني</label>
                <select x-model="filters.technician" @change="updateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الفنيين</option>
                    <option value="tech1">أحمد محمد</option>
                    <option value="tech2">سارة أحمد</option>
                    <option value="tech3">محمد علي</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة الطلب</label>
                <select x-model="filters.status" @change="updateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="completed">مكتملة</option>
                    <option value="in_progress">قيد التنفيذ</option>
                    <option value="pending">في الانتظار</option>
                    <option value="cancelled">ملغية</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Technician Performance -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">أداء الفنيين</h3>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفني</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الطلبات المكتملة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">متوسط الوقت</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">معدل النجاح</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تقييم العملاء</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإيرادات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="tech in technicianPerformance" :key="tech.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-sm font-medium text-blue-600 dark:text-blue-400" x-text="tech.name.charAt(0)"></span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="tech.name"></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400" x-text="tech.specialization"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="tech.completedRepairs"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="tech.avgTime"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="tech.successRate >= 95 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : tech.successRate >= 90 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'" x-text="tech.successRate + '%'"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="tech.customerRating + '/5'"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">₪<span x-text="tech.revenue"></span></td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Repair Trends -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">اتجاهات الصيانة</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">رسم بياني لاتجاهات الصيانة</p>
                </div>
            </div>
        </div>

        <!-- Device Types Distribution -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">توزيع أنواع الأجهزة</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">رسم دائري لتوزيع الأجهزة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Common Issues -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">المشاكل الأكثر شيوعاً</h3>
        
        <div class="space-y-4">
            <template x-for="issue in commonIssues" :key="issue.id">
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="issue.problem"></h4>
                            <p class="text-xs text-gray-500 dark:text-gray-400" x-text="issue.description"></p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="issue.count + ' حالة'"></p>
                        <p class="text-xs text-gray-500 dark:text-gray-400" x-text="issue.percentage + '%'"></p>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

@push('scripts')
<script>
function maintenanceReports() {
    return {
        metrics: {
            avgRepairTime: '2.5 يوم',
            successRate: '96.8%',
            customerSatisfaction: '4.7/5',
            technicianProductivity: '8.2'
        },
        filters: {
            period: '30days',
            deviceType: '',
            technician: '',
            status: ''
        },
        technicianPerformance: [
            {
                id: 1,
                name: 'أحمد محمد',
                specialization: 'هواتف ذكية',
                completedRepairs: 45,
                avgTime: '2.1 يوم',
                successRate: 98,
                customerRating: 4.8,
                revenue: '12,500'
            },
            {
                id: 2,
                name: 'سارة أحمد',
                specialization: 'أجهزة لوحية',
                completedRepairs: 38,
                avgTime: '2.8 يوم',
                successRate: 96,
                customerRating: 4.6,
                revenue: '9,800'
            },
            {
                id: 3,
                name: 'محمد علي',
                specialization: 'أجهزة كمبيوتر',
                completedRepairs: 32,
                avgTime: '3.2 يوم',
                successRate: 94,
                customerRating: 4.5,
                revenue: '11,200'
            }
        ],
        commonIssues: [
            {
                id: 1,
                problem: 'كسر في الشاشة',
                description: 'تلف أو كسر في شاشة الجهاز',
                count: 156,
                percentage: 35
            },
            {
                id: 2,
                problem: 'مشاكل البطارية',
                description: 'ضعف أو تلف في البطارية',
                count: 89,
                percentage: 20
            },
            {
                id: 3,
                problem: 'مشاكل الشحن',
                description: 'عدم الشحن أو شحن بطيء',
                count: 67,
                percentage: 15
            },
            {
                id: 4,
                problem: 'مشاكل الصوت',
                description: 'عدم وضوح الصوت أو انقطاعه',
                count: 45,
                percentage: 10
            }
        ],

        updateReport() {
            // تحديث التقرير بناءً على الفلاتر
            console.log('تحديث التقرير:', this.filters);
        },

        generateReport() {
            alert('سيتم إنشاء تقرير مخصص');
        },

        exportReport() {
            alert('سيتم تصدير التقرير');
        }
    }
}
</script>
@endpush
@endsection
