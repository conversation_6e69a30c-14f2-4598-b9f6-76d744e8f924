@extends('layouts.main')

@section('title', 'لوحة المخزون')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">لوحة المخزون</h1>
            <p class="text-gray-600 dark:text-gray-400">نظرة شاملة على حالة المخزون والحركات</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('parts.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة لقطع الغيار
            </a>
            <a href="{{ route('parts.export') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                تصدير التقرير
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي القطع</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ number_format($totalParts) }}</p>
                    <p class="text-xs text-gray-500">{{ number_format($activeParts) }} نشط</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مخزون منخفض</p>
                    <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ number_format($lowStockParts) }}</p>
                    <p class="text-xs text-gray-500">يحتاج إعادة طلب</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">نفد المخزون</p>
                    <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ number_format($outOfStockParts) }}</p>
                    <p class="text-xs text-gray-500">غير متوفر</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">قيمة المخزون</p>
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ number_format($totalValue, 2) }}</p>
                    <p class="text-xs text-gray-500">{{ number_format($totalSellingValue, 2) }} ر.س قيمة البيع</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Parts Needing Reorder -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">قطع تحتاج إعادة طلب</h3>
                <a href="{{ route('parts.low-stock') }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm">
                    عرض الكل
                </a>
            </div>
            
            @if($reorderParts->count() > 0)
            <div class="space-y-3">
                @foreach($reorderParts as $part)
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        @if($part->images && count($part->images) > 0)
                            <img src="{{ Storage::url($part->images[0]) }}" alt="{{ $part->name }}" class="w-10 h-10 rounded-lg object-cover ml-3">
                        @else
                            <div class="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center ml-3">
                                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"></path>
                                </svg>
                            </div>
                        @endif
                        <div>
                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $part->name }}</p>
                            <p class="text-xs text-gray-500">{{ $part->category->name ?? 'غير محدد' }}</p>
                        </div>
                    </div>
                    <div class="text-left">
                        <p class="text-sm font-bold text-red-600 dark:text-red-400">{{ $part->stock_quantity }}</p>
                        <p class="text-xs text-gray-500">من {{ $part->reorder_point }}</p>
                    </div>
                </div>
                @endforeach
            </div>
            @else
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">جميع القطع متوفرة</h3>
                <p class="mt-1 text-sm text-gray-500">لا توجد قطع تحتاج إعادة طلب حالياً</p>
            </div>
            @endif
        </div>

        <!-- Recent Stock Movements -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">حركات المخزون الأخيرة</h3>
            
            @if($recentMovements->count() > 0)
            <div class="space-y-3">
                @foreach($recentMovements as $movement)
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-2 rounded-lg ml-3 
                            @if(str_contains($movement->type, 'in')) bg-green-100 dark:bg-green-900
                            @else bg-red-100 dark:bg-red-900 @endif">
                            <svg class="w-4 h-4 
                                @if(str_contains($movement->type, 'in')) text-green-600 dark:text-green-400
                                @else text-red-600 dark:text-red-400 @endif" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                @if(str_contains($movement->type, 'in'))
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6"></path>
                                @endif
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $movement->part->name }}</p>
                            <p class="text-xs text-gray-500">
                                @switch($movement->type)
                                    @case('stock_in') إدخال @break
                                    @case('stock_out') إخراج @break
                                    @case('adjustment_in') تعديل زيادة @break
                                    @case('adjustment_out') تعديل نقص @break
                                    @case('initial_stock') مخزون أولي @break
                                    @default {{ $movement->type }}
                                @endswitch
                                - {{ $movement->created_at->diffForHumans() }}
                            </p>
                        </div>
                    </div>
                    <div class="text-left">
                        <p class="text-sm font-bold 
                            @if(str_contains($movement->type, 'in')) text-green-600 dark:text-green-400
                            @else text-red-600 dark:text-red-400 @endif">
                            @if(str_contains($movement->type, 'in'))+@else-@endif{{ $movement->quantity }}
                        </p>
                        <p class="text-xs text-gray-500">{{ $movement->createdBy->name ?? 'النظام' }}</p>
                    </div>
                </div>
                @endforeach
            </div>
            @else
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد حركات</h3>
                <p class="mt-1 text-sm text-gray-500">لم يتم تسجيل أي حركات مخزون مؤخراً</p>
            </div>
            @endif
        </div>
    </div>

    <!-- Top Categories by Value -->
    @if($topCategories->count() > 0)
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">أهم الفئات حسب القيمة</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            @foreach($topCategories as $category)
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div class="text-center">
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $category->name }}</p>
                    <p class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ number_format($category->parts_sum_stock_quantity ?? 0, 2) }}</p>
                    <p class="text-xs text-gray-500">ر.س</p>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات سريعة</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <a href="{{ route('parts.create') }}" class="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-blue-600 dark:text-blue-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <p class="text-sm font-medium text-blue-600 dark:text-blue-400">إضافة قطعة جديدة</p>
                </div>
            </a>

            <a href="{{ route('parts.low-stock') }}" class="flex items-center justify-center p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-yellow-600 dark:text-yellow-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <p class="text-sm font-medium text-yellow-600 dark:text-yellow-400">مخزون منخفض</p>
                </div>
            </a>

            <a href="{{ route('parts.out-of-stock') }}" class="flex items-center justify-center p-4 bg-red-50 dark:bg-red-900 rounded-lg hover:bg-red-100 dark:hover:bg-red-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-red-600 dark:text-red-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    <p class="text-sm font-medium text-red-600 dark:text-red-400">نفد المخزون</p>
                </div>
            </a>

            <a href="{{ route('parts.export') }}" class="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900 rounded-lg hover:bg-green-100 dark:hover:bg-green-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-green-600 dark:text-green-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-sm font-medium text-green-600 dark:text-green-400">تصدير التقرير</p>
                </div>
            </a>
        </div>
    </div>
</div>
@endsection
