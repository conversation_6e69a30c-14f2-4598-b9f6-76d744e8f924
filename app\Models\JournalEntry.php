<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class JournalEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'entry_number',
        'entry_date',
        'reference_type',
        'reference_id',
        'description',
        'total_debit',
        'total_credit',
        'status',
        'created_by',
        'posted_by',
        'posted_at',
        'reversed_by',
        'reversed_at',
        'reversal_reason',
        'metadata',
    ];

    protected $casts = [
        'entry_date' => 'date',
        'total_debit' => 'decimal:2',
        'total_credit' => 'decimal:2',
        'posted_at' => 'datetime',
        'reversed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the user who created the entry
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who posted the entry
     */
    public function poster(): BelongsTo
    {
        return $this->belongsTo(User::class, 'posted_by');
    }

    /**
     * Get the user who reversed the entry
     */
    public function reverser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reversed_by');
    }

    /**
     * Get the account transactions
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(AccountTransaction::class);
    }

    /**
     * Get the reference model (polymorphic)
     */
    public function reference(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope for posted entries
     */
    public function scopePosted($query)
    {
        return $query->where('status', 'posted');
    }

    /**
     * Scope for draft entries
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope for entries by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('entry_date', [$startDate, $endDate]);
    }

    /**
     * Generate entry number
     */
    public static function generateEntryNumber()
    {
        $year = date('Y');
        $lastEntry = static::where('entry_number', 'like', "JE{$year}%")
            ->orderBy('entry_number', 'desc')
            ->first();

        if ($lastEntry) {
            $lastNumber = intval(substr($lastEntry->entry_number, -6));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return "JE{$year}" . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Post the journal entry
     */
    public function post($userId)
    {
        if ($this->status !== 'draft') {
            throw new \Exception('يمكن ترحيل القيود في حالة المسودة فقط');
        }

        if (!$this->isBalanced()) {
            throw new \Exception('القيد غير متوازن - إجمالي المدين يجب أن يساوي إجمالي الدائن');
        }

        $this->update([
            'status' => 'posted',
            'posted_by' => $userId,
            'posted_at' => now(),
        ]);

        // Update account balances
        $this->updateAccountBalances();

        return true;
    }

    /**
     * Reverse the journal entry
     */
    public function reverse($userId, $reason)
    {
        if ($this->status !== 'posted') {
            throw new \Exception('يمكن عكس القيود المرحلة فقط');
        }

        // Create reversal entry
        $reversalEntry = static::create([
            'entry_number' => static::generateEntryNumber(),
            'entry_date' => now()->toDateString(),
            'reference_type' => 'reversal',
            'reference_id' => $this->id,
            'description' => 'عكس قيد رقم ' . $this->entry_number . ' - ' . $reason,
            'total_debit' => $this->total_credit,
            'total_credit' => $this->total_debit,
            'status' => 'posted',
            'created_by' => $userId,
            'posted_by' => $userId,
            'posted_at' => now(),
        ]);

        // Create reversal transactions
        foreach ($this->transactions as $transaction) {
            $reversalEntry->transactions()->create([
                'account_id' => $transaction->account_id,
                'debit_amount' => $transaction->credit_amount,
                'credit_amount' => $transaction->debit_amount,
                'description' => 'عكس: ' . $transaction->description,
            ]);
        }

        // Update original entry
        $this->update([
            'status' => 'reversed',
            'reversed_by' => $userId,
            'reversed_at' => now(),
            'reversal_reason' => $reason,
        ]);

        // Update account balances
        $reversalEntry->updateAccountBalances();

        return $reversalEntry;
    }

    /**
     * Check if entry is balanced
     */
    public function isBalanced()
    {
        return abs($this->total_debit - $this->total_credit) < 0.01;
    }

    /**
     * Get status label
     */
    public function getStatusLabel()
    {
        $statuses = [
            'draft' => 'مسودة',
            'posted' => 'مرحل',
            'reversed' => 'معكوس',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Get status color
     */
    public function getStatusColor()
    {
        $colors = [
            'draft' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
            'posted' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
            'reversed' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        ];

        return $colors[$this->status] ?? 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }

    /**
     * Update account balances
     */
    private function updateAccountBalances()
    {
        foreach ($this->transactions as $transaction) {
            $transaction->account->updateBalance();
        }
    }



    /**
     * Check if entry can be edited
     */
    public function canBeEdited()
    {
        return $this->status === 'draft';
    }

    /**
     * Check if entry can be posted
     */
    public function canBePosted()
    {
        return $this->status === 'draft' && $this->isBalanced();
    }

    /**
     * Check if entry can be reversed
     */
    public function canBeReversed()
    {
        return $this->status === 'posted';
    }
}
