@extends('layouts.main')

@section('title', 'تقارير الصيانة')

@section('content')
<div class="space-y-6" x-data="repairReports()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تقارير الصيانة</h1>
            <p class="text-gray-600 dark:text-gray-400">تحليل شامل لأداء الصيانة والإصلاح</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير التقرير
            </button>
            <button @click="printReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                طباعة
            </button>
        </div>
    </div>

    <!-- Report Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">فلاتر التقرير</h3>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع التقرير</label>
                <select x-model="reportType" @change="generateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="overview">نظرة عامة</option>
                    <option value="performance">تقرير الأداء</option>
                    <option value="technician">أداء الفنيين</option>
                    <option value="device_types">أنواع الأجهزة</option>
                    <option value="financial">التقرير المالي</option>
                    <option value="customer_satisfaction">رضا العملاء</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفترة الزمنية</label>
                <select x-model="timePeriod" @change="generateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="today">اليوم</option>
                    <option value="week">هذا الأسبوع</option>
                    <option value="month">هذا الشهر</option>
                    <option value="quarter">هذا الربع</option>
                    <option value="year">هذا العام</option>
                    <option value="custom">فترة مخصصة</option>
                </select>
            </div>
            <div x-show="timePeriod === 'custom'">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" x-model="dateFrom" @change="generateReport()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div x-show="timePeriod === 'custom'">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" x-model="dateTo" @change="generateReport()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفني</label>
                <select x-model="selectedTechnician" @change="generateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الفنيين</option>
                    <template x-for="tech in technicians" :key="tech.id">
                        <option :value="tech.id" x-text="tech.name"></option>
                    </template>
                </select>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="metrics.totalRepairs">156</p>
                    <p class="text-xs text-green-600 dark:text-green-400">+12% من الشهر الماضي</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">معدل الإنجاز</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="metrics.completionRate">94.2%</p>
                    <p class="text-xs text-green-600 dark:text-green-400">+2.1% من الشهر الماضي</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط وقت الإصلاح</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="metrics.avgRepairTime">3.2 يوم</p>
                    <p class="text-xs text-red-600 dark:text-red-400">+0.3 يوم من الشهر الماضي</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الإيرادات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">₪<span x-text="metrics.totalRevenue">45,230</span></p>
                    <p class="text-xs text-green-600 dark:text-green-400">+18% من الشهر الماضي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Repair Trends Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">اتجاهات الصيانة</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">رسم بياني لاتجاهات الصيانة</p>
                </div>
            </div>
        </div>

        <!-- Device Types Distribution -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">توزيع أنواع الأجهزة</h3>
            <div class="space-y-3">
                <template x-for="device in deviceDistribution" :key="device.type">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-900 dark:text-gray-100" x-text="device.name"></span>
                        <div class="flex items-center">
                            <div class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                                <div class="bg-blue-600 h-2 rounded-full" :style="`width: ${device.percentage}%`"></div>
                            </div>
                            <span class="text-sm text-gray-600 dark:text-gray-400" x-text="device.count"></span>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function repairReports() {
    return {
        reportType: 'overview',
        timePeriod: 'month',
        dateFrom: '',
        dateTo: '',
        selectedTechnician: '',
        technicians: [
            { id: 1, name: 'محمد الفني' },
            { id: 2, name: 'علي الفني' },
            { id: 3, name: 'أحمد الفني' }
        ],
        metrics: {
            totalRepairs: 156,
            completionRate: '94.2%',
            avgRepairTime: '3.2 يوم',
            totalRevenue: '45,230'
        },
        deviceDistribution: [
            { type: 'mobile', name: 'هواتف محمولة', count: 89, percentage: 57 },
            { type: 'laptop', name: 'لابتوب', count: 34, percentage: 22 },
            { type: 'desktop', name: 'حاسوب مكتبي', count: 21, percentage: 13 },
            { type: 'tablet', name: 'تابلت', count: 12, percentage: 8 }
        ],
        technicianPerformance: [
            {
                id: 1,
                name: 'محمد الفني',
                avatar: 'https://ui-avatars.com/api/?name=محمد+الفني&background=3B82F6&color=fff&size=32',
                completed_tasks: 45,
                avg_time: '2.8 يوم',
                success_rate: 96,
                rating: 4.8,
                revenue: '15,230'
            },
            {
                id: 2,
                name: 'علي الفني',
                avatar: 'https://ui-avatars.com/api/?name=علي+الفني&background=10B981&color=fff&size=32',
                completed_tasks: 38,
                avg_time: '3.1 يوم',
                success_rate: 94,
                rating: 4.6,
                revenue: '12,450'
            },
            {
                id: 3,
                name: 'أحمد الفني',
                avatar: 'https://ui-avatars.com/api/?name=أحمد+الفني&background=F59E0B&color=fff&size=32',
                completed_tasks: 29,
                avg_time: '3.5 يوم',
                success_rate: 91,
                rating: 4.4,
                revenue: '9,850'
            }
        ],
        financialSummary: {
            revenue: '45,230',
            costs: '12,450',
            profit: '32,780'
        },

        generateReport() {
            console.log('Generating report:', {
                type: this.reportType,
                period: this.timePeriod,
                technician: this.selectedTechnician
            });
        },

        exportReport() {
            alert('سيتم تصدير التقرير');
        },

        printReport() {
            window.print();
        }
    }
}
</script>
@endpush
@endsection
