<?php

namespace App\Services;

use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\AccountTransaction;
use App\Models\Sale;
use App\Models\Receivable;
use App\Models\Payable;
use Illuminate\Support\Facades\DB;
use Exception;

class AccountingService
{
    /**
     * Create journal entry for sale
     */
    public function createSaleEntry(Sale $sale)
    {
        DB::beginTransaction();
        
        try {
            $entry = JournalEntry::create([
                'entry_number' => JournalEntry::generateEntryNumber(),
                'entry_date' => $sale->sale_date,
                'reference_type' => 'sale',
                'reference_id' => $sale->id,
                'description' => 'قيد مبيعة رقم ' . $sale->sale_number,
                'created_by' => auth()->id() ?? $sale->user_id,
            ]);

            $totalAmount = $sale->total_amount;
            $taxAmount = $sale->tax_amount;
            $netAmount = $sale->subtotal;

            // Debit: Cash or Accounts Receivable
            if ($sale->payment_status === 'paid') {
                // Cash sale
                $cashAccount = Account::where('account_code', '11101')->first(); // الصندوق
                if ($cashAccount) {
                    $entry->transactions()->create([
                        'account_id' => $cashAccount->id,
                        'debit_amount' => $totalAmount,
                        'credit_amount' => 0,
                        'description' => 'تحصيل نقدي من مبيعة ' . $sale->sale_number,
                    ]);
                }
            } else {
                // Credit sale
                $receivableAccount = Account::where('account_code', '11201')->first(); // ذمم العملاء
                if ($receivableAccount) {
                    $entry->transactions()->create([
                        'account_id' => $receivableAccount->id,
                        'debit_amount' => $totalAmount,
                        'credit_amount' => 0,
                        'description' => 'ذمة عميل من مبيعة ' . $sale->sale_number,
                    ]);

                    // Create receivable record
                    $this->createReceivable($sale);
                }
            }

            // Credit: Sales Revenue
            $salesAccount = $this->getSalesAccount($sale->sale_type);
            if ($salesAccount) {
                $entry->transactions()->create([
                    'account_id' => $salesAccount->id,
                    'debit_amount' => 0,
                    'credit_amount' => $netAmount,
                    'description' => 'إيراد مبيعة ' . $sale->sale_number,
                ]);
            }

            // Credit: Tax Payable (if applicable)
            if ($taxAmount > 0) {
                $taxAccount = Account::where('account_code', '21300')->first(); // ضريبة القيمة المضافة
                if ($taxAccount) {
                    $entry->transactions()->create([
                        'account_id' => $taxAccount->id,
                        'debit_amount' => 0,
                        'credit_amount' => $taxAmount,
                        'description' => 'ضريبة قيمة مضافة على مبيعة ' . $sale->sale_number,
                    ]);
                }
            }

            // Update totals
            $entry->update([
                'total_debit' => $entry->transactions()->sum('debit_amount'),
                'total_credit' => $entry->transactions()->sum('credit_amount'),
            ]);

            // Post the entry
            $entry->post(auth()->id() ?? $sale->user_id);

            DB::commit();
            return $entry;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Create journal entry for payment received
     */
    public function createPaymentReceivedEntry($amount, $customerId, $paymentMethod = 'cash', $description = null)
    {
        DB::beginTransaction();
        
        try {
            $entry = JournalEntry::create([
                'entry_number' => JournalEntry::generateEntryNumber(),
                'entry_date' => now()->toDateString(),
                'reference_type' => 'payment_received',
                'reference_id' => $customerId,
                'description' => $description ?? 'تحصيل من عميل',
                'created_by' => auth()->id(),
            ]);

            // Debit: Cash/Bank
            $cashAccount = $this->getPaymentAccount($paymentMethod);
            if ($cashAccount) {
                $entry->transactions()->create([
                    'account_id' => $cashAccount->id,
                    'debit_amount' => $amount,
                    'credit_amount' => 0,
                    'description' => 'تحصيل من عميل',
                ]);
            }

            // Credit: Accounts Receivable
            $receivableAccount = Account::where('account_code', '11201')->first();
            if ($receivableAccount) {
                $entry->transactions()->create([
                    'account_id' => $receivableAccount->id,
                    'debit_amount' => 0,
                    'credit_amount' => $amount,
                    'description' => 'تسديد ذمة عميل',
                ]);
            }

            // Update totals and post
            $entry->update([
                'total_debit' => $entry->transactions()->sum('debit_amount'),
                'total_credit' => $entry->transactions()->sum('credit_amount'),
            ]);

            $entry->post(auth()->id());

            DB::commit();
            return $entry;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Create journal entry for inventory purchase
     */
    public function createPurchaseEntry($amount, $supplierId, $items = [], $paymentMethod = 'credit')
    {
        DB::beginTransaction();
        
        try {
            $entry = JournalEntry::create([
                'entry_number' => JournalEntry::generateEntryNumber(),
                'entry_date' => now()->toDateString(),
                'reference_type' => 'purchase',
                'reference_id' => $supplierId,
                'description' => 'قيد شراء مخزون',
                'created_by' => auth()->id(),
            ]);

            // Debit: Inventory
            $inventoryAccount = Account::where('account_code', '11301')->first(); // مخزون قطع الغيار
            if ($inventoryAccount) {
                $entry->transactions()->create([
                    'account_id' => $inventoryAccount->id,
                    'debit_amount' => $amount,
                    'credit_amount' => 0,
                    'description' => 'شراء مخزون',
                ]);
            }

            // Credit: Cash or Accounts Payable
            if ($paymentMethod === 'cash') {
                $cashAccount = Account::where('account_code', '11101')->first();
                if ($cashAccount) {
                    $entry->transactions()->create([
                        'account_id' => $cashAccount->id,
                        'debit_amount' => 0,
                        'credit_amount' => $amount,
                        'description' => 'دفع نقدي للمورد',
                    ]);
                }
            } else {
                $payableAccount = Account::where('account_code', '21101')->first(); // ذمم الموردين
                if ($payableAccount) {
                    $entry->transactions()->create([
                        'account_id' => $payableAccount->id,
                        'debit_amount' => 0,
                        'credit_amount' => $amount,
                        'description' => 'ذمة مورد',
                    ]);
                }
            }

            // Update totals and post
            $entry->update([
                'total_debit' => $entry->transactions()->sum('debit_amount'),
                'total_credit' => $entry->transactions()->sum('credit_amount'),
            ]);

            $entry->post(auth()->id());

            DB::commit();
            return $entry;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get sales account based on sale type
     */
    private function getSalesAccount($saleType)
    {
        $accountCodes = [
            'parts_only' => '41100',      // مبيعات قطع الغيار
            'accessories' => '41200',     // مبيعات الإكسسوارات
            'repair_service' => '42100',  // خدمات الإصلاح
            'mixed' => '41100',           // افتراضي لقطع الغيار
        ];

        $code = $accountCodes[$saleType] ?? '41100';
        return Account::where('account_code', $code)->first();
    }

    /**
     * Get payment account based on payment method
     */
    private function getPaymentAccount($paymentMethod)
    {
        $accountCodes = [
            'cash' => '11101',           // الصندوق
            'card' => '11102',           // البنك الأهلي
            'bank_transfer' => '11103',  // البنك الراجحي
        ];

        $code = $accountCodes[$paymentMethod] ?? '11101';
        return Account::where('account_code', $code)->first();
    }

    /**
     * Create receivable record
     */
    private function createReceivable(Sale $sale)
    {
        return Receivable::create([
            'customer_id' => $sale->customer_id,
            'sale_id' => $sale->id,
            'invoice_number' => $sale->sale_number,
            'invoice_date' => $sale->sale_date,
            'due_date' => $sale->sale_date->addDays(30), // 30 days default
            'original_amount' => $sale->total_amount,
            'paid_amount' => $sale->paid_amount,
            'remaining_amount' => $sale->remaining_amount,
            'status' => $sale->payment_status === 'paid' ? 'paid' : 'pending',
        ]);
    }

    /**
     * Get account balance
     */
    public function getAccountBalance($accountId, $startDate = null, $endDate = null)
    {
        $account = Account::findOrFail($accountId);
        return $account->getBalance($startDate, $endDate);
    }

    /**
     * Get trial balance
     */
    public function getTrialBalance($asOfDate = null)
    {
        $asOfDate = $asOfDate ?? now()->toDateString();
        
        $accounts = Account::with(['transactions' => function ($query) use ($asOfDate) {
            $query->whereHas('journalEntry', function ($q) use ($asOfDate) {
                $q->where('entry_date', '<=', $asOfDate)
                  ->where('status', 'posted');
            });
        }])->get();

        $trialBalance = [];
        $totalDebits = 0;
        $totalCredits = 0;

        foreach ($accounts as $account) {
            $balance = $account->getBalance(null, $asOfDate);
            
            if ($balance != 0) {
                $debitBalance = $account->balance_type === 'debit' && $balance > 0 ? $balance : 0;
                $creditBalance = $account->balance_type === 'credit' && $balance > 0 ? $balance : 0;
                
                // Handle negative balances
                if ($balance < 0) {
                    if ($account->balance_type === 'debit') {
                        $creditBalance = abs($balance);
                        $debitBalance = 0;
                    } else {
                        $debitBalance = abs($balance);
                        $creditBalance = 0;
                    }
                }

                $trialBalance[] = [
                    'account' => $account,
                    'debit_balance' => $debitBalance,
                    'credit_balance' => $creditBalance,
                ];

                $totalDebits += $debitBalance;
                $totalCredits += $creditBalance;
            }
        }

        return [
            'accounts' => $trialBalance,
            'total_debits' => $totalDebits,
            'total_credits' => $totalCredits,
            'is_balanced' => abs($totalDebits - $totalCredits) < 0.01,
        ];
    }
}
