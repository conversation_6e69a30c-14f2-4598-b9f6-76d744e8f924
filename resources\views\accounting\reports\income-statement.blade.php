@extends('layouts.main')

@section('title', 'قائمة الدخل')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">قائمة الدخل</h1>
            <p class="text-gray-600 dark:text-gray-400">الإيرادات والمصروفات من {{ $startDate }} إلى {{ $endDate }}</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <button onclick="printReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-print mr-2"></i>
                طباعة
            </button>
            
            <button onclick="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير
            </button>
            
            <a href="{{ route('accounting-reports.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" name="start_date" value="{{ $startDate }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" name="end_date" value="{{ $endDate }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                <i class="fas fa-search mr-2"></i>
                تحديث
            </button>
        </form>
    </div>

    <!-- Income Statement -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden" id="income-statement-report">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="text-center">
                <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100">قائمة الدخل</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">للفترة من {{ $startDate }} إلى {{ $endDate }}</p>
            </div>
        </div>
        
        <div class="p-6 space-y-6">
            <!-- Revenue Section -->
            <div>
                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                    الإيرادات
                </h3>
                
                <div class="space-y-3 mr-4">
                    @foreach($revenueAccounts as $revenue)
                        @php
                            // Calculate revenue balance for the period
                            $revenueTransactions = $revenue->transactions()
                                ->whereHas('journalEntry', function ($query) use ($startDate, $endDate) {
                                    $query->where('status', 'posted')
                                          ->whereBetween('entry_date', [$startDate, $endDate]);
                                })
                                ->get();

                            $totalCredits = $revenueTransactions->sum('credit_amount');
                            $totalDebits = $revenueTransactions->sum('debit_amount');
                            $revenueBalance = $totalCredits - $totalDebits; // Revenue accounts are credit type
                        @endphp
                        @if($revenueBalance > 0)
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 dark:text-gray-300">{{ $revenue->account_name }}</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ number_format($revenueBalance, 2) }}
                                </span>
                            </div>

                            @foreach($revenue->children as $child)
                                @php
                                    // Calculate child balance for the period
                                    $childTransactions = $child->transactions()
                                        ->whereHas('journalEntry', function ($query) use ($startDate, $endDate) {
                                            $query->where('status', 'posted')
                                                  ->whereBetween('entry_date', [$startDate, $endDate]);
                                        })
                                        ->get();

                                    $childCredits = $childTransactions->sum('credit_amount');
                                    $childDebits = $childTransactions->sum('debit_amount');
                                    $childBalance = $childCredits - $childDebits;
                                @endphp
                                @if($childBalance > 0)
                                    <div class="flex justify-between items-center mr-4">
                                        <span class="text-gray-600 dark:text-gray-400 text-sm">{{ $child->account_name }}</span>
                                        <span class="text-gray-800 dark:text-gray-200 text-sm">
                                            {{ number_format($childBalance, 2) }}
                                        </span>
                                    </div>
                                @endif
                            @endforeach
                        @endif
                    @endforeach
                </div>
                
                <div class="border-t border-gray-200 dark:border-gray-700 mt-4 pt-3">
                    <div class="flex justify-between items-center font-bold text-lg">
                        <span class="text-gray-900 dark:text-gray-100">إجمالي الإيرادات</span>
                        <span class="text-green-600 dark:text-green-400">{{ number_format($totalRevenue, 2) }} ريال</span>
                    </div>
                </div>
            </div>

            <!-- Expenses Section -->
            <div>
                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                    المصروفات
                </h3>
                
                <div class="space-y-3 mr-4">
                    @foreach($expenseAccounts as $expense)
                        @php
                            // Calculate expense balance for the period
                            $expenseTransactions = $expense->transactions()
                                ->whereHas('journalEntry', function ($query) use ($startDate, $endDate) {
                                    $query->where('status', 'posted')
                                          ->whereBetween('entry_date', [$startDate, $endDate]);
                                })
                                ->get();

                            $totalDebits = $expenseTransactions->sum('debit_amount');
                            $totalCredits = $expenseTransactions->sum('credit_amount');
                            $expenseBalance = $totalDebits - $totalCredits; // Expense accounts are debit type
                        @endphp
                        @if($expenseBalance > 0)
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 dark:text-gray-300">{{ $expense->account_name }}</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ number_format($expenseBalance, 2) }}
                                </span>
                            </div>

                            @foreach($expense->children as $child)
                                @php
                                    // Calculate child balance for the period
                                    $childTransactions = $child->transactions()
                                        ->whereHas('journalEntry', function ($query) use ($startDate, $endDate) {
                                            $query->where('status', 'posted')
                                                  ->whereBetween('entry_date', [$startDate, $endDate]);
                                        })
                                        ->get();

                                    $childDebits = $childTransactions->sum('debit_amount');
                                    $childCredits = $childTransactions->sum('credit_amount');
                                    $childBalance = $childDebits - $childCredits;
                                @endphp
                                @if($childBalance > 0)
                                    <div class="flex justify-between items-center mr-4">
                                        <span class="text-gray-600 dark:text-gray-400 text-sm">{{ $child->account_name }}</span>
                                        <span class="text-gray-800 dark:text-gray-200 text-sm">
                                            {{ number_format($childBalance, 2) }}
                                        </span>
                                    </div>
                                @endif
                            @endforeach
                        @endif
                    @endforeach
                </div>
                
                <div class="border-t border-gray-200 dark:border-gray-700 mt-4 pt-3">
                    <div class="flex justify-between items-center font-bold text-lg">
                        <span class="text-gray-900 dark:text-gray-100">إجمالي المصروفات</span>
                        <span class="text-red-600 dark:text-red-400">{{ number_format($totalExpenses, 2) }} ريال</span>
                    </div>
                </div>
            </div>

            <!-- Net Income -->
            <div class="border-t-2 border-gray-300 dark:border-gray-600 pt-4">
                <div class="flex justify-between items-center font-bold text-xl">
                    <span class="text-gray-900 dark:text-gray-100">صافي الدخل</span>
                    <span class="{{ $netIncome >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        {{ number_format($netIncome, 2) }} ريال
                    </span>
                </div>
                
                @if($netIncome >= 0)
                    <p class="text-sm text-green-600 dark:text-green-400 mt-2 text-center">
                        <i class="fas fa-arrow-up mr-1"></i>
                        الشركة حققت ربحاً خلال هذه الفترة
                    </p>
                @else
                    <p class="text-sm text-red-600 dark:text-red-400 mt-2 text-center">
                        <i class="fas fa-arrow-down mr-1"></i>
                        الشركة تكبدت خسارة خلال هذه الفترة
                    </p>
                @endif
            </div>

            <!-- Key Ratios -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3">النسب المالية الأساسية</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div class="text-center">
                        <div class="text-gray-600 dark:text-gray-400">هامش الربح الإجمالي</div>
                        <div class="font-bold text-gray-900 dark:text-gray-100">
                            {{ $totalRevenue > 0 ? number_format(($netIncome / $totalRevenue) * 100, 1) : 0 }}%
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-600 dark:text-gray-400">نسبة المصروفات</div>
                        <div class="font-bold text-gray-900 dark:text-gray-100">
                            {{ $totalRevenue > 0 ? number_format(($totalExpenses / $totalRevenue) * 100, 1) : 0 }}%
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-600 dark:text-gray-400">معدل النمو</div>
                        <div class="font-bold text-gray-900 dark:text-gray-100">
                            <span class="text-gray-500">قيد التطوير</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function printReport() {
    window.print();
}

function exportReport() {
    alert('ميزة التصدير قيد التطوير');
}

// Print styles
const printStyles = `
    @media print {
        body * {
            visibility: hidden;
        }
        #income-statement-report, #income-statement-report * {
            visibility: visible;
        }
        #income-statement-report {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none !important;
        }
    }
`;

// Add print styles to head
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
@endpush
@endsection
