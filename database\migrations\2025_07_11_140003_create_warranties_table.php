<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warranties', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->nullable()->constrained()->onDelete('set null');
            $table->string('warranty_number')->unique()->comment('رقم الضمان');
            $table->string('product_name')->comment('اسم المنتج');
            $table->string('brand')->nullable()->comment('الماركة');
            $table->string('model')->nullable()->comment('الموديل');
            $table->string('serial_number')->nullable()->comment('الرقم التسلسلي');
            $table->date('purchase_date')->comment('تاريخ الشراء');
            $table->date('warranty_start_date')->comment('تاريخ بداية الضمان');
            $table->date('warranty_end_date')->comment('تاريخ انتهاء الضمان');
            $table->integer('warranty_period_months')->comment('فترة الضمان بالأشهر');
            $table->enum('warranty_type', ['manufacturer', 'extended', 'service'])->default('manufacturer')->comment('نوع الضمان');
            $table->enum('status', ['active', 'expired', 'claimed', 'cancelled'])->default('active')->comment('حالة الضمان');
            $table->decimal('purchase_price', 10, 2)->nullable()->comment('سعر الشراء');
            $table->string('purchase_location')->nullable()->comment('مكان الشراء');
            $table->text('coverage_details')->nullable()->comment('تفاصيل التغطية');
            $table->text('terms_conditions')->nullable()->comment('الشروط والأحكام');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->json('attachments')->nullable()->comment('المرفقات (فواتير، صور، إلخ)');
            $table->timestamp('last_service_date')->nullable()->comment('تاريخ آخر خدمة');
            $table->integer('service_count')->default(0)->comment('عدد مرات الخدمة');
            $table->boolean('is_transferable')->default(false)->comment('قابل للنقل');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Indexes
            $table->index(['customer_id', 'status']);
            $table->index(['warranty_end_date', 'status']);
            $table->index(['brand', 'model']);
            $table->index('serial_number');
            $table->index('warranty_type');
            $table->index('purchase_date');
            $table->index('warranty_start_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warranties');
    }
};
