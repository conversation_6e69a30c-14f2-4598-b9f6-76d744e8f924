<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\SalePayment;
use App\Models\Customer;
use App\Models\Part;
use App\Models\Repair;
use App\Models\Location;
use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class POSController extends Controller
{
    /**
     * Display the POS interface
     */
    public function index()
    {
        $locations = Location::active()->get();

        // If no locations exist, create a default one
        if ($locations->isEmpty()) {
            Location::createDefaultLocation();
            $locations = Location::active()->get();
        }

        $currentLocation = session('pos_location_id')
            ? Location::find(session('pos_location_id'))
            : Location::getMainBranch();

        // If still no current location, use the first active one
        if (!$currentLocation) {
            $currentLocation = $locations->first();
        }

        $recentSales = Sale::with(['customer', 'items'])
            ->where('location_id', $currentLocation->id ?? null)
            ->latest()
            ->limit(10)
            ->get();

        $todaysStats = [
            'sales_count' => Sale::whereDate('sale_date', today())
                ->where('location_id', $currentLocation->id ?? null)
                ->count(),
            'revenue' => Sale::whereDate('sale_date', today())
                ->where('location_id', $currentLocation->id ?? null)
                ->where('status', 'completed')
                ->sum('total_amount'),
            'pending_sales' => Sale::where('status', 'pending')
                ->where('location_id', $currentLocation->id ?? null)
                ->count(),
        ];

        return view('pos.index', compact('locations', 'currentLocation', 'recentSales', 'todaysStats'));
    }

    /**
     * Create new sale
     */
    public function create()
    {
        $currentLocation = $this->getCurrentLocation();
        $customers = Customer::where('is_active', true)->orderBy('first_name')->get();
        $parts = Part::where('stock_quantity', '>', 0)->orderBy('name')->get();
        $repairs = Repair::whereIn('status', ['completed', 'ready_for_pickup'])
            ->with('customer')
            ->get();

        return view('pos.create', compact('currentLocation', 'customers', 'parts', 'repairs'));
    }

    /**
     * Store new sale
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'sale_type' => 'required|in:repair_service,parts_only,accessories,mixed',
            'items' => 'required|array|min:1',
            'items.*.type' => 'required|in:repair_service,part,accessory,labor,other',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'discount_type' => 'nullable|in:percentage,fixed',
            'discount_value' => 'nullable|numeric|min:0',
            'coupon_code' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $currentLocation = $this->getCurrentLocation();
            
            // Create sale
            $sale = Sale::create([
                'sale_number' => Sale::generateSaleNumber($currentLocation->code ?? null),
                'customer_id' => $request->customer_id,
                'user_id' => Auth::id(),
                'location_id' => $currentLocation->id,
                'sale_type' => $request->sale_type,
                'status' => 'pending',
                'payment_status' => 'pending',
                'sale_date' => now(),
                'discount_type' => $request->discount_type,
                'discount_value' => $request->discount_value,
                'coupon_code' => $request->coupon_code,
                'notes' => $request->notes,
            ]);

            // Add items
            foreach ($request->items as $itemData) {
                $item = new SaleItem([
                    'item_type' => $itemData['type'],
                    'item_name' => $itemData['name'],
                    'item_code' => $itemData['code'] ?? null,
                    'item_description' => $itemData['description'] ?? null,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'original_price' => $itemData['original_price'] ?? $itemData['unit_price'],
                    'part_id' => $itemData['part_id'] ?? null,
                    'repair_id' => $itemData['repair_id'] ?? null,
                    'tax_rate' => $currentLocation->getTaxRate(),
                ]);

                $item->calculateLineTotal();
                $sale->items()->save($item);

                // Update inventory if needed
                if ($item->affects_inventory && $item->part_id) {
                    $item->updateInventory();
                }
            }

            // Apply promotion if coupon code provided
            if ($request->coupon_code) {
                $promotion = Promotion::findByCode($request->coupon_code);
                if ($promotion && $promotion->isValid($sale->subtotal, $currentLocation->id)) {
                    $sale->promotion_id = $promotion->id;
                    $sale->save();
                }
            }

            // Calculate totals
            $sale->calculateTotals();

            DB::commit();

            return redirect()->route('pos.show', $sale)
                ->with('success', 'تم إنشاء المبيعة بنجاح.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء المبيعة: ' . $e->getMessage());
        }
    }

    /**
     * Display sale details
     */
    public function show(Sale $sale)
    {
        $sale->load(['customer', 'items.part', 'payments', 'location', 'user']);
        
        return view('pos.show', compact('sale'));
    }

    /**
     * Process payment for sale
     */
    public function processPayment(Request $request, Sale $sale)
    {
        $request->validate([
            'payment_method' => 'required|in:cash,card,bank_transfer,check,digital_wallet',
            'amount' => 'required|numeric|min:0.01',
            'reference_number' => 'nullable|string',
            'card_last_four' => 'nullable|string|size:4',
            'card_type' => 'nullable|string',
            'bank_name' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        if (!$sale->canBeModified()) {
            return redirect()->back()->with('error', 'لا يمكن تعديل هذه المبيعة.');
        }

        DB::beginTransaction();
        try {
            $paymentDetails = [
                'reference_number' => $request->reference_number,
                'card_last_four' => $request->card_last_four,
                'card_type' => $request->card_type,
                'bank_name' => $request->bank_name,
                'notes' => $request->notes,
            ];

            $payment = $sale->addPayment(
                $request->amount,
                $request->payment_method,
                $paymentDetails
            );

            DB::commit();

            return redirect()->route('pos.show', $sale)
                ->with('success', 'تم معالجة الدفع بنجاح.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء معالجة الدفع: ' . $e->getMessage());
        }
    }

    /**
     * Cancel sale
     */
    public function cancel(Request $request, Sale $sale)
    {
        if (!$sale->canBeCancelled()) {
            return redirect()->back()->with('error', 'لا يمكن إلغاء هذه المبيعة.');
        }

        DB::beginTransaction();
        try {
            // Restore inventory for all items
            foreach ($sale->items as $item) {
                if ($item->affects_inventory && $item->part_id) {
                    $item->restoreInventory();
                }
            }

            $sale->markAsCancelled($request->reason);

            DB::commit();

            return redirect()->route('pos.index')
                ->with('success', 'تم إلغاء المبيعة بنجاح.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إلغاء المبيعة: ' . $e->getMessage());
        }
    }

    /**
     * Search for products/parts
     */
    public function searchProducts(Request $request)
    {
        $query = $request->get('q');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $parts = Part::where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('part_number', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->where('stock_quantity', '>', 0)
            ->limit(20)
            ->get()
            ->map(function($part) {
                return [
                    'id' => $part->id,
                    'type' => 'part',
                    'name' => $part->name,
                    'code' => $part->part_number,
                    'description' => $part->description,
                    'price' => $part->price,
                    'stock' => $part->stock_quantity,
                    'category' => $part->category,
                ];
            });

        return response()->json($parts);
    }

    /**
     * Get customer details
     */
    public function getCustomer(Customer $customer)
    {
        $customer->load(['repairs' => function($query) {
            $query->latest()->limit(5);
        }]);

        return response()->json([
            'id' => $customer->id,
            'name' => $customer->full_name,
            'phone' => $customer->phone,
            'email' => $customer->email,
            'address' => $customer->address,
            'recent_repairs' => $customer->repairs->map(function($repair) {
                return [
                    'id' => $repair->id,
                    'repair_number' => $repair->repair_number,
                    'device' => $repair->device_brand . ' ' . $repair->device_model,
                    'status' => $repair->status_label,
                    'total_cost' => $repair->total_cost,
                ];
            }),
        ]);
    }

    /**
     * Set current location
     */
    public function setLocation(Request $request)
    {
        $request->validate([
            'location_id' => 'required|exists:locations,id',
        ]);

        $location = Location::find($request->location_id);
        
        if (!$location->canProcessSales()) {
            return response()->json([
                'success' => false,
                'message' => 'هذا الموقع غير مفعل للمبيعات.'
            ]);
        }

        session(['pos_location_id' => $request->location_id]);

        return response()->json([
            'success' => true,
            'message' => 'تم تغيير الموقع بنجاح.',
            'location' => [
                'id' => $location->id,
                'name' => $location->name,
                'code' => $location->code,
            ]
        ]);
    }



    /**
     * Get current location
     */
    private function getCurrentLocation()
    {
        $locationId = session('pos_location_id');

        if ($locationId) {
            $location = Location::find($locationId);
            if ($location && $location->canProcessSales()) {
                return $location;
            }
        }

        // Try to get main branch
        $mainBranch = Location::getMainBranch();
        if ($mainBranch) {
            return $mainBranch;
        }

        // If no main branch, create default location
        return Location::createDefaultLocation();
    }

    /**
     * Validate promotion code
     */
    public function validatePromotion(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
            'sale_amount' => 'required|numeric|min:0',
            'customer_id' => 'nullable|exists:customers,id',
        ]);

        $promotion = Promotion::findByCode($request->code);
        
        if (!$promotion) {
            return response()->json([
                'valid' => false,
                'message' => 'كود الخصم غير صحيح.'
            ]);
        }

        $currentLocation = $this->getCurrentLocation();
        
        if (!$promotion->isValid($request->sale_amount, $currentLocation->id)) {
            return response()->json([
                'valid' => false,
                'message' => 'كود الخصم غير صالح أو منتهي الصلاحية.'
            ]);
        }

        if ($request->customer_id && !$promotion->canBeUsedBy($request->customer_id)) {
            return response()->json([
                'valid' => false,
                'message' => 'تم استنفاد عدد مرات استخدام هذا الكود للعميل.'
            ]);
        }

        $discount = $promotion->calculateDiscount($request->sale_amount);

        return response()->json([
            'valid' => true,
            'promotion' => [
                'id' => $promotion->id,
                'name' => $promotion->name,
                'type' => $promotion->type,
                'discount_amount' => $discount,
                'description' => $promotion->description,
            ]
        ]);
    }
}
