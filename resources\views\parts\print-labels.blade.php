<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة ملصقات قطع الغيار</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .labels-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            max-width: 210mm;
            margin: 0 auto;
        }
        
        .label {
            width: 65mm;
            height: 38mm;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            background: white;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            page-break-inside: avoid;
            font-size: 10px;
            line-height: 1.2;
        }
        
        .label-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 4px;
        }
        
        .part-name {
            font-weight: bold;
            font-size: 11px;
            color: #2d3748;
            max-width: 70%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .part-number {
            font-size: 8px;
            color: #718096;
            background: #f7fafc;
            padding: 1px 4px;
            border-radius: 2px;
        }
        
        .label-body {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .part-details {
            margin: 2px 0;
        }
        
        .part-details div {
            margin: 1px 0;
            font-size: 9px;
            color: #4a5568;
        }
        
        .label-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 4px;
            padding-top: 4px;
            border-top: 1px solid #e2e8f0;
        }
        
        .barcode {
            font-family: 'Courier New', monospace;
            font-size: 8px;
            background: #000;
            color: white;
            padding: 1px 3px;
            border-radius: 2px;
            letter-spacing: 1px;
        }
        
        .price {
            font-weight: bold;
            color: #2b6cb0;
            font-size: 10px;
        }
        
        .stock-status {
            font-size: 8px;
            padding: 1px 4px;
            border-radius: 2px;
            font-weight: bold;
        }
        
        .stock-available {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .stock-low {
            background: #fef5e7;
            color: #744210;
        }
        
        .stock-out {
            background: #fed7d7;
            color: #742a2a;
        }
        
        .location {
            font-size: 8px;
            color: #805ad5;
            background: #f7fafc;
            padding: 1px 3px;
            border-radius: 2px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
                margin: 0;
            }
            
            .labels-container {
                gap: 5px;
            }
            
            .label {
                border: 1px solid #000;
                margin: 0;
            }
        }
        
        @page {
            size: A4;
            margin: 10mm;
        }
    </style>
</head>
<body>
    <div class="labels-container">
        @foreach($parts as $part)
        <div class="label">
            <div class="label-header">
                <div class="part-name" title="{{ $part->name }}">{{ $part->name }}</div>
                @if($part->part_number || $part->sku)
                    <div class="part-number">{{ $part->part_number ?? $part->sku }}</div>
                @endif
            </div>
            
            <div class="label-body">
                <div class="part-details">
                    @if($part->brand)
                        <div><strong>العلامة:</strong> {{ $part->brand }}</div>
                    @endif
                    @if($part->model)
                        <div><strong>الموديل:</strong> {{ $part->model }}</div>
                    @endif
                    @if($part->category)
                        <div><strong>الفئة:</strong> {{ $part->category->name }}</div>
                    @endif
                    <div><strong>المخزون:</strong> {{ $part->stock_quantity }}</div>
                </div>
                
                <div class="label-footer">
                    <div>
                        @if($part->barcode)
                            <div class="barcode">{{ $part->barcode }}</div>
                        @endif
                        @if($part->location || $part->shelf_location)
                            <div class="location">
                                {{ $part->location }}{{ $part->shelf_location ? ' - ' . $part->shelf_location : '' }}
                            </div>
                        @endif
                    </div>
                    
                    <div style="text-align: left;">
                        <div class="price">{{ number_format($part->selling_price, 2) }} ر.س</div>
                        <div class="stock-status 
                            @if($part->stock_quantity <= 0) stock-out
                            @elseif($part->stock_quantity <= ($part->reorder_point ?? 0)) stock-low
                            @else stock-available @endif">
                            @if($part->stock_quantity <= 0) نفد
                            @elseif($part->stock_quantity <= ($part->reorder_point ?? 0)) منخفض
                            @else متوفر @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            // Give time for styles to load
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
