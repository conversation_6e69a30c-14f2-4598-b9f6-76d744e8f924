<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Services\ReceiptService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class ReceiptController extends Controller
{
    protected $receiptService;

    public function __construct(ReceiptService $receiptService)
    {
        $this->receiptService = $receiptService;
    }

    /**
     * Display receipt
     */
    public function show(Request $request, Sale $sale)
    {
        $format = $request->get('format', 'standard');
        
        if (!$this->receiptService->validateFormat($format)) {
            $format = 'standard';
        }

        $html = $this->receiptService->generateReceiptHTML($sale, $format);
        
        return response($html)->header('Content-Type', 'text/html');
    }

    /**
     * Print receipt
     */
    public function print(Request $request, Sale $sale)
    {
        $format = $request->get('format', 'thermal');
        $printer = $request->get('printer');
        
        if (!$this->receiptService->validateFormat($format)) {
            $format = 'thermal';
        }

        $result = $this->receiptService->printReceipt($sale, $format, $printer);
        
        if ($request->expectsJson()) {
            return response()->json($result);
        }

        if ($result['success']) {
            return redirect()->back()->with('success', $result['message']);
        } else {
            return redirect()->back()->with('error', $result['message']);
        }
    }

    /**
     * Generate PDF receipt
     */
    public function pdf(Request $request, Sale $sale)
    {
        $format = $request->get('format', 'a4');
        
        if (!$this->receiptService->validateFormat($format)) {
            $format = 'a4';
        }

        try {
            $pdf = $this->receiptService->generateReceiptPDF($sale, $format);
            
            $filename = "receipt-{$sale->sale_number}.pdf";
            
            return $pdf->download($filename);

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إنشاء ملف PDF: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'حدث خطأ أثناء إنشاء ملف PDF');
        }
    }

    /**
     * Email receipt
     */
    public function email(Request $request, Sale $sale)
    {
        $request->validate([
            'email' => 'required|email',
            'format' => 'nullable|in:standard,thermal,a4',
        ]);

        $format = $request->get('format', 'a4');
        $email = $request->get('email');

        $result = $this->receiptService->emailReceipt($sale, $email, $format);

        if ($request->expectsJson()) {
            return response()->json($result);
        }

        if ($result['success']) {
            return redirect()->back()->with('success', $result['message']);
        } else {
            return redirect()->back()->with('error', $result['message']);
        }
    }

    /**
     * SMS receipt
     */
    public function sms(Request $request, Sale $sale)
    {
        $request->validate([
            'phone' => 'required|string',
        ]);

        $phone = $request->get('phone');

        $result = $this->receiptService->smsReceipt($sale, $phone);

        if ($request->expectsJson()) {
            return response()->json($result);
        }

        if ($result['success']) {
            return redirect()->back()->with('success', $result['message']);
        } else {
            return redirect()->back()->with('error', $result['message']);
        }
    }

    /**
     * Get receipt data as JSON
     */
    public function data(Sale $sale)
    {
        $data = $this->receiptService->getReceiptData($sale);
        
        return response()->json($data);
    }

    /**
     * Get available formats
     */
    public function formats()
    {
        $formats = $this->receiptService->getAvailableFormats();
        
        return response()->json($formats);
    }

    /**
     * Get receipt settings for location
     */
    public function settings(Request $request)
    {
        $locationId = $request->get('location_id');
        
        if (!$locationId) {
            return response()->json([
                'success' => false,
                'message' => 'معرف الموقع مطلوب'
            ], 400);
        }

        $location = \App\Models\Location::find($locationId);
        
        if (!$location) {
            return response()->json([
                'success' => false,
                'message' => 'الموقع غير موجود'
            ], 404);
        }

        $settings = $this->receiptService->getReceiptSettings($location);
        
        return response()->json([
            'success' => true,
            'settings' => $settings
        ]);
    }

    /**
     * Preview receipt
     */
    public function preview(Request $request, Sale $sale)
    {
        $format = $request->get('format', 'standard');
        
        if (!$this->receiptService->validateFormat($format)) {
            return response()->json([
                'success' => false,
                'message' => 'تنسيق الفاتورة غير صحيح'
            ], 400);
        }

        try {
            $html = $this->receiptService->generateReceiptHTML($sale, $format);
            
            return response()->json([
                'success' => true,
                'html' => $html,
                'format' => $format
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء معاينة الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk print receipts
     */
    public function bulkPrint(Request $request)
    {
        $request->validate([
            'sale_ids' => 'required|array',
            'sale_ids.*' => 'exists:sales,id',
            'format' => 'nullable|in:standard,thermal,a4',
            'printer' => 'nullable|string',
        ]);

        $saleIds = $request->get('sale_ids');
        $format = $request->get('format', 'thermal');
        $printer = $request->get('printer');

        $results = [];
        $successCount = 0;
        $errorCount = 0;

        foreach ($saleIds as $saleId) {
            try {
                $sale = Sale::findOrFail($saleId);
                $result = $this->receiptService->printReceipt($sale, $format, $printer);
                
                $results[] = [
                    'sale_id' => $saleId,
                    'sale_number' => $sale->sale_number,
                    'success' => $result['success'],
                    'message' => $result['message']
                ];

                if ($result['success']) {
                    $successCount++;
                } else {
                    $errorCount++;
                }

            } catch (\Exception $e) {
                $results[] = [
                    'sale_id' => $saleId,
                    'success' => false,
                    'message' => 'حدث خطأ: ' . $e->getMessage()
                ];
                $errorCount++;
            }
        }

        return response()->json([
            'success' => $errorCount === 0,
            'message' => "تم طباعة {$successCount} فاتورة بنجاح" . ($errorCount > 0 ? " و فشل في طباعة {$errorCount} فاتورة" : ""),
            'results' => $results,
            'summary' => [
                'total' => count($saleIds),
                'success' => $successCount,
                'errors' => $errorCount
            ]
        ]);
    }

    /**
     * Get QR code for receipt
     */
    public function qrCode(Sale $sale)
    {
        try {
            $qrCode = $this->receiptService->generateQRCode($sale);
            
            return response()->json([
                'success' => true,
                'qr_code' => $qrCode
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء رمز QR: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resend receipt
     */
    public function resend(Request $request, Sale $sale)
    {
        $request->validate([
            'method' => 'required|in:email,sms,print',
            'email' => 'required_if:method,email|email',
            'phone' => 'required_if:method,sms|string',
            'format' => 'nullable|in:standard,thermal,a4',
            'printer' => 'nullable|string',
        ]);

        $method = $request->get('method');
        $format = $request->get('format', 'a4');

        try {
            switch ($method) {
                case 'email':
                    $result = $this->receiptService->emailReceipt($sale, $request->get('email'), $format);
                    break;

                case 'sms':
                    $result = $this->receiptService->smsReceipt($sale, $request->get('phone'));
                    break;

                case 'print':
                    $result = $this->receiptService->printReceipt($sale, $format, $request->get('printer'));
                    break;

                default:
                    $result = [
                        'success' => false,
                        'message' => 'طريقة الإرسال غير صحيحة'
                    ];
            }

            if ($request->expectsJson()) {
                return response()->json($result);
            }

            if ($result['success']) {
                return redirect()->back()->with('success', $result['message']);
            } else {
                return redirect()->back()->with('error', $result['message']);
            }

        } catch (\Exception $e) {
            $errorMessage = 'حدث خطأ أثناء إعادة إرسال الفاتورة: ' . $e->getMessage();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $errorMessage
                ], 500);
            }

            return redirect()->back()->with('error', $errorMessage);
        }
    }
}
