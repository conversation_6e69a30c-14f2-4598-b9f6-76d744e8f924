<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sale_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sale_id')->constrained()->onDelete('cascade');
            $table->string('payment_number')->unique();
            
            // Payment details
            $table->enum('payment_method', ['cash', 'card', 'bank_transfer', 'check', 'digital_wallet', 'installment']);
            $table->decimal('amount', 10, 2);
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            
            // Payment method specific information
            $table->string('reference_number')->nullable(); // Transaction ID, check number, etc.
            $table->string('card_last_four')->nullable(); // Last 4 digits of card
            $table->string('card_type')->nullable(); // Visa, Mastercard, etc.
            $table->string('bank_name')->nullable();
            $table->string('account_number')->nullable();
            
            // Installment information
            $table->integer('installment_number')->nullable(); // Which installment (1, 2, 3...)
            $table->integer('total_installments')->nullable(); // Total number of installments
            $table->date('due_date')->nullable(); // For installments and checks
            
            // Processing information
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('processed_at')->nullable();
            $table->text('processing_notes')->nullable();
            
            // Refund information
            $table->decimal('refunded_amount', 10, 2)->default(0);
            $table->timestamp('refunded_at')->nullable();
            $table->foreignId('refunded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('refund_reason')->nullable();
            
            // Additional information
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Store additional payment gateway data
            
            $table->timestamps();
            
            // Indexes
            $table->index(['sale_id', 'status']);
            $table->index(['payment_method', 'status']);
            $table->index('payment_number');
            $table->index('reference_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sale_payments');
    }
};
