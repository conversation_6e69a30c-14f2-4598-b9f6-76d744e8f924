/**
 * الميزات التفاعلية المتقدمة
 * Advanced Interactive Features
 */

class AdvancedFeatures {
    constructor() {
        this.shortcuts = new Map();
        this.dragDropManager = null;
        this.contextMenus = new Map();
        this.tooltips = new Map();
        this.init();
    }

    init() {
        this.setupKeyboardShortcuts();
        this.setupDragAndDrop();
        this.setupContextMenus();
        this.setupTooltips();
        this.setupAdvancedSearch();
        this.setupDataVisualization();
        this.setupRealTimeUpdates();
        this.setupAdvancedFilters();
    }

    // اختصارات لوحة المفاتيح
    setupKeyboardShortcuts() {
        const shortcuts = {
            'ctrl+s': () => this.saveData(),
            'ctrl+n': () => this.createNew(),
            'ctrl+f': () => this.focusSearch(),
            'ctrl+e': () => this.exportData(),
            'ctrl+r': () => this.refreshData(),
            'escape': () => this.closeModals(),
            'ctrl+shift+d': () => this.toggleDarkMode(),
            'ctrl+shift+l': () => this.toggleLanguage(),
            'f11': () => this.toggleFullscreen(),
            'ctrl+z': () => this.undo(),
            'ctrl+y': () => this.redo()
        };

        document.addEventListener('keydown', (e) => {
            const key = this.getShortcutKey(e);
            if (shortcuts[key]) {
                e.preventDefault();
                shortcuts[key]();
            }
        });

        this.shortcuts = new Map(Object.entries(shortcuts));
    }

    getShortcutKey(e) {
        const parts = [];
        if (e.ctrlKey) parts.push('ctrl');
        if (e.shiftKey) parts.push('shift');
        if (e.altKey) parts.push('alt');
        parts.push(e.key.toLowerCase());
        return parts.join('+');
    }

    // السحب والإفلات
    setupDragAndDrop() {
        this.dragDropManager = new DragDropManager();
        
        // تفعيل السحب والإفلات للعناصر القابلة للسحب
        document.querySelectorAll('[draggable="true"]').forEach(element => {
            this.dragDropManager.makeDraggable(element);
        });

        // تفعيل مناطق الإفلات
        document.querySelectorAll('.drop-zone').forEach(zone => {
            this.dragDropManager.makeDroppable(zone);
        });
    }

    // القوائم السياقية
    setupContextMenus() {
        document.addEventListener('contextmenu', (e) => {
            const target = e.target.closest('[data-context-menu]');
            if (target) {
                e.preventDefault();
                this.showContextMenu(target, e.clientX, e.clientY);
            }
        });

        // إخفاء القائمة عند النقر في مكان آخر
        document.addEventListener('click', () => {
            this.hideAllContextMenus();
        });
    }

    showContextMenu(element, x, y) {
        const menuType = element.dataset.contextMenu;
        const menu = this.createContextMenu(menuType, element);
        
        menu.style.position = 'fixed';
        menu.style.left = x + 'px';
        menu.style.top = y + 'px';
        menu.style.zIndex = '9999';
        
        document.body.appendChild(menu);
        this.contextMenus.set(element, menu);
    }

    createContextMenu(type, element) {
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        
        const menus = {
            'table-row': [
                { text: 'عرض', icon: 'eye', action: () => this.viewItem(element) },
                { text: 'تعديل', icon: 'edit', action: () => this.editItem(element) },
                { text: 'حذف', icon: 'trash', action: () => this.deleteItem(element) },
                { text: 'نسخ', icon: 'copy', action: () => this.copyItem(element) }
            ],
            'card': [
                { text: 'تكبير', icon: 'expand', action: () => this.expandCard(element) },
                { text: 'تصغير', icon: 'compress', action: () => this.collapseCard(element) },
                { text: 'تصدير', icon: 'download', action: () => this.exportCard(element) }
            ]
        };

        const menuItems = menus[type] || [];
        menuItems.forEach(item => {
            const menuItem = document.createElement('div');
            menuItem.className = 'context-menu-item';
            menuItem.innerHTML = `
                <i class="icon-${item.icon}"></i>
                <span>${item.text}</span>
            `;
            menuItem.addEventListener('click', item.action);
            menu.appendChild(menuItem);
        });

        return menu;
    }

    hideAllContextMenus() {
        this.contextMenus.forEach(menu => {
            if (menu.parentNode) {
                menu.parentNode.removeChild(menu);
            }
        });
        this.contextMenus.clear();
    }

    // التلميحات المتقدمة
    setupTooltips() {
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            this.createTooltip(element);
        });
    }

    createTooltip(element) {
        const tooltip = document.createElement('div');
        tooltip.className = 'advanced-tooltip';
        tooltip.innerHTML = element.dataset.tooltip;
        
        element.addEventListener('mouseenter', () => {
            this.showTooltip(element, tooltip);
        });

        element.addEventListener('mouseleave', () => {
            this.hideTooltip(tooltip);
        });

        this.tooltips.set(element, tooltip);
    }

    showTooltip(element, tooltip) {
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.position = 'fixed';
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        tooltip.style.opacity = '1';
        tooltip.style.transform = 'translateY(0)';
    }

    hideTooltip(tooltip) {
        if (tooltip.parentNode) {
            tooltip.style.opacity = '0';
            tooltip.style.transform = 'translateY(10px)';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 200);
        }
    }

    // البحث المتقدم
    setupAdvancedSearch() {
        const searchInputs = document.querySelectorAll('.advanced-search');
        searchInputs.forEach(input => {
            this.enhanceSearchInput(input);
        });
    }

    enhanceSearchInput(input) {
        // إضافة اقتراحات البحث
        const suggestions = document.createElement('div');
        suggestions.className = 'search-suggestions';
        input.parentNode.appendChild(suggestions);

        input.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query.length >= 2) {
                this.showSearchSuggestions(input, suggestions, query);
            } else {
                this.hideSearchSuggestions(suggestions);
            }
        });

        // إضافة فلاتر البحث المتقدمة
        this.addAdvancedSearchFilters(input);
    }

    showSearchSuggestions(input, container, query) {
        // محاكاة الحصول على اقتراحات
        const suggestions = this.getSearchSuggestions(query);
        
        container.innerHTML = '';
        suggestions.forEach(suggestion => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.textContent = suggestion;
            item.addEventListener('click', () => {
                input.value = suggestion;
                this.hideSearchSuggestions(container);
                this.performSearch(suggestion);
            });
            container.appendChild(item);
        });

        container.style.display = 'block';
    }

    hideSearchSuggestions(container) {
        container.style.display = 'none';
    }

    getSearchSuggestions(query) {
        // محاكاة اقتراحات البحث
        const allSuggestions = [
            'iPhone 13', 'Samsung Galaxy', 'شاشة مكسورة', 'بطارية تالفة',
            'مشكلة شحن', 'مشكلة صوت', 'مشكلة واي فاي', 'تحديث نظام'
        ];
        
        return allSuggestions.filter(suggestion => 
            suggestion.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5);
    }

    addAdvancedSearchFilters(input) {
        const filtersButton = document.createElement('button');
        filtersButton.className = 'search-filters-btn';
        filtersButton.innerHTML = '<i class="icon-filter"></i>';
        filtersButton.addEventListener('click', () => {
            this.showAdvancedFilters(input);
        });
        
        input.parentNode.appendChild(filtersButton);
    }

    // تصور البيانات المتقدم
    setupDataVisualization() {
        document.querySelectorAll('.data-chart').forEach(chart => {
            this.createInteractiveChart(chart);
        });
    }

    createInteractiveChart(container) {
        // محاكاة إنشاء رسم بياني تفاعلي
        const chartData = this.generateChartData();
        const chart = new InteractiveChart(container, chartData);
        chart.render();
    }

    generateChartData() {
        // محاكاة بيانات الرسم البياني
        return {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'المبيعات',
                data: [12, 19, 3, 5, 2, 3],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };
    }

    // التحديثات الفورية
    setupRealTimeUpdates() {
        if ('WebSocket' in window) {
            this.setupWebSocketConnection();
        } else {
            // استخدام polling كبديل
            this.setupPolling();
        }
    }

    setupWebSocketConnection() {
        // محاكاة اتصال WebSocket
        console.log('Setting up WebSocket connection...');
        // const ws = new WebSocket('ws://localhost:8080');
        // ws.onmessage = (event) => {
        //     const data = JSON.parse(event.data);
        //     this.handleRealTimeUpdate(data);
        // };
    }

    setupPolling() {
        // تحديث البيانات كل 30 ثانية
        setInterval(() => {
            this.checkForUpdates();
        }, 30000);
    }

    checkForUpdates() {
        // محاكاة فحص التحديثات
        console.log('Checking for updates...');
    }

    handleRealTimeUpdate(data) {
        // معالجة التحديثات الفورية
        if (data.type === 'notification') {
            this.showRealTimeNotification(data.message);
        } else if (data.type === 'data_update') {
            this.updatePageData(data.data);
        }
    }

    showRealTimeNotification(message) {
        if (window.showNotification) {
            window.showNotification(message, 'info');
        }
    }

    updatePageData(data) {
        // تحديث بيانات الصفحة
        console.log('Updating page data:', data);
    }

    // الفلاتر المتقدمة
    setupAdvancedFilters() {
        document.querySelectorAll('.advanced-filter').forEach(filter => {
            this.enhanceFilter(filter);
        });
    }

    enhanceFilter(filter) {
        // إضافة خيارات فلترة متقدمة
        const options = filter.querySelectorAll('option');
        const searchBox = document.createElement('input');
        searchBox.type = 'text';
        searchBox.placeholder = 'البحث في الخيارات...';
        searchBox.className = 'filter-search';
        
        filter.parentNode.insertBefore(searchBox, filter);
        
        searchBox.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase();
            options.forEach(option => {
                const text = option.textContent.toLowerCase();
                option.style.display = text.includes(query) ? 'block' : 'none';
            });
        });
    }

    // تنفيذ الإجراءات
    saveData() {
        if (window.showNotification) {
            window.showNotification('تم حفظ البيانات', 'success');
        }
    }

    createNew() {
        if (window.showNotification) {
            window.showNotification('إنشاء عنصر جديد', 'info');
        }
    }

    focusSearch() {
        const searchInput = document.querySelector('.search-input, input[type="search"]');
        if (searchInput) {
            searchInput.focus();
        }
    }

    exportData() {
        if (window.showNotification) {
            window.showNotification('جاري تصدير البيانات...', 'info');
        }
    }

    refreshData() {
        if (window.showNotification) {
            window.showNotification('تم تحديث البيانات', 'success');
        }
    }

    closeModals() {
        document.querySelectorAll('.modal.show').forEach(modal => {
            modal.classList.remove('show');
        });
    }

    toggleDarkMode() {
        document.documentElement.classList.toggle('dark');
        const isDark = document.documentElement.classList.contains('dark');
        localStorage.setItem('theme', isDark ? 'dark' : 'light');
    }

    toggleLanguage() {
        const currentLang = document.documentElement.lang;
        const newLang = currentLang === 'ar' ? 'en' : 'ar';
        document.documentElement.lang = newLang;
        localStorage.setItem('language', newLang);
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    undo() {
        if (window.showNotification) {
            window.showNotification('تراجع عن آخر إجراء', 'info');
        }
    }

    redo() {
        if (window.showNotification) {
            window.showNotification('إعادة آخر إجراء', 'info');
        }
    }

    // إجراءات القائمة السياقية
    viewItem(element) {
        console.log('Viewing item:', element);
    }

    editItem(element) {
        console.log('Editing item:', element);
    }

    deleteItem(element) {
        if (confirm('هل أنت متأكد من الحذف؟')) {
            element.remove();
        }
    }

    copyItem(element) {
        console.log('Copying item:', element);
    }

    expandCard(element) {
        element.classList.add('expanded');
    }

    collapseCard(element) {
        element.classList.remove('expanded');
    }

    exportCard(element) {
        console.log('Exporting card:', element);
    }

    performSearch(query) {
        console.log('Performing search:', query);
    }

    showAdvancedFilters(input) {
        console.log('Showing advanced filters for:', input);
    }
}

// فئة السحب والإفلات
class DragDropManager {
    constructor() {
        this.draggedElement = null;
        this.dropZones = new Set();
    }

    makeDraggable(element) {
        element.addEventListener('dragstart', (e) => {
            this.draggedElement = element;
            element.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
        });

        element.addEventListener('dragend', () => {
            element.classList.remove('dragging');
            this.draggedElement = null;
        });
    }

    makeDroppable(zone) {
        this.dropZones.add(zone);

        zone.addEventListener('dragover', (e) => {
            e.preventDefault();
            zone.classList.add('drag-over');
        });

        zone.addEventListener('dragleave', () => {
            zone.classList.remove('drag-over');
        });

        zone.addEventListener('drop', (e) => {
            e.preventDefault();
            zone.classList.remove('drag-over');
            
            if (this.draggedElement) {
                this.handleDrop(this.draggedElement, zone);
            }
        });
    }

    handleDrop(element, zone) {
        // معالجة الإفلات
        console.log('Dropped element:', element, 'into zone:', zone);
        
        // يمكن إضافة منطق مخصص هنا
        if (zone.dataset.dropAction) {
            this.executeDropAction(zone.dataset.dropAction, element, zone);
        }
    }

    executeDropAction(action, element, zone) {
        const actions = {
            'move': () => zone.appendChild(element),
            'copy': () => zone.appendChild(element.cloneNode(true)),
            'delete': () => element.remove()
        };

        if (actions[action]) {
            actions[action]();
        }
    }
}

// فئة الرسم البياني التفاعلي
class InteractiveChart {
    constructor(container, data) {
        this.container = container;
        this.data = data;
    }

    render() {
        // محاكاة رسم الرسم البياني
        this.container.innerHTML = `
            <div class="chart-placeholder">
                <p>رسم بياني تفاعلي</p>
                <p>البيانات: ${this.data.datasets[0].data.join(', ')}</p>
            </div>
        `;
    }
}

// تهيئة الميزات المتقدمة
document.addEventListener('DOMContentLoaded', () => {
    window.advancedFeatures = new AdvancedFeatures();
});

// تصدير للاستخدام العام
window.AdvancedFeatures = AdvancedFeatures;
