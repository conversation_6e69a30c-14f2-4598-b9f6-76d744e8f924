-- إن<PERSON><PERSON>ء جداول نظام نقطة البيع
-- يمكن تشغيل هذا الملف مباشرة في phpMyAdmin أو MySQL

-- تحديث جدول المواقع
ALTER TABLE `locations` 
ADD COLUMN `is_main_branch` BOOLEAN DEFAULT FALSE AFTER `is_default`,
ADD COLUMN `description` TEXT NULL AFTER `name_en`,
ADD COLUMN `state` VARCHAR(255) NULL AFTER `city`,
ADD COLUMN `manager_name` VARCHAR(255) NULL AFTER `manager_id`,
ADD COLUMN `business_hours` JSON NULL AFTER `inventory_settings`,
ADD COLUMN `services_offered` JSON NULL AFTER `business_hours`,
ADD COLUMN `receipt_header` TEXT NULL AFTER `services_offered`,
ADD COLUMN `receipt_footer` TEXT NULL AFTER `receipt_header`,
ADD COLUMN `tax_number` VARCHAR(255) NULL AFTER `receipt_footer`;

-- إ<PERSON><PERSON><PERSON><PERSON> جدول المبيعات
CREATE TABLE IF NOT EXISTS `sales` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `sale_number` varchar(255) NOT NULL,
  `customer_id` bigint(20) UNSIGNED NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `location_id` bigint(20) UNSIGNED NOT NULL,
  `sale_type` enum('repair_service','parts_only','accessories','mixed') NOT NULL DEFAULT 'parts_only',
  `status` enum('pending','processing','completed','cancelled','refunded') NOT NULL DEFAULT 'pending',
  `payment_status` enum('pending','partial','paid','refunded','failed') NOT NULL DEFAULT 'pending',
  `sale_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL,
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `paid_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `change_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `remaining_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `notes` text NULL,
  `metadata` json NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sales_sale_number_unique` (`sale_number`),
  KEY `sales_customer_id_foreign` (`customer_id`),
  KEY `sales_user_id_foreign` (`user_id`),
  KEY `sales_location_id_foreign` (`location_id`),
  KEY `sales_sale_date_index` (`sale_date`),
  KEY `sales_status_index` (`status`),
  KEY `sales_payment_status_index` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول عناصر المبيعة
CREATE TABLE IF NOT EXISTS `sale_items` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `sale_id` bigint(20) UNSIGNED NOT NULL,
  `item_type` enum('repair_service','part','accessory','labor','other') NOT NULL,
  `repair_id` bigint(20) UNSIGNED NULL,
  `part_id` bigint(20) UNSIGNED NULL,
  `item_name` varchar(255) NOT NULL,
  `item_code` varchar(255) NULL,
  `item_description` text NULL,
  `item_category` varchar(255) NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `original_price` decimal(10,2) NOT NULL,
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `line_total` decimal(10,2) NOT NULL,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `affects_inventory` boolean NOT NULL DEFAULT TRUE,
  `inventory_movements` json NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sale_items_sale_id_foreign` (`sale_id`),
  KEY `sale_items_repair_id_foreign` (`repair_id`),
  KEY `sale_items_part_id_foreign` (`part_id`),
  KEY `sale_items_item_type_index` (`item_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول مدفوعات المبيعة
CREATE TABLE IF NOT EXISTS `sale_payments` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `sale_id` bigint(20) UNSIGNED NOT NULL,
  `payment_number` varchar(255) NOT NULL,
  `payment_method` enum('cash','card','bank_transfer','check','digital_wallet','installment') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','processing','completed','failed','cancelled','refunded') NOT NULL DEFAULT 'pending',
  `processed_by` bigint(20) UNSIGNED NULL,
  `processed_at` timestamp NULL,
  `reference_number` varchar(255) NULL,
  `card_last_four` varchar(4) NULL,
  `card_type` enum('visa','mastercard','amex','mada','other') NULL,
  `bank_name` varchar(255) NULL,
  `account_number` varchar(255) NULL,
  `installment_details` json NULL,
  `refund_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `refund_reason` text NULL,
  `refunded_by` bigint(20) UNSIGNED NULL,
  `refunded_at` timestamp NULL,
  `notes` text NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sale_payments_payment_number_unique` (`payment_number`),
  KEY `sale_payments_sale_id_foreign` (`sale_id`),
  KEY `sale_payments_processed_by_foreign` (`processed_by`),
  KEY `sale_payments_refunded_by_foreign` (`refunded_by`),
  KEY `sale_payments_payment_method_index` (`payment_method`),
  KEY `sale_payments_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول العروض والخصومات
CREATE TABLE IF NOT EXISTS `promotions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `description` text NULL,
  `type` enum('percentage','fixed_amount','buy_x_get_y','free_shipping') NOT NULL,
  `value` decimal(10,2) NOT NULL,
  `max_discount` decimal(10,2) NULL,
  `min_purchase` decimal(10,2) NULL,
  `buy_quantity` int(11) NULL,
  `get_quantity` int(11) NULL,
  `is_active` boolean NOT NULL DEFAULT TRUE,
  `start_date` timestamp NULL,
  `end_date` timestamp NULL,
  `usage_limit` int(11) NULL,
  `usage_limit_per_customer` int(11) NULL,
  `used_count` int(11) NOT NULL DEFAULT 0,
  `applicable_items` json NULL,
  `applicable_categories` json NULL,
  `applicable_customers` json NULL,
  `applicable_locations` json NULL,
  `conditions` json NULL,
  `created_by` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `promotions_code_unique` (`code`),
  KEY `promotions_created_by_foreign` (`created_by`),
  KEY `promotions_is_active_index` (`is_active`),
  KEY `promotions_start_date_index` (`start_date`),
  KEY `promotions_end_date_index` (`end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة المفاتيح الخارجية
ALTER TABLE `sales`
  ADD CONSTRAINT `sales_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `sales_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `sales_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`) ON DELETE CASCADE;

ALTER TABLE `sale_items`
  ADD CONSTRAINT `sale_items_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `sale_items_repair_id_foreign` FOREIGN KEY (`repair_id`) REFERENCES `repairs` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `sale_items_part_id_foreign` FOREIGN KEY (`part_id`) REFERENCES `parts` (`id`) ON DELETE SET NULL;

ALTER TABLE `sale_payments`
  ADD CONSTRAINT `sale_payments_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `sale_payments_processed_by_foreign` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `sale_payments_refunded_by_foreign` FOREIGN KEY (`refunded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `promotions`
  ADD CONSTRAINT `promotions_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- إدراج موقع افتراضي
INSERT INTO `locations` (
    `name`, `code`, `type`, `description`, `email`, `phone`, `address`, 
    `city`, `state`, `country`, `postal_code`, `is_active`, `is_default`, 
    `is_main_branch`, `pos_settings`, `receipt_header`, `receipt_footer`, 
    `tax_number`, `created_at`, `updated_at`
) VALUES (
    'الفرع الرئيسي', 
    'MAIN', 
    'store', 
    'الفرع الرئيسي لمركز الصيانة',
    '<EMAIL>',
    '+************',
    'شارع الملك فهد، الرياض',
    'الرياض',
    'الرياض',
    'المملكة العربية السعودية',
    '12345',
    1,
    1,
    1,
    '{"allow_sales": true, "tax_rate": 15, "payment_methods": ["cash", "card", "bank_transfer"], "auto_print_receipt": true, "require_customer_info": false}',
    'مركز الصيانة المتقدم',
    'شكراً لثقتكم - نتطلع لخدمتكم مرة أخرى',
    '***************',
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE `updated_at` = NOW();

-- إضافة عملاء تجريبيين
INSERT INTO `customers` (
    `customer_number`, `type`, `first_name`, `last_name`, `company_name`,
    `email`, `phone`, `mobile`, `address`, `city`, `country`,
    `is_active`, `created_by`, `created_at`, `updated_at`
) VALUES
(
    'CUST-001', 'individual', 'أحمد', 'محمد علي', NULL,
    '<EMAIL>', '**********', '**********',
    'شارع الملك فهد، الرياض', 'الرياض', 'المملكة العربية السعودية',
    1, 1, NOW(), NOW()
),
(
    'CUST-002', 'individual', 'سارة', 'أحمد خالد', NULL,
    '<EMAIL>', '**********', '**********',
    'شارع العليا، الرياض', 'الرياض', 'المملكة العربية السعودية',
    1, 1, NOW(), NOW()
),
(
    'CUST-003', 'company', NULL, NULL, 'شركة التقنية المتقدمة',
    '<EMAIL>', '0596543210', '0596543210',
    'طريق الملك عبدالعزيز، الدمام', 'الدمام', 'المملكة العربية السعودية',
    1, 1, NOW(), NOW()
);

-- إضافة قطع غيار تجريبية
INSERT INTO `parts` (
    `part_number`, `name`, `description`, `category`, `brand`, `model`,
    `price`, `cost_price`, `stock_quantity`, `min_stock_level`, `max_stock_level`,
    `unit`, `location`, `is_active`, `created_at`, `updated_at`
) VALUES
(
    'PART-001', 'شاشة iPhone 13', 'شاشة أصلية لجهاز iPhone 13',
    'screens', 'Apple', 'iPhone 13', 450.00, 350.00, 25, 5, 100,
    'piece', 'A1-01', 1, NOW(), NOW()
),
(
    'PART-002', 'بطارية iPhone 12', 'بطارية أصلية لجهاز iPhone 12',
    'batteries', 'Apple', 'iPhone 12', 120.00, 80.00, 40, 10, 100,
    'piece', 'B2-05', 1, NOW(), NOW()
),
(
    'ACC-001', 'كابل USB-C', 'كابل شحن USB-C عالي الجودة',
    'cables', 'Generic', 'Universal', 25.00, 15.00, 100, 20, 200,
    'piece', 'C3-10', 1, NOW(), NOW()
),
(
    'ACC-002', 'شاحن سريع 20W', 'شاحن سريع 20 واط متوافق مع iPhone',
    'chargers', 'Apple', 'Universal', 85.00, 60.00, 30, 10, 100,
    'piece', 'C3-15', 1, NOW(), NOW()
);

-- إضافة بعض البيانات التجريبية للمبيعات
INSERT INTO `sales` (
    `sale_number`, `user_id`, `location_id`, `sale_type`, `status`,
    `payment_status`, `sale_date`, `completed_at`, `subtotal`, `tax_amount`,
    `total_amount`, `paid_amount`, `remaining_amount`, `created_at`, `updated_at`
) VALUES
(
    'MAIN-000001', 1, 1, 'parts_only', 'completed', 'paid',
    NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, 100.00, 15.00,
    115.00, 115.00, 0.00, NOW(), NOW()
),
(
    'MAIN-000002', 1, 1, 'accessories', 'completed', 'paid',
    NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR, 200.00, 30.00,
    230.00, 230.00, 0.00, NOW(), NOW()
),
(
    'MAIN-000003', 1, 1, 'mixed', 'pending', 'pending',
    NOW() - INTERVAL 30 MINUTE, NULL, 150.00, 22.50,
    172.50, 0.00, 172.50, NOW(), NOW()
);
