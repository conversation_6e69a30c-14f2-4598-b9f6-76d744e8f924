<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerDevice extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'device_type',
        'brand',
        'model',
        'serial_number',
        'imei',
        'purchase_date',
        'warranty_expires_at',
        'notes',
        'is_active',
        'added_at'
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'warranty_expires_at' => 'date',
        'added_at' => 'datetime',
        'is_active' => 'boolean'
    ];

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function repairs()
    {
        return $this->hasMany(Repair::class, 'device_serial', 'serial_number');
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return trim($this->brand . ' ' . $this->model);
    }

    public function getWarrantyStatusAttribute()
    {
        if (!$this->warranty_expires_at) {
            return 'no_warranty';
        }
        
        if ($this->warranty_expires_at->isFuture()) {
            return 'active';
        }
        
        return 'expired';
    }

    public function getWarrantyStatusBadgeAttribute()
    {
        $statuses = [
            'active' => ['class' => 'badge-success', 'text' => 'ساري'],
            'expired' => ['class' => 'badge-danger', 'text' => 'منتهي'],
            'no_warranty' => ['class' => 'badge-secondary', 'text' => 'بدون ضمان']
        ];

        return $statuses[$this->warranty_status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByDeviceType($query, $deviceType)
    {
        return $query->where('device_type', $deviceType);
    }

    public function scopeByBrand($query, $brand)
    {
        return $query->where('brand', $brand);
    }

    public function scopeWithActiveWarranty($query)
    {
        return $query->where('warranty_expires_at', '>', now());
    }

    // Methods
    public function isWarrantyActive()
    {
        return $this->warranty_expires_at && $this->warranty_expires_at->isFuture();
    }

    public function getRepairHistory()
    {
        return $this->repairs()->orderBy('created_at', 'desc')->get();
    }

    public function getLastRepair()
    {
        return $this->repairs()->latest()->first();
    }

    public function getTotalRepairs()
    {
        return $this->repairs()->count();
    }
}
