# نظام الطارق لإدارة مراكز صيانة الحاسوب والموبايل

<p align="center">
  <img src="https://img.shields.io/badge/Laravel-10-red?style=for-the-badge&logo=laravel" alt="Laravel">
  <img src="https://img.shields.io/badge/TailwindCSS-3-blue?style=for-the-badge&logo=tailwindcss" alt="Tailwind CSS">
  <img src="https://img.shields.io/badge/Alpine.js-3-green?style=for-the-badge&logo=alpine.js" alt="Alpine.js">
  <img src="https://img.shields.io/badge/PHP-8.1+-purple?style=for-the-badge&logo=php" alt="PHP">
</p>

نظام ويب احترافي وعصري لإدارة مراكز صيانة الحاسوب والموبايل، مصمم بواجهة مستخدم حديثة مستوحاة من Visual Studio Code و Stripe Dashboard.

## 🌟 الميزات الرئيسية

### 📊 لوحة التحكم الرئيسية
- إحصائيات سريعة (طلبات الصيانة، المبيعات، المخزون، المصروفات)
- مخططات بيانية تفاعلية لحركة المبيعات والمشتريات
- إشعارات فورية للتنبيهات (مخزون منخفض، فواتير مستحقة)
- عرض طلبات الصيانة الأخيرة وتنبيهات المخزون المنخفض

### 🛒 نظام نقاط البيع (POS)
- واجهة حديثة مبنية على شبكة كروت للمنتجات
- دعم البحث والفلترة حسب التصنيف
- سلة مبيعات تفاعلية مع إدارة الكميات
- دعم خيارات الدفع المتعددة (نقدي، بطاقة)
- حساب الخصومات والمجاميع تلقائياً

### 🔧 إدارة عمليات الصيانة
- تتبع طلبات الصيانة مع مراحل التقدم
- إدارة حالات الطلبات (قيد الانتظار، قيد التنفيذ، مكتملة)
- ربط الطلبات بالعملاء والفنيين
- إشعارات العملاء حول حالة الصيانة
- تقارير مفصلة عن الأداء والإيرادات

### 📦 إدارة المنتجات والمخزون
- إدارة شاملة للمنتجات مع الصور والتصنيفات
- تتبع المخزون مع تنبيهات المخزون المنخفض
- دعم الباركود للمنتجات
- فلترة متقدمة حسب التصنيف وحالة المخزون
- تصدير البيانات إلى Excel

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء مع تاريخ التعاملات
- تتبع أرصدة العملاء والديون
- إحصائيات مفصلة عن كل عميل
- إدارة بيانات الموردين والمشتريات

### 💰 إدارة المصروفات والتقارير
- تصنيف المصروفات (إيجار، فواتير، مستلزمات، صيانة)
- مخططات بيانية لتحليل المصروفات
- تقارير مالية شاملة
- تصدير التقارير بصيغ مختلفة

## 🎨 التصميم والواجهة

### الألوان والثيم
- ألوان هادئة وحديثة (درجات الأزرق والرمادي والأخضر)
- دعم الوضع الليلي والنهاري
- تصميم متجاوب على جميع الأجهزة

### الخطوط والنصوص
- خطوط واضحة وداعمة للغة العربية (Cairo, Tajawal)
- دعم كامل للغة العربية مع RTL
- إمكانية التبديل بين العربية والإنجليزية

### التفاعل والحركة
- انتقالات سلسة وحركات جذابة
- أزرار ذات زوايا دائرية وتصميم بسيط
- إشعارات تفاعلية وتنبيهات ذكية

## 🛠️ التقنيات المستخدمة

### Backend
- **Laravel 10** - إطار عمل PHP الحديث
- **MySQL** - قاعدة البيانات
- **PHP 8.1+** - لغة البرمجة

### Frontend
- **Tailwind CSS** - إطار عمل CSS للتصميم
- **Alpine.js** - مكتبة JavaScript خفيفة للتفاعل
- **Chart.js** - مكتبة الرسوم البيانية
- **Vite** - أداة البناء السريعة

### الميزات التقنية
- تصميم متجاوب (Responsive Design)
- Progressive Web App (PWA) ready
- دعم الوضع الليلي
- تحسين الأداء والسرعة

## 📋 متطلبات التشغيل

- PHP 8.1 أو أحدث
- Composer
- Node.js و npm
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)

## 🚀 التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-username/tareq-maintenance-system.git
cd tareq-maintenance-system
```

### 2. تثبيت التبعيات
```bash
# تثبيت تبعيات PHP
composer install

# تثبيت تبعيات JavaScript
npm install
```

### 3. إعداد البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate
```

### 4. إعداد قاعدة البيانات
```bash
# تحديث ملف .env بمعلومات قاعدة البيانات
# ثم تشغيل الهجرات
php artisan migrate

# تشغيل البذور (اختياري)
php artisan db:seed
```

### 5. بناء الأصول
```bash
# للتطوير
npm run dev

# للإنتاج
npm run build
```

### 6. تشغيل الخادم
```bash
php artisan serve
```

## 📱 الاستخدام

### تسجيل الدخول
- افتح المتصفح وانتقل إلى `http://localhost:8000`
- استخدم بيانات الدخول الافتراضية أو أنشئ حساب جديد

### الوظائف الأساسية
1. **لوحة التحكم**: عرض الإحصائيات والمخططات
2. **نقاط البيع**: إجراء عمليات البيع السريعة
3. **إدارة الصيانة**: تتبع طلبات الصيانة
4. **إدارة المنتجات**: إضافة وتعديل المنتجات
5. **إدارة العملاء**: متابعة بيانات العملاء
6. **المصروفات**: تسجيل ومتابعة المصروفات

## 🔧 التخصيص والتطوير

### إضافة ميزات جديدة
- اتبع هيكل Laravel المعياري
- استخدم Alpine.js للتفاعل في الواجهة الأمامية
- اتبع نمط التصميم المستخدم في المشروع

### تخصيص التصميم
- عدّل ملف `tailwind.config.js` للألوان والخطوط
- استخدم ملف `resources/css/app.css` للتخصيصات الإضافية

## 📞 الدعم والمساعدة

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راسلنا على البريد الإلكتروني
- تابعنا على وسائل التواصل الاجتماعي

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذا النظام.

---

**نظام الطارق لإدارة مراكز الصيانة** - حل شامل ومتطور لإدارة أعمالك بكفاءة واحترافية.
