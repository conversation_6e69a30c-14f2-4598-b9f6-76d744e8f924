@extends('layouts.main')

@section('title', 'إدارة المخزون')

@section('content')
<div class="content-wrapper animate-fade-in-up" x-data="inventoryManager()">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">إدارة المخزون</h1>
                    <p class="page-subtitle">تتبع وإدارة مخزون المنتجات وقطع الغيار بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="showAddStock = true" class="btn btn-primary hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        تعديل المخزون
                    </button>
                    <button @click="exportInventory()" class="btn btn-success hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="stats-grid">
        <div class="stat-card hover-lift animate-scale-in animate-delay-100">
            <div class="stat-value" style="color: var(--accent-color);" x-text="summary.totalItems">245</div>
            <div class="stat-label">إجمالي الأصناف</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                منتج متنوع
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-200">
            <div class="stat-value" style="color: var(--success-color);" x-text="summary.inStock">198</div>
            <div class="stat-label">متوفر في المخزون</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                حالة جيدة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-300">
            <div class="stat-value" style="color: var(--warning-color);" x-text="summary.lowStock">23</div>
            <div class="stat-label">مخزون منخفض</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                يحتاج تجديد
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-500">
            <div class="stat-value" style="color: var(--danger-color);" x-text="summary.outOfStock">24</div>
            <div class="stat-label">نفد المخزون</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                يحتاج طلب فوري
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card animate-fade-in-up animate-delay-300">
        <div class="card-header">
            <h3 class="card-title">فلترة وبحث المخزون</h3>
            <button @click="clearFilters()" class="btn btn-ghost btn-sm hover-scale">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                مسح الفلاتر
            </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="form-group">
                <label class="form-label">الفئة</label>
                <select x-model="filters.category" @change="filterItems()" class="form-select focus-glow">
                    <option value="">جميع الفئات</option>
                    <option value="parts">قطع غيار</option>
                    <option value="accessories">إكسسوارات</option>
                    <option value="tools">أدوات</option>
                    <option value="supplies">مستلزمات</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label">حالة المخزون</label>
                <select x-model="filters.stockStatus" @change="filterItems()" class="form-select focus-glow">
                    <option value="">جميع الحالات</option>
                    <option value="in_stock">متوفر</option>
                    <option value="low_stock">مخزون منخفض</option>
                    <option value="out_of_stock">نفد المخزون</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label">المورد</label>
                <select x-model="filters.supplier" @change="filterItems()" class="form-select focus-glow">
                    <option value="">جميع الموردين</option>
                    <option value="supplier1">مورد الأجهزة الذكية</option>
                    <option value="supplier2">شركة قطع الغيار</option>
                    <option value="supplier3">مورد الإكسسوارات</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label">البحث</label>
                <div class="search-box">
                    <input type="text" x-model="filters.search" @input="filterItems()"
                           placeholder="البحث في المنتجات..."
                           class="search-input focus-glow">
                    <svg class="search-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Table -->
    <div class="table-container animate-fade-in-up animate-delay-500">
        <div class="card-header">
            <h3 class="card-title">جدول المخزون</h3>
            <div class="action-group">
                <button @click="refreshInventory()" class="btn btn-ghost btn-sm hover-rotate">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    تحديث
                </button>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>الفئة</th>
                        <th>الكمية الحالية</th>
                        <th>الحد الأدنى</th>
                        <th>السعر</th>
                        <th>القيمة الإجمالية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="(item, index) in filteredItems" :key="item.id">
                        <tr class="hover-lift" :class="'animate-fade-in-up animate-delay-' + (index * 100)">
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-accent-color/10 rounded-lg flex items-center justify-center">
                                        <span class="text-xs font-medium text-accent-color" x-text="item.name.charAt(0)"></span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="item.name"></div>
                                        <div class="text-sm text-muted-light dark:text-muted-dark" x-text="item.sku"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="text-sm text-primary-light dark:text-primary-dark" x-text="getCategoryText(item.category)"></td>
                            <td>
                                <div class="flex items-center gap-2">
                                    <span class="text-sm font-semibold" :class="item.currentStock <= item.minStock ? 'text-danger-color' : 'text-success-color'" x-text="item.currentStock"></span>
                                    <div class="progress" style="width: 60px; height: 4px;">
                                        <div class="progress-bar" :class="item.currentStock <= item.minStock ? 'danger' : 'success'" :style="'width: ' + Math.min((item.currentStock / (item.minStock * 2)) * 100, 100) + '%'"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="text-sm text-muted-light dark:text-muted-dark" x-text="item.minStock"></td>
                            <td class="text-sm font-semibold text-accent-color">₪<span x-text="item.price"></span></td>
                            <td class="text-sm font-semibold text-success-color">₪<span x-text="(item.currentStock * item.price).toFixed(2)"></span></td>
                            <td>
                                <span class="badge" :class="getStatusBadgeClass(item)" x-text="getStatusText(item)"></span>
                            </td>
                            <td>
                                <div class="action-group">
                                    <button @click="adjustStock(item)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        تعديل
                                    </button>
                                    <button @click="viewHistory(item.id)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        السجل
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Stock Adjustment Modal -->
    <div x-show="showAddStock" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-transition>
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تعديل المخزون</h3>
                <button @click="closeModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form @submit.prevent="saveStockAdjustment()">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المنتج</label>
                        <select x-model="currentAdjustment.productId" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            <option value="">اختر المنتج</option>
                            <template x-for="item in items" :key="item.id">
                                <option :value="item.id" x-text="item.name"></option>
                            </template>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع التعديل</label>
                        <select x-model="currentAdjustment.type" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            <option value="">اختر النوع</option>
                            <option value="add">إضافة</option>
                            <option value="subtract">خصم</option>
                            <option value="set">تحديد الكمية</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الكمية</label>
                        <input type="number" x-model="currentAdjustment.quantity" required min="0" placeholder="0" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">السبب</label>
                        <textarea x-model="currentAdjustment.reason" rows="3" placeholder="سبب التعديل..." class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
                    </div>
                </div>
                
                <div class="mt-6 flex space-x-2 space-x-reverse">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        حفظ التعديل
                    </button>
                    <button type="button" @click="closeModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function inventoryManager() {
    return {
        showAddStock: false,
        currentAdjustment: {
            productId: '',
            type: '',
            quantity: '',
            reason: ''
        },
        filters: {
            category: '',
            stockStatus: '',
            supplier: '',
            search: ''
        },
        summary: {
            totalItems: 245,
            inStock: 198,
            lowStock: 23,
            outOfStock: 24
        },
        items: [
            {
                id: 1,
                name: 'شاشة iPhone 13',
                sku: 'IP13-SCR-001',
                category: 'parts',
                currentStock: 15,
                minStock: 5,
                price: 250,
                supplier: 'supplier1'
            },
            {
                id: 2,
                name: 'بطارية Samsung S21',
                sku: 'SAM-BAT-021',
                category: 'parts',
                currentStock: 3,
                minStock: 10,
                price: 80,
                supplier: 'supplier2'
            },
            {
                id: 3,
                name: 'كفر حماية شفاف',
                sku: 'ACC-CVR-001',
                category: 'accessories',
                currentStock: 0,
                minStock: 20,
                price: 15,
                supplier: 'supplier3'
            }
        ],
        filteredItems: [],

        init() {
            this.filteredItems = [...this.items];
        },

        filterItems() {
            this.filteredItems = this.items.filter(item => {
                const matchesCategory = !this.filters.category || item.category === this.filters.category;
                const matchesSupplier = !this.filters.supplier || item.supplier === this.filters.supplier;
                const matchesSearch = !this.filters.search || 
                    item.name.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    item.sku.toLowerCase().includes(this.filters.search.toLowerCase());
                
                let matchesStatus = true;
                if (this.filters.stockStatus) {
                    if (this.filters.stockStatus === 'out_of_stock') {
                        matchesStatus = item.currentStock === 0;
                    } else if (this.filters.stockStatus === 'low_stock') {
                        matchesStatus = item.currentStock > 0 && item.currentStock <= item.minStock;
                    } else if (this.filters.stockStatus === 'in_stock') {
                        matchesStatus = item.currentStock > item.minStock;
                    }
                }

                return matchesCategory && matchesSupplier && matchesSearch && matchesStatus;
            });
        },

        getCategoryText(category) {
            const categories = {
                'parts': 'قطع غيار',
                'accessories': 'إكسسوارات',
                'tools': 'أدوات',
                'supplies': 'مستلزمات'
            };
            return categories[category] || category;
        },

        getStatusText(item) {
            if (item.currentStock === 0) return 'نفد المخزون';
            if (item.currentStock <= item.minStock) return 'مخزون منخفض';
            return 'متوفر';
        },

        getStatusColor(item) {
            if (item.currentStock === 0) {
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            }
            if (item.currentStock <= item.minStock) {
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
            }
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
        },

        adjustStock(item) {
            this.currentAdjustment.productId = item.id;
            this.showAddStock = true;
        },

        saveStockAdjustment() {
            // Here you would save the stock adjustment
            alert('تم حفظ تعديل المخزون');
            this.closeModal();
        },

        closeModal() {
            this.showAddStock = false;
            this.currentAdjustment = {
                productId: '',
                type: '',
                quantity: '',
                reason: ''
            };
        },

        viewHistory(id) {
            alert(`عرض سجل المخزون للمنتج ${id}`);
        },

        exportInventory() {
            alert('سيتم تصدير تقرير المخزون');
        }
    }
}
</script>
@endpush
@endsection
