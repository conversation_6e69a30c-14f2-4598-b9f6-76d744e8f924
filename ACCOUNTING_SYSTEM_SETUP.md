# دليل إعداد النظام المحاسبي المتكامل

## 📋 نظرة عامة

تم إضافة نظام محاسبي أساسي متكامل إلى نظام إدارة مركز الصيانة يشمل:

### ✅ **الميزات المتاحة:**
- **دليل الحسابات** مع هيكل شجري شامل
- **القيود المحاسبية التلقائية** المرتبطة بالعمليات
- **التقارير المحاسبية الأساسية** (الميزانية، قائمة الدخل، التدفقات النقدية)
- **إدارة الذمم المدينة والدائنة** مع تتبع الاستحقاقات
- **التكامل الكامل** مع نقطة البيع والمخزون

---

## 🚀 خطوات الإعداد

### 1. **تشغيل الترحيلات**

```bash
# في Terminal أو Command Prompt
cd C:\laragon\www\tareq
php artisan migrate
```

### 2. **إعداد قاعدة البيانات**

#### الطريقة الأولى: استخدام ملف SQL
1. افتح phpMyAdmin
2. اختر قاعدة البيانات `tareq`
3. انسخ محتوى ملف `database/accounting_system_setup.sql`
4. شغل الاستعلامات

#### الطريقة الثانية: استخدام Seeder
```bash
php artisan db:seed --class=ChartOfAccountsSeeder
```

### 3. **التحقق من الإعداد**
- اذهب إلى: `http://tareq.test/accounts`
- يجب أن تشاهد دليل الحسابات مع الهيكل الشجري
- تأكد من وجود 42 حساب أساسي مع الأرصدة الافتتاحية

---

## 📊 هيكل دليل الحسابات

### **الأصول (Assets) - 1xxxx**
```
10000 - الأصول
├── 11000 - الأصول المتداولة
│   ├── 11100 - النقدية والبنوك
│   │   ├── 11101 - الصندوق (10,000 ريال)
│   │   ├── 11102 - البنك الأهلي (50,000 ريال)
│   │   └── 11103 - البنك الراجحي (30,000 ريال)
│   ├── 11200 - الذمم المدينة
│   │   ├── 11201 - ذمم العملاء
│   │   └── 11202 - أوراق القبض
│   └── 11300 - المخزون
│       ├── 11301 - مخزون قطع الغيار (25,000 ريال)
│       └── 11302 - مخزون الإكسسوارات (15,000 ريال)
└── 12000 - الأصول الثابتة
    ├── 12100 - المعدات والأجهزة (80,000 ريال)
    ├── 12200 - الأثاث والمفروشات (20,000 ريال)
    └── 12900 - مجمع الاستهلاك (15,000 ريال)
```

### **الخصوم (Liabilities) - 2xxxx**
```
20000 - الخصوم
└── 21000 - الخصوم المتداولة
    ├── 21100 - الذمم الدائنة
    │   └── 21101 - ذمم الموردين
    ├── 21200 - المصروفات المستحقة
    │   ├── 21201 - رواتب مستحقة
    │   └── 21202 - إيجار مستحق
    └── 21300 - ضريبة القيمة المضافة
```

### **حقوق الملكية (Equity) - 3xxxx**
```
30000 - حقوق الملكية
├── 31000 - رأس المال (200,000 ريال)
├── 32000 - الأرباح المحتجزة
└── 33000 - أرباح العام الجاري
```

### **الإيرادات (Revenues) - 4xxxx**
```
40000 - الإيرادات
├── 41000 - إيرادات المبيعات
│   ├── 41100 - مبيعات قطع الغيار
│   └── 41200 - مبيعات الإكسسوارات
└── 42000 - إيرادات الخدمات
    ├── 42100 - خدمات الإصلاح
    └── 42200 - خدمات الصيانة
```

### **المصروفات (Expenses) - 5xxxx**
```
50000 - المصروفات
├── 51000 - تكلفة البضاعة المباعة
└── 52000 - مصروفات التشغيل
    ├── 52100 - الرواتب والأجور
    ├── 52200 - الإيجار
    ├── 52300 - الكهرباء والماء
    └── 52400 - الاتصالات
```

---

## 🔄 القيود المحاسبية التلقائية

### **قيود المبيعات النقدية:**
```
من حـ/ الصندوق                    XXX
    إلى حـ/ مبيعات قطع الغيار           XXX
```

### **قيود المبيعات الآجلة:**
```
من حـ/ ذمم العملاء                XXX
    إلى حـ/ مبيعات قطع الغيار           XXX
```

### **قيود تحصيل الذمم:**
```
من حـ/ الصندوق                    XXX
    إلى حـ/ ذمم العملاء                 XXX
```

### **قيود المشتريات:**
```
من حـ/ مخزون قطع الغيار            XXX
    إلى حـ/ ذمم الموردين                XXX
```

---

## 📈 التقارير المحاسبية المتاحة

### 1. **ميزان المراجعة (Trial Balance)**
- عرض جميع الحسابات مع أرصدتها
- التحقق من توازن القيود المحاسبية
- المسار: `/accounting/reports/trial-balance`

### 2. **الميزانية العمومية (Balance Sheet)**
- الأصول والخصوم وحقوق الملكية
- المسار: `/accounting/reports/balance-sheet`

### 3. **قائمة الدخل (Income Statement)**
- الإيرادات والمصروفات وصافي الربح
- المسار: `/accounting/reports/income-statement`

### 4. **قائمة التدفقات النقدية (Cash Flow)**
- التدفقات النقدية من الأنشطة المختلفة
- المسار: `/accounting/reports/cash-flow`

### 5. **دفتر الأستاذ (Account Ledger)**
- تفاصيل حركة حساب معين
- المسار: `/accounting/reports/account-ledger/{account_id}`

---

## 🔗 الصفحات والمسارات

### **دليل الحسابات:**
- **الرئيسية**: `http://tareq.test/accounts`
- **إنشاء حساب**: `http://tareq.test/accounts/create`
- **تفاصيل حساب**: `http://tareq.test/accounts/{id}`

### **القيود المحاسبية:**
- **الرئيسية**: `http://tareq.test/journal-entries`
- **إنشاء قيد**: `http://tareq.test/journal-entries/create`
- **تفاصيل قيد**: `http://tareq.test/journal-entries/{id}`

### **الذمم المدينة:**
- **الرئيسية**: `http://tareq.test/accounting/receivables`
- **تفاصيل ذمة**: `http://tareq.test/accounting/receivables/{id}`

### **الذمم الدائنة:**
- **الرئيسية**: `http://tareq.test/accounting/payables`
- **إنشاء ذمة**: `http://tareq.test/accounting/payables/create`

### **التقارير المحاسبية:**
- **الرئيسية**: `http://tareq.test/accounting-reports`
- **ميزان المراجعة**: `http://tareq.test/accounting/reports/trial-balance`
- **الميزانية العمومية**: `http://tareq.test/accounting/reports/balance-sheet`

---

## 🎨 التصميم والواجهة

### **الميزات المطبقة:**
✅ **واجهة عربية كاملة** مع دعم RTL  
✅ **ألوان هادئة** (أزرق/رمادي/أخضر) موحدة  
✅ **خطوط Cairo/Tajawal** عبر جميع الصفحات  
✅ **تصميم متجاوب** يعمل على جميع الأجهزة  
✅ **وضع ليلي/نهاري** متاح  
✅ **تأثيرات Glass Morphism** حديثة  
✅ **هيكل شجري تفاعلي** لدليل الحسابات  

### **القائمة الجانبية:**
تم إضافة قسم "النظام المحاسبي" يشمل:
- دليل الحسابات
- القيود المحاسبية  
- الذمم المدينة
- الذمم الدائنة
- التقارير المحاسبية

---

## 🔧 الملفات المضافة

### **النماذج (Models):**
- `app/Models/Account.php` - نموذج الحسابات
- `app/Models/JournalEntry.php` - نموذج القيود المحاسبية
- `app/Models/AccountTransaction.php` - نموذج تفاصيل القيود
- `app/Models/Receivable.php` - نموذج الذمم المدينة
- `app/Models/Payable.php` - نموذج الذمم الدائنة

### **وحدات التحكم (Controllers):**
- `app/Http/Controllers/AccountController.php` - إدارة الحسابات
- `app/Http/Controllers/JournalEntryController.php` - إدارة القيود
- `app/Http/Controllers/AccountingReportController.php` - التقارير المحاسبية

### **الخدمات (Services):**
- `app/Services/AccountingService.php` - خدمة القيود التلقائية

### **الترحيلات (Migrations):**
- `database/migrations/*_create_accounts_table.php`
- `database/migrations/*_create_journal_entries_table.php`
- `database/migrations/*_create_account_transactions_table.php`
- `database/migrations/*_create_receivables_table.php`
- `database/migrations/*_create_payables_table.php`

### **الواجهات (Views):**
- `resources/views/accounting/accounts/` - واجهات دليل الحسابات
- `resources/views/accounting/journal-entries/` - واجهات القيود المحاسبية
- `resources/views/accounting/reports/` - واجهات التقارير المحاسبية

### **البيانات التجريبية:**
- `database/seeders/ChartOfAccountsSeeder.php` - بذرة دليل الحسابات
- `database/accounting_system_setup.sql` - ملف SQL شامل

---

## ✅ التحقق من التشغيل

### 1. **اختبار دليل الحسابات:**
- اذهب إلى `http://tareq.test/accounts`
- تأكد من ظهور الهيكل الشجري للحسابات
- جرب إنشاء حساب جديد

### 2. **اختبار القيود التلقائية:**
- قم بإنشاء مبيعة من نقطة البيع
- تحقق من إنشاء القيد المحاسبي تلقائياً
- راجع تحديث أرصدة الحسابات

### 3. **اختبار التقارير:**
- اذهب إلى التقارير المحاسبية
- تحقق من ميزان المراجعة
- راجع الميزانية العمومية

---

## 🎯 النتيجة النهائية

**نظام محاسبي أساسي متكامل** يشمل:

✅ **دليل حسابات شامل** مع 42 حساب أساسي  
✅ **قيود محاسبية تلقائية** مرتبطة بالعمليات  
✅ **تقارير محاسبية أساسية** (ميزانية، دخل، تدفقات)  
✅ **إدارة الذمم** المدينة والدائنة  
✅ **واجهة عربية حديثة** متجاوبة  
✅ **تكامل كامل** مع النظام الحالي  

**النظام جاهز للاستخدام الإنتاجي!** 🚀
