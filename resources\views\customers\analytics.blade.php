@extends('layouts.main')

@section('title', 'تحليلات العميل')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تحليلات العميل</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $customer->display_name }} - {{ $customer->customer_number }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('customers.show', $customer) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة لتفاصيل العميل
            </a>
            <button onclick="exportAnalytics()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير التقرير
            </button>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">قيمة العميل الإجمالية</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ number_format($lifetimeValue, 2) }} ر.س</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط وقت الصيانة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ number_format($averageRepairTime, 1) }} يوم</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">تقييم الرضا</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ number_format($satisfactionRating, 1) }}/5</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">عميل منذ</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $customer->created_at->diffInMonths() }} شهر</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Repair Trends Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">اتجاهات طلبات الصيانة</h3>
            <div class="h-64 flex items-center justify-center">
                @if($repairTrends->count() > 0)
                    <canvas id="repairTrendsChart" width="400" height="200"></canvas>
                @else
                    <div class="text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">لا توجد بيانات كافية</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Device Breakdown Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">توزيع الأجهزة</h3>
            <div class="h-64 flex items-center justify-center">
                @if($deviceBreakdown->count() > 0)
                    <canvas id="deviceBreakdownChart" width="400" height="200"></canvas>
                @else
                    <div class="text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                        </svg>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">لا توجد أجهزة مسجلة</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Repair Status Distribution -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">توزيع حالات طلبات الصيانة</h3>
        @if($repairStatusDistribution->count() > 0)
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                @foreach($repairStatusDistribution as $status)
                    <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $status->count }}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            @switch($status->status)
                                @case('pending') معلق @break
                                @case('in_progress') قيد التنفيذ @break
                                @case('waiting_parts') انتظار قطع @break
                                @case('completed') مكتمل @break
                                @case('cancelled') ملغي @break
                                @default {{ $status->status }}
                            @endswitch
                        </div>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="h-2 rounded-full 
                                    @switch($status->status)
                                        @case('pending') bg-yellow-600 @break
                                        @case('in_progress') bg-blue-600 @break
                                        @case('waiting_parts') bg-orange-600 @break
                                        @case('completed') bg-green-600 @break
                                        @case('cancelled') bg-red-600 @break
                                        @default bg-gray-600
                                    @endswitch
                                " style="width: {{ ($status->count / $repairStatusDistribution->sum('count')) * 100 }}%"></div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">لا توجد طلبات صيانة</p>
            </div>
        @endif
    </div>

    <!-- Detailed Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Customer Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات العميل</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">نوع العميل:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ $customer->type == 'individual' ? 'فرد' : 'شركة' }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">تاريخ التسجيل:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ $customer->created_at->format('Y-m-d') }}</span>
                </div>
                @if($customer->city)
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">المدينة:</span>
                        <span class="text-gray-900 dark:text-gray-100">{{ $customer->city }}</span>
                    </div>
                @endif
                @if($customer->preferred_contact_method)
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">طريقة التواصل المفضلة:</span>
                        <span class="text-gray-900 dark:text-gray-100">
                            @switch($customer->preferred_contact_method)
                                @case('phone') هاتف @break
                                @case('email') بريد إلكتروني @break
                                @case('sms') رسائل نصية @break
                                @case('whatsapp') واتساب @break
                                @default {{ $customer->preferred_contact_method }}
                            @endswitch
                        </span>
                    </div>
                @endif
                @if($customer->source)
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">مصدر العميل:</span>
                        <span class="text-gray-900 dark:text-gray-100">
                            @switch($customer->source)
                                @case('website') الموقع الإلكتروني @break
                                @case('referral') إحالة @break
                                @case('social_media') وسائل التواصل @break
                                @case('advertisement') إعلان @break
                                @case('walk_in') زيارة مباشرة @break
                                @case('other') أخرى @break
                                @default {{ $customer->source }}
                            @endswitch
                        </span>
                    </div>
                @endif
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">مؤشرات الأداء</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">معدل تكرار الطلبات:</span>
                    <span class="text-gray-900 dark:text-gray-100">
                        {{ $customer->created_at->diffInMonths() > 0 ? number_format($repairStatusDistribution->sum('count') / $customer->created_at->diffInMonths(), 1) : 0 }} طلب/شهر
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">معدل الإنجاز:</span>
                    <span class="text-gray-900 dark:text-gray-100">
                        {{ $repairStatusDistribution->sum('count') > 0 ? number_format(($repairStatusDistribution->where('status', 'completed')->first()->count ?? 0) / $repairStatusDistribution->sum('count') * 100, 1) : 0 }}%
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">متوسط قيمة الطلب:</span>
                    <span class="text-gray-900 dark:text-gray-100">
                        {{ $repairStatusDistribution->sum('count') > 0 ? number_format($lifetimeValue / $repairStatusDistribution->sum('count'), 2) : 0 }} ر.س
                    </span>
                </div>
                @if($customer->last_repair_date)
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">آخر طلب صيانة:</span>
                        <span class="text-gray-900 dark:text-gray-100">{{ $customer->last_repair_date->diffForHumans() }}</span>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Repair Trends Chart
@if($repairTrends->count() > 0)
const repairTrendsCtx = document.getElementById('repairTrendsChart').getContext('2d');
new Chart(repairTrendsCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($repairTrends->pluck('month')) !!},
        datasets: [{
            label: 'عدد الطلبات',
            data: {!! json_encode($repairTrends->pluck('count')) !!},
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1
        }, {
            label: 'الإيرادات (ر.س)',
            data: {!! json_encode($repairTrends->pluck('revenue')) !!},
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.1,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});
@endif

// Device Breakdown Chart
@if($deviceBreakdown->count() > 0)
const deviceBreakdownCtx = document.getElementById('deviceBreakdownChart').getContext('2d');
new Chart(deviceBreakdownCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($deviceBreakdown->pluck('device_type')) !!},
        datasets: [{
            data: {!! json_encode($deviceBreakdown->pluck('count')) !!},
            backgroundColor: [
                'rgb(59, 130, 246)',
                'rgb(16, 185, 129)',
                'rgb(245, 158, 11)',
                'rgb(239, 68, 68)',
                'rgb(139, 92, 246)',
                'rgb(236, 72, 153)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
@endif

function exportAnalytics() {
    // Implementation for exporting analytics
    alert('تصدير التحليلات قيد التطوير');
}
</script>
@endsection
