@extends('layouts.main')

@section('page-title', 'لوحة التحكم')
@section('page-description', 'نظرة عامة على أداء مركز الصيانة')

@section('content')
<div class="content-wrapper" x-data="advancedDashboard()"
     x-init="
         // تحديث البيانات كل 30 ثانية
         setInterval(() => {
             updateRealTimeData();
         }, 30000);
         // تحديث الوقت كل ثانية
         setInterval(() => {
             updateCurrentTime();
         }, 1000);
     ">

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="page-title">لوحة التحكم الرئيسية</h1>
                    <p class="page-subtitle">نظرة عامة شاملة على أداء مركز الصيانة</p>
                </div>
                <div class="text-right">
                    <p class="text-sm text-muted-light dark:text-muted-dark" x-text="currentDate"></p>
                    <p class="text-xs text-muted-light dark:text-muted-dark" x-text="currentTime"></p>
                    <div class="mt-2 flex items-center justify-end">
                        <div class="w-2 h-2 bg-success-color rounded-full ml-2 animate-pulse"></div>
                        <span class="text-xs text-success-color">النظام متصل</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Welcome Message -->
    <div class="dashboard-card">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold text-primary-light dark:text-primary-dark">مرحباً، {{ auth()->user()->name }}</h2>
                <p class="text-secondary-light dark:text-secondary-dark mt-1">مدير النظام - مركز الصيانة المتقدم</p>
                <p class="text-sm text-muted-light dark:text-muted-dark mt-2">آخر تحديث: <span x-text="lastUpdate"></span></p>
            </div>
            <div class="text-right">
                <div class="status-indicator status-active">
                    <span class="status-dot"></span>
                    <span>النظام نشط</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="action-bar">
        <h3 class="text-lg font-semibold text-primary-light dark:text-primary-dark">الإجراءات السريعة</h3>
        <div class="action-group">
            <button @click="quickAction('new_repair')" class="btn btn-success btn-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                طلب صيانة جديد
            </button>

            <button @click="quickAction('schedule')" class="btn btn-info btn-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                جدولة موعد
            </button>

            <button @click="quickAction('parts')" class="btn btn-warning btn-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                قطع الغيار
            </button>

            <button @click="quickAction('reports')" class="btn btn-primary btn-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                التقارير
            </button>
        </div>
    </div>

    <!-- Repair Stats Cards -->
    <div class="stats-grid">
        <!-- Today's Repairs -->
        <div class="stat-card">
            <div class="stat-value" x-text="repairStats.todayRepairs">12</div>
            <div class="stat-label">طلبات اليوم</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
                +3 من أمس
            </div>
        </div>

        <!-- Pending Repairs -->
        <div class="stat-card">
            <div class="stat-value" style="color: var(--warning-color);" x-text="repairStats.pendingRepairs">23</div>
            <div class="stat-label">قيد الانتظار</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                يحتاج متابعة
            </div>
        </div>

        <!-- In Progress -->
        <div class="stat-card">
            <div class="stat-value" style="color: var(--info-color);" x-text="repairStats.inProgressRepairs">45</div>
            <div class="stat-label">قيد الإصلاح</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                </svg>
                جاري العمل
            </div>
        </div>

        <!-- Completed Today -->
        <div class="stat-card">
            <div class="stat-value" style="color: var(--success-color);" x-text="repairStats.completedToday">8</div>
            <div class="stat-label">مكتملة اليوم</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                معدل ممتاز
            </div>
        </div>

        <!-- Revenue Today -->
        <div class="stat-card">
            <div class="stat-value">₪<span x-text="repairStats.todayRevenue">2,450</span></div>
            <div class="stat-label">إيرادات اليوم</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
                +15% من أمس
            </div>
        </div>
    </div>

    <!-- Original Stats Cards -->
    <div class="stats-grid">
        <!-- Total Locations -->
        <div class="stat-card">
            <div class="stat-value">{{ $stats['total_locations'] }}</div>
            <div class="stat-label">إجمالي المواقع</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                {{ $stats['active_locations'] }} نشط
            </div>
        </div>

        <!-- Total Products -->
        <div class="stat-card">
            <div class="stat-value" style="color: var(--success-color);">{{ $stats['total_products'] }}</div>
            <div class="stat-label">إجمالي المنتجات</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                {{ $stats['active_products'] }} نشط
            </div>
        </div>

        <!-- Total Customers -->
        <div class="stat-card">
            <div class="stat-value" style="color: var(--info-color);">{{ $stats['total_customers'] }}</div>
            <div class="stat-label">إجمالي العملاء</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                {{ $stats['active_customers'] }} نشط
            </div>
        </div>

        <!-- Today Sales -->
        <div class="stat-card">
            <div class="stat-value">{{ number_format($stats['today_sales'], 2) }} ₪</div>
            <div class="stat-label">مبيعات اليوم</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                {{ $stats['total_sales'] }} فاتورة
            </div>
        </div>

        <!-- Inventory Items -->
        <div class="card p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 flex-1">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">عناصر المخزون</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.inventoryItems">1,234</p>
                    <p class="text-xs text-red-600 dark:text-red-400">15 عنصر منخفض</p>
                </div>
            </div>
        </div>

        <!-- Monthly Expenses -->
        <div class="card p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 flex-1">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مصروفات الشهر</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="formatCurrency(stats.monthlyExpenses)">890,000 د.ع</p>
                    <p class="text-xs text-yellow-600 dark:text-yellow-400">-5% من الشهر الماضي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 lg:gap-6">
        <!-- Monthly Sales -->
        <div class="card p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 flex-1">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مبيعات الشهر</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ number_format($stats['monthly_sales'], 2) }} ₪</p>
                    <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">{{ $stats['total_purchases'] }} مشترى</p>
                </div>
            </div>
        </div>

        <!-- Low Stock Products -->
        <div class="card p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 flex-1">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">منتجات منخفضة المخزون</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $stats['low_stock_products'] }}</p>
                    <p class="text-xs text-orange-600 dark:text-orange-400 mt-1">{{ $stats['out_of_stock_products'] }} نافد</p>
                </div>
            </div>
        </div>

        <!-- Total Users -->
        <div class="card p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 flex-1">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المستخدمين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $stats['total_users'] }}</p>
                    <p class="text-xs text-indigo-600 dark:text-indigo-400 mt-1">{{ $stats['active_users'] }} نشط</p>
                </div>
            </div>
        </div>

        <!-- Monthly Expenses -->
        <div class="card p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4 flex-1">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مصروفات الشهر</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ number_format($stats['monthly_expenses'], 2) }} ₪</p>
                    <p class="text-xs text-red-600 dark:text-red-400 mt-1">-5% من الشهر الماضي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables Row -->
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
        <!-- Sales Chart -->
        <div class="card xl:col-span-2">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="card-title">المبيعات والمشتريات</h3>
                    <div class="action-group">
                        <button @click="chartPeriod = '7d'"
                                :class="chartPeriod === '7d' ? 'btn btn-primary btn-sm' : 'btn btn-secondary btn-sm'"
                                class="transition-colors">7 أيام</button>
                        <button @click="chartPeriod = '30d'"
                                :class="chartPeriod === '30d' ? 'btn btn-primary btn-sm' : 'btn btn-secondary btn-sm'"
                                class="transition-colors">30 يوم</button>
                    </div>
                </div>
            </div>
            <div class="h-80">
                <canvas id="salesChart"></canvas>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="card-title">الأنشطة الأخيرة</h3>
                    <a href="#" class="btn btn-ghost btn-sm">عرض الكل</a>
                </div>
            </div>
            <div class="space-y-4">
                @foreach($recentActivities as $activity)
                <div class="flex items-start gap-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-accent-color/10 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm text-primary-light dark:text-primary-dark">{{ $activity->description }}</p>
                        <p class="text-xs text-muted-light dark:text-muted-dark mt-1">{{ $activity->created_at->diffForHumans() }}</p>
                    </div>
                </div>
                @endforeach

                @if($recentActivities->isEmpty())
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-muted-light dark:text-muted-dark mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <p class="text-muted-light dark:text-muted-dark">لا توجد أنشطة حديثة</p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Bottom Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Maintenance Orders -->
        <div class="card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">طلبات الصيانة الأخيرة</h3>
                <a href="{{ route('maintenance.index') }}" class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400">عرض الكل</a>
            </div>
            <div class="space-y-3">
                <template x-for="order in recentMaintenance" :key="order.id">
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <div class="w-2 h-2 rounded-full" :class="{
                                'bg-yellow-400': order.status === 'pending',
                                'bg-blue-400': order.status === 'in_progress',
                                'bg-green-400': order.status === 'completed',
                                'bg-red-400': order.status === 'cancelled'
                            }"></div>
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="order.device"></p>
                                <p class="text-xs text-gray-500 dark:text-gray-400" x-text="order.customer"></p>
                            </div>
                        </div>
                        <div class="text-left">
                            <p class="text-sm text-gray-900 dark:text-gray-100" x-text="order.id">#1234</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400" x-text="order.date">اليوم</p>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Low Stock Alerts -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="card-title">تنبيهات المخزون</h3>
                    <a href="#" class="btn btn-ghost btn-sm" style="color: var(--warning-color);">عرض الكل</a>
                </div>
            </div>
            <div class="space-y-3">
                @if($lowStockProducts->isNotEmpty())
                    @foreach($lowStockProducts->take(5) as $product)
                    <div class="alert alert-warning">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 bg-warning-color/20 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-warning-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-primary-light dark:text-primary-dark">{{ $product->name }}</p>
                                    <p class="text-xs text-muted-light dark:text-muted-dark">{{ $product->category->name ?? 'غير محدد' }}</p>
                                </div>
                            </div>
                            <div class="text-left">
                                <span class="badge badge-warning">منخفض</span>
                                <p class="text-xs text-muted-light dark:text-muted-dark mt-1">الحد الأدنى: {{ $product->min_stock_level }}</p>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @else
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-success-color mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-success-color">جميع المنتجات في المخزون</p>
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">إجراءات سريعة</h3>
            </div>
            <div class="space-y-3">
                <a href="#" class="btn btn-ghost w-full justify-start">
                    <div class="w-8 h-8 bg-accent-color/10 rounded-lg flex items-center justify-center ml-3">
                        <svg class="w-4 h-4 text-accent-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium">إضافة منتج جديد</span>
                </a>

                <a href="#" class="btn btn-ghost w-full justify-start">
                    <div class="w-8 h-8 bg-success-color/10 rounded-lg flex items-center justify-center ml-3">
                        <svg class="w-4 h-4 text-success-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium">إنشاء فاتورة مبيعات</span>
                </a>

                <a href="#" class="btn btn-ghost w-full justify-start">
                    <div class="w-8 h-8 bg-info-color/10 rounded-lg flex items-center justify-center ml-3">
                        <svg class="w-4 h-4 text-info-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium">إضافة عميل جديد</span>
                </a>

                <a href="#" class="btn btn-ghost w-full justify-start">
                    <div class="w-8 h-8 bg-warning-color/10 rounded-lg flex items-center justify-center ml-3">
                        <svg class="w-4 h-4 text-warning-color" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <span class="text-sm font-medium">عرض التقارير</span>
                </a>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function advancedDashboard() {
    return {
        chartPeriod: '30d',
        stats: {
            totalMaintenance: 127,
            todaySales: 2450000,
            inventoryItems: 1234,
            monthlyExpenses: 890000
        },
        recentMaintenance: [
            { id: '#1234', device: 'iPhone 13 Pro', customer: 'أحمد محمد', status: 'pending', date: 'اليوم' },
            { id: '#1235', device: 'Samsung Galaxy S21', customer: 'فاطمة علي', status: 'in_progress', date: 'أمس' },
            { id: '#1236', device: 'MacBook Pro', customer: 'محمد حسن', status: 'completed', date: 'أمس' },
            { id: '#1237', device: 'iPad Air', customer: 'سارة أحمد', status: 'pending', date: 'منذ يومين' }
        ],
        lowStockItems: [
            { id: 1, name: 'كابل USB-C', category: 'كابلات', stock: 5, min_stock: 20 },
            { id: 2, name: 'شاشة iPhone 12', category: 'قطع غيار', stock: 2, min_stock: 10 },
            { id: 3, name: 'بطارية Samsung', category: 'بطاريات', stock: 8, min_stock: 15 },
            { id: 4, name: 'سماعات AirPods', category: 'إكسسوارات', stock: 3, min_stock: 12 }
        ],
        
        init() {
            this.$nextTick(() => {
                this.initCharts();
            });
        },
        
        initCharts() {
            // Sales Chart
            const salesCtx = document.getElementById('salesChart').getContext('2d');
            new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30'],
                    datasets: [{
                        label: 'المبيعات',
                        data: [1200000, 1900000, 3000000, 5000000, 2000000, 3000000, 4500000, 3200000, 2800000, 3500000, 4200000, 3800000, 4100000, 3900000, 4300000, 3700000, 4000000, 4400000, 3600000, 4600000, 4800000, 4200000, 3900000, 4100000, 4500000, 4300000, 4700000, 4900000, 4600000, 5100000],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'المشتريات',
                        data: [800000, 1200000, 1800000, 2200000, 1500000, 1800000, 2100000, 1900000, 1700000, 2000000, 2300000, 2100000, 2200000, 2000000, 2400000, 2200000, 2300000, 2500000, 2100000, 2600000, 2800000, 2400000, 2200000, 2300000, 2500000, 2400000, 2600000, 2700000, 2500000, 2800000],
                        borderColor: 'rgb(16, 185, 129)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return (value / 1000000).toFixed(1) + 'M';
                                }
                            }
                        }
                    }
                }
            });

            // Maintenance Status Chart
            const maintenanceCtx = document.getElementById('maintenanceChart').getContext('2d');
            new Chart(maintenanceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['قيد الانتظار', 'قيد التنفيذ', 'مكتملة', 'ملغية'],
                    datasets: [{
                        data: [25, 35, 85, 7],
                        backgroundColor: [
                            'rgb(251, 191, 36)',
                            'rgb(59, 130, 246)',
                            'rgb(16, 185, 129)',
                            'rgb(239, 68, 68)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        },
        
        formatCurrency(amount) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'currency',
                currency: 'IQD',
                minimumFractionDigits: 0
            }).format(amount);
        },

        // إضافات جديدة للوحة التحكم المتقدمة
        currentDate: '',
        currentTime: '',
        lastUpdate: '',
        repairStats: {
            todayRepairs: 12,
            pendingRepairs: 23,
            inProgressRepairs: 45,
            completedToday: 8,
            todayRevenue: '2,450'
        },

        updateCurrentTime() {
            const now = new Date();
            this.currentDate = now.toLocaleDateString('ar-EG', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            this.currentTime = now.toLocaleTimeString('ar-EG', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        },

        updateLastUpdate() {
            this.lastUpdate = new Date().toLocaleTimeString('ar-EG', {
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        loadRealTimeData() {
            // محاكاة تحديث البيانات الحقيقية
            this.repairStats = {
                todayRepairs: Math.floor(Math.random() * 20) + 10,
                pendingRepairs: Math.floor(Math.random() * 30) + 15,
                inProgressRepairs: Math.floor(Math.random() * 50) + 30,
                completedToday: Math.floor(Math.random() * 15) + 5,
                todayRevenue: (Math.floor(Math.random() * 5000) + 2000).toLocaleString()
            };
        },

        updateRealTimeData() {
            this.loadRealTimeData();
            this.updateLastUpdate();
            console.log('تم تحديث البيانات:', new Date().toLocaleTimeString('ar-EG'));
        },

        quickAction(action) {
            switch(action) {
                case 'new_repair':
                    window.location.href = '/repairs/create';
                    break;
                case 'schedule':
                    window.location.href = '/schedule';
                    break;
                case 'parts':
                    window.location.href = '/parts';
                    break;
                case 'reports':
                    window.location.href = '/reports/repairs';
                    break;
                default:
                    console.log('إجراء غير معروف:', action);
            }
        }
    }
}
</script>
@endpush
@endsection
