<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_devices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->string('device_type')->comment('نوع الجهاز (mobile, tablet, laptop, etc.)');
            $table->string('brand')->comment('الماركة');
            $table->string('model')->comment('الموديل');
            $table->string('serial_number')->nullable()->comment('الرقم التسلسلي');
            $table->string('imei')->nullable()->comment('رقم IMEI للهواتف');
            $table->date('purchase_date')->nullable()->comment('تاريخ الشراء');
            $table->date('warranty_expires_at')->nullable()->comment('تاريخ انتهاء الضمان');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->boolean('is_active')->default(true)->comment('نشط');
            $table->timestamp('added_at')->useCurrent()->comment('تاريخ الإضافة');
            $table->timestamps();

            // Indexes
            $table->index(['customer_id', 'device_type']);
            $table->index(['brand', 'model']);
            $table->index('serial_number');
            $table->index('imei');
            $table->index('warranty_expires_at');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_devices');
    }
};
