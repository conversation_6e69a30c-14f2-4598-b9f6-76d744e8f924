<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_communications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->comment('المستخدم الذي أجرى التواصل');
            $table->enum('type', ['call', 'email', 'sms', 'whatsapp', 'visit', 'other'])->comment('نوع التواصل');
            $table->enum('direction', ['inbound', 'outbound'])->comment('اتجاه التواصل');
            $table->string('subject')->nullable()->comment('موضوع التواصل');
            $table->text('content')->comment('محتوى التواصل');
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('completed')->comment('حالة التواصل');
            $table->timestamp('scheduled_at')->nullable()->comment('موعد التواصل المجدول');
            $table->timestamp('completed_at')->nullable()->comment('وقت إتمام التواصل');
            $table->json('metadata')->nullable()->comment('بيانات إضافية (رقم الهاتف، البريد، إلخ)');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->timestamps();

            // Indexes
            $table->index(['customer_id', 'type']);
            $table->index(['user_id', 'created_at']);
            $table->index(['type', 'direction']);
            $table->index('status');
            $table->index('scheduled_at');
            $table->index('completed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_communications');
    }
};
