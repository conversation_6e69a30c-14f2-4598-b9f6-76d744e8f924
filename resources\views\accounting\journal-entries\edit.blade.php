@extends('layouts.main')

@section('title', 'تعديل القيد - ' . $journalEntry->entry_number)

@push('styles')
<style>
.account-select {
    max-width: 250px;
}
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تعديل القيد المحاسبي</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $journalEntry->entry_number }} - {{ $journalEntry->entry_date->format('Y-m-d') }}</p>
        </div>
        
        <div class="flex gap-2">
            <a href="{{ route('journal-entries.show', $journalEntry) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- Warning for System Entries -->
    @if($journalEntry->reference_type)
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
            </div>
            <div class="mr-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    تحذير: قيد مرتبط بالنظام
                </h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>هذا القيد مرتبط بعملية في النظام ({{ $journalEntry->reference_type }}). تعديله قد يؤثر على البيانات المرتبطة.</p>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تعديل معلومات القيد</h3>
        </div>
        
        <form id="journal-entry-form" action="{{ route('journal-entries.update', $journalEntry) }}" method="POST" class="p-6 space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="entry_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        تاريخ القيد <span class="text-red-500">*</span>
                    </label>
                    <input type="date" 
                           id="entry_date" 
                           name="entry_date" 
                           value="{{ old('entry_date', $journalEntry->entry_date->format('Y-m-d')) }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('entry_date') border-red-500 @enderror"
                           required>
                    @error('entry_date')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="entry_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        رقم القيد
                    </label>
                    <input type="text" 
                           id="entry_number" 
                           value="{{ $journalEntry->entry_number }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400"
                           readonly>
                </div>
            </div>

            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    وصف القيد <span class="text-red-500">*</span>
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="2"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('description') border-red-500 @enderror"
                          placeholder="وصف مختصر للقيد المحاسبي"
                          required>{{ old('description', $journalEntry->description) }}</textarea>
                @error('description')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Current Information Display -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3">المعلومات الحالية</h4>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">الحالة:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100 mr-2">
                            {{ $journalEntry->getStatusLabel() }}
                        </span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">المنشئ:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100 mr-2">
                            {{ $journalEntry->creator->name ?? 'غير محدد' }}
                        </span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">تاريخ الإنشاء:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100 mr-2">
                            {{ $journalEntry->created_at->format('Y-m-d') }}
                        </span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">عدد السطور:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100 mr-2">
                            {{ $journalEntry->transactions->count() }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Transactions -->
            <div>
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100">تفاصيل القيد</h4>
                    <button type="button" onclick="addTransaction()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة سطر
                    </button>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full border border-gray-200 dark:border-gray-700 rounded-lg">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                                    الحساب
                                </th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                                    الوصف
                                </th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                                    مدين
                                </th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                                    دائن
                                </th>
                                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                                    إجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody id="transactions-table" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Transaction rows will be loaded here -->
                        </tbody>
                        <tfoot class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <td colspan="2" class="px-4 py-3 text-right font-medium text-gray-900 dark:text-gray-100">
                                    الإجمالي
                                </td>
                                <td class="px-4 py-3 text-right font-bold text-gray-900 dark:text-gray-100">
                                    <span id="total-debit">0.00</span> ريال
                                </td>
                                <td class="px-4 py-3 text-right font-bold text-gray-900 dark:text-gray-100">
                                    <span id="total-credit">0.00</span> ريال
                                </td>
                                <td class="px-4 py-3 text-center">
                                    <span id="balance-status" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        غير متوازن
                                    </span>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                @error('transactions')
                    <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                @enderror
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="{{ route('journal-entries.show', $journalEntry) }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg">
                    إلغاء
                </a>
                <button type="submit" 
                        id="submit-btn"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled>
                    <i class="fas fa-save mr-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
let transactionIndex = 0;
const accounts = {!! json_encode($accounts->map(function($account) {
    return [
        'id' => $account->id,
        'code' => $account->account_code,
        'name' => $account->account_name,
        'full_name' => $account->account_code . ' - ' . $account->account_name
    ];
})) !!};

const existingTransactions = {!! json_encode($journalEntry->transactions->map(function($transaction) {
    return [
        'account_id' => $transaction->account_id,
        'description' => $transaction->description,
        'debit_amount' => $transaction->debit_amount,
        'credit_amount' => $transaction->credit_amount
    ];
})) !!};

function addTransaction(transactionData = null) {
    const tbody = document.getElementById('transactions-table');
    const row = document.createElement('tr');
    row.className = 'transaction-row';
    
    const accountId = transactionData ? transactionData.account_id : '';
    const description = transactionData ? transactionData.description : '';
    const debitAmount = transactionData ? transactionData.debit_amount : '';
    const creditAmount = transactionData ? transactionData.credit_amount : '';
    
    row.innerHTML = `
        <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-600">
            <select name="transactions[${transactionIndex}][account_id]" 
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 text-sm account-select"
                    required>
                <option value="">اختر الحساب</option>
                ${accounts.map(account => `<option value="${account.id}" ${account.id == accountId ? 'selected' : ''}>${account.full_name}</option>`).join('')}
            </select>
        </td>
        <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-600">
            <input type="text" 
                   name="transactions[${transactionIndex}][description]" 
                   value="${description}"
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 text-sm"
                   placeholder="وصف العملية">
        </td>
        <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-600">
            <input type="number" 
                   name="transactions[${transactionIndex}][debit_amount]" 
                   value="${debitAmount}"
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 text-sm amount-input debit-input"
                   placeholder="0.00"
                   step="0.01"
                   min="0"
                   onchange="updateTotals()"
                   oninput="handleAmountInput(this, 'credit')">
        </td>
        <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-600">
            <input type="number" 
                   name="transactions[${transactionIndex}][credit_amount]" 
                   value="${creditAmount}"
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 text-sm amount-input credit-input"
                   placeholder="0.00"
                   step="0.01"
                   min="0"
                   onchange="updateTotals()"
                   oninput="handleAmountInput(this, 'debit')">
        </td>
        <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-600 text-center">
            <button type="button" onclick="removeTransaction(this)" class="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(row);
    transactionIndex++;
    updateTotals();
}

function removeTransaction(button) {
    const row = button.closest('tr');
    row.remove();
    updateTotals();
}

function handleAmountInput(input, oppositeType) {
    if (input.value && parseFloat(input.value) > 0) {
        // Clear the opposite input in the same row
        const row = input.closest('tr');
        const oppositeInput = row.querySelector(`.${oppositeType}-input`);
        if (oppositeInput) {
            oppositeInput.value = '';
        }
    }
}

function updateTotals() {
    const debitInputs = document.querySelectorAll('.debit-input');
    const creditInputs = document.querySelectorAll('.credit-input');
    
    let totalDebit = 0;
    let totalCredit = 0;
    
    debitInputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        totalDebit += value;
    });
    
    creditInputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        totalCredit += value;
    });
    
    document.getElementById('total-debit').textContent = totalDebit.toFixed(2);
    document.getElementById('total-credit').textContent = totalCredit.toFixed(2);
    
    const balanceStatus = document.getElementById('balance-status');
    const submitBtn = document.getElementById('submit-btn');
    const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01 && totalDebit > 0;
    
    if (isBalanced) {
        balanceStatus.textContent = 'متوازن';
        balanceStatus.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        submitBtn.disabled = false;
    } else {
        balanceStatus.textContent = 'غير متوازن';
        balanceStatus.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
        submitBtn.disabled = true;
    }
}

// Load existing transactions
document.addEventListener('DOMContentLoaded', function() {
    existingTransactions.forEach(transaction => {
        addTransaction(transaction);
    });
    
    // If no existing transactions, add two empty rows
    if (existingTransactions.length === 0) {
        addTransaction();
        addTransaction();
    }
});

// Form validation
document.getElementById('journal-entry-form').addEventListener('submit', function(e) {
    const rows = document.querySelectorAll('.transaction-row');
    if (rows.length < 2) {
        e.preventDefault();
        alert('يجب إضافة سطرين على الأقل للقيد المحاسبي');
        return;
    }
    
    const totalDebit = parseFloat(document.getElementById('total-debit').textContent);
    const totalCredit = parseFloat(document.getElementById('total-credit').textContent);
    
    if (Math.abs(totalDebit - totalCredit) >= 0.01) {
        e.preventDefault();
        alert('القيد غير متوازن - إجمالي المدين يجب أن يساوي إجمالي الدائن');
        return;
    }
    
    if (totalDebit === 0) {
        e.preventDefault();
        alert('يجب إدخال مبالغ في القيد المحاسبي');
        return;
    }
});
</script>
@endpush
@endsection
