@extends('layouts.main')

@section('title', 'إدارة المشتريات')

@section('content')
<div class="content-wrapper animate-fade-in-up" x-data="purchasesManager()">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">إدارة المشتريات</h1>
                    <p class="page-subtitle">إدارة فواتير المشتريات والموردين بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="exportPurchases()" class="btn btn-success hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        تصدير
                    </button>
                    <button @click="createPurchase()" class="btn btn-primary hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        فاتورة شراء جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card hover-lift animate-scale-in animate-delay-100">
            <div class="stat-value" style="color: var(--accent-color);" x-text="stats.totalPurchases">8</div>
            <div class="stat-label">إجمالي المشتريات</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                فواتير متنوعة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-200">
            <div class="stat-value" style="color: var(--danger-color);" x-text="stats.totalCost">₪18,500</div>
            <div class="stat-label">إجمالي التكلفة</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                استثمار جيد
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-300">
            <div class="stat-value" style="color: var(--warning-color);" x-text="stats.pendingOrders">2</div>
            <div class="stat-label">طلبات معلقة</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                تحتاج متابعة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-500">
            <div class="stat-value" style="color: var(--success-color);" x-text="stats.activeSuppliers">5</div>
            <div class="stat-label">الموردين النشطين</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                شراكات قوية
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card animate-fade-in-up animate-delay-300">
        <div class="card-header">
            <h3 class="card-title">فلترة وبحث المشتريات</h3>
            <button @click="clearFilters()" class="btn btn-ghost btn-sm hover-scale">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                مسح الفلاتر
            </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div class="form-group">
                <label class="form-label">البحث</label>
                <div class="search-box">
                    <input type="text" x-model="filters.search" @input="filterPurchases()"
                           placeholder="رقم الفاتورة أو المورد..."
                           class="search-input focus-glow">
                    <svg class="search-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">الحالة</label>
                <select x-model="filters.status" @change="filterPurchases()" class="form-select focus-glow">
                    <option value="">جميع الحالات</option>
                    <option value="pending">معلقة</option>
                    <option value="received">مستلمة</option>
                    <option value="cancelled">ملغية</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">المورد</label>
                <select x-model="filters.supplier" @change="filterPurchases()" class="form-select focus-glow">
                    <option value="">جميع الموردين</option>
                    <template x-for="supplier in suppliers" :key="supplier.id">
                        <option :value="supplier.id" x-text="supplier.name"></option>
                    </template>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">من تاريخ</label>
                <input type="date" x-model="filters.dateFrom" @change="filterPurchases()"
                       class="form-input focus-glow">
            </div>
            <div class="form-group">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" x-model="filters.dateTo" @change="filterPurchases()"
                       class="form-input focus-glow">
            </div>
        </div>
    </div>

    <!-- Purchases Table -->
    <div class="table-container animate-fade-in-up animate-delay-500">
        <div class="card-header">
            <h3 class="card-title">فواتير المشتريات</h3>
            <div class="action-group">
                <button @click="refreshPurchases()" class="btn btn-ghost btn-sm hover-rotate">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    تحديث
                </button>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>المورد</th>
                        <th>التاريخ</th>
                        <th>المبلغ</th>
                        <th>عدد الأصناف</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="(purchase, index) in filteredPurchases" :key="purchase.id">
                        <tr class="hover-lift" :class="'animate-fade-in-up animate-delay-' + (index * 100)">
                            <td>
                                <div class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="purchase.invoice_number"></div>
                            </td>
                            <td>
                                <div class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="purchase.supplier_name"></div>
                                <div class="text-sm text-muted-light dark:text-muted-dark" x-text="purchase.supplier_phone"></div>
                            </td>
                            <td class="text-sm text-primary-light dark:text-primary-dark" x-text="purchase.date"></td>
                            <td class="text-sm font-semibold text-success-color" x-text="'₪' + purchase.total"></td>
                            <td>
                                <span class="badge badge-info" x-text="purchase.items_count + ' صنف'"></span>
                            </td>
                            <td>
                                <span class="badge" :class="getStatusBadgeClass(purchase.status)" x-text="getStatusText(purchase.status)"></span>
                            </td>
                            <td>
                                <div class="action-group">
                                    <button @click="viewPurchase(purchase.id)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        عرض
                                    </button>
                                    <button @click="editPurchase(purchase.id)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        تعديل
                                    </button>
                                    <button @click="receivePurchase(purchase.id)" x-show="purchase.status === 'pending'" class="btn btn-ghost btn-sm hover-scale" style="color: var(--accent-color);">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        استلام
                                    </button>
                                    <button @click="printPurchase(purchase.id)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                        </svg>
                                        طباعة
                                    </button>
                                    <button @click="deletePurchase(purchase.id)" class="btn btn-ghost btn-sm hover-scale" style="color: var(--danger-color);">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        حذف
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button @click="previousPage()" :disabled="currentPage === 1" 
                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
                        السابق
                    </button>
                    <button @click="nextPage()" :disabled="currentPage === totalPages" 
                            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
                        التالي
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700 dark:text-gray-300">
                            عرض <span class="font-medium" x-text="((currentPage - 1) * itemsPerPage) + 1"></span> 
                            إلى <span class="font-medium" x-text="Math.min(currentPage * itemsPerPage, totalItems)"></span> 
                            من <span class="font-medium" x-text="totalItems"></span> نتيجة
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            <button @click="previousPage()" :disabled="currentPage === 1"
                                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                                السابق
                            </button>
                            <template x-for="page in getPageNumbers()" :key="page">
                                <button @click="goToPage(page)" 
                                        :class="page === currentPage ? 'bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'"
                                        class="relative inline-flex items-center px-4 py-2 border text-sm font-medium"
                                        x-text="page">
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages"
                                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                                التالي
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function purchasesManager() {
    return {
        purchases: [
            {
                id: 1,
                invoice_number: 'PUR-001',
                supplier_name: 'شركة التقنية المتقدمة',
                supplier_phone: '02-123-4567',
                date: '2024-07-09',
                total: '2500.00',
                items_count: 5,
                status: 'received'
            },
            {
                id: 2,
                invoice_number: 'PUR-002',
                supplier_name: 'مؤسسة الإلكترونيات الحديثة',
                supplier_phone: '02-234-5678',
                date: '2024-07-08',
                total: '1800.00',
                items_count: 3,
                status: 'pending'
            },
            {
                id: 3,
                invoice_number: 'PUR-003',
                supplier_name: 'شركة قطع الغيار المحدودة',
                supplier_phone: '02-345-6789',
                date: '2024-07-07',
                total: '3200.00',
                items_count: 8,
                status: 'received'
            }
        ],
        suppliers: [
            { id: 1, name: 'شركة التقنية المتقدمة' },
            { id: 2, name: 'مؤسسة الإلكترونيات الحديثة' },
            { id: 3, name: 'شركة قطع الغيار المحدودة' }
        ],
        filteredPurchases: [],
        filters: {
            search: '',
            status: '',
            supplier: '',
            dateFrom: '',
            dateTo: ''
        },
        stats: {
            totalPurchases: 8,
            totalCost: '₪18,500',
            pendingOrders: 2,
            activeSuppliers: 5
        },
        currentPage: 1,
        itemsPerPage: 10,
        totalItems: 0,
        totalPages: 0,

        init() {
            this.filteredPurchases = [...this.purchases];
            this.updatePagination();
        },

        filterPurchases() {
            this.filteredPurchases = this.purchases.filter(purchase => {
                const matchesSearch = !this.filters.search || 
                    purchase.invoice_number.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    purchase.supplier_name.toLowerCase().includes(this.filters.search.toLowerCase());
                
                const matchesStatus = !this.filters.status || purchase.status === this.filters.status;
                const matchesSupplier = !this.filters.supplier || purchase.supplier_id === parseInt(this.filters.supplier);
                
                const matchesDateFrom = !this.filters.dateFrom || purchase.date >= this.filters.dateFrom;
                const matchesDateTo = !this.filters.dateTo || purchase.date <= this.filters.dateTo;

                return matchesSearch && matchesStatus && matchesSupplier && matchesDateFrom && matchesDateTo;
            });
            
            this.currentPage = 1;
            this.updatePagination();
        },

        updatePagination() {
            this.totalItems = this.filteredPurchases.length;
            this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        },

        getPageNumbers() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        },

        goToPage(page) {
            this.currentPage = page;
        },

        previousPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },

        getStatusText(status) {
            const statuses = {
                'pending': 'معلقة',
                'received': 'مستلمة',
                'cancelled': 'ملغية'
            };
            return statuses[status] || status;
        },

        createPurchase() {
            window.location.href = '/purchases/create';
        },

        viewPurchase(id) {
            window.location.href = `/purchases/${id}`;
        },

        editPurchase(id) {
            window.location.href = `/purchases/${id}/edit`;
        },

        receivePurchase(id) {
            if (confirm('هل تريد تأكيد استلام هذه المشتريات؟')) {
                const purchase = this.purchases.find(p => p.id === id);
                if (purchase) {
                    purchase.status = 'received';
                    this.filterPurchases();
                    alert('تم تأكيد الاستلام بنجاح');
                }
            }
        },

        printPurchase(id) {
            window.open(`/purchases/${id}/print`, '_blank');
        },

        deletePurchase(id) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                this.purchases = this.purchases.filter(purchase => purchase.id !== id);
                this.filterPurchases();
            }
        },

        exportPurchases() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري التصدير...
            `;
            button.disabled = true;

            // محاكاة عملية التصدير
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                if (window.showNotification) {
                    window.showNotification('تم تصدير بيانات المشتريات إلى Excel بنجاح!', 'success');
                }
            }, 2000);
        },

        refreshPurchases() {
            // إضافة تأثير التحديث
            const button = event.target;
            button.classList.add('animate-spin');

            // محاكاة تحديث البيانات
            setTimeout(() => {
                button.classList.remove('animate-spin');
                if (window.showNotification) {
                    window.showNotification('تم تحديث بيانات المشتريات', 'info');
                }
            }, 1000);
        },

        clearFilters() {
            this.filters = {
                search: '',
                status: '',
                supplier: '',
                dateFrom: '',
                dateTo: ''
            };
            this.filterPurchases();

            if (window.showNotification) {
                window.showNotification('تم مسح جميع الفلاتر', 'info');
            }
        },

        getStatusBadgeClass(status) {
            const classes = {
                'received': 'badge-success',
                'pending': 'badge-warning',
                'cancelled': 'badge-danger'
            };
            return classes[status] || 'badge-secondary';
        }
    }
}
</script>
@endpush
@endsection
