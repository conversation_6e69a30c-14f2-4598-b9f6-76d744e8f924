<?php

use App\Helpers\PaymentHelper;

if (!function_exists('getPaymentMethodColor')) {
    /**
     * Get payment method color
     */
    function getPaymentMethodColor($method)
    {
        return PaymentHelper::getPaymentMethodColor($method);
    }
}

if (!function_exists('getPaymentMethodLabel')) {
    /**
     * Get payment method label
     */
    function getPaymentMethodLabel($method)
    {
        return PaymentHelper::getPaymentMethodLabel($method);
    }
}

if (!function_exists('getPaymentMethodIcon')) {
    /**
     * Get payment method icon
     */
    function getPaymentMethodIcon($method)
    {
        return PaymentHelper::getPaymentMethodIcon($method);
    }
}

if (!function_exists('getPaymentStatusColor')) {
    /**
     * Get payment status color
     */
    function getPaymentStatusColor($status)
    {
        return PaymentHelper::getPaymentStatusColor($status);
    }
}

if (!function_exists('getPaymentStatusLabel')) {
    /**
     * Get payment status label
     */
    function getPaymentStatusLabel($status)
    {
        return PaymentHelper::getPaymentStatusLabel($status);
    }
}

if (!function_exists('formatCurrency')) {
    /**
     * Format currency
     */
    function formatCurrency($amount, $currency = 'ريال')
    {
        return number_format($amount, 2) . ' ' . $currency;
    }
}

if (!function_exists('formatDate')) {
    /**
     * Format date for Arabic display
     */
    function formatDate($date, $format = 'Y-m-d')
    {
        if (!$date) return '';
        
        if (is_string($date)) {
            $date = \Carbon\Carbon::parse($date);
        }
        
        return $date->format($format);
    }
}

if (!function_exists('formatDateTime')) {
    /**
     * Format datetime for Arabic display
     */
    function formatDateTime($datetime, $format = 'Y-m-d H:i')
    {
        if (!$datetime) return '';
        
        if (is_string($datetime)) {
            $datetime = \Carbon\Carbon::parse($datetime);
        }
        
        return $datetime->format($format);
    }
}

if (!function_exists('getStatusColor')) {
    /**
     * Get general status color
     */
    function getStatusColor($status)
    {
        return match($status) {
            'active', 'completed', 'paid', 'success' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
            'inactive', 'cancelled', 'failed', 'error' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
            'pending', 'waiting', 'warning' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
            'processing', 'in_progress', 'info' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
            'partial', 'draft' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
            default => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
        };
    }
}

if (!function_exists('getStatusLabel')) {
    /**
     * Get general status label in Arabic
     */
    function getStatusLabel($status)
    {
        return match($status) {
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'pending' => 'في الانتظار',
            'processing' => 'قيد المعالجة',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            'failed' => 'فشل',
            'paid' => 'مدفوع',
            'partial' => 'جزئي',
            'draft' => 'مسودة',
            'success' => 'نجح',
            'error' => 'خطأ',
            'warning' => 'تحذير',
            'info' => 'معلومات',
            'waiting' => 'انتظار',
            'in_progress' => 'قيد التنفيذ',
            default => $status
        };
    }
}

if (!function_exists('generateUniqueCode')) {
    /**
     * Generate unique code with prefix
     */
    function generateUniqueCode($prefix = '', $length = 6)
    {
        $code = $prefix . strtoupper(substr(uniqid(), -$length));
        return $code;
    }
}

if (!function_exists('calculatePercentage')) {
    /**
     * Calculate percentage
     */
    function calculatePercentage($part, $total, $decimals = 1)
    {
        if ($total == 0) return 0;
        return round(($part / $total) * 100, $decimals);
    }
}

if (!function_exists('arabicNumbers')) {
    /**
     * Convert English numbers to Arabic
     */
    function arabicNumbers($string)
    {
        $english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        
        return str_replace($english, $arabic, $string);
    }
}

if (!function_exists('englishNumbers')) {
    /**
     * Convert Arabic numbers to English
     */
    function englishNumbers($string)
    {
        $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        
        return str_replace($arabic, $english, $string);
    }
}
