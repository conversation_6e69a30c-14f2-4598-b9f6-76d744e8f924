@extends('layouts.main')

@section('title', 'قطع الغيار - مخزون منخفض')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">قطع الغيار - مخزون منخفض</h1>
            <p class="text-gray-600 dark:text-gray-400">قطع الغيار التي تحتاج إعادة طلب</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('parts.inventory') }}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                لوحة المخزون
            </a>
            <a href="{{ route('parts.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                جميع قطع الغيار
            </a>
        </div>
    </div>

    <!-- Alert -->
    <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div class="mr-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    تنبيه مخزون منخفض
                </h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>هذه القطع وصلت إلى نقطة إعادة الطلب أو أقل. يُنصح بإعادة طلبها قريباً لتجنب نفاد المخزون.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Parts Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                قطع الغيار ({{ $parts->total() }} قطعة)
            </h3>
        </div>

        @if($parts->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            القطعة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الفئة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المورد
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المخزون الحالي
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            نقطة إعادة الطلب
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            كمية الطلب المقترحة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($parts as $part)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                @if($part->images && count($part->images) > 0)
                                    <img src="{{ Storage::url($part->images[0]) }}" alt="{{ $part->name }}" class="w-10 h-10 rounded-lg object-cover ml-3">
                                @else
                                    <div class="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center ml-3">
                                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"></path>
                                        </svg>
                                    </div>
                                @endif
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        <a href="{{ route('parts.show', $part) }}" class="hover:text-blue-600 dark:hover:text-blue-400">
                                            {{ $part->name }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $part->part_number ?? $part->sku }}
                                    </div>
                                    @if($part->brand)
                                        <div class="text-xs text-gray-400">{{ $part->brand }} {{ $part->model }}</div>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {{ $part->category->name ?? 'غير محدد' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {{ $part->supplier->name ?? 'غير محدد' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <span class="text-lg font-bold text-red-600 dark:text-red-400">{{ $part->stock_quantity }}</span>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2 bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    منخفض
                                </span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {{ $part->reorder_point ?? 'غير محدد' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-sm font-medium text-green-600 dark:text-green-400">
                                {{ $part->reorder_quantity ?? ($part->reorder_point ? $part->reorder_point * 2 : 10) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{{ route('parts.show', $part) }}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200">
                                    عرض
                                </a>
                                <a href="{{ route('parts.edit', $part) }}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-200">
                                    تعديل
                                </a>
                                <button onclick="updateStock({{ $part->id }})" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200">
                                    تحديث المخزون
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            {{ $parts->links() }}
        </div>
        @else
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">جميع القطع متوفرة</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لا توجد قطع غيار تحتاج إعادة طلب حالياً.</p>
            <div class="mt-6">
                <a href="{{ route('parts.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <svg class="-mr-1 ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"></path>
                    </svg>
                    عرض جميع قطع الغيار
                </a>
            </div>
        </div>
        @endif
    </div>

    <!-- Bulk Actions -->
    @if($parts->count() > 0)
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات جماعية</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button onclick="createPurchaseOrder()" class="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-blue-600 dark:text-blue-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                    <p class="text-sm font-medium text-blue-600 dark:text-blue-400">إنشاء أمر شراء</p>
                </div>
            </button>

            <button onclick="exportLowStock()" class="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900 rounded-lg hover:bg-green-100 dark:hover:bg-green-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-green-600 dark:text-green-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-sm font-medium text-green-600 dark:text-green-400">تصدير القائمة</p>
                </div>
            </button>

            <button onclick="sendNotifications()" class="flex items-center justify-center p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-800 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-yellow-600 dark:text-yellow-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2zM4 7h8V5H4v2z"></path>
                    </svg>
                    <p class="text-sm font-medium text-yellow-600 dark:text-yellow-400">إرسال تنبيهات</p>
                </div>
            </button>
        </div>
    </div>
    @endif
</div>

<script>
function updateStock(partId) {
    // Implementation for stock update modal
    alert('سيتم إضافة نافذة تحديث المخزون قريباً');
}

function createPurchaseOrder() {
    // Implementation for creating purchase order
    alert('سيتم إضافة وظيفة إنشاء أمر الشراء قريباً');
}

function exportLowStock() {
    // Implementation for exporting low stock list
    window.location.href = '{{ route("parts.export") }}?stock_status=low_stock';
}

function sendNotifications() {
    // Implementation for sending notifications
    alert('سيتم إضافة وظيفة إرسال التنبيهات قريباً');
}
</script>
@endsection
