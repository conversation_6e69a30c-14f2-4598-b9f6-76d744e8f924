<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Repair;
use App\Models\Customer;
use App\Models\Technician;

class RepairControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
    }

    /** @test */
    public function it_can_display_repairs_index()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('repairs.index'));

        $response->assertStatus(200);
        $response->assertViewIs('repairs.index');
    }

    /** @test */
    public function it_can_display_repair_create_form()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('repairs.create'));

        $response->assertStatus(200);
        $response->assertViewIs('repairs.create');
    }

    /** @test */
    public function it_can_create_a_repair()
    {
        $customer = Customer::factory()->create();
        $technician = Technician::factory()->create();

        $repairData = [
            'customer_id' => $customer->id,
            'device_type' => 'mobile',
            'device_brand' => 'Samsung',
            'device_model' => 'Galaxy S21',
            'problem_description' => 'Screen is broken',
            'repair_type' => 'paid',
            'priority' => 'normal',
            'estimated_cost' => 150.00,
            'technician_id' => $technician->id,
        ];

        $response = $this->actingAs($this->user)
                         ->post(route('repairs.store'), $repairData);

        $response->assertRedirect(route('repairs.index'));
        $this->assertDatabaseHas('repairs', [
            'customer_id' => $customer->id,
            'device_brand' => 'Samsung',
            'device_model' => 'Galaxy S21',
            'problem_description' => 'Screen is broken'
        ]);
    }

    /** @test */
    public function it_can_display_a_repair()
    {
        $repair = Repair::factory()->create();

        $response = $this->actingAs($this->user)
                         ->get(route('repairs.show', $repair));

        $response->assertStatus(200);
        $response->assertViewIs('repairs.show');
        $response->assertViewHas('repair', $repair);
    }

    /** @test */
    public function it_can_display_repair_edit_form()
    {
        $repair = Repair::factory()->create();

        $response = $this->actingAs($this->user)
                         ->get(route('repairs.edit', $repair));

        $response->assertStatus(200);
        $response->assertViewIs('repairs.edit');
        $response->assertViewHas('repair', $repair);
    }

    /** @test */
    public function it_can_update_a_repair()
    {
        $repair = Repair::factory()->create();
        $customer = Customer::factory()->create();

        $updateData = [
            'customer_id' => $customer->id,
            'device_type' => 'laptop',
            'device_brand' => 'HP',
            'device_model' => 'Pavilion 15',
            'problem_description' => 'Won\'t turn on',
            'repair_type' => 'warranty',
            'priority' => 'high',
            'estimated_cost' => 200.00,
        ];

        $response = $this->actingAs($this->user)
                         ->put(route('repairs.update', $repair), $updateData);

        $response->assertRedirect(route('repairs.show', $repair));
        $this->assertDatabaseHas('repairs', [
            'id' => $repair->id,
            'device_brand' => 'HP',
            'device_model' => 'Pavilion 15',
            'problem_description' => 'Won\'t turn on'
        ]);
    }

    /** @test */
    public function it_can_update_repair_status()
    {
        $repair = Repair::factory()->create(['status' => 'pending']);

        $response = $this->actingAs($this->user)
                         ->post(route('repairs.update-status', $repair), [
                             'status' => 'in_progress',
                             'notes' => 'Started working on the repair'
                         ]);

        $response->assertRedirect(route('repairs.show', $repair));
        $this->assertDatabaseHas('repairs', [
            'id' => $repair->id,
            'status' => 'in_progress'
        ]);
    }

    /** @test */
    public function it_can_delete_a_repair()
    {
        $repair = Repair::factory()->create();

        $response = $this->actingAs($this->user)
                         ->delete(route('repairs.destroy', $repair));

        $response->assertRedirect(route('repairs.index'));
        $this->assertSoftDeleted('repairs', ['id' => $repair->id]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_repair()
    {
        $response = $this->actingAs($this->user)
                         ->post(route('repairs.store'), []);

        $response->assertSessionHasErrors([
            'customer_id',
            'device_type',
            'device_brand',
            'device_model',
            'problem_description',
            'repair_type',
            'priority'
        ]);
    }

    /** @test */
    public function it_can_filter_repairs_by_status()
    {
        Repair::factory()->create(['status' => 'pending']);
        Repair::factory()->create(['status' => 'completed']);

        $response = $this->actingAs($this->user)
                         ->get(route('repairs.index', ['status' => 'pending']));

        $response->assertStatus(200);
        $response->assertViewIs('repairs.index');
    }

    /** @test */
    public function it_can_search_repairs()
    {
        $repair = Repair::factory()->create(['device_brand' => 'iPhone']);

        $response = $this->actingAs($this->user)
                         ->get(route('repairs.index', ['search' => 'iPhone']));

        $response->assertStatus(200);
        $response->assertViewIs('repairs.index');
    }
}
