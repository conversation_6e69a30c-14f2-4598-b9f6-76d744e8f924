<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SaleItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'sale_id',
        'item_type',
        'part_id',
        'repair_id',
        'item_name',
        'item_code',
        'item_description',
        'item_category',
        'quantity',
        'unit_price',
        'original_price',
        'discount_amount',
        'line_total',
        'tax_rate',
        'tax_amount',
        'affects_inventory',
        'inventory_movements',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'inventory_movements' => 'array',
        'metadata' => 'array',
        'unit_price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'line_total' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'affects_inventory' => 'boolean',
    ];

    // Relationships
    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }

    public function part(): BelongsTo
    {
        return $this->belongsTo(Part::class);
    }

    public function repair(): BelongsTo
    {
        return $this->belongsTo(Repair::class);
    }

    // Accessors
    public function getItemTypeLabelAttribute()
    {
        $types = [
            'repair_service' => 'خدمة صيانة',
            'part' => 'قطعة غيار',
            'accessory' => 'إكسسوار',
            'labor' => 'عمالة',
            'other' => 'أخرى',
        ];

        return $types[$this->item_type] ?? $this->item_type;
    }

    public function getTotalPriceAttribute()
    {
        return $this->quantity * $this->unit_price;
    }

    public function calculateLineTotal()
    {
        $subtotal = $this->quantity * $this->unit_price;
        $this->line_total = $subtotal - $this->discount_amount;

        if ($this->tax_rate > 0) {
            $this->tax_amount = ($this->line_total * $this->tax_rate) / 100;
        }

        $this->save();
        return $this->line_total;
    }
}
