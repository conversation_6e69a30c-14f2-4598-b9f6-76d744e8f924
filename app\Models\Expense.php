<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Expense extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'expense_number',
        'category_id',
        'supplier_id',
        'title',
        'description',
        'amount',
        'tax_amount',
        'total_amount',
        'currency',
        'exchange_rate',
        'expense_date',
        'due_date',
        'payment_date',
        'payment_method',
        'payment_reference',
        'status',
        'category',
        'subcategory',
        'project_id',
        'department',
        'location',
        'receipt_number',
        'receipt_image',
        'attachments',
        'tags',
        'is_recurring',
        'recurring_frequency',
        'recurring_until',
        'approved_by',
        'approved_at',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'expense_date' => 'date',
        'due_date' => 'date',
        'payment_date' => 'date',
        'approved_at' => 'datetime',
        'recurring_until' => 'date',
        'attachments' => 'array',
        'tags' => 'array',
        'is_recurring' => 'boolean'
    ];

    protected $dates = [
        'expense_date',
        'due_date',
        'payment_date',
        'approved_at',
        'recurring_until',
        'deleted_at'
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function recurringExpenses()
    {
        return $this->hasMany(Expense::class, 'parent_expense_id');
    }

    public function parentExpense()
    {
        return $this->belongsTo(Expense::class, 'parent_expense_id');
    }

    public function expensable()
    {
        return $this->morphTo();
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'related');
    }

    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'draft' => ['class' => 'badge-secondary', 'text' => 'مسودة'],
            'pending' => ['class' => 'badge-warning', 'text' => 'في الانتظار'],
            'approved' => ['class' => 'badge-info', 'text' => 'معتمد'],
            'paid' => ['class' => 'badge-success', 'text' => 'مدفوع'],
            'rejected' => ['class' => 'badge-danger', 'text' => 'مرفوض'],
            'cancelled' => ['class' => 'badge-dark', 'text' => 'ملغي']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getPaymentStatusAttribute()
    {
        if ($this->payment_date) {
            return 'paid';
        } elseif ($this->due_date && $this->due_date->isPast()) {
            return 'overdue';
        } elseif ($this->status === 'approved') {
            return 'pending_payment';
        } else {
            return 'not_due';
        }
    }

    public function getPaymentStatusBadgeAttribute()
    {
        $statuses = [
            'paid' => ['class' => 'badge-success', 'text' => 'مدفوع'],
            'overdue' => ['class' => 'badge-danger', 'text' => 'متأخر'],
            'pending_payment' => ['class' => 'badge-warning', 'text' => 'في انتظار الدفع'],
            'not_due' => ['class' => 'badge-info', 'text' => 'غير مستحق']
        ];

        return $statuses[$this->payment_status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getIsOverdueAttribute()
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !$this->payment_date && 
               $this->status === 'approved';
    }

    public function getDaysOverdueAttribute()
    {
        if ($this->is_overdue) {
            return $this->due_date->diffInDays(now());
        }
        return 0;
    }

    public function getFormattedAmountAttribute()
    {
        return number_format($this->total_amount, 2) . ' ' . $this->currency;
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePaid($query)
    {
        return $query->whereNotNull('payment_date');
    }

    public function scopeUnpaid($query)
    {
        return $query->whereNull('payment_date')
                    ->where('status', 'approved');
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNull('payment_date')
                    ->where('status', 'approved');
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('expense_date', [$startDate, $endDate]);
    }

    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    // Methods
    public function generateExpenseNumber()
    {
        $prefix = 'EXP';
        $year = now()->year;
        $month = now()->format('m');
        
        $lastExpense = static::whereYear('created_at', $year)
                            ->whereMonth('created_at', now()->month)
                            ->orderBy('id', 'desc')
                            ->first();
        
        $sequence = $lastExpense ? (int)substr($lastExpense->expense_number, -4) + 1 : 1;
        
        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function calculateTotalAmount()
    {
        $total = $this->amount + $this->tax_amount;
        $this->update(['total_amount' => $total]);
        return $total;
    }

    public function approve($approverId = null)
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approverId ?? auth()->id(),
            'approved_at' => now()
        ]);

        return true;
    }

    public function reject($reason = null)
    {
        $this->update([
            'status' => 'rejected',
            'notes' => $this->notes . "\n\nRejected: {$reason}"
        ]);

        return true;
    }

    public function markAsPaid($paymentDate = null, $paymentMethod = null, $reference = null)
    {
        $this->update([
            'status' => 'paid',
            'payment_date' => $paymentDate ?? now(),
            'payment_method' => $paymentMethod,
            'payment_reference' => $reference
        ]);

        return true;
    }

    public function addAttachment($filePath, $fileName = null)
    {
        $attachments = $this->attachments ?? [];
        $attachments[] = [
            'path' => $filePath,
            'name' => $fileName ?? basename($filePath),
            'uploaded_at' => now()->toISOString()
        ];
        
        $this->update(['attachments' => $attachments]);
    }

    public function removeAttachment($filePath)
    {
        $attachments = $this->attachments ?? [];
        $attachments = array_filter($attachments, function($attachment) use ($filePath) {
            return $attachment['path'] !== $filePath;
        });
        
        $this->update(['attachments' => array_values($attachments)]);
    }

    public function addTag($tag)
    {
        $tags = $this->tags ?? [];
        
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->update(['tags' => $tags]);
        }
    }

    public function removeTag($tag)
    {
        $tags = $this->tags ?? [];
        $tags = array_filter($tags, function($t) use ($tag) {
            return $t !== $tag;
        });
        
        $this->update(['tags' => array_values($tags)]);
    }

    public function hasTag($tag)
    {
        return in_array($tag, $this->tags ?? []);
    }

    public function createRecurringExpenses()
    {
        if (!$this->is_recurring || !$this->recurring_frequency) {
            return false;
        }

        $nextDate = $this->expense_date;
        $createdExpenses = [];

        while ($nextDate->lte($this->recurring_until)) {
            $nextDate = $this->getNextRecurringDate($nextDate);
            
            if ($nextDate->lte($this->recurring_until)) {
                $newExpense = $this->replicate();
                $newExpense->expense_number = null; // Will be auto-generated
                $newExpense->expense_date = $nextDate;
                $newExpense->due_date = $nextDate->copy()->addDays(30); // Default 30 days
                $newExpense->status = 'draft';
                $newExpense->parent_expense_id = $this->id;
                $newExpense->save();
                
                $createdExpenses[] = $newExpense;
            }
        }

        return $createdExpenses;
    }

    private function getNextRecurringDate($currentDate)
    {
        switch ($this->recurring_frequency) {
            case 'daily':
                return $currentDate->addDay();
            case 'weekly':
                return $currentDate->addWeek();
            case 'monthly':
                return $currentDate->addMonth();
            case 'quarterly':
                return $currentDate->addMonths(3);
            case 'yearly':
                return $currentDate->addYear();
            default:
                return $currentDate->addMonth();
        }
    }

    public function canBeEdited()
    {
        return in_array($this->status, ['draft', 'pending']);
    }

    public function canBeDeleted()
    {
        return in_array($this->status, ['draft', 'rejected']);
    }

    public function canBeApproved()
    {
        return $this->status === 'pending';
    }

    public function canBePaid()
    {
        return $this->status === 'approved' && !$this->payment_date;
    }

    public static function getTotalByCategory($categoryId, $startDate = null, $endDate = null)
    {
        $query = static::where('category_id', $categoryId)
                      ->where('status', 'paid');

        if ($startDate && $endDate) {
            $query->whereBetween('expense_date', [$startDate, $endDate]);
        }

        return $query->sum('total_amount');
    }

    public static function getTotalBySupplier($supplierId, $startDate = null, $endDate = null)
    {
        $query = static::where('supplier_id', $supplierId)
                      ->where('status', 'paid');

        if ($startDate && $endDate) {
            $query->whereBetween('expense_date', [$startDate, $endDate]);
        }

        return $query->sum('total_amount');
    }

    public static function getMonthlyTotals($year = null)
    {
        $year = $year ?? now()->year;
        
        $expenses = static::selectRaw('MONTH(expense_date) as month, SUM(total_amount) as total')
                          ->whereYear('expense_date', $year)
                          ->where('status', 'paid')
                          ->groupBy('month')
                          ->orderBy('month')
                          ->get();

        $monthlyTotals = array_fill(1, 12, 0);
        
        foreach ($expenses as $expense) {
            $monthlyTotals[$expense->month] = $expense->total;
        }

        return $monthlyTotals;
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($expense) {
            if (!$expense->expense_number) {
                $expense->expense_number = $expense->generateExpenseNumber();
            }
            
            if (!$expense->total_amount) {
                $expense->total_amount = $expense->amount + ($expense->tax_amount ?? 0);
            }
            
            $expense->created_by = auth()->id();
        });
        
        static::updating(function ($expense) {
            $expense->updated_by = auth()->id();
        });
    }
}
