<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Payable extends Model
{
    use HasFactory;

    protected $fillable = [
        'supplier_id',
        'purchase_id',
        'invoice_number',
        'our_reference',
        'invoice_date',
        'due_date',
        'original_amount',
        'paid_amount',
        'remaining_amount',
        'status',
        'notes',
        'payment_history',
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'original_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'payment_history' => 'array',
    ];

    /**
     * Get the supplier
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Scope for pending payables
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for overdue payables
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now()->toDateString())
                    ->whereIn('status', ['pending', 'partial']);
    }

    /**
     * Scope for paid payables
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Check if payable is overdue
     */
    public function isOverdue()
    {
        return $this->due_date < now()->toDateString() && 
               in_array($this->status, ['pending', 'partial']);
    }

    /**
     * Get days overdue
     */
    public function getDaysOverdue()
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return Carbon::parse($this->due_date)->diffInDays(now());
    }

    /**
     * Record payment
     */
    public function recordPayment($amount, $paymentMethod = 'cash', $notes = null)
    {
        if ($amount <= 0) {
            throw new \Exception('مبلغ الدفع يجب أن يكون أكبر من صفر');
        }

        if ($amount > $this->remaining_amount) {
            throw new \Exception('مبلغ الدفع أكبر من المبلغ المتبقي');
        }

        // Update amounts
        $this->paid_amount += $amount;
        $this->remaining_amount -= $amount;

        // Update status
        if ($this->remaining_amount <= 0.01) {
            $this->status = 'paid';
            $this->remaining_amount = 0;
        } else {
            $this->status = 'partial';
        }

        // Add to payment history
        $paymentHistory = $this->payment_history ?? [];
        $paymentHistory[] = [
            'date' => now()->toDateString(),
            'amount' => $amount,
            'method' => $paymentMethod,
            'notes' => $notes,
            'recorded_at' => now()->toDateTimeString(),
        ];
        $this->payment_history = $paymentHistory;

        $this->save();

        return true;
    }

    /**
     * Get status label
     */
    public function getStatusLabel()
    {
        $statuses = [
            'pending' => 'معلق',
            'partial' => 'مدفوع جزئياً',
            'paid' => 'مدفوع',
            'overdue' => 'متأخر',
            'cancelled' => 'ملغي',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Get status color
     */
    public function getStatusColor()
    {
        if ($this->isOverdue()) {
            return 'bg-red-100 text-red-800';
        }

        $colors = [
            'pending' => 'bg-yellow-100 text-yellow-800',
            'partial' => 'bg-blue-100 text-blue-800',
            'paid' => 'bg-green-100 text-green-800',
            'cancelled' => 'bg-gray-100 text-gray-800',
        ];

        return $colors[$this->status] ?? 'bg-gray-100 text-gray-800';
    }
}
