@extends('layouts.main')

@section('title', 'إنشاء فاتورة مشتريات')

@section('content')
<div class="space-y-6" x-data="createPurchase()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إنشاء فاتورة مشتريات</h1>
            <p class="text-gray-600 dark:text-gray-400">إنشاء فاتورة مشتريات جديدة من المورد</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('purchases.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                إلغاء
            </a>
            <button @click="saveDraft()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg">
                حفظ كمسودة
            </button>
            <button @click="savePurchase()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                حفظ الفاتورة
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Purchase Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Supplier & Invoice Info -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات الفاتورة</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الفاتورة</label>
                        <input type="text" x-model="purchase.number" readonly 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 dark:text-gray-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التاريخ</label>
                        <input type="date" x-model="purchase.date" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المورد</label>
                        <div class="relative">
                            <input type="text" x-model="supplierSearch" @input="searchSuppliers()" @focus="showSupplierDropdown = true"
                                   placeholder="البحث عن مورد أو إضافة مورد جديد..."
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            
                            <!-- Supplier Dropdown -->
                            <div x-show="showSupplierDropdown && filteredSuppliers.length > 0" 
                                 @click.away="showSupplierDropdown = false"
                                 class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                                <template x-for="supplier in filteredSuppliers" :key="supplier.id">
                                    <div @click="selectSupplier(supplier)" 
                                         class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                                        <div class="font-medium text-gray-900 dark:text-gray-100" x-text="supplier.name"></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400" x-text="supplier.phone"></div>
                                    </div>
                                </template>
                                <div @click="addNewSupplier()" class="px-4 py-2 border-t border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-blue-600 dark:text-blue-400">
                                    + إضافة مورد جديد
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم فاتورة المورد</label>
                        <input type="text" x-model="purchase.supplier_invoice" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الاستحقاق</label>
                        <input type="date" x-model="purchase.due_date" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">المنتجات</h3>
                    <button @click="showProductModal = true" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                        إضافة منتج
                    </button>
                </div>

                <!-- Products Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">المنتج</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">سعر الشراء</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الكمية</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الإجمالي</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <template x-for="(item, index) in purchase.items" :key="index">
                                <tr>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="item.name"></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400" x-text="item.sku"></div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <input type="number" x-model="item.cost_price" @input="calculateItemTotal(index)" step="0.01"
                                               class="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-gray-100">
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <input type="number" x-model="item.quantity" @input="calculateItemTotal(index)" min="1"
                                               class="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-gray-100">
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        ₪<span x-text="item.total.toFixed(2)"></span>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <button @click="removeItem(index)" class="text-red-600 hover:text-red-900 dark:text-red-400">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                            </template>
                            <tr x-show="purchase.items.length === 0">
                                <td colspan="5" class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                                    لم يتم إضافة أي منتجات بعد
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Notes -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">ملاحظات</h3>
                <textarea x-model="purchase.notes" rows="3" placeholder="ملاحظات إضافية..."
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
            </div>
        </div>

        <!-- Purchase Summary -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm sticky top-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">ملخص الفاتورة</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">₪<span x-text="subtotal.toFixed(2)"></span></span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">الخصم:</span>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <input type="number" x-model="purchase.discount" @input="calculateTotals()" min="0" step="0.01"
                                   class="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-gray-100">
                            <select x-model="purchase.discountType" @change="calculateTotals()"
                                    class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-gray-100">
                                <option value="amount">₪</option>
                                <option value="percentage">%</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الضريبة (16%):</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">₪<span x-text="tax.toFixed(2)"></span></span>
                    </div>
                    
                    <hr class="border-gray-200 dark:border-gray-700">
                    
                    <div class="flex justify-between text-lg font-bold">
                        <span class="text-gray-900 dark:text-gray-100">الإجمالي:</span>
                        <span class="text-blue-600 dark:text-blue-400">₪<span x-text="total.toFixed(2)"></span></span>
                    </div>
                </div>

                <!-- Payment Terms -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">شروط الدفع</label>
                    <select x-model="purchase.paymentTerms" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="cash">نقدي</option>
                        <option value="30_days">30 يوم</option>
                        <option value="60_days">60 يوم</option>
                        <option value="90_days">90 يوم</option>
                    </select>
                </div>

                <!-- Status -->
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                    <select x-model="purchase.status" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="pending">معلقة</option>
                        <option value="received">مستلمة</option>
                        <option value="cancelled">ملغية</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Selection Modal -->
    <div x-show="showProductModal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" @click="showProductModal = false">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">اختيار منتج</h3>
                    
                    <div class="mb-4">
                        <input type="text" x-model="productSearch" @input="searchProducts()" 
                               placeholder="البحث في المنتجات..."
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    </div>

                    <div class="max-h-60 overflow-y-auto">
                        <template x-for="product in filteredProducts" :key="product.id">
                            <div @click="addProduct(product)" 
                                 class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg mb-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                <div class="font-medium text-gray-900 dark:text-gray-100" x-text="product.name"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <span x-text="product.sku"></span> - سعر البيع: ₪<span x-text="product.price"></span>
                                </div>
                                <div class="text-xs text-gray-400 dark:text-gray-500">
                                    المخزون الحالي: <span x-text="product.stock"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="showProductModal = false" 
                            class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-100 dark:border-gray-500">
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function createPurchase() {
    return {
        purchase: {
            number: 'PUR-' + Date.now(),
            date: new Date().toISOString().split('T')[0],
            supplier: null,
            supplier_invoice: '',
            due_date: '',
            items: [],
            notes: '',
            discount: 0,
            discountType: 'amount',
            paymentTerms: 'cash',
            status: 'pending'
        },
        supplierSearch: '',
        showSupplierDropdown: false,
        filteredSuppliers: [],
        suppliers: [
            { id: 1, name: 'شركة التقنية المتقدمة', phone: '02-123-4567', email: '<EMAIL>' },
            { id: 2, name: 'مؤسسة الإلكترونيات الحديثة', phone: '02-234-5678', email: '<EMAIL>' },
            { id: 3, name: 'شركة قطع الغيار المحدودة', phone: '02-345-6789', email: '<EMAIL>' }
        ],
        showProductModal: false,
        productSearch: '',
        filteredProducts: [],
        products: [
            { id: 1, name: 'كابل USB-C', sku: 'CAB-001', price: 25.00, stock: 50 },
            { id: 2, name: 'شاحن سريع 20W', sku: 'CHR-001', price: 85.00, stock: 30 },
            { id: 3, name: 'جراب حماية iPhone', sku: 'CAS-001', price: 45.00, stock: 25 },
            { id: 4, name: 'سماعات بلوتوث', sku: 'HED-001', price: 120.00, stock: 15 },
            { id: 5, name: 'شاشة Samsung A52', sku: 'SCR-001', price: 250.00, stock: 8 },
            { id: 6, name: 'بطارية iPhone 12', sku: 'BAT-001', price: 180.00, stock: 12 }
        ],
        subtotal: 0,
        tax: 0,
        total: 0,

        init() {
            this.filteredSuppliers = [...this.suppliers];
            this.filteredProducts = [...this.products];
            this.calculateTotals();
        },

        searchSuppliers() {
            if (this.supplierSearch.length === 0) {
                this.filteredSuppliers = [...this.suppliers];
            } else {
                this.filteredSuppliers = this.suppliers.filter(supplier =>
                    supplier.name.toLowerCase().includes(this.supplierSearch.toLowerCase()) ||
                    supplier.phone.includes(this.supplierSearch)
                );
            }
        },

        selectSupplier(supplier) {
            this.purchase.supplier = supplier;
            this.supplierSearch = supplier.name;
            this.showSupplierDropdown = false;
        },

        addNewSupplier() {
            // Open supplier creation modal or redirect
            alert('سيتم إضافة نافذة إنشاء مورد جديد');
            this.showSupplierDropdown = false;
        },

        searchProducts() {
            if (this.productSearch.length === 0) {
                this.filteredProducts = [...this.products];
            } else {
                this.filteredProducts = this.products.filter(product =>
                    product.name.toLowerCase().includes(this.productSearch.toLowerCase()) ||
                    product.sku.toLowerCase().includes(this.productSearch.toLowerCase())
                );
            }
        },

        addProduct(product) {
            const existingItem = this.purchase.items.find(item => item.id === product.id);
            
            if (existingItem) {
                existingItem.quantity += 1;
                this.calculateItemTotal(this.purchase.items.indexOf(existingItem));
            } else {
                this.purchase.items.push({
                    id: product.id,
                    name: product.name,
                    sku: product.sku,
                    cost_price: product.price * 0.7, // Default cost price (70% of selling price)
                    quantity: 1,
                    total: product.price * 0.7
                });
            }
            
            this.showProductModal = false;
            this.productSearch = '';
            this.filteredProducts = [...this.products];
            this.calculateTotals();
        },

        removeItem(index) {
            this.purchase.items.splice(index, 1);
            this.calculateTotals();
        },

        calculateItemTotal(index) {
            const item = this.purchase.items[index];
            item.total = item.cost_price * item.quantity;
            this.calculateTotals();
        },

        calculateTotals() {
            this.subtotal = this.purchase.items.reduce((sum, item) => sum + item.total, 0);
            
            let discountAmount = 0;
            if (this.purchase.discountType === 'percentage') {
                discountAmount = this.subtotal * (this.purchase.discount / 100);
            } else {
                discountAmount = this.purchase.discount;
            }
            
            const afterDiscount = this.subtotal - discountAmount;
            this.tax = afterDiscount * 0.16; // 16% tax
            this.total = afterDiscount + this.tax;
        },

        saveDraft() {
            alert('تم حفظ الفاتورة كمسودة');
        },

        savePurchase() {
            if (this.purchase.items.length === 0) {
                alert('يجب إضافة منتج واحد على الأقل');
                return;
            }
            
            if (!this.purchase.supplier) {
                alert('يجب اختيار مورد');
                return;
            }
            
            alert('تم حفظ فاتورة المشتريات بنجاح');
            window.location.href = '/purchases';
        }
    }
}
</script>
@endpush
@endsection
