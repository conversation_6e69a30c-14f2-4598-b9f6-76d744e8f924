@extends('layouts.main')

@section('title', 'إنشاء طلب صيانة جديد')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إنشاء طلب صيانة جديد</h1>
            <p class="text-gray-600 dark:text-gray-400">تسجيل جهاز جديد للصيانة والإصلاح</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('repairs.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                إلغاء
            </a>
            <button type="submit" form="repair-form" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                حفظ الطلب
            </button>
        </div>
    </div>

    <form id="repair-form" action="{{ route('repairs.store') }}" method="POST" enctype="multipart/form-data">
        @csrf

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Form -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Customer Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات العميل</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="md:col-span-2">
                            <label for="customer_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العميل</label>
                            <select name="customer_id" id="customer_id" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="">اختر العميل</option>
                                @foreach($customers as $customer)
                                    <option value="{{ $customer->id }}">
                                        {{ $customer->first_name }} {{ $customer->last_name }}
                                        @if($customer->company_name) - {{ $customer->company_name }} @endif
                                        ({{ $customer->phone }})
                                    </option>
                                @endforeach
                            </select>
                            @error('customer_id')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Device Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات الجهاز</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="device_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الجهاز</label>
                            <select name="device_type" id="device_type" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="">اختر نوع الجهاز</option>
                                @foreach($deviceTypes as $type)
                                    <option value="{{ $type }}">{{ $type }}</option>
                                @endforeach
                            </select>
                            @error('device_type')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="device_brand" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الماركة</label>
                            <input type="text" name="device_brand" id="device_brand" required placeholder="مثل: Samsung, Apple, HP"
                                   value="{{ old('device_brand') }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('device_brand')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="device_model" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموديل</label>
                            <input type="text" name="device_model" id="device_model" required placeholder="مثل: Galaxy S21, iPhone 13"
                                   value="{{ old('device_model') }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('device_model')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="device_serial" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الرقم التسلسلي</label>
                            <input type="text" name="device_serial" id="device_serial"
                                   value="{{ old('device_serial') }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('device_serial')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="device_imei" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IMEI (للهواتف)</label>
                            <input type="text" name="device_imei" id="device_imei"
                                   value="{{ old('device_imei') }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('device_imei')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Problem Description -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">وصف المشكلة</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="problem_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وصف المشكلة</label>
                            <textarea name="problem_description" id="problem_description" rows="4" required placeholder="اشرح المشكلة بالتفصيل..."
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">{{ old('problem_description') }}</textarea>
                            @error('problem_description')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="repair_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الإصلاح</label>
                                <select name="repair_type" id="repair_type" required
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                    <option value="">اختر نوع الإصلاح</option>
                                    <option value="warranty" {{ old('repair_type') == 'warranty' ? 'selected' : '' }}>ضمان</option>
                                    <option value="paid" {{ old('repair_type') == 'paid' ? 'selected' : '' }}>مدفوع</option>
                                    <option value="internal" {{ old('repair_type') == 'internal' ? 'selected' : '' }}>داخلي</option>
                                </select>
                                @error('repair_type')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الأولوية</label>
                                <select name="priority" id="priority" required
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                    <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>منخفضة</option>
                                    <option value="normal" {{ old('priority', 'normal') == 'normal' ? 'selected' : '' }}>عادية</option>
                                    <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>عالية</option>
                                    <option value="urgent" {{ old('priority') == 'urgent' ? 'selected' : '' }}>عاجلة</option>
                                </select>
                                @error('priority')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="estimated_cost" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التكلفة المقدرة</label>
                                <input type="number" name="estimated_cost" id="estimated_cost" step="0.01" placeholder="0.00"
                                       value="{{ old('estimated_cost') }}"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                @error('estimated_cost')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="technician_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفني المسؤول</label>
                                <select name="technician_id" id="technician_id"
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                    <option value="">تعيين تلقائي</option>
                                    @foreach($technicians as $technician)
                                        <option value="{{ $technician->id }}" {{ old('technician_id') == $technician->id ? 'selected' : '' }}>
                                            {{ $technician->first_name }} {{ $technician->last_name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('technician_id')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="estimated_completion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الإنجاز المتوقع</label>
                                <input type="date" name="estimated_completion" id="estimated_completion"
                                       value="{{ old('estimated_completion') }}"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                @error('estimated_completion')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات إضافية</h3>

                    <div class="space-y-4">
                        <div>
                            <label for="accessories" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الملحقات المرفقة</label>
                            <input type="text" name="accessories[]" id="accessories" placeholder="مثل: شاحن، سماعات، كفر..."
                                   value="{{ old('accessories.0') }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('accessories')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="condition_on_arrival" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة الجهاز عند الوصول</label>
                            <textarea name="condition_on_arrival" id="condition_on_arrival" rows="3" placeholder="وصف حالة الجهاز عند الاستلام..."
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">{{ old('condition_on_arrival') }}</textarea>
                            @error('condition_on_arrival')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="customer_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات العميل</label>
                            <textarea name="customer_notes" id="customer_notes" rows="3" placeholder="أي ملاحظات من العميل..."
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">{{ old('customer_notes') }}</textarea>
                            @error('customer_notes')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="photos_before" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">صور الجهاز قبل الإصلاح</label>
                            <input type="file" name="photos_before[]" id="photos_before" multiple accept="image/*"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            <p class="text-sm text-gray-500 mt-1">يمكنك اختيار عدة صور (JPEG, PNG, JPG, GIF)</p>
                            @error('photos_before')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@endsection
