-- بيانات تجريبية للمبيعات والمدفوعات لاختبار التقارير المالية

-- إدراج مبيعات تجريبية
INSERT IGNORE INTO `sales` (
    `id`, `sale_number`, `customer_id`, `user_id`, `location_id`, `sale_type`, 
    `status`, `payment_status`, `sale_date`, `completed_at`, `subtotal`, 
    `tax_amount`, `total_amount`, `paid_amount`, `remaining_amount`, 
    `created_at`, `updated_at`
) VALUES 
(1, 'MAIN-000001', 1, 1, 1, 'parts_only', 'completed', 'paid', 
    NOW() - INTERVAL 5 DAY, NOW() - INTERVAL 5 DAY, 400.00, 60.00, 
    460.00, 460.00, 0.00, NOW() - INTERVAL 5 DAY, NOW()),

(2, 'MAIN-000002', 2, 1, 1, 'accessories', 'completed', 'paid', 
    NOW() - INTERVAL 4 DAY, NOW() - INTERVAL 4 DAY, 150.00, 22.50, 
    172.50, 172.50, 0.00, NOW() - INTERVAL 4 DAY, NOW()),

(3, 'MAIN-000003', 3, 1, 1, 'repair_service', 'completed', 'paid', 
    NOW() - INTERVAL 3 DAY, NOW() - INTERVAL 3 DAY, 300.00, 45.00, 
    345.00, 345.00, 0.00, NOW() - INTERVAL 3 DAY, NOW()),

(4, 'MAIN-000004', 1, 1, 1, 'mixed', 'completed', 'paid', 
    NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY, 250.00, 37.50, 
    287.50, 287.50, 0.00, NOW() - INTERVAL 2 DAY, NOW()),

(5, 'MAIN-000005', 2, 1, 1, 'parts_only', 'completed', 'paid', 
    NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, 180.00, 27.00, 
    207.00, 207.00, 0.00, NOW() - INTERVAL 1 DAY, NOW()),

(6, 'MAIN-000006', 3, 1, 1, 'accessories', 'completed', 'paid', 
    NOW(), NOW(), 120.00, 18.00, 138.00, 138.00, 0.00, NOW(), NOW()),

(7, 'MAIN-000007', 1, 1, 1, 'parts_only', 'pending', 'pending', 
    NOW(), NULL, 200.00, 30.00, 230.00, 0.00, 230.00, NOW(), NOW());

-- إدراج عناصر المبيعات
INSERT IGNORE INTO `sale_items` (
    `id`, `sale_id`, `item_type`, `part_id`, `item_name`, `item_code`, 
    `quantity`, `unit_price`, `original_price`, `line_total`, `tax_rate`, 
    `tax_amount`, `created_at`, `updated_at`
) VALUES 
(1, 1, 'part', 1, 'شاشة iPhone 13', 'PART-001', 1, 400.00, 450.00, 400.00, 15.00, 60.00, NOW(), NOW()),
(2, 2, 'part', 3, 'كابل USB-C', 'ACC-001', 6, 25.00, 25.00, 150.00, 15.00, 22.50, NOW(), NOW()),
(3, 3, 'repair_service', NULL, 'إصلاح شاشة', 'SRV-001', 1, 300.00, 300.00, 300.00, 15.00, 45.00, NOW(), NOW()),
(4, 4, 'part', 2, 'بطارية iPhone 12', 'PART-002', 1, 120.00, 120.00, 120.00, 15.00, 18.00, NOW(), NOW()),
(5, 4, 'part', 4, 'شاحن سريع 20W', 'ACC-002', 1, 85.00, 85.00, 85.00, 15.00, 12.75, NOW(), NOW()),
(6, 4, 'repair_service', NULL, 'تركيب بطارية', 'SRV-002', 1, 45.00, 45.00, 45.00, 15.00, 6.75, NOW(), NOW()),
(7, 5, 'part', 2, 'بطارية iPhone 12', 'PART-002', 1, 120.00, 120.00, 120.00, 15.00, 18.00, NOW(), NOW()),
(8, 5, 'part', 3, 'كابل USB-C', 'ACC-001', 2, 25.00, 25.00, 50.00, 15.00, 7.50, NOW(), NOW()),
(9, 5, 'repair_service', NULL, 'فحص عام', 'SRV-003', 1, 10.00, 10.00, 10.00, 15.00, 1.50, NOW(), NOW()),
(10, 6, 'part', 4, 'شاحن سريع 20W', 'ACC-002', 1, 85.00, 85.00, 85.00, 15.00, 12.75, NOW(), NOW()),
(11, 6, 'part', 3, 'كابل USB-C', 'ACC-001', 1, 25.00, 25.00, 25.00, 15.00, 3.75, NOW(), NOW()),
(12, 6, 'repair_service', NULL, 'استشارة فنية', 'SRV-004', 1, 10.00, 10.00, 10.00, 15.00, 1.50, NOW(), NOW()),
(13, 7, 'part', 1, 'شاشة iPhone 13', 'PART-001', 1, 200.00, 450.00, 200.00, 15.00, 30.00, NOW(), NOW());

-- إدراج مدفوعات المبيعات
INSERT IGNORE INTO `sale_payments` (
    `id`, `sale_id`, `payment_number`, `payment_method`, `amount`, `status`, 
    `processed_at`, `created_at`, `updated_at`
) VALUES 
(1, 1, 'PAY-000001', 'cash', 460.00, 'completed', NOW() - INTERVAL 5 DAY, NOW() - INTERVAL 5 DAY, NOW()),
(2, 2, 'PAY-000002', 'card', 172.50, 'completed', NOW() - INTERVAL 4 DAY, NOW() - INTERVAL 4 DAY, NOW()),
(3, 3, 'PAY-000003', 'bank_transfer', 345.00, 'completed', NOW() - INTERVAL 3 DAY, NOW() - INTERVAL 3 DAY, NOW()),
(4, 4, 'PAY-000004', 'cash', 287.50, 'completed', NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 2 DAY, NOW()),
(5, 5, 'PAY-000005', 'card', 207.00, 'completed', NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY, NOW()),
(6, 6, 'PAY-000006', 'cash', 138.00, 'completed', NOW(), NOW(), NOW());

-- تحديث تسلسل الجداول
ALTER TABLE `sales` AUTO_INCREMENT = 8;
ALTER TABLE `sale_items` AUTO_INCREMENT = 14;
ALTER TABLE `sale_payments` AUTO_INCREMENT = 7;
