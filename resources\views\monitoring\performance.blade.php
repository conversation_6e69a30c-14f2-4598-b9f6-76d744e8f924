@extends('layouts.main')

@section('title', 'مراقبة الأداء')

@section('content')
<div class="space-y-6" x-data="performanceMonitoring()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">مراقبة الأداء</h1>
            <p class="text-gray-600 dark:text-gray-400">مراقبة أداء النظام والتنبيهات الذكية</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="refreshData()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                تحديث البيانات
            </button>
            <button @click="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير التقرير
            </button>
        </div>
    </div>

    <!-- System Health Status -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">حالة النظام</p>
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400" x-text="systemHealth.status">ممتاز</p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span>الأداء العام</span>
                    <span x-text="systemHealth.performance + '%'">98%</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                    <div class="bg-green-500 h-2 rounded-full transition-all duration-300" :style="`width: ${systemHealth.performance}%`"></div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">استخدام الخادم</p>
                    <p class="text-2xl font-bold text-blue-600 dark:text-blue-400" x-text="systemHealth.serverUsage + '%'">45%</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span>المعالج</span>
                    <span x-text="systemHealth.cpuUsage + '%'">32%</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                    <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" :style="`width: ${systemHealth.cpuUsage}%`"></div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">استخدام الذاكرة</p>
                    <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400" x-text="systemHealth.memoryUsage + '%'">67%</p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span>متاح</span>
                    <span x-text="(100 - systemHealth.memoryUsage) + '%'">33%</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                    <div class="bg-yellow-500 h-2 rounded-full transition-all duration-300" :style="`width: ${systemHealth.memoryUsage}%`"></div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">المستخدمين النشطين</p>
                    <p class="text-2xl font-bold text-purple-600 dark:text-purple-400" x-text="systemHealth.activeUsers">24</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between text-sm">
                    <span>ذروة اليوم</span>
                    <span x-text="systemHealth.peakUsers">45</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Response Time Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">زمن الاستجابة</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">رسم بياني لزمن الاستجابة</p>
                    <p class="text-xs text-gray-400">متوسط: <span x-text="performanceMetrics.avgResponseTime">120ms</span></p>
                </div>
            </div>
        </div>

        <!-- Error Rate Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معدل الأخطاء</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">رسم بياني لمعدل الأخطاء</p>
                    <p class="text-xs text-gray-400">معدل الأخطاء: <span x-text="performanceMetrics.errorRate">0.2%</span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts and Notifications -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">التنبيهات النشطة</h3>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200" x-text="alerts.length + ' تنبيه'"></span>
        </div>
        
        <div class="space-y-3">
            <template x-for="alert in alerts" :key="alert.id">
                <div class="flex items-start p-4 border rounded-lg" :class="{
                    'border-red-200 bg-red-50 dark:border-red-700 dark:bg-red-900/20': alert.severity === 'critical',
                    'border-yellow-200 bg-yellow-50 dark:border-yellow-700 dark:bg-yellow-900/20': alert.severity === 'warning',
                    'border-blue-200 bg-blue-50 dark:border-blue-700 dark:bg-blue-900/20': alert.severity === 'info'
                }">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5" :class="{
                            'text-red-600 dark:text-red-400': alert.severity === 'critical',
                            'text-yellow-600 dark:text-yellow-400': alert.severity === 'warning',
                            'text-blue-600 dark:text-blue-400': alert.severity === 'info'
                        }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="mr-3 flex-1">
                        <h4 class="text-sm font-medium" :class="{
                            'text-red-800 dark:text-red-200': alert.severity === 'critical',
                            'text-yellow-800 dark:text-yellow-200': alert.severity === 'warning',
                            'text-blue-800 dark:text-blue-200': alert.severity === 'info'
                        }" x-text="alert.title"></h4>
                        <p class="text-sm mt-1" :class="{
                            'text-red-700 dark:text-red-300': alert.severity === 'critical',
                            'text-yellow-700 dark:text-yellow-300': alert.severity === 'warning',
                            'text-blue-700 dark:text-blue-300': alert.severity === 'info'
                        }" x-text="alert.message"></p>
                        <p class="text-xs mt-2 opacity-75" x-text="alert.timestamp"></p>
                    </div>
                    <button @click="dismissAlert(alert.id)" class="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </template>
        </div>

        <div x-show="alerts.length === 0" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد تنبيهات</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">النظام يعمل بشكل طبيعي</p>
        </div>
    </div>

    <!-- System Logs -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">سجل النظام</h3>
            <button @click="clearLogs()" class="text-sm text-red-600 hover:text-red-800 dark:text-red-400">مسح السجل</button>
        </div>
        
        <div class="space-y-2 max-h-64 overflow-y-auto">
            <template x-for="log in systemLogs" :key="log.id">
                <div class="flex items-center justify-between py-2 px-3 text-sm border-b border-gray-100 dark:border-gray-700">
                    <div class="flex items-center">
                        <span class="w-2 h-2 rounded-full mr-3" :class="{
                            'bg-green-500': log.level === 'info',
                            'bg-yellow-500': log.level === 'warning',
                            'bg-red-500': log.level === 'error'
                        }"></span>
                        <span class="text-gray-900 dark:text-gray-100" x-text="log.message"></span>
                    </div>
                    <span class="text-gray-500 dark:text-gray-400 text-xs" x-text="log.timestamp"></span>
                </div>
            </template>
        </div>
    </div>
</div>

@push('scripts')
<script>
function performanceMonitoring() {
    return {
        systemHealth: {
            status: 'ممتاز',
            performance: 98,
            serverUsage: 45,
            cpuUsage: 32,
            memoryUsage: 67,
            activeUsers: 24,
            peakUsers: 45
        },
        performanceMetrics: {
            avgResponseTime: '120ms',
            errorRate: '0.2%'
        },
        alerts: [
            {
                id: 1,
                severity: 'warning',
                title: 'استخدام عالي للذاكرة',
                message: 'استخدام الذاكرة وصل إلى 67%، يُنصح بمراقبة الوضع',
                timestamp: '2024-07-09 14:30'
            },
            {
                id: 2,
                severity: 'info',
                title: 'تحديث النظام متاح',
                message: 'يتوفر تحديث جديد للنظام، يُنصح بالتحديث في أقرب وقت',
                timestamp: '2024-07-09 12:15'
            }
        ],
        systemLogs: [
            {
                id: 1,
                level: 'info',
                message: 'تم تسجيل دخول المستخدم: <EMAIL>',
                timestamp: '14:35:22'
            },
            {
                id: 2,
                level: 'info',
                message: 'تم إنشاء طلب صيانة جديد: REP-001',
                timestamp: '14:32:15'
            },
            {
                id: 3,
                level: 'warning',
                message: 'محاولة دخول فاشلة من IP: *************',
                timestamp: '14:28:45'
            },
            {
                id: 4,
                level: 'info',
                message: 'تم تحديث حالة الطلب REP-002 إلى مكتمل',
                timestamp: '14:25:30'
            }
        ],

        init() {
            this.startRealTimeMonitoring();
        },

        startRealTimeMonitoring() {
            // تحديث البيانات كل 5 ثوانٍ
            setInterval(() => {
                this.updateSystemHealth();
            }, 5000);
        },

        updateSystemHealth() {
            // محاكاة تحديث بيانات الأداء
            this.systemHealth.cpuUsage = Math.floor(Math.random() * 20) + 25;
            this.systemHealth.memoryUsage = Math.floor(Math.random() * 30) + 50;
            this.systemHealth.activeUsers = Math.floor(Math.random() * 10) + 20;
        },

        refreshData() {
            this.updateSystemHealth();
            this.addSystemLog('info', 'تم تحديث بيانات مراقبة الأداء');
        },

        dismissAlert(alertId) {
            this.alerts = this.alerts.filter(alert => alert.id !== alertId);
        },

        clearLogs() {
            if (confirm('هل أنت متأكد من مسح سجل النظام؟')) {
                this.systemLogs = [];
            }
        },

        addSystemLog(level, message) {
            const now = new Date();
            this.systemLogs.unshift({
                id: Date.now(),
                level: level,
                message: message,
                timestamp: now.toLocaleTimeString('ar-EG')
            });
            
            // الاحتفاظ بآخر 50 سجل فقط
            if (this.systemLogs.length > 50) {
                this.systemLogs = this.systemLogs.slice(0, 50);
            }
        },

        exportReport() {
            alert('سيتم تصدير تقرير مراقبة الأداء');
        }
    }
}
</script>
@endpush
@endsection
