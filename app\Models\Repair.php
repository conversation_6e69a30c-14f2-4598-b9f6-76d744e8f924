<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Repair extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'repair_number',
        'customer_id',
        'technician_id',
        'device_type',
        'device_brand',
        'device_model',
        'device_serial',
        'device_imei',
        'problem_description',
        'diagnosis',
        'repair_type',
        'priority',
        'status',
        'estimated_cost',
        'actual_cost',
        'labor_cost',
        'parts_cost',
        'total_cost',
        'estimated_completion',
        'started_at',
        'completed_at',
        'delivered_at',
        'warranty_period',
        'warranty_expires_at',
        'customer_notes',
        'technician_notes',
        'internal_notes',
        'accessories',
        'condition_on_arrival',
        'test_results',
        'repair_steps',
        'quality_check',
        'customer_signature',
        'photos_before',
        'photos_after',
        'is_warranty_repair',
        'original_repair_id',
        'payment_status',
        'payment_method',
        'discount_amount',
        'tax_amount',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'labor_cost' => 'decimal:2',
        'parts_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'estimated_completion' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'delivered_at' => 'datetime',
        'warranty_expires_at' => 'datetime',
        'accessories' => 'array',
        'repair_steps' => 'array',
        'photos_before' => 'array',
        'photos_after' => 'array',
        'is_warranty_repair' => 'boolean'
    ];

    protected $dates = [
        'estimated_completion',
        'started_at',
        'completed_at',
        'delivered_at',
        'warranty_expires_at',
        'deleted_at'
    ];

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function technician()
    {
        return $this->belongsTo(Technician::class);
    }

    public function parts()
    {
        return $this->belongsToMany(Part::class, 'repair_parts')
            ->withPivot('quantity', 'unit_price', 'total_price')
            ->withTimestamps();
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function statusHistory()
    {
        return $this->hasMany(RepairStatusHistory::class);
    }

    public function timeEntries()
    {
        return $this->hasMany(TimeEntry::class);
    }

    public function schedules()
    {
        return $this->hasMany(Schedule::class);
    }

    public function workOrders()
    {
        return $this->hasMany(WorkOrder::class);
    }

    public function originalRepair()
    {
        return $this->belongsTo(Repair::class, 'original_repair_id');
    }

    public function warrantyRepairs()
    {
        return $this->hasMany(Repair::class, 'original_repair_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function warranty()
    {
        return $this->hasOne(Warranty::class);
    }

    public function expenses()
    {
        return $this->morphMany(Expense::class, 'expensable');
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'related');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'repair_categories');
    }

    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'pending' => ['class' => 'badge-warning', 'text' => 'في الانتظار'],
            'diagnosed' => ['class' => 'badge-info', 'text' => 'تم التشخيص'],
            'in_progress' => ['class' => 'badge-primary', 'text' => 'قيد الإصلاح'],
            'waiting_parts' => ['class' => 'badge-secondary', 'text' => 'انتظار قطع غيار'],
            'completed' => ['class' => 'badge-success', 'text' => 'مكتمل'],
            'delivered' => ['class' => 'badge-success', 'text' => 'تم التسليم'],
            'cancelled' => ['class' => 'badge-danger', 'text' => 'ملغي'],
            'on_hold' => ['class' => 'badge-warning', 'text' => 'معلق']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getPriorityBadgeAttribute()
    {
        $priorities = [
            'low' => ['class' => 'badge-success', 'text' => 'منخفضة'],
            'normal' => ['class' => 'badge-info', 'text' => 'عادية'],
            'high' => ['class' => 'badge-warning', 'text' => 'عالية'],
            'urgent' => ['class' => 'badge-danger', 'text' => 'عاجلة']
        ];

        return $priorities[$this->priority] ?? ['class' => 'badge-info', 'text' => 'عادية'];
    }

    public function getPaymentStatusBadgeAttribute()
    {
        $statuses = [
            'pending' => ['class' => 'badge-warning', 'text' => 'في الانتظار'],
            'partial' => ['class' => 'badge-info', 'text' => 'جزئي'],
            'paid' => ['class' => 'badge-success', 'text' => 'مدفوع'],
            'refunded' => ['class' => 'badge-secondary', 'text' => 'مسترد']
        ];

        return $statuses[$this->payment_status] ?? ['class' => 'badge-warning', 'text' => 'في الانتظار'];
    }

    public function getDeviceFullNameAttribute()
    {
        return trim($this->device_brand . ' ' . $this->device_model);
    }

    public function getDurationAttribute()
    {
        if ($this->started_at && $this->completed_at) {
            return $this->started_at->diffForHumans($this->completed_at, true);
        }
        
        if ($this->started_at) {
            return $this->started_at->diffForHumans(now(), true);
        }
        
        return null;
    }

    public function getIsOverdueAttribute()
    {
        return $this->estimated_completion && 
               $this->estimated_completion->isPast() && 
               !in_array($this->status, ['completed', 'delivered', 'cancelled']);
    }

    public function getWarrantyStatusAttribute()
    {
        if (!$this->warranty_expires_at) {
            return 'no_warranty';
        }
        
        if ($this->warranty_expires_at->isFuture()) {
            return 'active';
        }
        
        return 'expired';
    }

    public function getStatusLabelAttribute()
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'diagnosed' => 'تم التشخيص',
            'in_progress' => 'قيد التنفيذ',
            'waiting_parts' => 'انتظار قطع غيار',
            'completed' => 'مكتمل',
            'delivered' => 'تم التسليم',
            'cancelled' => 'ملغي',
            'on_hold' => 'معلق'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'bg-yellow-100 text-yellow-800',
            'diagnosed' => 'bg-blue-100 text-blue-800',
            'in_progress' => 'bg-orange-100 text-orange-800',
            'waiting_parts' => 'bg-purple-100 text-purple-800',
            'completed' => 'bg-green-100 text-green-800',
            'delivered' => 'bg-teal-100 text-teal-800',
            'cancelled' => 'bg-red-100 text-red-800',
            'on_hold' => 'bg-gray-100 text-gray-800'
        ];

        return $colors[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    public function getPriorityLabelAttribute()
    {
        $priorities = [
            'low' => 'منخفضة',
            'normal' => 'عادية',
            'high' => 'عالية',
            'urgent' => 'عاجلة'
        ];

        return $priorities[$this->priority] ?? $this->priority;
    }

    public function getPriorityColorAttribute()
    {
        $colors = [
            'low' => 'bg-gray-100 text-gray-800',
            'normal' => 'bg-blue-100 text-blue-800',
            'high' => 'bg-orange-100 text-orange-800',
            'urgent' => 'bg-red-100 text-red-800'
        ];

        return $colors[$this->priority] ?? 'bg-blue-100 text-blue-800';
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeOverdue($query)
    {
        return $query->where('estimated_completion', '<', now())
                    ->whereNotIn('status', ['completed', 'delivered', 'cancelled']);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByTechnician($query, $technicianId)
    {
        return $query->where('technician_id', $technicianId);
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeWarrantyActive($query)
    {
        return $query->where('warranty_expires_at', '>', now());
    }

    // Methods
    public function generateRepairNumber()
    {
        $prefix = 'REP';
        $year = now()->year;
        $month = now()->format('m');
        
        $lastRepair = static::whereYear('created_at', $year)
                           ->whereMonth('created_at', now()->month)
                           ->orderBy('id', 'desc')
                           ->first();
        
        $sequence = $lastRepair ? (int)substr($lastRepair->repair_number, -4) + 1 : 1;
        
        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function updateStatus($status, $notes = null)
    {
        $oldStatus = $this->status;
        
        $this->update([
            'status' => $status,
            'updated_by' => auth()->id()
        ]);
        
        // Record status change
        $this->statusHistory()->create([
            'old_status' => $oldStatus,
            'new_status' => $status,
            'notes' => $notes,
            'changed_by' => auth()->id(),
            'changed_at' => now()
        ]);
        
        // Update timestamps based on status
        if ($status === 'in_progress' && !$this->started_at) {
            $this->update(['started_at' => now()]);
        } elseif ($status === 'completed' && !$this->completed_at) {
            $this->update(['completed_at' => now()]);
        } elseif ($status === 'delivered' && !$this->delivered_at) {
            $this->update(['delivered_at' => now()]);
        }
    }

    public function calculateTotalCost()
    {
        $partsCost = $this->parts->sum('pivot.total_price');
        $totalCost = $this->labor_cost + $partsCost - $this->discount_amount + $this->tax_amount;
        
        $this->update([
            'parts_cost' => $partsCost,
            'total_cost' => $totalCost
        ]);
        
        return $totalCost;
    }

    public function addPart($partId, $quantity, $unitPrice = null)
    {
        $part = Part::find($partId);
        if (!$part) {
            return false;
        }
        
        $price = $unitPrice ?? $part->selling_price;
        $totalPrice = $quantity * $price;
        
        $this->parts()->attach($partId, [
            'quantity' => $quantity,
            'unit_price' => $price,
            'total_price' => $totalPrice
        ]);
        
        // Update part stock
        $part->decrement('stock_quantity', $quantity);
        
        $this->calculateTotalCost();
        
        return true;
    }

    public function removePart($partId)
    {
        $pivot = $this->parts()->where('part_id', $partId)->first();
        if ($pivot) {
            // Restore part stock
            $part = Part::find($partId);
            if ($part) {
                $part->increment('stock_quantity', $pivot->pivot->quantity);
            }
            
            $this->parts()->detach($partId);
            $this->calculateTotalCost();
            
            return true;
        }
        
        return false;
    }

    public function isWarrantyValid()
    {
        return $this->warranty_expires_at && $this->warranty_expires_at->isFuture();
    }

    public function canBeEdited()
    {
        return !in_array($this->status, ['delivered', 'cancelled']);
    }

    public function canBeCancelled()
    {
        return !in_array($this->status, ['completed', 'delivered', 'cancelled']);
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($repair) {
            if (!$repair->repair_number) {
                $repair->repair_number = $repair->generateRepairNumber();
            }
            $repair->created_by = auth()->id();
        });
        
        static::updating(function ($repair) {
            $repair->updated_by = auth()->id();
        });
    }
}
