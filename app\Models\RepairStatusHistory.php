<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RepairStatusHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'repair_id',
        'old_status',
        'new_status',
        'notes',
        'changed_by',
        'changed_at'
    ];

    protected $casts = [
        'changed_at' => 'datetime'
    ];

    // Relationships
    public function repair()
    {
        return $this->belongsTo(Repair::class);
    }

    public function changedBy()
    {
        return $this->belongsTo(User::class, 'changed_by');
    }

    // Accessors
    public function getOldStatusBadgeAttribute()
    {
        return $this->getStatusBadge($this->old_status);
    }

    public function getNewStatusBadgeAttribute()
    {
        return $this->getStatusBadge($this->new_status);
    }

    private function getStatusBadge($status)
    {
        $statuses = [
            'pending' => ['class' => 'badge-warning', 'text' => 'في الانتظار'],
            'diagnosed' => ['class' => 'badge-info', 'text' => 'تم التشخيص'],
            'in_progress' => ['class' => 'badge-primary', 'text' => 'قيد الإصلاح'],
            'waiting_parts' => ['class' => 'badge-secondary', 'text' => 'انتظار قطع غيار'],
            'completed' => ['class' => 'badge-success', 'text' => 'مكتمل'],
            'delivered' => ['class' => 'badge-success', 'text' => 'تم التسليم'],
            'cancelled' => ['class' => 'badge-danger', 'text' => 'ملغي'],
            'on_hold' => ['class' => 'badge-warning', 'text' => 'معلق']
        ];

        return $statuses[$status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }
}
