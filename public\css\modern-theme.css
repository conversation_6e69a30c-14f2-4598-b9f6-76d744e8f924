/* ===================================
   نظام التصميم الموحد لنظام إدارة مراكز الصيانة
   Modern Theme CSS - Version 2.0
   =================================== */

/* خطوط عربية محسنة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap');

/* ===================================
   متغيرات الألوان والتصميم العصري
   =================================== */
:root {
    /* ألوان القائمة الجانبية */
    --sidebar-bg-light: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    --sidebar-bg-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    --sidebar-border-light: rgba(148, 163, 184, 0.2);
    --sidebar-border-dark: rgba(71, 85, 105, 0.3);
    
    /* ألوان الروابط والتفاعل */
    --link-hover-light: rgba(59, 130, 246, 0.08);
    --link-hover-dark: rgba(59, 130, 246, 0.15);
    --link-active-light: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    --link-active-dark: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    
    /* ألوان النصوص */
    --text-primary-light: #1e293b;
    --text-primary-dark: #f1f5f9;
    --text-secondary-light: #64748b;
    --text-secondary-dark: #94a3b8;
    --text-muted-light: #94a3b8;
    --text-muted-dark: #64748b;
    
    /* ألوان الخلفيات */
    --bg-primary-light: #ffffff;
    --bg-primary-dark: #0f172a;
    --bg-secondary-light: #f8fafc;
    --bg-secondary-dark: #1e293b;
    --bg-tertiary-light: #f1f5f9;
    --bg-tertiary-dark: #334155;
    
    /* ألوان الحدود */
    --border-light: rgba(148, 163, 184, 0.2);
    --border-dark: rgba(71, 85, 105, 0.3);
    --border-hover-light: rgba(59, 130, 246, 0.3);
    --border-hover-dark: rgba(59, 130, 246, 0.5);
    
    /* ألوان الأكسنت والحالات */
    --accent-color: #3b82f6;
    --accent-hover: #2563eb;
    --success-color: #10b981;
    --success-hover: #059669;
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --info-color: #06b6d4;
    --info-hover: #0891b2;
    
    /* تأثيرات Glass Morphism */
    --glass-bg-light: rgba(255, 255, 255, 0.1);
    --glass-bg-dark: rgba(0, 0, 0, 0.1);
    --glass-border-light: rgba(255, 255, 255, 0.2);
    --glass-border-dark: rgba(255, 255, 255, 0.1);
    
    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* الظلال الملونة */
    --shadow-blue: 0 4px 12px rgba(59, 130, 246, 0.15);
    --shadow-green: 0 4px 12px rgba(16, 185, 129, 0.15);
    --shadow-red: 0 4px 12px rgba(239, 68, 68, 0.15);
    --shadow-yellow: 0 4px 12px rgba(245, 158, 11, 0.15);
    
    /* المسافات والأحجام */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* نصف الأقطار */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* الخطوط */
    --font-family-primary: 'Cairo', 'Tajawal', sans-serif;
    --font-family-secondary: 'Tajawal', 'Cairo', sans-serif;
    
    /* أحجام الخطوط */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    
    /* أوزان الخطوط */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    
    /* الانتقالات */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-bounce: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===================================
   إعدادات عامة للصفحة
   =================================== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: 1.6;
    color: var(--text-primary-light);
    background-color: var(--bg-secondary-light);
    direction: rtl;
    text-align: right;
    transition: background-color var(--transition-normal), color var(--transition-normal);
    overflow-x: hidden;
}

.dark body {
    color: var(--text-primary-dark);
    background-color: var(--bg-secondary-dark);
}

/* ===================================
   تحسينات الخطوط والنصوص
   =================================== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-primary);
    font-weight: var(--font-semibold);
    line-height: 1.4;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary-light);
}

.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
    color: var(--text-primary-dark);
}

h1 { font-size: var(--text-3xl); }
h2 { font-size: var(--text-2xl); }
h3 { font-size: var(--text-xl); }
h4 { font-size: var(--text-lg); }
h5 { font-size: var(--text-base); }
h6 { font-size: var(--text-sm); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary-light);
}

.dark p {
    color: var(--text-secondary-dark);
}

/* ===================================
   الحاويات والتخطيط
   =================================== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.main-content {
    background: var(--bg-primary-light);
    min-height: 100vh;
    transition: background-color var(--transition-normal);
}

.dark .main-content {
    background: var(--bg-primary-dark);
}

.content-wrapper {
    padding: var(--spacing-xl);
    max-width: 100%;
}

@media (max-width: 768px) {
    .content-wrapper {
        padding: var(--spacing-md);
    }
}

/* ===================================
   البطاقات والعناصر
   =================================== */
.card {
    background: var(--bg-primary-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-bounce);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.dark .card {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
    box-shadow: var(--shadow-lg);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-hover-light);
}

.dark .card:hover {
    border-color: var(--border-hover-dark);
}

.card-header {
    border-bottom: 1px solid var(--border-light);
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.dark .card-header {
    border-color: var(--border-dark);
}

.card-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary-light);
    margin-bottom: var(--spacing-sm);
}

.dark .card-title {
    color: var(--text-primary-dark);
}

.card-subtitle {
    font-size: var(--text-sm);
    color: var(--text-muted-light);
    margin-bottom: 0;
}

.dark .card-subtitle {
    color: var(--text-muted-dark);
}

/* ===================================
   الأزرار العصرية
   =================================== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-family: var(--font-family-primary);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.btn:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* أزرار أساسية */
.btn-primary {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    color: white;
    border-color: var(--accent-color);
    box-shadow: var(--shadow-blue);
}

.btn-primary:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.btn-primary:active {
    transform: translateY(0) scale(0.98);
}

.btn-secondary {
    background: var(--bg-primary-light);
    color: var(--text-primary-light);
    border-color: var(--border-light);
}

.dark .btn-secondary {
    background: var(--bg-secondary-dark);
    color: var(--text-primary-dark);
    border-color: var(--border-dark);
}

.btn-secondary:hover {
    background: var(--bg-tertiary-light);
    border-color: var(--border-hover-light);
    transform: translateY(-1px);
}

.dark .btn-secondary:hover {
    background: var(--bg-tertiary-dark);
    border-color: var(--border-hover-dark);
}

/* أزرار الحالات */
.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
    color: white;
    border-color: var(--success-color);
    box-shadow: var(--shadow-green);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-hover) 100%);
    color: white;
    border-color: var(--warning-color);
    box-shadow: var(--shadow-yellow);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
    color: white;
    border-color: var(--danger-color);
    box-shadow: var(--shadow-red);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-hover) 100%);
    color: white;
    border-color: var(--info-color);
}

/* أحجام الأزرار */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--text-lg);
}

.btn-xl {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--text-xl);
}

/* أزرار دائرية */
.btn-rounded {
    border-radius: var(--radius-full);
}

/* أزرار شفافة */
.btn-ghost {
    background: transparent;
    border-color: transparent;
}

.btn-ghost:hover {
    background: var(--glass-bg-light);
    border-color: var(--glass-border-light);
}

.dark .btn-ghost:hover {
    background: var(--glass-bg-dark);
    border-color: var(--glass-border-dark);
}

/* ===================================
   النماذج والمدخلات
   =================================== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-family: var(--font-family-primary);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary-light);
    margin-bottom: var(--spacing-sm);
}

.dark .form-label {
    color: var(--text-primary-dark);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-family: var(--font-family-primary);
    font-size: var(--text-sm);
    color: var(--text-primary-light);
    background: var(--bg-primary-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    transition: all var(--transition-bounce);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.dark .form-input,
.dark .form-select,
.dark .form-textarea {
    color: var(--text-primary-dark);
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
    border-color: var(--border-hover-light);
}

.dark .form-input:hover,
.dark .form-select:hover,
.dark .form-textarea:hover {
    border-color: var(--border-hover-dark);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-error {
    color: var(--danger-color);
    font-size: var(--text-xs);
    margin-top: var(--spacing-xs);
}

.form-help {
    color: var(--text-muted-light);
    font-size: var(--text-xs);
    margin-top: var(--spacing-xs);
}

.dark .form-help {
    color: var(--text-muted-dark);
}

/* ===================================
   الجداول العصرية
   =================================== */
.table-container {
    background: var(--bg-primary-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
}

.dark .table-container {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--font-family-primary);
}

.table th {
    background: var(--bg-tertiary-light);
    color: var(--text-primary-light);
    font-weight: var(--font-semibold);
    font-size: var(--text-sm);
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-light);
}

.dark .table th {
    background: var(--bg-tertiary-dark);
    color: var(--text-primary-dark);
    border-color: var(--border-dark);
}

.table td {
    padding: var(--spacing-md);
    color: var(--text-secondary-light);
    font-size: var(--text-sm);
    border-bottom: 1px solid var(--border-light);
    transition: background-color var(--transition-normal);
}

.dark .table td {
    color: var(--text-secondary-dark);
    border-color: var(--border-dark);
}

.table tbody tr:hover {
    background: var(--bg-tertiary-light);
}

.dark .table tbody tr:hover {
    background: var(--bg-tertiary-dark);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* ===================================
   التنبيهات والرسائل
   =================================== */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
    font-family: var(--font-family-primary);
    font-size: var(--text-sm);
    border: 1px solid transparent;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.alert-success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    color: var(--success-color);
    border-color: rgba(16, 185, 129, 0.2);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
    color: var(--warning-color);
    border-color: rgba(245, 158, 11, 0.2);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
    color: var(--danger-color);
    border-color: rgba(239, 68, 68, 0.2);
}

.alert-info {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(8, 145, 178, 0.05) 100%);
    color: var(--info-color);
    border-color: rgba(6, 182, 212, 0.2);
}

/* ===================================
   الشارات والعلامات
   =================================== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-family: var(--font-family-primary);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background: var(--accent-color);
    color: white;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

.badge-info {
    background: var(--info-color);
    color: white;
}

.badge-secondary {
    background: var(--bg-tertiary-light);
    color: var(--text-secondary-light);
}

.dark .badge-secondary {
    background: var(--bg-tertiary-dark);
    color: var(--text-secondary-dark);
}

/* ===================================
   الإحصائيات والمؤشرات
   =================================== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-primary-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-bounce);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.dark .stat-card {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
}

.stat-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--success-color));
}

.stat-value {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--accent-color);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary-light);
    font-weight: var(--font-medium);
}

.dark .stat-label {
    color: var(--text-secondary-dark);
}

.stat-change {
    font-size: var(--text-xs);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* ===================================
   التنقل والصفحات
   =================================== */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    font-size: var(--text-sm);
}

.breadcrumb-item {
    color: var(--text-muted-light);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.dark .breadcrumb-item {
    color: var(--text-muted-dark);
}

.breadcrumb-item:hover {
    color: var(--accent-color);
}

.breadcrumb-item.active {
    color: var(--text-primary-light);
    font-weight: var(--font-medium);
}

.dark .breadcrumb-item.active {
    color: var(--text-primary-dark);
}

.breadcrumb-separator {
    color: var(--text-muted-light);
}

.dark .breadcrumb-separator {
    color: var(--text-muted-dark);
}

/* ===================================
   التحميل والحالات
   =================================== */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

.dark .loading {
    border-color: var(--border-dark);
    border-top-color: var(--accent-color);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.skeleton {
    background: linear-gradient(90deg, var(--bg-tertiary-light) 25%, var(--bg-secondary-light) 50%, var(--bg-tertiary-light) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--radius-md);
}

.dark .skeleton {
    background: linear-gradient(90deg, var(--bg-tertiary-dark) 25%, var(--bg-secondary-dark) 50%, var(--bg-tertiary-dark) 75%);
    background-size: 200% 100%;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===================================
   التصميم المتجاوب
   =================================== */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .content-wrapper {
        padding: var(--spacing-lg);
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }

    .card {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .card {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .table-container {
        overflow-x: auto;
    }

    .table {
        min-width: 600px;
    }
}

@media (max-width: 480px) {
    .content-wrapper {
        padding: var(--spacing-md);
    }

    h1 { font-size: var(--text-2xl); }
    h2 { font-size: var(--text-xl); }
    h3 { font-size: var(--text-lg); }

    .stat-card {
        padding: var(--spacing-lg);
    }

    .stat-value {
        font-size: var(--text-2xl);
    }
}

/* ===================================
   تحسينات إمكانية الوصول
   =================================== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-contrast: high) {
    :root {
        --border-light: #000000;
        --border-dark: #ffffff;
        --text-secondary-light: #000000;
        --text-secondary-dark: #ffffff;
    }
}

/* Focus styles for keyboard navigation */
.btn:focus-visible,
.form-input:focus-visible,
.form-select:focus-visible,
.form-textarea:focus-visible {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* ===================================
   أنماط الطباعة
   =================================== */
@media print {
    .sidebar,
    .btn,
    .alert {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
        break-inside: avoid;
    }

    .table {
        border-collapse: collapse !important;
    }

    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}

/* ===================================
   أنماط مخصصة للمكونات
   =================================== */

/* Header العصري */
.page-header {
    background: var(--bg-primary-light);
    border-bottom: 1px solid var(--border-light);
    padding: var(--spacing-lg) 0;
    margin-bottom: var(--spacing-xl);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.dark .page-header {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
}

.page-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--text-primary-light);
    margin-bottom: var(--spacing-sm);
}

.dark .page-title {
    color: var(--text-primary-dark);
}

.page-subtitle {
    color: var(--text-secondary-light);
    font-size: var(--text-base);
    margin-bottom: 0;
}

.dark .page-subtitle {
    color: var(--text-secondary-dark);
}

/* Action Bar */
.action-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.action-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Search Box */
.search-box {
    position: relative;
    max-width: 400px;
    width: 100%;
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 2.5rem;
    font-family: var(--font-family-primary);
    font-size: var(--text-sm);
    color: var(--text-primary-light);
    background: var(--bg-primary-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-full);
    transition: all var(--transition-bounce);
}

.dark .search-input {
    color: var(--text-primary-dark);
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted-light);
    pointer-events: none;
}

.dark .search-icon {
    color: var(--text-muted-dark);
}

/* Filter Dropdown */
.filter-dropdown {
    position: relative;
    display: inline-block;
}

.filter-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-primary-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    color: var(--text-primary-light);
    font-family: var(--font-family-primary);
    font-size: var(--text-sm);
    cursor: pointer;
    transition: all var(--transition-bounce);
}

.dark .filter-button {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
    color: var(--text-primary-dark);
}

.filter-button:hover {
    border-color: var(--border-hover-light);
    background: var(--bg-tertiary-light);
}

.dark .filter-button:hover {
    border-color: var(--border-hover-dark);
    background: var(--bg-tertiary-dark);
}

.filter-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 200px;
    background: var(--bg-primary-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    margin-top: var(--spacing-xs);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.dark .filter-menu {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
}

.filter-item {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary-light);
    text-decoration: none;
    font-size: var(--text-sm);
    transition: background-color var(--transition-normal);
    border: none;
    background: none;
    text-align: right;
    cursor: pointer;
}

.dark .filter-item {
    color: var(--text-primary-dark);
}

.filter-item:hover {
    background: var(--bg-tertiary-light);
}

.dark .filter-item:hover {
    background: var(--bg-tertiary-dark);
}

.filter-item:first-child {
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.filter-item:last-child {
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-xl);
}

.pagination-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: var(--text-primary-light);
    text-decoration: none;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    transition: all var(--transition-bounce);
}

.dark .pagination-item {
    color: var(--text-primary-dark);
    border-color: var(--border-dark);
}

.pagination-item:hover {
    background: var(--bg-tertiary-light);
    border-color: var(--border-hover-light);
}

.dark .pagination-item:hover {
    background: var(--bg-tertiary-dark);
    border-color: var(--border-hover-dark);
}

.pagination-item.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.pagination-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* ===================================
   تحسينات خاصة بالنظام
   =================================== */

/* Dashboard Cards */
.dashboard-card {
    background: linear-gradient(135deg, var(--bg-primary-light) 0%, var(--bg-tertiary-light) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.dark .dashboard-card {
    background: linear-gradient(135deg, var(--bg-secondary-dark) 0%, var(--bg-tertiary-dark) 100%);
    border-color: var(--border-dark);
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color), var(--success-color), var(--info-color));
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-active .status-dot {
    background: var(--success-color);
}

.status-pending .status-dot {
    background: var(--warning-color);
}

.status-inactive .status-dot {
    background: var(--danger-color);
}

.status-completed .status-dot {
    background: var(--info-color);
}

/* Progress Bars */
.progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary-light);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.dark .progress {
    background: var(--bg-tertiary-dark);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color), var(--success-color));
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
}

.progress-bar.success {
    background: var(--success-color);
}

.progress-bar.warning {
    background: var(--warning-color);
}

.progress-bar.danger {
    background: var(--danger-color);
}

/* ===================================
   مكونات إضافية متقدمة
   =================================== */

/* صندوق البحث المحسن */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: 2.5rem;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-primary-light);
    color: var(--text-primary-light);
    font-size: var(--text-sm);
    font-family: var(--font-family-primary);
    transition: all var(--transition-normal);
}

.dark .search-input {
    border-color: var(--border-dark);
    background: var(--bg-primary-dark);
    color: var(--text-primary-dark);
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
    position: absolute;
    left: var(--spacing-sm);
    color: var(--text-muted-light);
    pointer-events: none;
}

.dark .search-icon {
    color: var(--text-muted-dark);
}

/* مجموعة الإجراءات المحسنة */
.action-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* تحسينات الأداء والرسوم المتحركة */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.will-change-auto {
    will-change: auto;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .action-group {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        margin-bottom: var(--spacing-sm);
    }

    .pagination {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .pagination-item {
        min-width: 35px;
        height: 35px;
        font-size: var(--text-xs);
    }
}

/* تحسينات الطباعة */
@media print {
    .action-group,
    .pagination,
    .search-box {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
        break-inside: avoid;
    }

    .table th,
    .table td {
        border: 1px solid #000 !important;
        padding: 8px !important;
    }

    .btn {
        display: none !important;
    }
}
