<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Location;
use App\Models\Product;
use App\Models\Contact;
use App\Models\User;
use App\Models\ActivityLog;
use App\Models\Sale;
use App\Models\Purchase;
use App\Models\Inventory;

class DashboardController extends Controller
{
    /**
     * إنشاء instance جديد من controller
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * عرض لوحة التحكم الرئيسية
     */
    public function index()
    {
        $user = Auth::user()->load(['company', 'role']);
        $companyId = $user->company_id;

        // إحصائيات أساسية
        $stats = [
            // المواقع
            'total_locations' => Location::where('company_id', $companyId)->count(),
            'active_locations' => Location::where('company_id', $companyId)->where('is_active', true)->count(),

            // المنتجات
            'total_products' => Product::where('company_id', $companyId)->count(),
            'active_products' => Product::where('company_id', $companyId)->where('is_active', true)->count(),
            'low_stock_products' => $this->getLowStockCount($companyId),
            'out_of_stock_products' => $this->getOutOfStockCount($companyId),

            // العملاء والموردين
            'total_customers' => Contact::where('company_id', $companyId)->customers()->count(),
            'total_suppliers' => Contact::where('company_id', $companyId)->suppliers()->count(),
            'active_customers' => Contact::where('company_id', $companyId)->customers()->active()->count(),
            'active_suppliers' => Contact::where('company_id', $companyId)->suppliers()->active()->count(),

            // المستخدمين
            'total_users' => User::where('company_id', $companyId)->count(),
            'active_users' => User::where('company_id', $companyId)->active()->count(),

            // المبيعات والمشتريات
            'today_sales' => Sale::where('company_id', $companyId)
                                ->whereDate('sale_date', today())
                                ->sum('total_amount'),
            'monthly_sales' => Sale::where('company_id', $companyId)
                                 ->whereMonth('sale_date', now()->month)
                                 ->whereYear('sale_date', now()->year)
                                 ->sum('total_amount'),
            'total_sales' => Sale::where('company_id', $companyId)->count(),
            'total_purchases' => Purchase::where('company_id', $companyId)->count(),

            // بيانات وهمية للصيانة والمصروفات (سيتم استبدالها لاحقاً)
            'pending_maintenance' => 25,
            'in_progress_maintenance' => 35,
            'completed_maintenance' => 85,
            'monthly_expenses' => 8900.00,
        ];

        // أحدث الأنشطة
        $recentActivities = ActivityLog::with('user')
            ->whereHas('user', function ($q) use ($companyId) {
                $q->where('company_id', $companyId);
            })
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // المنتجات منخفضة المخزون (مؤقتاً فارغة)
        $lowStockProducts = collect();

        // العملاء مع مستحقات قريبة (مؤقتاً فارغة)
        $upcomingDues = collect();

        // إحصائيات الأنشطة
        $activityStats = ActivityLog::getActivityStats(30);

        return view('dashboard', compact(
            'stats',
            'user',
            'recentActivities',
            'lowStockProducts',
            'upcomingDues',
            'activityStats'
        ));
    }

    /**
     * حساب عدد المنتجات منخفضة المخزون
     */
    private function getLowStockCount($companyId)
    {
        return Product::where('company_id', $companyId)
            ->whereExists(function ($query) {
                $query->select(\DB::raw(1))
                      ->from('inventories')
                      ->whereColumn('inventories.product_id', 'products.id')
                      ->havingRaw('SUM(inventories.quantity) <= products.min_stock_level');
            })
            ->count();
    }

    /**
     * حساب عدد المنتجات نافدة المخزون
     */
    private function getOutOfStockCount($companyId)
    {
        return Product::where('company_id', $companyId)
            ->whereExists(function ($query) {
                $query->select(\DB::raw(1))
                      ->from('inventories')
                      ->whereColumn('inventories.product_id', 'products.id')
                      ->havingRaw('SUM(inventories.quantity) <= 0');
            })
            ->count();
    }
}
