@extends('layouts.main')

@section('title', 'نقطة البيع - POS')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">نقطة البيع</h1>
            <p class="text-gray-600 dark:text-gray-400">
                @if($currentLocation)
                    الموقع الحالي: {{ $currentLocation->name }}
                @else
                    لم يتم تحديد موقع
                @endif
            </p>
        </div>

        <div class="flex flex-wrap gap-2">
            <!-- Location Selector -->
            <div class="relative">
                <select id="locationSelector" class="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 text-gray-900 dark:text-gray-100">
                    @foreach($locations as $location)
                        <option value="{{ $location->id }}" {{ $currentLocation && $currentLocation->id == $location->id ? 'selected' : '' }}>
                            {{ $location->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <a href="{{ route('pos.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                <i class="fas fa-plus mr-2"></i>
                مبيعة جديدة
            </a>

            <button onclick="openQuickSaleModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-bolt mr-2"></i>
                بيع سريع
            </button>
        </div>
    </div>

    <!-- Today's Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100">مبيعات اليوم</p>
                    <p class="text-3xl font-bold">{{ $todaysStats['sales_count'] }}</p>
                    <p class="text-blue-100 text-sm">عملية بيع</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100">إيرادات اليوم</p>
                    <p class="text-3xl font-bold">{{ number_format($todaysStats['revenue'], 2) }}</p>
                    <p class="text-green-100 text-sm">ريال سعودي</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100">مبيعات معلقة</p>
                    <p class="text-3xl font-bold">{{ $todaysStats['pending_sales'] }}</p>
                    <p class="text-orange-100 text-sm">تحتاج معالجة</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات سريعة</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button onclick="openQuickSaleModal()" class="flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                <i class="fas fa-plus-circle text-blue-600 text-2xl mb-2"></i>
                <span class="text-sm font-medium text-blue-800 dark:text-blue-200">بيع سريع</span>
            </button>

            <button onclick="searchCustomer()" class="flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                <i class="fas fa-search text-green-600 text-2xl mb-2"></i>
                <span class="text-sm font-medium text-green-800 dark:text-green-200">بحث عميل</span>
            </button>

            <button onclick="openCashDrawer()" class="flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
                <i class="fas fa-cash-register text-purple-600 text-2xl mb-2"></i>
                <span class="text-sm font-medium text-purple-800 dark:text-purple-200">فتح الدرج</span>
            </button>

            <a href="{{ route('reports.index') }}" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <i class="fas fa-chart-bar text-gray-600 dark:text-gray-400 text-2xl mb-2"></i>
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">التقارير</span>
            </a>
        </div>
    </div>

    <!-- Recent Sales -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">المبيعات الأخيرة</h3>
                <a href="{{ route('sales.index') }}" class="text-blue-600 hover:text-blue-700 text-sm">
                    عرض الكل
                </a>
            </div>
        </div>
        <div class="p-6">
            @if($recentSales->count() > 0)
                <div class="space-y-4">
                    @foreach($recentSales as $sale)
                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-gray-100">{{ $sale->sale_number }}</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $sale->customer ? $sale->customer->first_name . ' ' . $sale->customer->last_name : 'عميل مجهول' }}</p>
                                <p class="text-xs text-gray-400 dark:text-gray-500">{{ $sale->sale_date->diffForHumans() }}</p>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-gray-900 dark:text-gray-100">{{ number_format($sale->total_amount, 2) }} ريال</p>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    مكتملة
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-shopping-cart text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد مبيعات</p>
                    <a href="{{ route('pos.create') }}" class="text-blue-600 hover:text-blue-700 mt-2 inline-block">
                        إنشاء مبيعة جديدة
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
// Quick sale modal functions
function openQuickSaleModal() {
    alert('ميزة البيع السريع قيد التطوير');
}

function searchCustomer() {
    alert('ميزة البحث عن العملاء قيد التطوير');
}

function openCashDrawer() {
    alert('تم فتح درج النقد');
}

// Location selector
document.getElementById('locationSelector').addEventListener('change', function() {
    const locationId = this.value;

    fetch('{{ route("pos.set-location") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ location_id: locationId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تغيير الموقع');
    });
});
</script>
@endpush
@endsection
