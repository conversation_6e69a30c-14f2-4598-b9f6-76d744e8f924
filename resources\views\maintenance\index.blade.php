@extends('layouts.main')

@section('page-title', 'إدارة الصيانة')
@section('page-description', 'تتبع وإدارة طلبات الصيانة')

@section('content')
<div class="content-wrapper animate-fade-in-up" x-data="maintenanceManager()">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">إدارة الصيانة</h1>
                    <p class="page-subtitle">تتبع وإدارة جميع طلبات الصيانة بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="showAddModal = true" class="btn btn-primary hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        طلب صيانة جديد
                    </button>
                    <button @click="exportMaintenance()" class="btn btn-success hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card hover-lift animate-scale-in animate-delay-100">
            <div class="stat-value" style="color: var(--warning-color);" x-text="getStatusCount('pending')">12</div>
            <div class="stat-label">قيد الانتظار</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                يحتاج متابعة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-200">
            <div class="stat-value" style="color: var(--info-color);" x-text="getStatusCount('in_progress')">8</div>
            <div class="stat-label">قيد التنفيذ</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                </svg>
                جاري العمل
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-300">
            <div class="stat-value" style="color: var(--success-color);" x-text="getStatusCount('completed')">45</div>
            <div class="stat-label">مكتملة</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                معدل ممتاز
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-500">
            <div class="stat-value" x-text="formatCurrency(totalRevenue)">2,450,000 د.ع</div>
            <div class="stat-label">إجمالي الإيرادات</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
                +18% هذا الشهر
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card animate-fade-in-up animate-delay-300">
        <div class="card-header">
            <h3 class="card-title">فلترة وبحث طلبات الصيانة</h3>
            <button @click="clearFilters()" class="btn btn-ghost btn-sm hover-scale">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                مسح الفلاتر
            </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <!-- Search -->
            <div class="md:col-span-2 form-group">
                <label class="form-label">البحث</label>
                <div class="search-box">
                    <input type="text"
                           x-model="searchQuery"
                           placeholder="البحث برقم الطلب أو اسم العميل..."
                           class="search-input focus-glow">
                    <svg class="search-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>

            <!-- Status Filter -->
            <div class="form-group">
                <label class="form-label">الحالة</label>
                <select x-model="selectedStatus" class="form-select focus-glow">
                    <option value="">جميع الحالات</option>
                    <option value="pending">قيد الانتظار</option>
                    <option value="in_progress">قيد التنفيذ</option>
                    <option value="waiting_approval">بانتظار الموافقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="delivered">تم التسليم</option>
                    <option value="cancelled">ملغية</option>
                </select>
            </div>

            <!-- Device Type Filter -->
            <div class="form-group">
                <label class="form-label">نوع الجهاز</label>
                <select x-model="selectedDeviceType" class="form-select focus-glow">
                    <option value="">جميع الأجهزة</option>
                    <option value="mobile">موبايل</option>
                    <option value="laptop">لابتوب</option>
                    <option value="tablet">تابلت</option>
                    <option value="desktop">كمبيوتر مكتبي</option>
                </select>
            </div>

            <!-- Date Filter -->
            <div class="form-group">
                <label class="form-label">التاريخ</label>
                <input type="date" x-model="selectedDate" class="form-input focus-glow">
            </div>
        </div>
    </div>

    <!-- Maintenance Orders Table -->
    <div class="table-container animate-fade-in-up animate-delay-500">
        <div class="card-header">
            <h3 class="card-title">جدول طلبات الصيانة</h3>
            <div class="action-group">
                <button @click="refreshOrders()" class="btn btn-ghost btn-sm hover-rotate">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    تحديث
                </button>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>العميل</th>
                        <th>الجهاز</th>
                        <th>العطل</th>
                        <th>الحالة</th>
                        <th>التكلفة</th>
                        <th>التاريخ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="(order, index) in filteredOrders" :key="order.id">
                        <tr class="hover-lift" :class="'animate-fade-in-up animate-delay-' + (index * 100)">
                            <td>
                                <div class="text-sm font-medium text-accent-color" x-text="'#' + order.id"></div>
                            </td>
                            <td>
                                <div class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="order.customer_name"></div>
                                <div class="text-sm text-muted-light dark:text-muted-dark" x-text="order.customer_phone"></div>
                            </td>
                            <td>
                                <div class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="order.device_name"></div>
                                <div class="text-sm text-muted-light dark:text-muted-dark" x-text="getDeviceTypeName(order.device_type)"></div>
                            </td>
                            <td>
                                <div class="text-sm text-primary-light dark:text-primary-dark" x-text="order.issue_description"></div>
                            </td>
                            <td>
                                <span class="badge" :class="getStatusBadgeClass(order.status)" x-text="getStatusName(order.status)"></span>
                            </td>
                            <td class="text-sm font-semibold text-success-color" x-text="order.cost ? formatCurrency(order.cost) : 'غير محدد'"></td>
                            <td class="text-sm text-muted-light dark:text-muted-dark" x-text="formatDate(order.created_at)"></td>
                            <td>
                                <div class="action-group">
                                    <button @click="viewOrder(order)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        عرض
                                    </button>
                                    <button @click="editOrder(order)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        تعديل
                                    </button>
                                    <button @click="sendNotification(order)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V11H1V7.414a2 2 0 01.586-1.414l.707-.707a5 5 0 013.535-1.465z"></path>
                                        </svg>
                                        إشعار
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <!-- Empty State -->
        <div x-show="filteredOrders.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد طلبات صيانة</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">ابدأ بإضافة طلب صيانة جديد</p>
            <div class="mt-6">
                <button @click="showAddModal = true" class="btn-primary">
                    إضافة طلب صيانة جديد
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function maintenanceManager() {
    return {
        searchQuery: '',
        selectedStatus: '',
        selectedDeviceType: '',
        selectedDate: '',
        showAddModal: false,
        
        orders: [
            {
                id: 1001,
                customer_name: 'أحمد محمد علي',
                customer_phone: '07901234567',
                device_name: 'iPhone 13 Pro',
                device_type: 'mobile',
                issue_description: 'شاشة مكسورة',
                status: 'pending',
                cost: 150000,
                created_at: '2024-01-15',
                technician: 'محمد حسن'
            },
            {
                id: 1002,
                customer_name: 'فاطمة أحمد',
                customer_phone: '07801234567',
                device_name: 'Samsung Galaxy S21',
                device_type: 'mobile',
                issue_description: 'لا يشحن',
                status: 'in_progress',
                cost: 85000,
                created_at: '2024-01-14',
                technician: 'علي حسين'
            },
            {
                id: 1003,
                customer_name: 'محمد حسن',
                customer_phone: '07701234567',
                device_name: 'MacBook Pro',
                device_type: 'laptop',
                issue_description: 'بطء في الأداء',
                status: 'completed',
                cost: 120000,
                created_at: '2024-01-13',
                technician: 'سارة أحمد'
            },
            {
                id: 1004,
                customer_name: 'سارة علي',
                customer_phone: '07601234567',
                device_name: 'iPad Air',
                device_type: 'tablet',
                issue_description: 'مشكلة في الصوت',
                status: 'waiting_approval',
                cost: 95000,
                created_at: '2024-01-12',
                technician: 'محمد حسن'
            }
        ],
        
        get filteredOrders() {
            return this.orders.filter(order => {
                const matchesSearch = order.customer_name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                                    order.id.toString().includes(this.searchQuery) ||
                                    order.device_name.toLowerCase().includes(this.searchQuery.toLowerCase());
                const matchesStatus = !this.selectedStatus || order.status === this.selectedStatus;
                const matchesDeviceType = !this.selectedDeviceType || order.device_type === this.selectedDeviceType;
                const matchesDate = !this.selectedDate || order.created_at === this.selectedDate;
                
                return matchesSearch && matchesStatus && matchesDeviceType && matchesDate;
            });
        },
        
        get totalRevenue() {
            return this.orders
                .filter(order => order.status === 'completed')
                .reduce((sum, order) => sum + (order.cost || 0), 0);
        },
        
        getStatusCount(status) {
            return this.orders.filter(order => order.status === status).length;
        },
        
        getStatusName(status) {
            const statuses = {
                'pending': 'قيد الانتظار',
                'in_progress': 'قيد التنفيذ',
                'waiting_approval': 'بانتظار الموافقة',
                'completed': 'مكتملة',
                'delivered': 'تم التسليم',
                'cancelled': 'ملغية'
            };
            return statuses[status] || status;
        },
        
        getStatusColor(status) {
            const colors = {
                'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                'in_progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                'waiting_approval': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
                'completed': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                'delivered': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
            };
            return colors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        },
        
        getDeviceTypeName(type) {
            const types = {
                'mobile': 'موبايل',
                'laptop': 'لابتوب',
                'tablet': 'تابلت',
                'desktop': 'كمبيوتر مكتبي'
            };
            return types[type] || type;
        },
        
        viewOrder(order) {
            alert(`عرض تفاصيل الطلب #${order.id}\nالعميل: ${order.customer_name}\nالجهاز: ${order.device_name}\nالعطل: ${order.issue_description}`);
        },
        
        editOrder(order) {
            alert(`تعديل الطلب #${order.id}`);
        },
        
        sendNotification(order) {
            alert(`إرسال إشعار للعميل ${order.customer_name} حول الطلب #${order.id}`);
        },
        
        exportMaintenance() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري التصدير...
            `;
            button.disabled = true;

            // محاكاة عملية التصدير
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                if (window.showNotification) {
                    window.showNotification('تم تصدير تقرير الصيانة بنجاح!', 'success');
                }
            }, 2000);
        },

        refreshOrders() {
            // إضافة تأثير التحديث
            const button = event.target;
            button.classList.add('animate-spin');

            // محاكاة تحديث البيانات
            setTimeout(() => {
                button.classList.remove('animate-spin');
                if (window.showNotification) {
                    window.showNotification('تم تحديث البيانات', 'info');
                }
            }, 1000);
        },

        clearFilters() {
            this.searchQuery = '';
            this.selectedStatus = '';
            this.selectedDeviceType = '';
            this.selectedDate = '';

            if (window.showNotification) {
                window.showNotification('تم مسح جميع الفلاتر', 'info');
            }
        },

        getStatusBadgeClass(status) {
            const classes = {
                'pending': 'badge-warning',
                'in_progress': 'badge-info',
                'waiting_approval': 'badge-secondary',
                'completed': 'badge-success',
                'delivered': 'badge-success',
                'cancelled': 'badge-danger'
            };
            return classes[status] || 'badge-secondary';
        },
        
        formatCurrency(amount) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'currency',
                currency: 'IQD',
                minimumFractionDigits: 0
            }).format(amount);
        },
        
        formatDate(date) {
            return new Intl.DateTimeFormat('ar-IQ', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            }).format(new Date(date));
        }
    }
}
</script>
@endpush
@endsection
