<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير قطع الغيار</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2d3748;
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        
        .header p {
            color: #718096;
            margin: 0;
            font-size: 14px;
        }
        
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .summary-card h3 {
            margin: 0 0 10px 0;
            color: #2d3748;
            font-size: 18px;
        }
        
        .summary-card p {
            margin: 0;
            color: #4a5568;
            font-size: 14px;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        th {
            background-color: #f7fafc;
            padding: 12px 8px;
            text-align: right;
            font-weight: bold;
            color: #2d3748;
            border-bottom: 2px solid #e2e8f0;
        }
        
        td {
            padding: 10px 8px;
            border-bottom: 1px solid #e2e8f0;
            color: #4a5568;
        }
        
        tr:hover {
            background-color: #f7fafc;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
            min-width: 60px;
        }
        
        .status-available {
            background-color: #c6f6d5;
            color: #22543d;
        }
        
        .status-low {
            background-color: #fef5e7;
            color: #744210;
        }
        
        .status-out {
            background-color: #fed7d7;
            color: #742a2a;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #718096;
            font-size: 12px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            
            .header, .summary-card, .table-container, .footer {
                box-shadow: none;
                border: 1px solid #e2e8f0;
            }
            
            .summary {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>تقرير قطع الغيار</h1>
        <p>تاريخ التقرير: {{ now()->format('Y-m-d H:i') }}</p>
        <p>إجمالي القطع: {{ $parts->count() }}</p>
    </div>

    <!-- Summary -->
    <div class="summary">
        @php
            $totalParts = $parts->count();
            $availableParts = $parts->where('stock_quantity', '>', 0)->count();
            $lowStockParts = $parts->filter(function($part) {
                return $part->stock_quantity > 0 && $part->stock_quantity <= ($part->reorder_point ?? 0);
            })->count();
            $outOfStockParts = $parts->where('stock_quantity', '<=', 0)->count();
            $totalValue = $parts->sum(function($part) {
                return $part->stock_quantity * $part->cost_price;
            });
        @endphp
        
        <div class="summary-card">
            <h3>{{ number_format($totalParts) }}</h3>
            <p>إجمالي القطع</p>
        </div>
        
        <div class="summary-card">
            <h3>{{ number_format($availableParts) }}</h3>
            <p>قطع متوفرة</p>
        </div>
        
        <div class="summary-card">
            <h3>{{ number_format($lowStockParts) }}</h3>
            <p>مخزون منخفض</p>
        </div>
        
        <div class="summary-card">
            <h3>{{ number_format($outOfStockParts) }}</h3>
            <p>نفد المخزون</p>
        </div>
        
        <div class="summary-card">
            <h3>{{ number_format($totalValue, 2) }} ر.س</h3>
            <p>قيمة المخزون</p>
        </div>
    </div>

    <!-- Parts Table -->
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>اسم القطعة</th>
                    <th>رقم القطعة</th>
                    <th>العلامة التجارية</th>
                    <th>الفئة</th>
                    <th>المورد</th>
                    <th>الكمية الحالية</th>
                    <th>نقطة إعادة الطلب</th>
                    <th>سعر التكلفة</th>
                    <th>سعر البيع</th>
                    <th>قيمة المخزون</th>
                    <th>الحالة</th>
                    <th>حالة المخزون</th>
                </tr>
            </thead>
            <tbody>
                @foreach($parts as $part)
                <tr>
                    <td>{{ $part->name }}</td>
                    <td>{{ $part->part_number ?? $part->sku ?? '-' }}</td>
                    <td>{{ $part->brand ?? '-' }}</td>
                    <td>{{ $part->category->name ?? '-' }}</td>
                    <td>{{ $part->supplier->name ?? '-' }}</td>
                    <td>{{ $part->stock_quantity }}</td>
                    <td>{{ $part->reorder_point ?? '-' }}</td>
                    <td>{{ number_format($part->cost_price, 2) }} ر.س</td>
                    <td>{{ number_format($part->selling_price, 2) }} ر.س</td>
                    <td>{{ number_format($part->stock_quantity * $part->cost_price, 2) }} ر.س</td>
                    <td>
                        @switch($part->condition)
                            @case('new') جديد @break
                            @case('refurbished') مجدد @break
                            @case('used') مستعمل @break
                            @case('damaged') تالف @break
                            @default {{ $part->condition }}
                        @endswitch
                    </td>
                    <td>
                        @if($part->stock_quantity <= 0)
                            <span class="status-badge status-out">نفد المخزون</span>
                        @elseif($part->stock_quantity <= ($part->reorder_point ?? 0))
                            <span class="status-badge status-low">مخزون منخفض</span>
                        @else
                            <span class="status-badge status-available">متوفر</span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مراكز الصيانة</p>
        <p>{{ config('app.name') }} - {{ now()->format('Y') }}</p>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            // Give time for styles to load
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
