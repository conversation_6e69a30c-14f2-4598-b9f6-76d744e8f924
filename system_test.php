<?php

/**
 * System Test Script
 * 
 * Comprehensive test of the repair center management system
 */

echo "🧪 اختبار شامل لنظام إدارة مراكز الصيانة\n";
echo "==========================================\n\n";

$errors = [];
$warnings = [];
$success = [];

// Test 1: Check Core Files
echo "1️⃣ فحص الملفات الأساسية...\n";

$coreFiles = [
    'Controllers' => [
        'app/Http/Controllers/RepairController.php',
        'app/Http/Controllers/TechnicianController.php'
    ],
    'Models' => [
        'app/Models/Repair.php',
        'app/Models/Technician.php',
        'app/Models/Customer.php',
        'app/Models/RepairStatusHistory.php'
    ],
    'Views' => [
        'resources/views/repairs/index.blade.php',
        'resources/views/repairs/create.blade.php',
        'resources/views/repairs/show.blade.php',
        'resources/views/repairs/edit.blade.php',
        'resources/views/technicians/index.blade.php',
        'resources/views/technicians/create.blade.php',
        'resources/views/technicians/show.blade.php',
        'resources/views/technicians/edit.blade.php',
        'resources/views/technicians/schedule.blade.php'
    ],
    'Config' => [
        'routes/web.php',
        'app/Http/Kernel.php'
    ]
];

foreach ($coreFiles as $category => $files) {
    echo "   📁 $category:\n";
    foreach ($files as $file) {
        if (file_exists($file)) {
            echo "      ✅ " . basename($file) . "\n";
            $success[] = "$category: " . basename($file);
        } else {
            echo "      ❌ " . basename($file) . " - مفقود\n";
            $errors[] = "$category: " . basename($file) . " مفقود";
        }
    }
}

echo "\n";

// Test 2: Check Routes Configuration
echo "2️⃣ فحص تكوين Routes...\n";

if (file_exists('routes/web.php')) {
    $routesContent = file_get_contents('routes/web.php');
    
    $requiredRoutes = [
        'repairs resource' => "Route::resource('repairs'",
        'technicians resource' => "Route::resource('technicians'",
        'technicians schedule' => "Route::get('technicians/schedule'",
        'RepairController' => 'RepairController::class',
        'TechnicianController' => 'TechnicianController::class'
    ];
    
    foreach ($requiredRoutes as $name => $pattern) {
        if (strpos($routesContent, $pattern) !== false) {
            echo "   ✅ $name route موجود\n";
            $success[] = "Route: $name";
        } else {
            echo "   ❌ $name route مفقود\n";
            $errors[] = "Route: $name مفقود";
        }
    }
} else {
    echo "   ❌ ملف routes/web.php غير موجود\n";
    $errors[] = "ملف routes/web.php غير موجود";
}

echo "\n";

// Test 3: Check Controllers Methods
echo "3️⃣ فحص وظائف Controllers...\n";

$controllerTests = [
    'RepairController' => [
        'file' => 'app/Http/Controllers/RepairController.php',
        'methods' => ['index', 'create', 'store', 'show', 'edit', 'update', 'destroy', 'updateStatus']
    ],
    'TechnicianController' => [
        'file' => 'app/Http/Controllers/TechnicianController.php',
        'methods' => ['index', 'create', 'store', 'show', 'edit', 'update', 'destroy', 'schedule', 'updateAvailability', 'assignRepair']
    ]
];

foreach ($controllerTests as $controllerName => $config) {
    echo "   🎮 $controllerName:\n";
    
    if (file_exists($config['file'])) {
        $content = file_get_contents($config['file']);
        
        foreach ($config['methods'] as $method) {
            if (strpos($content, "public function $method(") !== false) {
                echo "      ✅ $method method\n";
                $success[] = "$controllerName: $method method";
            } else {
                echo "      ❌ $method method مفقود\n";
                $errors[] = "$controllerName: $method method مفقود";
            }
        }
        
        // Check for permission middleware issues
        if (strpos($content, 'permission:') !== false && strpos($content, '// TODO:') === false) {
            echo "      ⚠️  يحتوي على middleware صلاحيات نشطة\n";
            $warnings[] = "$controllerName: middleware صلاحيات نشطة";
        } else {
            echo "      ✅ middleware الصلاحيات مُعطل أو مُعلق\n";
            $success[] = "$controllerName: middleware صحيح";
        }
    } else {
        echo "      ❌ الملف غير موجود\n";
        $errors[] = "$controllerName: الملف غير موجود";
    }
}

echo "\n";

// Test 4: Check Models Structure
echo "4️⃣ فحص هيكل Models...\n";

$modelTests = [
    'Repair' => [
        'file' => 'app/Models/Repair.php',
        'relationships' => ['customer', 'technician', 'statusHistory'],
        'methods' => ['canBeEdited', 'canBeCancelled']
    ],
    'Technician' => [
        'file' => 'app/Models/Technician.php',
        'relationships' => ['user', 'repairs', 'schedules'],
        'methods' => ['isAvailable', 'updateAvailability', 'assignToRepair']
    ]
];

foreach ($modelTests as $modelName => $config) {
    echo "   📊 $modelName:\n";
    
    if (file_exists($config['file'])) {
        $content = file_get_contents($config['file']);
        
        // Check class definition
        if (strpos($content, "class $modelName extends Model") !== false) {
            echo "      ✅ Class definition صحيح\n";
            $success[] = "$modelName: Class definition";
        } else {
            echo "      ❌ Class definition غير صحيح\n";
            $errors[] = "$modelName: Class definition غير صحيح";
        }
        
        // Check relationships
        foreach ($config['relationships'] as $relationship) {
            if (strpos($content, "function $relationship(") !== false) {
                echo "      ✅ $relationship relationship\n";
                $success[] = "$modelName: $relationship relationship";
            } else {
                echo "      ⚠️  $relationship relationship مفقود\n";
                $warnings[] = "$modelName: $relationship relationship مفقود";
            }
        }
        
        // Check methods
        foreach ($config['methods'] as $method) {
            if (strpos($content, "function $method(") !== false) {
                echo "      ✅ $method method\n";
                $success[] = "$modelName: $method method";
            } else {
                echo "      ⚠️  $method method مفقود\n";
                $warnings[] = "$modelName: $method method مفقود";
            }
        }
        
        // Check for duplicate functions
        preg_match_all('/public function (\w+)\s*\(/', $content, $matches);
        $functions = $matches[1];
        $duplicates = array_diff_assoc($functions, array_unique($functions));
        
        if (empty($duplicates)) {
            echo "      ✅ لا توجد دوال مكررة\n";
            $success[] = "$modelName: لا توجد دوال مكررة";
        } else {
            echo "      ❌ دوال مكررة: " . implode(', ', array_unique($duplicates)) . "\n";
            $errors[] = "$modelName: دوال مكررة - " . implode(', ', array_unique($duplicates));
        }
    } else {
        echo "      ❌ الملف غير موجود\n";
        $errors[] = "$modelName: الملف غير موجود";
    }
}

echo "\n";

// Test 5: Check Views Structure
echo "5️⃣ فحص هيكل Views...\n";

$viewCategories = [
    'repairs' => ['index', 'create', 'show', 'edit'],
    'technicians' => ['index', 'create', 'show', 'edit', 'schedule']
];

foreach ($viewCategories as $category => $views) {
    echo "   👁️  $category views:\n";
    
    foreach ($views as $view) {
        $viewPath = "resources/views/$category/$view.blade.php";
        
        if (file_exists($viewPath)) {
            $viewContent = file_get_contents($viewPath);
            
            // Check if extends layout
            if (strpos($viewContent, '@extends') !== false) {
                echo "      ✅ $view.blade.php - يمتد من layout\n";
                $success[] = "$category: $view view صحيح";
            } else {
                echo "      ⚠️  $view.blade.php - لا يمتد من layout\n";
                $warnings[] = "$category: $view view لا يمتد من layout";
            }
            
            // Check for Arabic content
            if (preg_match('/[\x{0600}-\x{06FF}]/u', $viewContent)) {
                echo "      ✅ $view.blade.php - يحتوي على محتوى عربي\n";
                $success[] = "$category: $view view يحتوي على عربي";
            } else {
                echo "      ⚠️  $view.blade.php - لا يحتوي على محتوى عربي\n";
                $warnings[] = "$category: $view view لا يحتوي على عربي";
            }
        } else {
            echo "      ❌ $view.blade.php - مفقود\n";
            $errors[] = "$category: $view view مفقود";
        }
    }
}

echo "\n";

// Test 6: Check Middleware Configuration
echo "6️⃣ فحص تكوين Middleware...\n";

if (file_exists('app/Http/Kernel.php')) {
    $kernelContent = file_get_contents('app/Http/Kernel.php');
    
    if (strpos($kernelContent, "'permission' => \\App\\Http\\Middleware\\CheckPermission::class") !== false) {
        echo "   ✅ Permission middleware مُسجل في Kernel\n";
        $success[] = "Middleware: Permission مُسجل";
    } else {
        echo "   ❌ Permission middleware غير مُسجل في Kernel\n";
        $errors[] = "Middleware: Permission غير مُسجل";
    }
    
    if (file_exists('app/Http/Middleware/CheckPermission.php')) {
        echo "   ✅ CheckPermission middleware موجود\n";
        $success[] = "Middleware: CheckPermission موجود";
    } else {
        echo "   ❌ CheckPermission middleware غير موجود\n";
        $errors[] = "Middleware: CheckPermission غير موجود";
    }
} else {
    echo "   ❌ ملف Kernel.php غير موجود\n";
    $errors[] = "Kernel.php غير موجود";
}

echo "\n";

// Test 7: Check Route Order (Schedule Route Issue)
echo "7️⃣ فحص ترتيب Routes (مشكلة route الجدولة)...\n";

if (file_exists('routes/web.php')) {
    $routesContent = file_get_contents('routes/web.php');
    
    // Find positions of schedule and resource routes
    $schedulePos = strpos($routesContent, "Route::get('technicians/schedule'");
    $resourcePos = strpos($routesContent, "Route::resource('technicians'");
    
    if ($schedulePos !== false && $resourcePos !== false) {
        if ($schedulePos < $resourcePos) {
            echo "   ✅ Schedule route مُعرف قبل resource route\n";
            $success[] = "Routes: ترتيب صحيح";
        } else {
            echo "   ❌ Schedule route مُعرف بعد resource route (سيسبب 404)\n";
            $errors[] = "Routes: ترتيب خاطئ - schedule بعد resource";
        }
    } else {
        if ($schedulePos === false) {
            echo "   ❌ Schedule route غير موجود\n";
            $errors[] = "Routes: Schedule route غير موجود";
        }
        if ($resourcePos === false) {
            echo "   ❌ Resource route غير موجود\n";
            $errors[] = "Routes: Resource route غير موجود";
        }
    }
} else {
    echo "   ❌ ملف routes/web.php غير موجود\n";
    $errors[] = "routes/web.php غير موجود";
}

echo "\n";

// Summary
echo "📊 ملخص نتائج الاختبار\n";
echo "======================\n";

echo "✅ نجح: " . count($success) . " اختبار\n";
echo "⚠️  تحذيرات: " . count($warnings) . " تحذير\n";
echo "❌ أخطاء: " . count($errors) . " خطأ\n\n";

if (!empty($errors)) {
    echo "🚨 الأخطاء التي تحتاج إلى إصلاح:\n";
    foreach ($errors as $error) {
        echo "   ❌ $error\n";
    }
    echo "\n";
}

if (!empty($warnings)) {
    echo "⚠️  التحذيرات (غير حرجة):\n";
    foreach ($warnings as $warning) {
        echo "   ⚠️  $warning\n";
    }
    echo "\n";
}

// Final Assessment
$totalTests = count($success) + count($warnings) + count($errors);
$successRate = round((count($success) / $totalTests) * 100, 1);

echo "📈 معدل النجاح: $successRate%\n\n";

if (count($errors) === 0) {
    echo "🎉 النظام جاهز للاستخدام!\n";
    echo "✅ جميع المكونات الأساسية تعمل بشكل صحيح\n";
    echo "✅ لا توجد أخطاء حرجة\n\n";
    
    echo "🚀 للبدء في استخدام النظام:\n";
    echo "1. تأكد من إعداد قاعدة البيانات في .env\n";
    echo "2. شغل: php artisan migrate\n";
    echo "3. شغل: php artisan serve\n";
    echo "4. افتح المتصفح على: http://tareq.test\n\n";
    
    echo "📋 الروابط المتاحة:\n";
    echo "   • قائمة طلبات الصيانة: http://tareq.test/repairs\n";
    echo "   • إضافة طلب جديد: http://tareq.test/repairs/create\n";
    echo "   • قائمة الفنيين: http://tareq.test/technicians\n";
    echo "   • جدولة الفنيين: http://tareq.test/technicians/schedule\n";
    echo "   • إضافة فني جديد: http://tareq.test/technicians/create\n";
} else {
    echo "⚠️  يوجد " . count($errors) . " خطأ يحتاج إلى إصلاح قبل الاستخدام\n";
    echo "📝 راجع قائمة الأخطاء أعلاه وقم بإصلاحها\n";
}

echo "\n🏁 انتهى الاختبار الشامل\n";
