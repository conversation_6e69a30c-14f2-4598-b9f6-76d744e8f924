@extends('layouts.main')

@section('title', 'تعديل الحساب - ' . $account->account_name)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تعديل الحساب</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $account->account_code }} - {{ $account->account_name }}</p>
        </div>
        
        <div class="flex gap-2">
            <a href="{{ route('accounts.show', $account) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تعديل معلومات الحساب</h3>
        </div>
        
        <form action="{{ route('accounts.update', $account) }}" method="POST" class="p-6 space-y-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Account Code -->
                <div>
                    <label for="account_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        رقم الحساب <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="account_code" 
                           name="account_code" 
                           value="{{ old('account_code', $account->account_code) }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('account_code') border-red-500 @enderror"
                           placeholder="مثال: 11101"
                           required>
                    @error('account_code')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Account Name (Arabic) -->
                <div>
                    <label for="account_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        اسم الحساب <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="account_name" 
                           name="account_name" 
                           value="{{ old('account_name', $account->account_name) }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('account_name') border-red-500 @enderror"
                           placeholder="مثال: الصندوق"
                           required>
                    @error('account_name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Account Name (English) -->
                <div>
                    <label for="account_name_en" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        اسم الحساب (إنجليزي)
                    </label>
                    <input type="text" 
                           id="account_name_en" 
                           name="account_name_en" 
                           value="{{ old('account_name_en', $account->account_name_en) }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('account_name_en') border-red-500 @enderror"
                           placeholder="Example: Cash">
                    @error('account_name_en')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Parent Account -->
                <div>
                    <label for="parent_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        الحساب الأب
                    </label>
                    <select name="parent_id" 
                            id="parent_id"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('parent_id') border-red-500 @enderror">
                        <option value="">حساب رئيسي</option>
                        @foreach($parentAccounts as $parent)
                            <option value="{{ $parent->id }}" {{ old('parent_id', $account->parent_id) == $parent->id ? 'selected' : '' }}>
                                {{ $parent->account_code }} - {{ $parent->account_name }}
                            </option>
                        @endforeach
                    </select>
                    @error('parent_id')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Account Type -->
                <div>
                    <label for="account_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        نوع الحساب <span class="text-red-500">*</span>
                    </label>
                    <select name="account_type" 
                            id="account_type"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('account_type') border-red-500 @enderror"
                            required>
                        <option value="">اختر نوع الحساب</option>
                        @foreach($accountTypes as $key => $label)
                            <option value="{{ $key }}" {{ old('account_type', $account->account_type) == $key ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('account_type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Account Category -->
                <div>
                    <label for="account_category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        فئة الحساب
                    </label>
                    <input type="text" 
                           id="account_category" 
                           name="account_category" 
                           value="{{ old('account_category', $account->account_category) }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('account_category') border-red-500 @enderror"
                           placeholder="مثال: current_asset">
                    @error('account_category')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Balance Type -->
                <div>
                    <label for="balance_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        طبيعة الرصيد <span class="text-red-500">*</span>
                    </label>
                    <select name="balance_type" 
                            id="balance_type"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('balance_type') border-red-500 @enderror"
                            required>
                        <option value="">اختر طبيعة الرصيد</option>
                        @foreach($balanceTypes as $key => $label)
                            <option value="{{ $key }}" {{ old('balance_type', $account->balance_type) == $key ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('balance_type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    وصف الحساب
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('description') border-red-500 @enderror"
                          placeholder="وصف مختصر للحساب وغرض استخدامه">{{ old('description', $account->description) }}</textarea>
                @error('description')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Current Information Display -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3">المعلومات الحالية</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">الرصيد الافتتاحي:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100 mr-2">
                            {{ number_format($account->opening_balance, 2) }} ريال
                        </span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">الرصيد الحالي:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100 mr-2">
                            {{ number_format($account->current_balance, 2) }} ريال
                        </span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">عدد المعاملات:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100 mr-2">
                            {{ $account->transactions()->count() }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Warning for System Accounts -->
            @if($account->is_system)
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="mr-3">
                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            تحذير: حساب نظام
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                            <p>هذا حساب نظام أساسي. يُنصح بعدم تغيير نوع الحساب أو طبيعة الرصيد لتجنب مشاكل في النظام.</p>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Submit Buttons -->
            <div class="flex justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="{{ route('accounts.show', $account) }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg">
                    إلغاء
                </a>
                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                    <i class="fas fa-save mr-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
// Auto-suggest balance type based on account type
document.getElementById('account_type').addEventListener('change', function() {
    const accountType = this.value;
    const balanceTypeSelect = document.getElementById('balance_type');
    
    // Default balance types for each account type
    const defaultBalanceTypes = {
        'asset': 'debit',
        'expense': 'debit',
        'liability': 'credit',
        'equity': 'credit',
        'revenue': 'credit'
    };
    
    if (defaultBalanceTypes[accountType] && !balanceTypeSelect.value) {
        balanceTypeSelect.value = defaultBalanceTypes[accountType];
    }
});
</script>
@endpush
@endsection
