import './bootstrap';
import '../css/app.css';
import Alpine from 'alpinejs';
import { Chart, registerables } from 'chart.js';

// Register Chart.js components
Chart.register(...registerables);

// Alpine.js setup
window.Alpine = Alpine;

// Dark mode functionality
Alpine.data('darkMode', () => ({
    isDark: localStorage.getItem('darkMode') === 'true' ||
           (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches),

    init() {
        this.updateTheme();
    },

    toggle() {
        this.isDark = !this.isDark;
        this.updateTheme();
        localStorage.setItem('darkMode', this.isDark);
    },

    updateTheme() {
        if (this.isDark) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }
}));

// Sidebar functionality
Alpine.data('sidebar', () => ({
    isOpen: window.innerWidth >= 1024,
    isMobile: window.innerWidth < 1024,

    init() {
        window.addEventListener('resize', () => {
            this.isMobile = window.innerWidth < 1024;
            if (!this.isMobile) {
                this.isOpen = true;
            }
        });
    },

    toggle() {
        this.isOpen = !this.isOpen;
    },

    close() {
        if (this.isMobile) {
            this.isOpen = false;
        }
    }
}));

// Notification system
Alpine.data('notifications', () => ({
    notifications: [],

    add(message, type = 'info', duration = 5000) {
        const id = Date.now();
        const notification = { id, message, type };
        this.notifications.push(notification);

        if (duration > 0) {
            setTimeout(() => {
                this.remove(id);
            }, duration);
        }
    },

    remove(id) {
        this.notifications = this.notifications.filter(n => n.id !== id);
    },

    success(message, duration = 5000) {
        this.add(message, 'success', duration);
    },

    error(message, duration = 7000) {
        this.add(message, 'error', duration);
    },

    warning(message, duration = 6000) {
        this.add(message, 'warning', duration);
    },

    info(message, duration = 5000) {
        this.add(message, 'info', duration);
    }
}));

// Language switcher
Alpine.data('language', () => ({
    current: localStorage.getItem('language') || 'ar',

    init() {
        this.updateDirection();
    },

    switch(lang) {
        this.current = lang;
        localStorage.setItem('language', lang);
        this.updateDirection();
        // Here you would typically reload the page or update content
        window.location.reload();
    },

    updateDirection() {
        document.documentElement.setAttribute('dir', this.current === 'ar' ? 'rtl' : 'ltr');
        document.documentElement.setAttribute('lang', this.current);
    }
}));

// Start Alpine
Alpine.start();

// Global utilities
window.formatCurrency = (amount, currency = 'IQD') => {
    return new Intl.NumberFormat('ar-IQ', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount);
};

window.formatDate = (date, locale = 'ar-IQ') => {
    return new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
};

window.formatNumber = (number, locale = 'ar-IQ') => {
    return new Intl.NumberFormat(locale).format(number);
};
