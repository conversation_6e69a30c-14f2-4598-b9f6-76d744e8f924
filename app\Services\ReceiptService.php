<?php

namespace App\Services;

use App\Models\Sale;
use App\Models\Location;
use Illuminate\Support\Facades\View;

class ReceiptService
{
    /**
     * Generate receipt HTML
     */
    public function generateReceiptHTML(Sale $sale, $format = 'standard')
    {
        $sale->load(['customer', 'items.part', 'payments', 'location', 'user']);
        
        $data = [
            'sale' => $sale,
            'location' => $sale->location,
            'customer' => $sale->customer,
            'items' => $sale->items,
            'payments' => $sale->payments,
            'format' => $format,
            'generated_at' => now(),
        ];

        switch ($format) {
            case 'thermal':
                return View::make('receipts.thermal', $data)->render();
            case 'a4':
                return View::make('receipts.a4', $data)->render();
            default:
                return View::make('receipts.standard', $data)->render();
        }
    }

    /**
     * Generate receipt PDF
     */
    public function generateReceiptPDF(Sale $sale, $format = 'standard')
    {
        $html = $this->generateReceiptHTML($sale, $format);
        
        // Using DomPDF or similar library
        $pdf = app('dompdf.wrapper');
        $pdf->loadHTML($html);
        
        if ($format === 'thermal') {
            $pdf->setPaper([0, 0, 226.77, 841.89], 'portrait'); // 80mm width
        } else {
            $pdf->setPaper('A4', 'portrait');
        }

        return $pdf;
    }

    /**
     * Print receipt
     */
    public function printReceipt(Sale $sale, $format = 'thermal', $printer = null)
    {
        $html = $this->generateReceiptHTML($sale, $format);
        
        // Implementation would depend on the printing system used
        // This could integrate with ESC/POS printers, network printers, etc.
        
        return [
            'success' => true,
            'message' => 'تم إرسال الفاتورة للطباعة',
            'receipt_html' => $html,
        ];
    }

    /**
     * Email receipt
     */
    public function emailReceipt(Sale $sale, $email = null, $format = 'a4')
    {
        $email = $email ?? $sale->customer?->email;
        
        if (!$email) {
            return [
                'success' => false,
                'message' => 'لا يوجد بريد إلكتروني للعميل',
            ];
        }

        try {
            $pdf = $this->generateReceiptPDF($sale, $format);
            
            // Send email with PDF attachment
            \Mail::send('emails.receipt', ['sale' => $sale], function($message) use ($email, $sale, $pdf) {
                $message->to($email)
                        ->subject('فاتورة رقم ' . $sale->sale_number)
                        ->attachData($pdf->output(), "receipt-{$sale->sale_number}.pdf");
            });

            return [
                'success' => true,
                'message' => 'تم إرسال الفاتورة بالبريد الإلكتروني',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الفاتورة: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * SMS receipt
     */
    public function smsReceipt(Sale $sale, $phone = null)
    {
        $phone = $phone ?? $sale->customer?->phone;
        
        if (!$phone) {
            return [
                'success' => false,
                'message' => 'لا يوجد رقم هاتف للعميل',
            ];
        }

        $message = $this->generateSMSMessage($sale);

        try {
            // Implementation would depend on SMS service provider
            // This could integrate with Twilio, local SMS gateway, etc.
            
            return [
                'success' => true,
                'message' => 'تم إرسال الفاتورة عبر الرسائل النصية',
                'sms_content' => $message,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الرسالة: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Generate SMS message
     */
    private function generateSMSMessage(Sale $sale)
    {
        $locationName = $sale->location?->name ?? 'مركز الصيانة';
        
        $message = "{$locationName}\n";
        $message .= "فاتورة رقم: {$sale->sale_number}\n";
        $message .= "التاريخ: " . $sale->sale_date->format('Y-m-d H:i') . "\n";
        $message .= "المبلغ: " . number_format($sale->total_amount, 2) . " ريال\n";
        
        if ($sale->customer) {
            $message .= "العميل: {$sale->customer->full_name}\n";
        }
        
        $message .= "شكراً لثقتكم";

        return $message;
    }

    /**
     * Get receipt data for API
     */
    public function getReceiptData(Sale $sale)
    {
        $sale->load(['customer', 'items.part', 'payments', 'location', 'user']);
        
        return [
            'sale' => [
                'id' => $sale->id,
                'sale_number' => $sale->sale_number,
                'sale_date' => $sale->sale_date->format('Y-m-d H:i:s'),
                'status' => $sale->status,
                'status_label' => $sale->status_label,
                'payment_status' => $sale->payment_status,
                'payment_status_label' => $sale->payment_status_label,
                'subtotal' => $sale->subtotal,
                'tax_amount' => $sale->tax_amount,
                'discount_amount' => $sale->discount_amount,
                'total_amount' => $sale->total_amount,
                'paid_amount' => $sale->paid_amount,
                'change_amount' => $sale->change_amount,
                'remaining_amount' => $sale->remaining_amount,
                'notes' => $sale->notes,
            ],
            'location' => [
                'name' => $sale->location?->name,
                'address' => $sale->location?->full_address,
                'phone' => $sale->location?->phone,
                'email' => $sale->location?->email,
                'tax_number' => $sale->location?->tax_number,
                'receipt_header' => $sale->location?->getReceiptHeader(),
                'receipt_footer' => $sale->location?->getReceiptFooter(),
            ],
            'customer' => $sale->customer ? [
                'name' => $sale->customer->full_name,
                'phone' => $sale->customer->phone,
                'email' => $sale->customer->email,
                'address' => $sale->customer->address,
            ] : null,
            'items' => $sale->items->map(function($item) {
                return [
                    'name' => $item->item_name,
                    'code' => $item->item_code,
                    'description' => $item->item_description,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'discount_amount' => $item->discount_amount,
                    'line_total' => $item->line_total,
                    'tax_amount' => $item->tax_amount,
                ];
            }),
            'payments' => $sale->payments->map(function($payment) {
                return [
                    'payment_method' => $payment->payment_method,
                    'payment_method_label' => $payment->payment_method_label,
                    'amount' => $payment->amount,
                    'status' => $payment->status,
                    'status_label' => $payment->status_label,
                    'reference_number' => $payment->reference_number,
                    'processed_at' => $payment->processed_at?->format('Y-m-d H:i:s'),
                ];
            }),
            'cashier' => [
                'name' => $sale->user?->name,
                'id' => $sale->user?->id,
            ],
            'generated_at' => now()->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Generate QR code for receipt
     */
    public function generateQRCode(Sale $sale)
    {
        $data = [
            'sale_number' => $sale->sale_number,
            'total_amount' => $sale->total_amount,
            'tax_amount' => $sale->tax_amount,
            'sale_date' => $sale->sale_date->format('Y-m-d H:i:s'),
            'location' => $sale->location?->name,
        ];

        $qrData = json_encode($data);
        
        // Generate QR code using a library like SimpleSoftwareIO/simple-qrcode
        // return QrCode::size(150)->generate($qrData);
        
        return base64_encode($qrData); // Placeholder implementation
    }

    /**
     * Validate receipt format
     */
    public function validateFormat($format)
    {
        $validFormats = ['standard', 'thermal', 'a4'];
        return in_array($format, $validFormats);
    }

    /**
     * Get available receipt formats
     */
    public function getAvailableFormats()
    {
        return [
            'standard' => 'قياسي',
            'thermal' => 'طابعة حرارية',
            'a4' => 'A4',
        ];
    }

    /**
     * Get receipt settings for location
     */
    public function getReceiptSettings(Location $location)
    {
        $settings = $location->pos_settings ?? [];
        
        return [
            'auto_print' => $settings['auto_print_receipt'] ?? false,
            'default_format' => $settings['default_receipt_format'] ?? 'thermal',
            'include_qr_code' => $settings['include_qr_code'] ?? true,
            'include_logo' => $settings['include_logo'] ?? true,
            'footer_message' => $location->getReceiptFooter(),
            'header_message' => $location->getReceiptHeader(),
        ];
    }
}
