/* ==========================================================================
   تحسينات إمكانية الوصول (Accessibility Enhancements)
   ========================================================================== */

/* تحسين التركيز */
:focus {
    outline: 3px solid var(--focus-color, #0066cc);
    outline-offset: 2px;
    border-radius: 4px;
}

:focus:not(:focus-visible) {
    outline: none;
}

:focus-visible {
    outline: 3px solid var(--focus-color, #0066cc);
    outline-offset: 2px;
}

/* تحسين التباين */
.high-contrast {
    --text-color: #000000;
    --background-color: #ffffff;
    --border-color: #000000;
    --link-color: #0000ee;
    --visited-link-color: #551a8b;
}

.high-contrast .btn {
    border: 2px solid currentColor;
    background: var(--background-color);
    color: var(--text-color);
}

.high-contrast .card {
    border: 2px solid var(--border-color);
    background: var(--background-color);
}

/* تحسين حجم النص */
.large-text {
    font-size: 1.25em;
    line-height: 1.6;
}

.extra-large-text {
    font-size: 1.5em;
    line-height: 1.8;
}

/* تحسين المسافات */
.increased-spacing {
    padding: 1.5rem;
    margin: 1rem 0;
}

.increased-spacing .btn {
    padding: 1rem 1.5rem;
    margin: 0.5rem;
}

/* تحسين الروابط */
.accessible-link {
    color: var(--link-color, #0066cc);
    text-decoration: underline;
    text-underline-offset: 3px;
    text-decoration-thickness: 2px;
}

.accessible-link:hover,
.accessible-link:focus {
    background-color: rgba(0, 102, 204, 0.1);
    padding: 2px 4px;
    border-radius: 4px;
}

.accessible-link:visited {
    color: var(--visited-link-color, #551a8b);
}

/* تحسين الأزرار */
.accessible-btn {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
    border: 2px solid transparent;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.accessible-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.accessible-btn:active {
    transform: translateY(0);
}

.accessible-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* تحسين النماذج */
.accessible-form-group {
    margin-bottom: 1.5rem;
}

.accessible-label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.accessible-input {
    width: 100%;
    min-height: 44px;
    padding: 12px 16px;
    border: 2px solid var(--border-color, #ccc);
    border-radius: 8px;
    font-size: 16px;
    background: var(--input-background, #fff);
    color: var(--text-color);
}

.accessible-input:focus {
    border-color: var(--focus-color, #0066cc);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.2);
}

.accessible-input:invalid {
    border-color: var(--error-color, #dc3545);
}

.accessible-input:invalid:focus {
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}

/* رسائل الخطأ */
.error-message {
    color: var(--error-color, #dc3545);
    font-size: 14px;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-message::before {
    content: "⚠";
    font-weight: bold;
}

/* رسائل النجاح */
.success-message {
    color: var(--success-color, #28a745);
    font-size: 14px;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.success-message::before {
    content: "✓";
    font-weight: bold;
}

/* تحسين الجداول */
.accessible-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.accessible-table th,
.accessible-table td {
    padding: 12px 16px;
    text-align: right;
    border: 1px solid var(--border-color, #ccc);
}

.accessible-table th {
    background-color: var(--header-background, #f8f9fa);
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.accessible-table tr:nth-child(even) {
    background-color: var(--row-background, #f8f9fa);
}

.accessible-table tr:hover {
    background-color: var(--hover-background, #e9ecef);
}

/* تحسين القوائم */
.accessible-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.accessible-list-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color, #ccc);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.accessible-list-item:last-child {
    border-bottom: none;
}

.accessible-list-item:hover,
.accessible-list-item:focus-within {
    background-color: var(--hover-background, #f8f9fa);
}

/* تحسين النوافذ المنبثقة */
.accessible-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.accessible-modal-content {
    background: var(--background-color, #fff);
    border-radius: 12px;
    padding: 2rem;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.accessible-modal-close {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
}

.accessible-modal-close:hover,
.accessible-modal-close:focus {
    background-color: var(--hover-background, #f8f9fa);
}

/* تحسين التنقل */
.accessible-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem;
    background: var(--nav-background, #f8f9fa);
    border-radius: 8px;
}

.accessible-nav-item {
    padding: 12px 16px;
    border-radius: 6px;
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: all 0.2s ease;
}

.accessible-nav-item:hover,
.accessible-nav-item:focus {
    background-color: var(--primary-color, #0066cc);
    color: white;
}

.accessible-nav-item.active {
    background-color: var(--primary-color, #0066cc);
    color: white;
}

/* تحسين البحث */
.accessible-search {
    position: relative;
    display: flex;
    align-items: center;
}

.accessible-search-input {
    width: 100%;
    min-height: 44px;
    padding: 12px 48px 12px 16px;
    border: 2px solid var(--border-color, #ccc);
    border-radius: 8px;
    font-size: 16px;
}

.accessible-search-button {
    position: absolute;
    left: 8px;
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px;
}

.accessible-search-button:hover,
.accessible-search-button:focus {
    background-color: var(--hover-background, #f8f9fa);
}

/* تحسين الإشعارات */
.accessible-notification {
    padding: 16px 20px;
    border-radius: 8px;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 2px solid;
}

.accessible-notification.success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.accessible-notification.error {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.accessible-notification.warning {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.accessible-notification.info {
    background-color: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* تحسين التبويبات */
.accessible-tabs {
    border-bottom: 2px solid var(--border-color, #ccc);
}

.accessible-tab {
    display: inline-block;
    padding: 12px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
}

.accessible-tab:hover,
.accessible-tab:focus {
    background-color: var(--hover-background, #f8f9fa);
}

.accessible-tab.active {
    border-bottom-color: var(--primary-color, #0066cc);
    color: var(--primary-color, #0066cc);
}

.accessible-tab-content {
    padding: 2rem 0;
}

/* تحسين الأكورديون */
.accessible-accordion-item {
    border: 1px solid var(--border-color, #ccc);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.accessible-accordion-header {
    width: 100%;
    padding: 16px 20px;
    background: var(--header-background, #f8f9fa);
    border: none;
    text-align: right;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.accessible-accordion-header:hover,
.accessible-accordion-header:focus {
    background-color: var(--hover-background, #e9ecef);
}

.accessible-accordion-content {
    padding: 20px;
    border-top: 1px solid var(--border-color, #ccc);
}

/* تحسين شرائط التقدم */
.accessible-progress {
    width: 100%;
    height: 20px;
    background-color: var(--progress-background, #e9ecef);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.accessible-progress-bar {
    height: 100%;
    background-color: var(--primary-color, #0066cc);
    transition: width 0.3s ease;
    position: relative;
}

.accessible-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: var(--text-color);
}

/* تحسين التحكم في الصوت */
.accessible-audio-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--control-background, #f8f9fa);
    border-radius: 8px;
}

.accessible-audio-button {
    min-width: 44px;
    min-height: 44px;
    border: 2px solid var(--border-color, #ccc);
    background: var(--button-background, #fff);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.accessible-audio-button:hover,
.accessible-audio-button:focus {
    background-color: var(--primary-color, #0066cc);
    color: white;
}

/* تحسين التحكم في الفيديو */
.accessible-video-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 8px;
}

.accessible-video-button {
    min-width: 44px;
    min-height: 44px;
    border: 2px solid white;
    background: transparent;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.accessible-video-button:hover,
.accessible-video-button:focus {
    background-color: white;
    color: black;
}

/* تحسين الخرائط */
.accessible-map {
    border: 2px solid var(--border-color, #ccc);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.accessible-map-controls {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 10;
}

.accessible-map-button {
    min-width: 44px;
    min-height: 44px;
    background: white;
    border: 2px solid var(--border-color, #ccc);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.accessible-map-button:hover,
.accessible-map-button:focus {
    background-color: var(--primary-color, #0066cc);
    color: white;
}

/* تحسين الرسوم البيانية */
.accessible-chart {
    border: 2px solid var(--border-color, #ccc);
    border-radius: 8px;
    padding: 1rem;
    background: var(--background-color, #fff);
}

.accessible-chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.accessible-chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
}

.accessible-chart-legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 14px;
}

.accessible-chart-legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    border: 1px solid var(--border-color, #ccc);
}

/* تحسين الطباعة */
@media print {
    .accessible-print {
        color: black !important;
        background: white !important;
        font-size: 12pt;
        line-height: 1.5;
    }
    
    .accessible-print .btn {
        border: 1px solid black;
        padding: 0.5rem;
    }
    
    .accessible-print .card {
        border: 1px solid black;
        margin: 1rem 0;
        page-break-inside: avoid;
    }
    
    .no-print {
        display: none !important;
    }
}

/* تحسين الحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .respect-motion {
        animation: none !important;
        transition: none !important;
    }
    
    .respect-motion * {
        animation: none !important;
        transition: none !important;
    }
}

/* تحسين التباين العالي */
@media (prefers-contrast: high) {
    .respect-contrast {
        border: 2px solid currentColor !important;
        background: white !important;
        color: black !important;
    }
    
    .respect-contrast .btn {
        border: 3px solid black !important;
        background: white !important;
        color: black !important;
    }
}

/* تحسين الشاشات الصغيرة */
@media (max-width: 768px) {
    .mobile-accessible {
        font-size: 16px;
        line-height: 1.6;
    }

    .mobile-accessible .btn {
        min-height: 48px;
        min-width: 48px;
        font-size: 16px;
    }

    .mobile-accessible .accessible-input {
        min-height: 48px;
        font-size: 16px;
    }

    .mobile-accessible .accessible-nav-item {
        padding: 16px 20px;
    }
}

/* تحسين قارئ الشاشة */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    padding: 0.5rem;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
    background: var(--focus-background, #fff);
    color: var(--focus-color, #000);
    border: 2px solid var(--focus-border, #0066cc);
    border-radius: 4px;
}

/* تحسين التنقل بلوحة المفاتيح */
.keyboard-navigation {
    outline: none;
}

.keyboard-navigation:focus-visible {
    outline: 3px solid var(--focus-color, #0066cc);
    outline-offset: 2px;
}

/* تحسين الألوان للمكفوفين */
.colorblind-friendly {
    --success-color: #0066cc;
    --warning-color: #ff6600;
    --error-color: #cc0000;
    --info-color: #6600cc;
}

/* تحسين النصوص للقراءة */
.readable-text {
    font-family: 'Arial', 'Helvetica', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    letter-spacing: 0.5px;
    word-spacing: 2px;
}

/* تحسين التباين للنصوص */
.high-contrast-text {
    color: #000000;
    background-color: #ffffff;
    text-shadow: none;
}

.high-contrast-text.dark {
    color: #ffffff;
    background-color: #000000;
}

/* تحسين المؤشرات */
.accessible-cursor {
    cursor: pointer;
}

.accessible-cursor:hover {
    cursor: pointer;
    transform: scale(1.05);
}

.accessible-cursor:active {
    transform: scale(0.95);
}

/* تحسين الرسائل الحية */
.live-region {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

.live-region.polite {
    /* سيتم قراءتها عند انتهاء قارئ الشاشة من المحتوى الحالي */
}

.live-region.assertive {
    /* سيتم قراءتها فوراً */
}

/* تحسين التحكم في الوقت */
.accessible-timer {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 2px solid var(--border-color, #ccc);
    border-radius: 8px;
    background: var(--background-color, #fff);
}

.accessible-timer-display {
    font-size: 1.5rem;
    font-weight: 600;
    font-family: monospace;
}

.accessible-timer-controls {
    display: flex;
    gap: 0.5rem;
}

/* تحسين التحكم في التشغيل */
.accessible-media-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--control-background, #f8f9fa);
    border-radius: 8px;
    border: 2px solid var(--border-color, #ccc);
}

.accessible-media-button {
    min-width: 48px;
    min-height: 48px;
    border: 2px solid var(--border-color, #ccc);
    background: var(--button-background, #fff);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.accessible-media-button:hover,
.accessible-media-button:focus {
    background-color: var(--primary-color, #0066cc);
    color: white;
    transform: scale(1.05);
}

.accessible-media-button:active {
    transform: scale(0.95);
}

.accessible-media-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* تحسين التحكم في مستوى الصوت */
.accessible-volume-control {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.accessible-volume-slider {
    width: 100px;
    height: 8px;
    background: var(--slider-background, #e9ecef);
    border-radius: 4px;
    outline: none;
    cursor: pointer;
}

.accessible-volume-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--primary-color, #0066cc);
    border-radius: 50%;
    cursor: pointer;
}

.accessible-volume-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--primary-color, #0066cc);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.accessible-volume-slider:focus {
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3);
}

/* تحسين التحكم في السرعة */
.accessible-speed-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.accessible-speed-button {
    min-width: 40px;
    min-height: 40px;
    border: 2px solid var(--border-color, #ccc);
    background: var(--button-background, #fff);
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
}

.accessible-speed-button:hover,
.accessible-speed-button:focus {
    background-color: var(--primary-color, #0066cc);
    color: white;
}

.accessible-speed-button.active {
    background-color: var(--primary-color, #0066cc);
    color: white;
}

/* تحسين التحكم في الترجمة */
.accessible-caption-control {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.accessible-caption-toggle {
    min-width: 48px;
    min-height: 48px;
    border: 2px solid var(--border-color, #ccc);
    background: var(--button-background, #fff);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.accessible-caption-toggle:hover,
.accessible-caption-toggle:focus {
    background-color: var(--primary-color, #0066cc);
    color: white;
}

.accessible-caption-toggle.active {
    background-color: var(--success-color, #28a745);
    color: white;
}

/* تحسين التحكم في الحجم */
.accessible-size-control {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--control-background, #f8f9fa);
    border-radius: 8px;
    border: 2px solid var(--border-color, #ccc);
}

.accessible-size-button {
    min-width: 44px;
    min-height: 44px;
    border: 2px solid var(--border-color, #ccc);
    background: var(--button-background, #fff);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.accessible-size-button:hover,
.accessible-size-button:focus {
    background-color: var(--primary-color, #0066cc);
    color: white;
}

.accessible-size-button.active {
    background-color: var(--primary-color, #0066cc);
    color: white;
}

/* تحسين التحكم في التباين */
.accessible-contrast-control {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--control-background, #f8f9fa);
    border-radius: 8px;
    border: 2px solid var(--border-color, #ccc);
}

.accessible-contrast-button {
    min-width: 44px;
    min-height: 44px;
    border: 2px solid var(--border-color, #ccc);
    background: var(--button-background, #fff);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.accessible-contrast-button:hover,
.accessible-contrast-button:focus {
    background-color: var(--primary-color, #0066cc);
    color: white;
}

.accessible-contrast-button.active {
    background-color: var(--primary-color, #0066cc);
    color: white;
    border-color: white;
}
