<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة حرارية - {{ $sale->sale_number }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            width: 80mm;
            margin: 0 auto;
            padding: 5mm;
            background: white;
        }
        
        .receipt-header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        
        .company-name {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .company-info {
            font-size: 10px;
            line-height: 1.3;
        }
        
        .receipt-info {
            margin: 10px 0;
            font-size: 11px;
        }
        
        .receipt-info div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .items-table {
            width: 100%;
            margin: 10px 0;
            border-collapse: collapse;
        }
        
        .items-table th,
        .items-table td {
            padding: 3px 2px;
            text-align: right;
            font-size: 10px;
        }
        
        .items-table th {
            border-bottom: 1px solid #000;
            font-weight: 600;
        }
        
        .item-name {
            font-weight: 500;
        }
        
        .item-details {
            font-size: 9px;
            color: #666;
        }
        
        .totals {
            margin-top: 10px;
            border-top: 1px dashed #000;
            padding-top: 5px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 11px;
        }
        
        .total-row.grand-total {
            font-weight: 700;
            font-size: 13px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        
        .payment-info {
            margin: 10px 0;
            border-top: 1px dashed #000;
            padding-top: 10px;
        }
        
        .payment-method {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 10px;
        }
        
        .receipt-footer {
            text-align: center;
            margin-top: 15px;
            border-top: 1px dashed #000;
            padding-top: 10px;
            font-size: 10px;
        }
        
        .qr-code {
            text-align: center;
            margin: 10px 0;
        }
        
        .thank-you {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .return-policy {
            font-size: 8px;
            color: #666;
            margin-top: 5px;
        }
        
        @media print {
            body {
                width: 80mm;
                margin: 0;
                padding: 0;
            }
            
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Receipt Header -->
    <div class="receipt-header">
        <div class="company-name">{{ $location->getReceiptHeader() }}</div>
        <div class="company-info">
            @if($location->address)
                <div>{{ $location->address }}</div>
            @endif
            @if($location->phone)
                <div>هاتف: {{ $location->phone }}</div>
            @endif
            @if($location->tax_number)
                <div>الرقم الضريبي: {{ $location->tax_number }}</div>
            @endif
        </div>
    </div>

    <!-- Receipt Info -->
    <div class="receipt-info">
        <div>
            <span>رقم الفاتورة:</span>
            <span>{{ $sale->sale_number }}</span>
        </div>
        <div>
            <span>التاريخ:</span>
            <span>{{ $sale->sale_date->format('Y-m-d H:i') }}</span>
        </div>
        @if($customer)
        <div>
            <span>العميل:</span>
            <span>{{ $customer->full_name }}</span>
        </div>
        @if($customer->phone)
        <div>
            <span>الهاتف:</span>
            <span>{{ $customer->phone }}</span>
        </div>
        @endif
        @endif
        <div>
            <span>الكاشير:</span>
            <span>{{ $sale->user->name }}</span>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50%">الصنف</th>
                <th style="width: 15%">الكمية</th>
                <th style="width: 20%">السعر</th>
                <th style="width: 15%">المجموع</th>
            </tr>
        </thead>
        <tbody>
            @foreach($items as $item)
            <tr>
                <td>
                    <div class="item-name">{{ $item->item_name }}</div>
                    @if($item->item_code)
                        <div class="item-details">كود: {{ $item->item_code }}</div>
                    @endif
                    @if($item->item_description)
                        <div class="item-details">{{ Str::limit($item->item_description, 30) }}</div>
                    @endif
                </td>
                <td style="text-align: center">{{ $item->quantity }}</td>
                <td style="text-align: left">{{ number_format($item->unit_price, 2) }}</td>
                <td style="text-align: left">{{ number_format($item->line_total, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Totals -->
    <div class="totals">
        <div class="total-row">
            <span>المجموع الفرعي:</span>
            <span>{{ number_format($sale->subtotal, 2) }} ريال</span>
        </div>
        
        @if($sale->discount_amount > 0)
        <div class="total-row">
            <span>الخصم:</span>
            <span>-{{ number_format($sale->discount_amount, 2) }} ريال</span>
        </div>
        @endif
        
        @if($sale->tax_amount > 0)
        <div class="total-row">
            <span>ضريبة القيمة المضافة ({{ $location->getTaxRate() }}%):</span>
            <span>{{ number_format($sale->tax_amount, 2) }} ريال</span>
        </div>
        @endif
        
        <div class="total-row grand-total">
            <span>المجموع الكلي:</span>
            <span>{{ number_format($sale->total_amount, 2) }} ريال</span>
        </div>
    </div>

    <!-- Payment Info -->
    <div class="payment-info">
        <div style="font-weight: 600; margin-bottom: 5px;">تفاصيل الدفع:</div>
        @foreach($payments as $payment)
        <div class="payment-method">
            <span>{{ $payment->payment_method_label }}:</span>
            <span>{{ number_format($payment->amount, 2) }} ريال</span>
        </div>
        @endforeach
        
        @if($sale->change_amount > 0)
        <div class="payment-method" style="font-weight: 600;">
            <span>المبلغ المرتجع:</span>
            <span>{{ number_format($sale->change_amount, 2) }} ريال</span>
        </div>
        @endif
        
        @if($sale->remaining_amount > 0)
        <div class="payment-method" style="color: #d32f2f;">
            <span>المبلغ المتبقي:</span>
            <span>{{ number_format($sale->remaining_amount, 2) }} ريال</span>
        </div>
        @endif
    </div>

    <!-- QR Code (placeholder) -->
    <div class="qr-code">
        <div style="border: 1px solid #000; width: 60px; height: 60px; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 8px;">
            QR CODE
        </div>
    </div>

    <!-- Receipt Footer -->
    <div class="receipt-footer">
        <div class="thank-you">{{ $location->getReceiptFooter() }}</div>
        
        @if($sale->notes)
        <div style="margin: 5px 0; font-size: 9px;">
            <strong>ملاحظات:</strong> {{ $sale->notes }}
        </div>
        @endif
        
        <div style="margin: 5px 0; font-size: 9px;">
            تاريخ الطباعة: {{ $generated_at->format('Y-m-d H:i:s') }}
        </div>
        
        <div class="return-policy">
            سياسة الاسترداد: يمكن استرداد المنتجات خلال 7 أيام من تاريخ الشراء
        </div>
    </div>

    <!-- Print Button (hidden in print) -->
    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()" style="background: #2196F3; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            طباعة الفاتورة
        </button>
    </div>
</body>
</html>
