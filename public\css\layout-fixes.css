/* ==========================================================================
   إصلاحات التخطيط والتداخلات
   Layout Fixes & Conflict Resolution
   ========================================================================== */

/* إخفاء العناصر المضافة ديناميكياً التي تسبب ازدحام */
.accessible-size-control,
.accessible-contrast-control {
    display: none !important;
}

/* إصلاح تداخلات CSS */
.performance-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 1rem !important;
}

/* تحسين العناصر التي قد تؤثر على التخطيط */
.lazy-load {
    min-height: auto !important;
    transition: opacity 0.3s ease, transform 0.3s ease !important;
}

.lazy-load.loaded {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* إصلاح مشاكل الصور */
.image-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
    background-size: 200% 100% !important;
    animation: shimmer 1.5s infinite !important;
    min-height: 200px !important;
}

/* إصلاح الجداول الافتراضية */
.virtual-scroll {
    height: 400px !important;
    overflow-y: auto !important;
    contain: layout style paint !important;
}

/* إصلاح العناصر المحسنة للأداء */
.memory-efficient {
    contain: layout style paint !important;
    content-visibility: auto !important;
    contain-intrinsic-size: auto !important;
}

/* إصلاح الشبكات المحسنة */
.optimized-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 1rem !important;
    contain: layout !important;
}

.flexible-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(min(250px, 100%), 1fr)) !important;
    gap: clamp(1rem, 2vw, 2rem) !important;
    contain: layout !important;
}

/* إصلاح الأزرار المحسنة */
.efficient-button {
    contain: layout style !important;
    isolation: isolate !important;
    transform: translateZ(0) !important;
    min-height: auto !important;
    min-width: auto !important;
}

/* إصلاح النماذج المحسنة */
.optimized-form {
    contain: layout style !important;
}

.form-field {
    contain: layout !important;
    isolation: isolate !important;
}

/* إصلاح القوائم المحسنة */
.efficient-list {
    contain: layout !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.list-item {
    contain: layout style !important;
    display: flex !important;
    align-items: center !important;
    padding: 0.5rem !important;
}

/* إصلاح الإحصائيات المحسنة */
.efficient-stats {
    contain: layout style !important;
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 1rem !important;
}

/* إصلاح الفلاتر المحسنة */
.efficient-filters {
    contain: layout style !important;
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
}

/* إصلاح الرسوم البيانية المحسنة */
.efficient-chart {
    contain: layout size !important;
    width: 100% !important;
    height: 300px !important;
    position: relative !important;
}

/* إصلاح النوافذ المنبثقة المحسنة */
.efficient-modal {
    contain: layout style !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 1000 !important;
    backdrop-filter: blur(5px) !important;
}

/* إصلاح التحميل المحسن */
.loading-spinner {
    contain: layout size !important;
    width: 40px !important;
    height: 40px !important;
    border: 4px solid var(--border-color) !important;
    border-top: 4px solid var(--primary-color) !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

/* إصلاح العناصر المتجاوبة */
@media (max-width: 768px) {
    .mobile-optimized {
        contain: layout style !important;
        transform: translateZ(0) !important;
    }
    
    .mobile-grid {
        grid-template-columns: 1fr !important;
        gap: 0.5rem !important;
    }
    
    .mobile-text {
        font-size: 14px !important;
        line-height: 1.4 !important;
    }
    
    /* إخفاء العناصر الإضافية على الشاشات الصغيرة */
    .accessible-size-control,
    .accessible-contrast-control,
    .accessible-audio-controls,
    .accessible-video-controls {
        display: none !important;
    }
}

/* إصلاح عناصر إمكانية الوصول المشكلة */
.accessible-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.8) !important;
    display: none !important; /* مخفية افتراضياً */
    align-items: center !important;
    justify-content: center !important;
    z-index: 1000 !important;
}

.accessible-modal.show {
    display: flex !important;
}

/* إصلاح التحكم في الصوت والفيديو */
.accessible-audio-controls,
.accessible-video-controls {
    display: none !important; /* مخفية افتراضياً */
    align-items: center !important;
    gap: 1rem !important;
    padding: 1rem !important;
    border-radius: 8px !important;
}

/* إصلاح الخرائط */
.accessible-map {
    border: 2px solid var(--border-color, #ccc) !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    position: relative !important;
    height: auto !important;
    min-height: 300px !important;
}

/* إصلاح الرسوم البيانية */
.accessible-chart {
    border: 2px solid var(--border-color, #ccc) !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    background: var(--background-color, #fff) !important;
    margin: 1rem 0 !important;
}

/* إصلاح التحكم في الحجم والتباين */
.accessible-size-control,
.accessible-contrast-control {
    position: fixed !important;
    top: 20px !important;
    left: 20px !important;
    z-index: 9999 !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 12px !important;
    padding: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    display: none !important; /* مخفية افتراضياً */
    flex-direction: row !important;
    gap: 4px !important;
}

/* إظهار أزرار التحكم عند الحاجة فقط */
.show-accessibility-controls .accessible-size-control,
.show-accessibility-controls .accessible-contrast-control {
    display: flex !important;
}

.accessible-size-control {
    top: 20px !important;
    left: 20px !important;
}

.accessible-contrast-control {
    top: 80px !important;
    left: 20px !important;
}

/* تحسين أزرار التحكم */
.accessible-size-button,
.accessible-contrast-button {
    min-width: 32px !important;
    min-height: 32px !important;
    border: 1px solid var(--border-color, #ccc) !important;
    background: var(--button-background, #fff) !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease !important;
}

.accessible-size-button:hover,
.accessible-size-button:focus,
.accessible-contrast-button:hover,
.accessible-contrast-button:focus {
    background-color: var(--primary-color, #0066cc) !important;
    color: white !important;
    transform: scale(1.05) !important;
}

.accessible-size-button.active,
.accessible-contrast-button.active {
    background-color: var(--primary-color, #0066cc) !important;
    color: white !important;
}

/* إصلاح قارئ الشاشة */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.sr-only-focusable:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: 0.5rem !important;
    margin: 0 !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
    background: var(--focus-background, #fff) !important;
    color: var(--focus-color, #000) !important;
    border: 2px solid var(--focus-border, #0066cc) !important;
    border-radius: 4px !important;
}

/* إصلاح المناطق الحية */
.live-region {
    position: absolute !important;
    left: -10000px !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* إصلاح التحكم في الوقت */
.accessible-timer {
    display: none !important; /* مخفي افتراضياً */
    align-items: center !important;
    gap: 1rem !important;
    padding: 1rem !important;
    border: 2px solid var(--border-color, #ccc) !important;
    border-radius: 8px !important;
    background: var(--background-color, #fff) !important;
    margin: 1rem 0 !important;
}

/* إصلاح التحكم في الوسائط */
.accessible-media-controls {
    display: none !important; /* مخفي افتراضياً */
    align-items: center !important;
    gap: 1rem !important;
    padding: 1rem !important;
    background: var(--control-background, #f8f9fa) !important;
    border-radius: 8px !important;
    border: 2px solid var(--border-color, #ccc) !important;
    margin: 1rem 0 !important;
}

/* إصلاح التحكم في مستوى الصوت */
.accessible-volume-control {
    display: none !important; /* مخفي افتراضياً */
    align-items: center !important;
    gap: 1rem !important;
}

/* إصلاح التحكم في السرعة */
.accessible-speed-control {
    display: none !important; /* مخفي افتراضياً */
    align-items: center !important;
    gap: 0.5rem !important;
}

/* إصلاح التحكم في الترجمة */
.accessible-caption-control {
    display: none !important; /* مخفي افتراضياً */
    align-items: center !important;
    gap: 1rem !important;
}

/* تحسين الطباعة */
@media print {
    .accessible-size-control,
    .accessible-contrast-control,
    .accessible-audio-controls,
    .accessible-video-controls,
    .accessible-timer,
    .accessible-media-controls,
    .accessible-volume-control,
    .accessible-speed-control,
    .accessible-caption-control {
        display: none !important;
    }
}

/* إصلاح الحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .respect-motion,
    .respect-motion * {
        animation: none !important;
        transition: none !important;
    }
    
    .lazy-load {
        opacity: 1 !important;
        transform: translateY(0) !important;
    }
}

/* إصلاح التباين العالي */
@media (prefers-contrast: high) {
    .respect-contrast {
        border: 2px solid currentColor !important;
        background: white !important;
        color: black !important;
    }
    
    .respect-contrast .btn {
        border: 3px solid black !important;
        background: white !important;
        color: black !important;
    }
}
