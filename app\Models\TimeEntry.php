<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TimeEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'technician_id',
        'repair_id',
        'start_time',
        'end_time',
        'duration',
        'description',
        'billable',
        'hourly_rate',
        'total_cost'
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'duration' => 'integer', // in minutes
        'billable' => 'boolean',
        'hourly_rate' => 'decimal:2',
        'total_cost' => 'decimal:2'
    ];

    // Relationships
    public function technician()
    {
        return $this->belongsTo(Technician::class);
    }

    public function repair()
    {
        return $this->belongsTo(Repair::class);
    }

    // Accessors
    public function getFormattedDurationAttribute()
    {
        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;
        
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    // Methods
    public function calculateDuration()
    {
        if ($this->start_time && $this->end_time) {
            $this->duration = $this->start_time->diffInMinutes($this->end_time);
            $this->save();
        }
    }

    public function calculateCost()
    {
        if ($this->duration && $this->hourly_rate) {
            $hours = $this->duration / 60;
            $this->total_cost = $hours * $this->hourly_rate;
            $this->save();
        }
    }
}
