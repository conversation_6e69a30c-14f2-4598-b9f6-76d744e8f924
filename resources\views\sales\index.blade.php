@extends('layouts.main')

@section('title', 'إدارة المبيعات')

@section('content')
<div class="content-wrapper animate-fade-in-up" x-data="salesManager()">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">إدارة المبيعات</h1>
                    <p class="page-subtitle">إدارة فواتير المبيعات والمدفوعات بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="exportSales()" class="btn btn-success hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        تصدير البيانات
                    </button>
                    <button @click="createSale()" class="btn btn-primary hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        فاتورة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card hover-lift animate-scale-in animate-delay-100">
            <div class="stat-value" style="color: var(--accent-color);" x-text="stats.totalInvoices">10</div>
            <div class="stat-label">إجمالي الفواتير</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                +2 هذا الأسبوع
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-200">
            <div class="stat-value" style="color: var(--success-color);" x-text="stats.totalSales">₪15,750</div>
            <div class="stat-label">إجمالي المبيعات</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                +12% من الشهر الماضي
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-300">
            <div class="stat-value" style="color: var(--warning-color);" x-text="stats.pendingInvoices">3</div>
            <div class="stat-label">فواتير معلقة</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                تحتاج متابعة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-500">
            <div class="stat-value" style="color: var(--danger-color);" x-text="stats.outstandingAmount">₪2,500</div>
            <div class="stat-label">مستحقات</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                تحتاج تحصيل
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card animate-fade-in-up animate-delay-300">
        <div class="card-header">
            <h3 class="card-title">فلترة وبحث المبيعات</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div class="form-group">
                <label class="form-label">البحث</label>
                <div class="search-box">
                    <input type="text" x-model="filters.search" @input="filterSales()"
                           placeholder="رقم الفاتورة أو العميل..."
                           class="search-input focus-glow">
                    <svg class="search-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">الحالة</label>
                <select x-model="filters.status" @change="filterSales()" class="form-select focus-glow">
                    <option value="">جميع الحالات</option>
                    <option value="completed">مكتملة</option>
                    <option value="pending">معلقة</option>
                    <option value="cancelled">ملغية</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">طريقة الدفع</label>
                <select x-model="filters.paymentMethod" @change="filterSales()" class="form-select focus-glow">
                    <option value="">جميع الطرق</option>
                    <option value="cash">نقدي</option>
                    <option value="card">بطاقة</option>
                    <option value="credit">آجل</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">من تاريخ</label>
                <input type="date" x-model="filters.dateFrom" @change="filterSales()"
                       class="form-input focus-glow">
            </div>
            <div class="form-group">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" x-model="filters.dateTo" @change="filterSales()"
                       class="form-input focus-glow">
            </div>
        </div>
    </div>

    <!-- Sales Table -->
    <div class="table-container animate-fade-in-up animate-delay-500">
        <div class="card-header">
            <h3 class="card-title">فواتير المبيعات</h3>
            <div class="action-group">
                <button @click="refreshData()" class="btn btn-ghost btn-sm hover-rotate">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    تحديث
                </button>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>التاريخ</th>
                        <th>المبلغ</th>
                        <th>طريقة الدفع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="(sale, index) in filteredSales" :key="sale.id">
                        <tr class="hover-lift" :class="'animate-fade-in-up animate-delay-' + (index * 100)">
                            <td>
                                <div class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="sale.invoice_number"></div>
                            </td>
                            <td>
                                <div class="text-sm text-primary-light dark:text-primary-dark" x-text="sale.customer_name"></div>
                                <div class="text-sm text-muted-light dark:text-muted-dark" x-text="sale.customer_phone"></div>
                            </td>
                            <td class="text-sm text-primary-light dark:text-primary-dark" x-text="sale.date"></td>
                            <td class="text-sm font-semibold text-success-color" x-text="'₪' + sale.total"></td>
                            <td>
                                <span class="badge"
                                      :class="{
                                          'badge-success': sale.payment_method === 'cash',
                                          'badge-info': sale.payment_method === 'card',
                                          'badge-warning': sale.payment_method === 'credit'
                                      }"
                                      x-text="getPaymentMethodText(sale.payment_method)">
                                </span>
                            </td>
                            <td>
                                <span class="badge"
                                      :class="{
                                          'badge-success': sale.status === 'completed',
                                          'badge-warning': sale.status === 'pending',
                                          'badge-danger': sale.status === 'cancelled'
                                      }"
                                      x-text="getStatusText(sale.status)">
                                </span>
                            </td>
                            <td>
                                <div class="action-group">
                                    <button @click="viewSale(sale.id)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        عرض
                                    </button>
                                    <button @click="editSale(sale.id)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        تعديل
                                    </button>
                                    <button @click="printSale(sale.id)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                        </svg>
                                        طباعة
                                    </button>
                                    <button @click="deleteSale(sale.id)" class="btn btn-ghost btn-sm hover-scale" style="color: var(--danger-color);">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        حذف
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="card-header">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button @click="previousPage()" :disabled="currentPage === 1"
                            class="btn btn-secondary btn-sm hover-slide-right">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        السابق
                    </button>
                    <button @click="nextPage()" :disabled="currentPage === totalPages"
                            class="btn btn-secondary btn-sm hover-slide-left">
                        التالي
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-muted-light dark:text-muted-dark">
                            عرض <span class="font-medium text-primary-light dark:text-primary-dark" x-text="((currentPage - 1) * itemsPerPage) + 1"></span>
                            إلى <span class="font-medium text-primary-light dark:text-primary-dark" x-text="Math.min(currentPage * itemsPerPage, totalItems)"></span>
                            من <span class="font-medium text-primary-light dark:text-primary-dark" x-text="totalItems"></span> نتيجة
                        </p>
                    </div>
                    <div class="pagination">
                        <button @click="previousPage()" :disabled="currentPage === 1"
                                class="pagination-item hover-scale">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                        <template x-for="page in getPageNumbers()" :key="page">
                            <button @click="goToPage(page)"
                                    :class="page === currentPage ? 'pagination-item active' : 'pagination-item hover-scale'"
                                    x-text="page">
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages"
                                class="pagination-item hover-scale">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function salesManager() {
    return {
        sales: [
            {
                id: 1,
                invoice_number: 'INV-001',
                customer_name: 'أحمد محمد علي',
                customer_phone: '0599123456',
                date: '2024-07-09',
                total: '1250.00',
                payment_method: 'cash',
                status: 'completed'
            },
            {
                id: 2,
                invoice_number: 'INV-002',
                customer_name: 'سارة أحمد خالد',
                customer_phone: '0598765432',
                date: '2024-07-08',
                total: '850.00',
                payment_method: 'card',
                status: 'completed'
            },
            {
                id: 3,
                invoice_number: 'INV-003',
                customer_name: 'محمد عبدالله حسن',
                customer_phone: '0597654321',
                date: '2024-07-07',
                total: '2100.00',
                payment_method: 'credit',
                status: 'pending'
            }
        ],
        filteredSales: [],
        filters: {
            search: '',
            status: '',
            paymentMethod: '',
            dateFrom: '',
            dateTo: ''
        },
        stats: {
            totalInvoices: 10,
            totalSales: '₪15,750',
            pendingInvoices: 3,
            outstandingAmount: '₪2,500'
        },
        currentPage: 1,
        itemsPerPage: 10,
        totalItems: 0,
        totalPages: 0,

        init() {
            this.filteredSales = [...this.sales];
            this.updatePagination();

            // إضافة تأثيرات الرسوم المتحركة للعناصر
            this.$nextTick(() => {
                this.animateElements();
            });
        },

        animateElements() {
            // إضافة تأثيرات متدرجة للصفوف
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                setTimeout(() => {
                    row.classList.add('animate-fade-in-up');
                }, index * 100);
            });
        },

        filterSales() {
            this.filteredSales = this.sales.filter(sale => {
                const matchesSearch = !this.filters.search || 
                    sale.invoice_number.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    sale.customer_name.toLowerCase().includes(this.filters.search.toLowerCase());
                
                const matchesStatus = !this.filters.status || sale.status === this.filters.status;
                const matchesPayment = !this.filters.paymentMethod || sale.payment_method === this.filters.paymentMethod;
                
                const matchesDateFrom = !this.filters.dateFrom || sale.date >= this.filters.dateFrom;
                const matchesDateTo = !this.filters.dateTo || sale.date <= this.filters.dateTo;

                return matchesSearch && matchesStatus && matchesPayment && matchesDateFrom && matchesDateTo;
            });
            
            this.currentPage = 1;
            this.updatePagination();
        },

        updatePagination() {
            this.totalItems = this.filteredSales.length;
            this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        },

        getPageNumbers() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        },

        goToPage(page) {
            this.currentPage = page;
        },

        previousPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },

        getPaymentMethodText(method) {
            const methods = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'credit': 'آجل'
            };
            return methods[method] || method;
        },

        getStatusText(status) {
            const statuses = {
                'completed': 'مكتملة',
                'pending': 'معلقة',
                'cancelled': 'ملغية'
            };
            return statuses[status] || status;
        },

        createSale() {
            // Redirect to create sale page
            window.location.href = '/sales/create';
        },

        viewSale(id) {
            // Redirect to view sale page
            window.location.href = `/sales/${id}`;
        },

        editSale(id) {
            // Redirect to edit sale page
            window.location.href = `/sales/${id}/edit`;
        },

        printSale(id) {
            // Open print dialog for sale
            window.open(`/sales/${id}/print`, '_blank');
        },

        deleteSale(id) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                // Delete sale logic
                this.sales = this.sales.filter(sale => sale.id !== id);
                this.filterSales();
            }
        },

        exportSales() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري التصدير...
            `;
            button.disabled = true;

            // محاكاة عملية التصدير
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                this.showNotification('تم تصدير البيانات بنجاح!', 'success');
            }, 2000);
        },

        refreshData() {
            // إضافة تأثير التحديث
            const button = event.target;
            button.classList.add('animate-spin');

            // محاكاة تحديث البيانات
            setTimeout(() => {
                button.classList.remove('animate-spin');
                this.showNotification('تم تحديث البيانات', 'info');
            }, 1000);
        },

        showNotification(message, type = 'info') {
            // إنشاء إشعار ديناميكي
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} animate-slide-in-down`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '300px';
            notification.innerHTML = message;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                notification.classList.remove('animate-slide-in-down');
                notification.classList.add('animate-slide-out-up');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    }
}
</script>
@endpush
@endsection
