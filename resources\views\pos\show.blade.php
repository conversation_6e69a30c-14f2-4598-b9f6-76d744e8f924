@extends('layouts.main')

@section('title', 'مبيعة رقم ' . $sale->sale_number)

@section('content')
<div class="space-y-6" x-data="posShowSale()">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">مبيعة رقم {{ $sale->sale_number }}</h1>
            <p class="text-gray-600 dark:text-gray-400">
                {{ $sale->sale_date->format('Y-m-d H:i') }} | {{ $sale->location->name }}
            </p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('pos.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
            
            @if($sale->canBeModified())
                <button @click="openPaymentModal" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-credit-card mr-2"></i>
                    معالجة الدفع
                </button>
            @endif
            
            <button @click="printReceipt" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-print mr-2"></i>
                طباعة
            </button>
            
            @if($sale->canBeCancelled())
                <button @click="cancelSale" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-times mr-2"></i>
                    إلغاء
                </button>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Sale Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Sale Info -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات المبيعة</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">رقم المبيعة</label>
                        <p class="text-gray-900 dark:text-gray-100 font-medium">{{ $sale->sale_number }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">التاريخ</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $sale->sale_date->format('Y-m-d H:i') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">نوع المبيعة</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $sale->sale_type_label }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الحالة</label>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $sale->status_color }}">
                            {{ $sale->status_label }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الكاشير</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $sale->user->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">حالة الدفع</label>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $sale->payment_status === 'paid' ? 'bg-green-100 text-green-800' : ($sale->payment_status === 'partial' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                            {{ $sale->payment_status_label }}
                        </span>
                    </div>
                </div>
                
                @if($sale->customer)
                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">معلومات العميل</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الاسم</label>
                            <p class="text-gray-900 dark:text-gray-100">{{ $sale->customer->full_name }}</p>
                        </div>
                        
                        @if($sale->customer->phone)
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الهاتف</label>
                            <p class="text-gray-900 dark:text-gray-100">{{ $sale->customer->phone }}</p>
                        </div>
                        @endif
                        
                        @if($sale->customer->email)
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">البريد الإلكتروني</label>
                            <p class="text-gray-900 dark:text-gray-100">{{ $sale->customer->email }}</p>
                        </div>
                        @endif
                    </div>
                </div>
                @endif
                
                @if($sale->notes)
                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">ملاحظات</label>
                    <p class="text-gray-900 dark:text-gray-100">{{ $sale->notes }}</p>
                </div>
                @endif
            </div>

            <!-- Sale Items -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">عناصر المبيعة</h3>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الصنف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">سعر الوحدة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الخصم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المجموع</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($sale->items as $item)
                            <tr>
                                <td class="px-6 py-4">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $item->item_name }}</div>
                                        @if($item->item_code)
                                            <div class="text-sm text-gray-500 dark:text-gray-400">كود: {{ $item->item_code }}</div>
                                        @endif
                                        @if($item->item_description)
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ $item->item_description }}</div>
                                        @endif
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                            {{ $item->item_type_label }}
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-center">
                                    {{ $item->quantity }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    {{ number_format($item->unit_price, 2) }} ريال
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    {{ number_format($item->discount_amount, 2) }} ريال
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                    {{ number_format($item->line_total, 2) }} ريال
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Payment History -->
            @if($sale->payments->count() > 0)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تاريخ المدفوعات</h3>
                </div>
                
                <div class="p-6">
                    <div class="space-y-4">
                        @foreach($sale->payments as $payment)
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="flex items-center gap-2">
                                        <span class="font-medium text-gray-900 dark:text-gray-100">{{ $payment->payment_method_label }}</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $payment->status_color }}">
                                            {{ $payment->status_label }}
                                        </span>
                                    </div>
                                    
                                    @if($payment->reference_number)
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                            رقم المرجع: {{ $payment->reference_number }}
                                        </div>
                                    @endif
                                    
                                    @if($payment->processed_at)
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                            {{ $payment->processed_at->format('Y-m-d H:i') }}
                                        </div>
                                    @endif
                                </div>
                                
                                <div class="text-right">
                                    <div class="text-lg font-bold text-gray-900 dark:text-gray-100">
                                        {{ number_format($payment->amount, 2) }} ريال
                                    </div>
                                    
                                    @if($payment->canBeRefunded())
                                        <button @click="openRefundModal({{ $payment->id }}, {{ $payment->getRefundableAmount() }})"
                                                class="text-sm text-red-600 hover:text-red-700 mt-1">
                                            استرداد
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Summary & Actions -->
        <div class="space-y-6">
            <!-- Financial Summary -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الملخص المالي</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                        <span class="font-medium">{{ number_format($sale->subtotal, 2) }} ريال</span>
                    </div>
                    
                    @if($sale->discount_amount > 0)
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الخصم:</span>
                        <span class="text-red-600 font-medium">-{{ number_format($sale->discount_amount, 2) }} ريال</span>
                    </div>
                    @endif
                    
                    @if($sale->tax_amount > 0)
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الضريبة:</span>
                        <span class="font-medium">{{ number_format($sale->tax_amount, 2) }} ريال</span>
                    </div>
                    @endif
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                        <div class="flex justify-between text-lg font-bold">
                            <span>المجموع الكلي:</span>
                            <span>{{ number_format($sale->total_amount, 2) }} ريال</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-between text-green-600">
                        <span>المدفوع:</span>
                        <span class="font-medium">{{ number_format($sale->paid_amount, 2) }} ريال</span>
                    </div>
                    
                    @if($sale->change_amount > 0)
                    <div class="flex justify-between text-blue-600">
                        <span>المرتجع:</span>
                        <span class="font-medium">{{ number_format($sale->change_amount, 2) }} ريال</span>
                    </div>
                    @endif
                    
                    @if($sale->remaining_amount > 0)
                    <div class="flex justify-between text-red-600">
                        <span>المتبقي:</span>
                        <span class="font-medium">{{ number_format($sale->remaining_amount, 2) }} ريال</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات سريعة</h3>
                
                <div class="space-y-3">
                    <button @click="printReceipt" 
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm">
                        <i class="fas fa-print mr-2"></i>
                        طباعة الفاتورة
                    </button>
                    
                    @if($sale->customer && $sale->customer->email)
                    <button @click="emailReceipt" 
                            class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg text-sm">
                        <i class="fas fa-envelope mr-2"></i>
                        إرسال بالبريد
                    </button>
                    @endif
                    
                    @if($sale->customer && $sale->customer->phone)
                    <button @click="smsReceipt" 
                            class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-sm">
                        <i class="fas fa-sms mr-2"></i>
                        إرسال رسالة نصية
                    </button>
                    @endif
                    
                    <a href="{{ route('pos.receipts.pdf', $sale) }}" target="_blank"
                       class="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg text-sm text-center block">
                        <i class="fas fa-file-pdf mr-2"></i>
                        تحميل PDF
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div x-show="showPaymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 z-50" x-cloak>
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">معالجة الدفع</h3>
                    <button @click="closePaymentModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form @submit.prevent="processPayment">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة الدفع</label>
                            <select x-model="paymentData.method" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة ائتمان</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ</label>
                            <input type="number" x-model="paymentData.amount" step="0.01" min="0.01" 
                                   :max="{{ $sale->remaining_amount }}" required
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            <p class="text-sm text-gray-500 mt-1">المتبقي: {{ number_format($sale->remaining_amount, 2) }} ريال</p>
                        </div>
                        
                        <div x-show="paymentData.method === 'card'">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">آخر 4 أرقام من البطاقة</label>
                            <input type="text" x-model="paymentData.card_last_four" maxlength="4"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        </div>
                        
                        <div x-show="paymentData.method !== 'cash'">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم المرجع</label>
                            <input type="text" x-model="paymentData.reference_number"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات</label>
                            <textarea x-model="paymentData.notes" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-2 space-x-reverse mt-6">
                        <button type="button" @click="closePaymentModal"
                                class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                            إلغاء
                        </button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            معالجة الدفع
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function posShowSale() {
    return {
        showPaymentModal: false,
        paymentData: {
            method: 'cash',
            amount: {{ $sale->remaining_amount }},
            card_last_four: '',
            reference_number: '',
            notes: ''
        },

        openPaymentModal() {
            this.showPaymentModal = true;
        },

        closePaymentModal() {
            this.showPaymentModal = false;
        },

        async processPayment() {
            try {
                const response = await fetch(`/pos/payments/sales/{{ $sale->id }}/${this.paymentData.method}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(this.paymentData)
                });

                const data = await response.json();

                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message);
                }
            } catch (error) {
                console.error('Error processing payment:', error);
                alert('حدث خطأ أثناء معالجة الدفع');
            }
        },

        printReceipt() {
            window.open(`{{ route('pos.receipts.show', $sale) }}?format=thermal`, '_blank');
        },

        async emailReceipt() {
            try {
                const response = await fetch(`{{ route('pos.receipts.email', $sale) }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        email: '{{ $sale->customer->email ?? '' }}'
                    })
                });

                const data = await response.json();
                alert(data.message);
            } catch (error) {
                console.error('Error sending email:', error);
                alert('حدث خطأ أثناء إرسال البريد الإلكتروني');
            }
        },

        async smsReceipt() {
            try {
                const response = await fetch(`{{ route('pos.receipts.sms', $sale) }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        phone: '{{ $sale->customer->phone ?? '' }}'
                    })
                });

                const data = await response.json();
                alert(data.message);
            } catch (error) {
                console.error('Error sending SMS:', error);
                alert('حدث خطأ أثناء إرسال الرسالة النصية');
            }
        },

        cancelSale() {
            if (confirm('هل أنت متأكد من إلغاء هذه المبيعة؟')) {
                const reason = prompt('سبب الإلغاء (اختياري):');
                
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ route("pos.cancel", $sale) }}';
                
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                
                const reasonInput = document.createElement('input');
                reasonInput.type = 'hidden';
                reasonInput.name = 'reason';
                reasonInput.value = reason || '';
                
                form.appendChild(csrfToken);
                form.appendChild(reasonInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    }
}
</script>
@endpush
@endsection
