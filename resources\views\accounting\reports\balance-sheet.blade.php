@extends('layouts.main')

@section('title', 'الميزانية العمومية')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">الميزانية العمومية</h1>
            <p class="text-gray-600 dark:text-gray-400">الأصول والخصوم وحقوق الملكية كما في {{ $asOfDate }}</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <button onclick="printReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-print mr-2"></i>
                طباعة
            </button>
            
            <button onclick="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير
            </button>
            
            <a href="{{ route('accounting-reports.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كما في تاريخ</label>
                <input type="date" name="as_of_date" value="{{ $asOfDate }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                <i class="fas fa-search mr-2"></i>
                تحديث
            </button>
        </form>
    </div>

    <!-- Balance Sheet -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden" id="balance-sheet-report">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="text-center">
                <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100">الميزانية العمومية</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">كما في {{ $asOfDate }}</p>
            </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
            <!-- Assets -->
            <div>
                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                    الأصول
                </h3>
                
                <!-- Current Assets -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3">الأصول المتداولة</h4>
                    <div class="space-y-2 mr-4">
                        @foreach($currentAssets as $asset)
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 dark:text-gray-300">{{ $asset->account_name }}</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ number_format($asset->getBalance(null, $asOfDate), 2) }}
                                </span>
                            </div>
                            @foreach($asset->children as $child)
                                <div class="flex justify-between items-center mr-4">
                                    <span class="text-gray-600 dark:text-gray-400 text-sm">{{ $child->account_name }}</span>
                                    <span class="text-gray-800 dark:text-gray-200 text-sm">
                                        {{ number_format($child->getBalance(null, $asOfDate), 2) }}
                                    </span>
                                </div>
                            @endforeach
                        @endforeach
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 mt-3 pt-2">
                        <div class="flex justify-between items-center font-semibold">
                            <span class="text-gray-800 dark:text-gray-200">إجمالي الأصول المتداولة</span>
                            <span class="text-gray-900 dark:text-gray-100">{{ number_format($totalCurrentAssets, 2) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Fixed Assets -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3">الأصول الثابتة</h4>
                    <div class="space-y-2 mr-4">
                        @foreach($fixedAssets as $asset)
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 dark:text-gray-300">{{ $asset->account_name }}</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ number_format($asset->getBalance(null, $asOfDate), 2) }}
                                </span>
                            </div>
                            @foreach($asset->children as $child)
                                <div class="flex justify-between items-center mr-4">
                                    <span class="text-gray-600 dark:text-gray-400 text-sm">{{ $child->account_name }}</span>
                                    <span class="text-gray-800 dark:text-gray-200 text-sm">
                                        {{ number_format($child->getBalance(null, $asOfDate), 2) }}
                                    </span>
                                </div>
                            @endforeach
                        @endforeach
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 mt-3 pt-2">
                        <div class="flex justify-between items-center font-semibold">
                            <span class="text-gray-800 dark:text-gray-200">إجمالي الأصول الثابتة</span>
                            <span class="text-gray-900 dark:text-gray-100">{{ number_format($totalFixedAssets, 2) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Total Assets -->
                <div class="border-t-2 border-gray-300 dark:border-gray-600 pt-3">
                    <div class="flex justify-between items-center font-bold text-lg">
                        <span class="text-gray-900 dark:text-gray-100">إجمالي الأصول</span>
                        <span class="text-blue-600 dark:text-blue-400">{{ number_format($totalAssets, 2) }} ريال</span>
                    </div>
                </div>
            </div>

            <!-- Liabilities and Equity -->
            <div>
                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                    الخصوم وحقوق الملكية
                </h3>
                
                <!-- Current Liabilities -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3">الخصوم المتداولة</h4>
                    <div class="space-y-2 mr-4">
                        @foreach($currentLiabilities as $liability)
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 dark:text-gray-300">{{ $liability->account_name }}</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ number_format($liability->getBalance(null, $asOfDate), 2) }}
                                </span>
                            </div>
                            @foreach($liability->children as $child)
                                <div class="flex justify-between items-center mr-4">
                                    <span class="text-gray-600 dark:text-gray-400 text-sm">{{ $child->account_name }}</span>
                                    <span class="text-gray-800 dark:text-gray-200 text-sm">
                                        {{ number_format($child->getBalance(null, $asOfDate), 2) }}
                                    </span>
                                </div>
                            @endforeach
                        @endforeach
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 mt-3 pt-2">
                        <div class="flex justify-between items-center font-semibold">
                            <span class="text-gray-800 dark:text-gray-200">إجمالي الخصوم المتداولة</span>
                            <span class="text-gray-900 dark:text-gray-100">{{ number_format($totalCurrentLiabilities, 2) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Equity -->
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3">حقوق الملكية</h4>
                    <div class="space-y-2 mr-4">
                        @foreach($equity as $equityAccount)
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 dark:text-gray-300">{{ $equityAccount->account_name }}</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ number_format($equityAccount->getBalance(null, $asOfDate), 2) }}
                                </span>
                            </div>
                            @foreach($equityAccount->children as $child)
                                <div class="flex justify-between items-center mr-4">
                                    <span class="text-gray-600 dark:text-gray-400 text-sm">{{ $child->account_name }}</span>
                                    <span class="text-gray-800 dark:text-gray-200 text-sm">
                                        {{ number_format($child->getBalance(null, $asOfDate), 2) }}
                                    </span>
                                </div>
                            @endforeach
                        @endforeach
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 mt-3 pt-2">
                        <div class="flex justify-between items-center font-semibold">
                            <span class="text-gray-800 dark:text-gray-200">إجمالي حقوق الملكية</span>
                            <span class="text-gray-900 dark:text-gray-100">{{ number_format($totalEquity, 2) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Total Liabilities and Equity -->
                <div class="border-t-2 border-gray-300 dark:border-gray-600 pt-3">
                    <div class="flex justify-between items-center font-bold text-lg">
                        <span class="text-gray-900 dark:text-gray-100">إجمالي الخصوم وحقوق الملكية</span>
                        <span class="text-green-600 dark:text-green-400">{{ number_format($totalLiabilitiesAndEquity, 2) }} ريال</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Balance Verification -->
        <div class="p-6 border-t border-gray-200 dark:border-gray-700">
            <div class="flex justify-center">
                @if(abs($totalAssets - $totalLiabilitiesAndEquity) < 0.01)
                    <div class="flex items-center text-green-600 dark:text-green-400">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span class="font-medium">الميزانية متوازنة - الأصول = الخصوم + حقوق الملكية</span>
                    </div>
                @else
                    <div class="flex items-center text-red-600 dark:text-red-400">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span class="font-medium">
                            تحذير: الميزانية غير متوازنة - الفرق: {{ number_format(abs($totalAssets - $totalLiabilitiesAndEquity), 2) }} ريال
                        </span>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function printReport() {
    window.print();
}

function exportReport() {
    alert('ميزة التصدير قيد التطوير');
}

// Print styles
const printStyles = `
    @media print {
        body * {
            visibility: hidden;
        }
        #balance-sheet-report, #balance-sheet-report * {
            visibility: visible;
        }
        #balance-sheet-report {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none !important;
        }
    }
`;

// Add print styles to head
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
@endpush
@endsection
