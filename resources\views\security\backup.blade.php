@extends('layouts.main')

@section('title', 'النسخ الاحتياطي والأمان')

@section('content')
<div class="space-y-6" x-data="securityBackup()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">النسخ الاحتياطي والأمان</h1>
            <p class="text-gray-600 dark:text-gray-400">إدارة النسخ الاحتياطية وأمان النظام</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="createBackup()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                نسخة احتياطية جديدة
            </button>
            <button @click="securityScan()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                فحص أمني
            </button>
        </div>
    </div>

    <!-- Security Status -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">حالة الأمان</p>
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400" x-text="securityStatus.level">آمن</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">آخر نسخة احتياطية</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="backupStatus.lastBackup">اليوم</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">تهديدات محتملة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="securityStatus.threats">0</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">محاولات دخول فاشلة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="securityStatus.failedLogins">3</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup Management -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">إدارة النسخ الاحتياطية</h3>
            <div class="flex space-x-2 space-x-reverse">
                <select x-model="backupFilter" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="all">جميع النسخ</option>
                    <option value="automatic">تلقائية</option>
                    <option value="manual">يدوية</option>
                </select>
            </div>
        </div>

        <!-- Backup Schedule -->
        <div class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="font-medium text-blue-900 dark:text-blue-100">الجدولة التلقائية</h4>
                    <p class="text-sm text-blue-700 dark:text-blue-300">نسخة احتياطية يومية في الساعة 2:00 صباحاً</p>
                </div>
                <label class="flex items-center">
                    <input type="checkbox" x-model="autoBackupEnabled" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="mr-2 text-sm text-blue-900 dark:text-blue-100">مفعل</span>
                </label>
            </div>
        </div>

        <!-- Backup List -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">اسم النسخة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحجم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">النوع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="backup in filteredBackups" :key="backup.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100" x-text="backup.name"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="backup.date"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="backup.size"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': backup.type === 'automatic',
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': backup.type === 'manual'
                                      }"
                                      x-text="backup.type === 'automatic' ? 'تلقائية' : 'يدوية'">
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': backup.status === 'completed',
                                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': backup.status === 'in_progress',
                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': backup.status === 'failed'
                                      }"
                                      x-text="getStatusText(backup.status)">
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button @click="downloadBackup(backup.id)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400">تحميل</button>
                                    <button @click="restoreBackup(backup.id)" class="text-green-600 hover:text-green-900 dark:text-green-400">استعادة</button>
                                    <button @click="deleteBackup(backup.id)" class="text-red-600 hover:text-red-900 dark:text-red-400">حذف</button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Security Logs -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">سجل الأمان</h3>
            <button @click="clearSecurityLogs()" class="text-sm text-red-600 hover:text-red-800 dark:text-red-400">مسح السجل</button>
        </div>
        
        <div class="space-y-3">
            <template x-for="log in securityLogs" :key="log.id">
                <div class="flex items-start p-4 border rounded-lg" :class="{
                    'border-red-200 bg-red-50 dark:border-red-700 dark:bg-red-900/20': log.level === 'critical',
                    'border-yellow-200 bg-yellow-50 dark:border-yellow-700 dark:bg-yellow-900/20': log.level === 'warning',
                    'border-green-200 bg-green-50 dark:border-green-700 dark:bg-green-900/20': log.level === 'info'
                }">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5" :class="{
                            'text-red-600 dark:text-red-400': log.level === 'critical',
                            'text-yellow-600 dark:text-yellow-400': log.level === 'warning',
                            'text-green-600 dark:text-green-400': log.level === 'info'
                        }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <div class="mr-3 flex-1">
                        <h4 class="text-sm font-medium" :class="{
                            'text-red-800 dark:text-red-200': log.level === 'critical',
                            'text-yellow-800 dark:text-yellow-200': log.level === 'warning',
                            'text-green-800 dark:text-green-200': log.level === 'info'
                        }" x-text="log.event"></h4>
                        <p class="text-sm mt-1" :class="{
                            'text-red-700 dark:text-red-300': log.level === 'critical',
                            'text-yellow-700 dark:text-yellow-300': log.level === 'warning',
                            'text-green-700 dark:text-green-300': log.level === 'info'
                        }" x-text="log.description"></p>
                        <div class="flex justify-between items-center mt-2">
                            <p class="text-xs opacity-75" x-text="log.timestamp"></p>
                            <p class="text-xs opacity-75" x-text="'IP: ' + log.ip"></p>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

@push('scripts')
<script>
function securityBackup() {
    return {
        securityStatus: {
            level: 'آمن',
            threats: 0,
            failedLogins: 3
        },
        backupStatus: {
            lastBackup: 'اليوم'
        },
        autoBackupEnabled: true,
        backupFilter: 'all',
        backups: [
            {
                id: 1,
                name: 'backup_2024_07_09_02_00',
                date: '2024-07-09 02:00',
                size: '245 MB',
                type: 'automatic',
                status: 'completed'
            },
            {
                id: 2,
                name: 'backup_manual_2024_07_08',
                date: '2024-07-08 15:30',
                size: '238 MB',
                type: 'manual',
                status: 'completed'
            },
            {
                id: 3,
                name: 'backup_2024_07_08_02_00',
                date: '2024-07-08 02:00',
                size: '242 MB',
                type: 'automatic',
                status: 'completed'
            }
        ],
        securityLogs: [
            {
                id: 1,
                level: 'info',
                event: 'تسجيل دخول ناجح',
                description: 'تم تسجيل دخول المستخدم <EMAIL> بنجاح',
                timestamp: '2024-07-09 14:30:22',
                ip: '************'
            },
            {
                id: 2,
                level: 'warning',
                event: 'محاولة دخول فاشلة',
                description: 'محاولة دخول فاشلة للمستخدم <EMAIL>',
                timestamp: '2024-07-09 14:25:15',
                ip: '*************'
            },
            {
                id: 3,
                level: 'info',
                event: 'نسخة احتياطية مكتملة',
                description: 'تم إنشاء النسخة الاحتياطية التلقائية بنجاح',
                timestamp: '2024-07-09 02:00:00',
                ip: 'localhost'
            }
        ],

        get filteredBackups() {
            if (this.backupFilter === 'all') {
                return this.backups;
            }
            return this.backups.filter(backup => backup.type === this.backupFilter);
        },

        createBackup() {
            const newBackup = {
                id: Date.now(),
                name: `backup_manual_${new Date().toISOString().split('T')[0]}`,
                date: new Date().toLocaleString('ar-EG'),
                size: Math.floor(Math.random() * 50 + 200) + ' MB',
                type: 'manual',
                status: 'in_progress'
            };
            
            this.backups.unshift(newBackup);
            
            // محاكاة إكمال النسخة الاحتياطية
            setTimeout(() => {
                newBackup.status = 'completed';
                this.addSecurityLog('info', 'نسخة احتياطية يدوية مكتملة', 'تم إنشاء نسخة احتياطية يدوية بنجاح');
            }, 3000);
        },

        downloadBackup(id) {
            alert(`تحميل النسخة الاحتياطية ${id}`);
        },

        restoreBackup(id) {
            if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                alert(`استعادة النسخة الاحتياطية ${id}`);
                this.addSecurityLog('warning', 'استعادة نسخة احتياطية', `تم استعادة النسخة الاحتياطية ${id}`);
            }
        },

        deleteBackup(id) {
            if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
                this.backups = this.backups.filter(backup => backup.id !== id);
                this.addSecurityLog('warning', 'حذف نسخة احتياطية', `تم حذف النسخة الاحتياطية ${id}`);
            }
        },

        securityScan() {
            alert('بدء الفحص الأمني...');
            this.addSecurityLog('info', 'فحص أمني', 'تم بدء فحص أمني شامل للنظام');
        },

        getStatusText(status) {
            const statuses = {
                'completed': 'مكتملة',
                'in_progress': 'قيد التنفيذ',
                'failed': 'فاشلة'
            };
            return statuses[status] || status;
        },

        addSecurityLog(level, event, description) {
            this.securityLogs.unshift({
                id: Date.now(),
                level: level,
                event: event,
                description: description,
                timestamp: new Date().toLocaleString('ar-EG'),
                ip: '************'
            });
            
            // الاحتفاظ بآخر 20 سجل فقط
            if (this.securityLogs.length > 20) {
                this.securityLogs = this.securityLogs.slice(0, 20);
            }
        },

        clearSecurityLogs() {
            if (confirm('هل أنت متأكد من مسح سجل الأمان؟')) {
                this.securityLogs = [];
            }
        }
    }
}
</script>
@endpush
@endsection
