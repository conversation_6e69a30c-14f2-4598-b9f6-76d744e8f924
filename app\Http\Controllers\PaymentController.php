<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\SalePayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    /**
     * Process cash payment
     */
    public function processCashPayment(Request $request, Sale $sale)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'notes' => 'nullable|string|max:500',
        ]);

        if (!$sale->canBeModified()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تعديل هذه المبيعة.'
            ], 400);
        }

        DB::beginTransaction();
        try {
            $payment = SalePayment::createCashPayment(
                $sale->id,
                $request->amount,
                $request->notes
            );

            // Update sale payment status
            $sale->paid_amount += $request->amount;
            
            if ($sale->paid_amount >= $sale->total_amount) {
                $sale->payment_status = 'paid';
                $sale->change_amount = $sale->paid_amount - $sale->total_amount;
                $sale->status = 'completed';
                $sale->completed_at = now();
            } elseif ($sale->paid_amount > 0) {
                $sale->payment_status = 'partial';
            }

            $sale->remaining_amount = $sale->total_amount - $sale->paid_amount;
            $sale->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم معالجة الدفع النقدي بنجاح.',
                'payment' => $payment,
                'sale' => $sale->fresh(),
                'change_amount' => $sale->change_amount,
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء معالجة الدفع: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process card payment
     */
    public function processCardPayment(Request $request, Sale $sale)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'card_last_four' => 'required|string|size:4',
            'card_type' => 'required|string|in:visa,mastercard,amex,mada',
            'reference_number' => 'required|string|max:50',
            'notes' => 'nullable|string|max:500',
        ]);

        if (!$sale->canBeModified()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تعديل هذه المبيعة.'
            ], 400);
        }

        DB::beginTransaction();
        try {
            $cardDetails = [
                'last_four' => $request->card_last_four,
                'card_type' => $request->card_type,
                'reference_number' => $request->reference_number,
                'notes' => $request->notes,
            ];

            $payment = SalePayment::createCardPayment(
                $sale->id,
                $request->amount,
                $cardDetails
            );

            // Update sale payment status
            $sale->paid_amount += $request->amount;
            
            if ($sale->paid_amount >= $sale->total_amount) {
                $sale->payment_status = 'paid';
                $sale->status = 'completed';
                $sale->completed_at = now();
            } elseif ($sale->paid_amount > 0) {
                $sale->payment_status = 'partial';
            }

            $sale->remaining_amount = $sale->total_amount - $sale->paid_amount;
            $sale->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم معالجة دفع البطاقة بنجاح.',
                'payment' => $payment,
                'sale' => $sale->fresh(),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء معالجة الدفع: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process bank transfer payment
     */
    public function processBankTransferPayment(Request $request, Sale $sale)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'bank_name' => 'required|string|max:100',
            'reference_number' => 'required|string|max:50',
            'account_number' => 'nullable|string|max:50',
            'notes' => 'nullable|string|max:500',
        ]);

        if (!$sale->canBeModified()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تعديل هذه المبيعة.'
            ], 400);
        }

        DB::beginTransaction();
        try {
            $bankDetails = [
                'bank_name' => $request->bank_name,
                'reference_number' => $request->reference_number,
                'account_number' => $request->account_number,
                'notes' => $request->notes,
            ];

            $payment = SalePayment::createBankTransferPayment(
                $sale->id,
                $request->amount,
                $bankDetails
            );

            // Bank transfers usually need verification, so keep as pending
            $sale->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل التحويل البنكي. في انتظار التحقق.',
                'payment' => $payment,
                'sale' => $sale->fresh(),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء معالجة التحويل: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process installment payment
     */
    public function processInstallmentPayment(Request $request, Sale $sale)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'total_installments' => 'required|integer|min:2|max:12',
            'installment_number' => 'required|integer|min:1',
            'due_date' => 'required|date|after:today',
            'notes' => 'nullable|string|max:500',
        ]);

        if (!$sale->canBeModified()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تعديل هذه المبيعة.'
            ], 400);
        }

        DB::beginTransaction();
        try {
            $installmentDetails = [
                'total_installments' => $request->total_installments,
                'installment_number' => $request->installment_number,
                'due_date' => $request->due_date,
                'notes' => $request->notes,
            ];

            $payment = SalePayment::createInstallmentPayment(
                $sale->id,
                $request->amount,
                $installmentDetails
            );

            // For first installment, update sale status
            if ($request->installment_number == 1) {
                $sale->paid_amount += $request->amount;
                $sale->payment_status = 'partial';
                $sale->remaining_amount = $sale->total_amount - $sale->paid_amount;
                $sale->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل القسط بنجاح.',
                'payment' => $payment,
                'sale' => $sale->fresh(),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء معالجة القسط: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Confirm pending payment
     */
    public function confirmPayment(Request $request, SalePayment $payment)
    {
        if ($payment->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'هذا الدفع ليس في حالة انتظار.'
            ], 400);
        }

        DB::beginTransaction();
        try {
            $payment->markAsCompleted(Auth::id());
            
            $sale = $payment->sale;
            $sale->paid_amount += $payment->amount;
            
            if ($sale->paid_amount >= $sale->total_amount) {
                $sale->payment_status = 'paid';
                $sale->status = 'completed';
                $sale->completed_at = now();
            } elseif ($sale->paid_amount > 0) {
                $sale->payment_status = 'partial';
            }

            $sale->remaining_amount = $sale->total_amount - $sale->paid_amount;
            $sale->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تأكيد الدفع بنجاح.',
                'payment' => $payment->fresh(),
                'sale' => $sale->fresh(),
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تأكيد الدفع: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process refund
     */
    public function processRefund(Request $request, SalePayment $payment)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . $payment->getRefundableAmount(),
            'reason' => 'required|string|max:500',
        ]);

        if (!$payment->canBeRefunded()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن استرداد هذا الدفع.'
            ], 400);
        }

        DB::beginTransaction();
        try {
            $payment->processRefund(
                $request->amount,
                $request->reason,
                Auth::id()
            );

            $sale = $payment->sale;
            $sale->paid_amount -= $request->amount;
            
            if ($sale->paid_amount <= 0) {
                $sale->payment_status = 'refunded';
                $sale->status = 'refunded';
            } elseif ($sale->paid_amount < $sale->total_amount) {
                $sale->payment_status = 'partial';
            }

            $sale->remaining_amount = $sale->total_amount - $sale->paid_amount;
            $sale->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم معالجة الاسترداد بنجاح.',
                'payment' => $payment->fresh(),
                'sale' => $sale->fresh(),
                'refunded_amount' => $request->amount,
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء معالجة الاسترداد: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment methods for location
     */
    public function getPaymentMethods(Request $request)
    {
        $locationId = $request->get('location_id');
        
        if ($locationId) {
            $location = \App\Models\Location::find($locationId);
            $methods = $location ? $location->getDefaultPaymentMethods() : ['cash', 'card'];
        } else {
            $methods = ['cash', 'card', 'bank_transfer'];
        }

        $paymentMethods = [];
        foreach ($methods as $method) {
            $paymentMethods[] = [
                'value' => $method,
                'label' => $this->getPaymentMethodLabel($method),
                'icon' => $this->getPaymentMethodIcon($method),
            ];
        }

        return response()->json($paymentMethods);
    }

    /**
     * Get payment method label
     */
    private function getPaymentMethodLabel($method)
    {
        $labels = [
            'cash' => 'نقدي',
            'card' => 'بطاقة ائتمان',
            'bank_transfer' => 'تحويل بنكي',
            'check' => 'شيك',
            'digital_wallet' => 'محفظة رقمية',
            'installment' => 'تقسيط',
        ];

        return $labels[$method] ?? $method;
    }

    /**
     * Get payment method icon
     */
    private function getPaymentMethodIcon($method)
    {
        $icons = [
            'cash' => 'fas fa-money-bill-wave',
            'card' => 'fas fa-credit-card',
            'bank_transfer' => 'fas fa-university',
            'check' => 'fas fa-money-check',
            'digital_wallet' => 'fas fa-mobile-alt',
            'installment' => 'fas fa-calendar-alt',
        ];

        return $icons[$method] ?? 'fas fa-dollar-sign';
    }
}
