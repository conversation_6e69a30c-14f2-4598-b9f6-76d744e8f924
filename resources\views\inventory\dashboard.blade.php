@extends('layouts.main')

@section('title', 'لوحة تحكم المخزون')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">لوحة تحكم المخزون</h1>
            <p class="text-gray-600 dark:text-gray-400">متابعة وإدارة المخزون والمنتجات</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('inventory.adjust-stock') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-plus mr-2"></i>
                تعديل مخزون
            </a>
            <a href="{{ route('inventory.reports') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-chart-bar mr-2"></i>
                التقارير
            </a>
            <a href="{{ route('inventory.movements') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-exchange-alt mr-2"></i>
                حركات المخزون
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Items -->
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium">إجمالي الأصناف</h3>
                    <p class="text-3xl font-bold">{{ number_format($stats['total_items']) }}</p>
                    <p class="text-blue-100 text-sm">صنف في المخزون</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-boxes"></i>
                </div>
            </div>
        </div>

        <!-- Total Value -->
        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium">القيمة الإجمالية</h3>
                    <p class="text-3xl font-bold">{{ number_format($stats['total_value'], 2) }}</p>
                    <p class="text-green-100 text-sm">ريال سعودي</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
        </div>

        <!-- Low Stock -->
        <div class="bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium">مخزون منخفض</h3>
                    <p class="text-3xl font-bold">{{ $stats['low_stock_items'] }}</p>
                    <p class="text-yellow-100 text-sm">يحتاج إعادة طلب</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>

        <!-- Out of Stock -->
        <div class="bg-gradient-to-br from-red-500 to-red-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium">نفد المخزون</h3>
                    <p class="text-3xl font-bold">{{ $stats['out_of_stock_items'] }}</p>
                    <p class="text-red-100 text-sm">يحتاج طلب فوري</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-times-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Movements -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">آخر حركات المخزون</h3>
                <a href="{{ route('inventory.movements') }}" class="text-blue-600 hover:text-blue-700 text-sm">
                    عرض الكل
                </a>
            </div>
        </div>
        <div class="p-6">
            @if($recentMovements->count() > 0)
                <div class="space-y-4">
                    @foreach($recentMovements as $movement)
                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <div class="w-10 h-10 rounded-full flex items-center justify-center
                                    @if($movement->type == 'in') bg-green-100 text-green-600
                                    @elseif($movement->type == 'out') bg-red-100 text-red-600
                                    @elseif($movement->type == 'transfer') bg-blue-100 text-blue-600
                                    @else bg-yellow-100 text-yellow-600 @endif">
                                    <i class="fas 
                                        @if($movement->type == 'in') fa-arrow-down
                                        @elseif($movement->type == 'out') fa-arrow-up
                                        @elseif($movement->type == 'transfer') fa-exchange-alt
                                        @else fa-edit @endif"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900 dark:text-gray-100">
                                        {{ $movement->inventory->product->name ?? 'منتج محذوف' }}
                                    </p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $movement->reason }} - {{ $movement->quantity }} قطعة
                                    </p>
                                </div>
                            </div>
                            <div class="text-left">
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $movement->created_at->diffForHumans() }}
                                </p>
                                <p class="text-xs text-gray-400 dark:text-gray-500">
                                    {{ $movement->user->name ?? 'مستخدم محذوف' }}
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-exchange-alt text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد حركات مخزون حديثة</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Low Stock Alerts -->
    @if($lowStockItems->count() > 0)
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تنبيهات المخزون المنخفض</h3>
                <a href="{{ route('inventory.low-stock') }}" class="text-yellow-600 hover:text-yellow-700 text-sm">
                    عرض الكل
                </a>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                @foreach($lowStockItems->take(5) as $item)
                    <div class="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <div class="w-10 h-10 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ $item->product->name }}
                                </p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $item->location->name ?? 'موقع غير محدد' }}
                                </p>
                            </div>
                        </div>
                        <div class="text-left">
                            <p class="text-lg font-bold text-yellow-600">{{ $item->quantity }}</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">متبقي</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Near Expiry Items -->
    @if($nearExpiryItems->count() > 0)
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">أصناف قريبة انتهاء الصلاحية</h3>
                <a href="{{ route('inventory.reports', ['type' => 'expiry']) }}" class="text-red-600 hover:text-red-700 text-sm">
                    عرض الكل
                </a>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                @foreach($nearExpiryItems->take(5) as $item)
                    <div class="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <div class="w-10 h-10 bg-red-100 text-red-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-times"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ $item->product->name }}
                                </p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    ينتهي في {{ $item->expiry_date->diffForHumans() }}
                                </p>
                            </div>
                        </div>
                        <div class="text-left">
                            <p class="text-lg font-bold text-red-600">{{ $item->quantity }}</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">قطعة</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
