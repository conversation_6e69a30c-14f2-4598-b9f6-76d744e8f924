<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Form Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Cairo', 'Tajawal', sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">Customer Creation Form Test</h1>
        
        <!-- Test Individual Customer -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Test 1: Individual Customer</h2>
            <form action="{{ route('customers.store') }}" method="POST" class="space-y-4">
                @csrf
                <input type="hidden" name="type" value="individual">
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">الاسم الأول</label>
                        <input type="text" name="first_name" value="أحمد" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">الاسم الأخير</label>
                        <input type="text" name="last_name" value="محمد" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">رقم الجوال</label>
                        <input type="text" name="mobile" value="0501234567" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">البريد الإلكتروني</label>
                        <input type="email" name="email" value="<EMAIL>" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" checked class="mr-2">
                        عميل نشط
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_vip" value="1" class="mr-2">
                        عميل VIP
                    </label>
                </div>
                
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                    إنشاء عميل فردي
                </button>
            </form>
        </div>
        
        <!-- Test Business Customer -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Test 2: Business Customer</h2>
            <form action="{{ route('customers.store') }}" method="POST" class="space-y-4">
                @csrf
                <input type="hidden" name="type" value="business">
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">اسم الشركة</label>
                        <input type="text" name="company_name" value="شركة التقنية المتقدمة" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">رقم الجوال</label>
                        <input type="text" name="mobile" value="0509876543" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">البريد الإلكتروني</label>
                        <input type="email" name="email" value="<EMAIL>" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">المدينة</label>
                        <input type="text" name="city" value="الرياض" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" checked class="mr-2">
                        عميل نشط
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_vip" value="1" checked class="mr-2">
                        عميل VIP
                    </label>
                </div>
                
                <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">
                    إنشاء عميل تجاري
                </button>
            </form>
        </div>
        
        <!-- Test Form Validation -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Test 3: Validation Test (Empty Form)</h2>
            <form action="{{ route('customers.store') }}" method="POST" class="space-y-4">
                @csrf
                <input type="hidden" name="type" value="individual">
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">الاسم الأول (فارغ)</label>
                        <input type="text" name="first_name" value="" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">رقم الجوال (فارغ)</label>
                        <input type="text" name="mobile" value="" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                </div>
                
                <button type="submit" class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700">
                    اختبار التحقق من الصحة
                </button>
            </form>
        </div>
        
        <!-- Display Errors -->
        @if($errors->any())
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mt-8">
                <h3 class="text-red-800 font-semibold mb-2">أخطاء التحقق:</h3>
                <ul class="list-disc list-inside text-red-700">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        
        <!-- Display Success -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mt-8">
                <p class="text-green-800 font-semibold">{{ session('success') }}</p>
            </div>
        @endif
        
        <!-- Display General Errors -->
        @if(session('error'))
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mt-8">
                <p class="text-red-800 font-semibold">{{ session('error') }}</p>
            </div>
        @endif
    </div>
</body>
</html>
