# تعليمات إعداد نظام نقطة البيع

## 🚨 حل مشكلة الترحيلات

### الطريقة الأولى: تشغيل الترحيلات (الأفضل)

1. افتح Terminal أو Command Prompt
2. انتقل إلى مجلد المشروع:
   ```bash
   cd C:\laragon\www\tareq
   ```
3. شغل الترحيلات:
   ```bash
   php artisan migrate
   ```

### الطريقة الثانية: تشغيل ملف SQL مباشرة

إذا لم تتمكن من تشغيل الأمر أعلاه:

1. افتح **phpMyAdmin** من Laragon
2. اختر قاعدة البيانات `tareq`
3. اذهب إلى تبويب **SQL**
4. انسخ محتوى ملف `database/pos_tables.sql` والصقه
5. اضغط **Go** لتنفيذ الاستعلامات

### الطريقة الثالثة: إنشاء الجداول يدوياً

إذا فشلت الطرق السابقة، يمكنك إنشاء الجداول يدوياً:

#### 1. تحديث جدول locations
```sql
ALTER TABLE `locations` 
ADD COLUMN `is_main_branch` BOOLEAN DEFAULT FALSE,
ADD COLUMN `description` TEXT NULL,
ADD COLUMN `receipt_header` TEXT NULL,
ADD COLUMN `receipt_footer` TEXT NULL;
```

#### 2. إنشاء جدول sales
```sql
CREATE TABLE `sales` (
  `id` bigint UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `sale_number` varchar(255) NOT NULL UNIQUE,
  `customer_id` bigint UNSIGNED NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `location_id` bigint UNSIGNED NOT NULL,
  `sale_type` enum('repair_service','parts_only','accessories','mixed') DEFAULT 'parts_only',
  `status` enum('pending','processing','completed','cancelled','refunded') DEFAULT 'pending',
  `payment_status` enum('pending','partial','paid','refunded','failed') DEFAULT 'pending',
  `sale_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `subtotal` decimal(10,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `total_amount` decimal(10,2) DEFAULT 0.00,
  `paid_amount` decimal(10,2) DEFAULT 0.00,
  `remaining_amount` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NULL,
  `updated_at` timestamp NULL
);
```

#### 3. إدراج موقع افتراضي
```sql
INSERT INTO `locations` (
    `name`, `code`, `type`, `is_active`, `is_default`, `is_main_branch`,
    `pos_settings`, `receipt_header`, `receipt_footer`, `created_at`, `updated_at`
) VALUES (
    'الفرع الرئيسي', 'MAIN', 'store', 1, 1, 1,
    '{"allow_sales": true, "tax_rate": 15}',
    'مركز الصيانة المتقدم',
    'شكراً لثقتكم',
    NOW(), NOW()
);
```

## ✅ التحقق من نجاح الإعداد

بعد تنفيذ أي من الطرق أعلاه:

1. اذهب إلى: `http://tareq.test/pos`
2. يجب أن تظهر صفحة نقطة البيع بدون أخطاء
3. ستجد إحصائيات اليوم والإجراءات السريعة

## 📊 إضافة البيانات التجريبية

لإضافة عملاء وقطع غيار تجريبية:

### الطريقة السريعة:
1. افتح phpMyAdmin
2. اختر قاعدة البيانات `tareq`
3. انسخ محتوى ملف `database/quick_sample_data.sql`
4. شغل الاستعلامات

### البيانات التي ستُضاف:
- **3 عملاء تجريبيين** (أفراد وشركة)
- **6 قطع غيار وإكسسوارات** مختلفة
- **مستخدم افتراضي** للنظام

بعد إضافة البيانات:
- اذهب إلى: `http://tareq.test/pos/create`
- ستجد قائمة العملاء والمنتجات جاهزة

## 📊 إضافة بيانات التقارير

لاختبار صفحة التقارير مع بيانات الإصلاحات:

### إضافة بيانات الإصلاحات والفنيين:
1. افتح phpMyAdmin
2. اختر قاعدة البيانات `tareq`
3. انسخ محتوى ملف `database/sample_repairs_data.sql`
4. شغل الاستعلامات

### البيانات التي ستُضاف:
- **3 فنيين** بتخصصات مختلفة
- **6 إصلاحات** بحالات مختلفة (مكتملة، قيد التنفيذ، انتظار قطع)
- **بيانات مالية** للتقارير

بعد إضافة البيانات:
- اذهب إلى: `http://tareq.test/reports`
- ستجد لوحة تحكم التقارير مع إحصائيات حقيقية

## 💰 إضافة بيانات المبيعات للتقارير المالية

لاختبار التقارير المالية مع بيانات المبيعات:

### إضافة بيانات المبيعات والمدفوعات:
1. افتح phpMyAdmin
2. اختر قاعدة البيانات `tareq`
3. انسخ محتوى ملف `database/sample_sales_data.sql`
4. شغل الاستعلامات

### البيانات التي ستُضاف:
- **7 مبيعات** بحالات مختلفة (مكتملة ومعلقة)
- **13 عنصر مبيعة** (قطع غيار وخدمات)
- **6 مدفوعات** بطرق دفع مختلفة (نقدي، بطاقة، تحويل)
- **بيانات مالية** شاملة للتقارير

بعد إضافة البيانات:
- اذهب إلى: `http://tareq.test/reports/financial`
- ستجد تقارير مالية مفصلة مع رسوم بيانية

## 🔧 حل المشاكل الشائعة

### مشكلة: "Class 'Location' not found"
- تأكد من وجود ملف `app/Models/Location.php`
- شغل: `composer dump-autoload`

### مشكلة: "Column not found"
- تأكد من تشغيل جميع الترحيلات
- تحقق من وجود الأعمدة في قاعدة البيانات

### مشكلة: "Route not found"
- تأكد من وجود المسارات في `routes/web.php`
- شغل: `php artisan route:cache`

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملفات الـ logs في `storage/logs/laravel.log`
2. تأكد من أن جميع الملفات موجودة
3. تحقق من إعدادات قاعدة البيانات في `.env`

## 🎯 الخطوات التالية

بعد حل مشكلة الترحيلات:
1. اختبر إنشاء مبيعة جديدة: `/pos/create`
2. اختبر عرض التقارير: `/pos/reports/dashboard`
3. أضف منتجات وعملاء حقيقيين
4. اختبر جميع وظائف النظام
