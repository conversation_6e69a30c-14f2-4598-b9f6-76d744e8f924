<?php

namespace App\Services;

use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\SalePayment;
use App\Models\Part;
use App\Models\Customer;
use App\Models\Location;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OfflineModeService
{
    const CACHE_PREFIX = 'offline_pos_';
    const SYNC_QUEUE_KEY = 'offline_sync_queue';
    const OFFLINE_DATA_TTL = 3600; // 1 hour

    /**
     * Prepare offline data for POS
     */
    public function prepareOfflineData($locationId = null)
    {
        try {
            $data = [
                'timestamp' => now()->timestamp,
                'location_id' => $locationId,
                'parts' => $this->getPartsData($locationId),
                'customers' => $this->getCustomersData(),
                'location' => $this->getLocationData($locationId),
                'settings' => $this->getOfflineSettings($locationId),
                'last_sale_number' => $this->getLastSaleNumber($locationId),
            ];

            // Cache the data
            Cache::put(
                self::CACHE_PREFIX . 'data_' . ($locationId ?? 'main'),
                $data,
                self::OFFLINE_DATA_TTL
            );

            return [
                'success' => true,
                'data' => $data,
                'size' => strlen(json_encode($data)),
                'expires_at' => now()->addSeconds(self::OFFLINE_DATA_TTL),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to prepare offline data', [
                'location_id' => $locationId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'فشل في تحضير البيانات للوضع غير المتصل',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Store offline sale
     */
    public function storeOfflineSale(array $saleData)
    {
        try {
            // Generate temporary offline ID
            $offlineId = 'offline_' . uniqid() . '_' . time();
            
            // Add offline metadata
            $saleData['offline_id'] = $offlineId;
            $saleData['created_offline'] = true;
            $saleData['offline_timestamp'] = now()->timestamp;
            $saleData['sync_status'] = 'pending';

            // Store in sync queue
            $syncQueue = Cache::get(self::SYNC_QUEUE_KEY, []);
            $syncQueue[] = [
                'type' => 'sale',
                'data' => $saleData,
                'offline_id' => $offlineId,
                'created_at' => now()->timestamp,
            ];

            Cache::put(self::SYNC_QUEUE_KEY, $syncQueue, 86400); // 24 hours

            // Store individual sale for quick access
            Cache::put(
                self::CACHE_PREFIX . 'sale_' . $offlineId,
                $saleData,
                86400
            );

            Log::info('Offline sale stored', [
                'offline_id' => $offlineId,
                'total_amount' => $saleData['total_amount'] ?? 0
            ]);

            return [
                'success' => true,
                'offline_id' => $offlineId,
                'message' => 'تم حفظ المبيعة في الوضع غير المتصل'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to store offline sale', [
                'error' => $e->getMessage(),
                'sale_data' => $saleData
            ]);

            return [
                'success' => false,
                'message' => 'فشل في حفظ المبيعة',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync offline data to database
     */
    public function syncOfflineData()
    {
        $syncQueue = Cache::get(self::SYNC_QUEUE_KEY, []);
        
        if (empty($syncQueue)) {
            return [
                'success' => true,
                'message' => 'لا توجد بيانات للمزامنة',
                'synced_count' => 0
            ];
        }

        $syncedCount = 0;
        $failedCount = 0;
        $errors = [];

        DB::beginTransaction();
        try {
            foreach ($syncQueue as $index => $queueItem) {
                try {
                    if ($queueItem['type'] === 'sale') {
                        $result = $this->syncOfflineSale($queueItem['data']);
                        
                        if ($result['success']) {
                            $syncedCount++;
                            // Remove from queue
                            unset($syncQueue[$index]);
                            // Remove individual cache
                            Cache::forget(self::CACHE_PREFIX . 'sale_' . $queueItem['offline_id']);
                        } else {
                            $failedCount++;
                            $errors[] = $result['message'];
                        }
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "خطأ في مزامنة العنصر {$queueItem['offline_id']}: " . $e->getMessage();
                    Log::error('Failed to sync offline item', [
                        'offline_id' => $queueItem['offline_id'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Update sync queue
            Cache::put(self::SYNC_QUEUE_KEY, array_values($syncQueue), 86400);

            DB::commit();

            Log::info('Offline sync completed', [
                'synced' => $syncedCount,
                'failed' => $failedCount
            ]);

            return [
                'success' => $failedCount === 0,
                'message' => "تم مزامنة {$syncedCount} عنصر" . ($failedCount > 0 ? " وفشل في {$failedCount}" : ""),
                'synced_count' => $syncedCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Offline sync failed', ['error' => $e->getMessage()]);

            return [
                'success' => false,
                'message' => 'فشل في المزامنة: ' . $e->getMessage(),
                'synced_count' => 0,
                'failed_count' => count($syncQueue)
            ];
        }
    }

    /**
     * Sync individual offline sale
     */
    private function syncOfflineSale(array $saleData)
    {
        try {
            // Create sale
            $sale = Sale::create([
                'sale_number' => $this->generateSaleNumber($saleData['location_id'] ?? null),
                'customer_id' => $saleData['customer_id'] ?? null,
                'user_id' => $saleData['user_id'],
                'location_id' => $saleData['location_id'],
                'sale_type' => $saleData['sale_type'],
                'status' => $saleData['status'] ?? 'completed',
                'payment_status' => $saleData['payment_status'] ?? 'paid',
                'sale_date' => $saleData['sale_date'] ?? now(),
                'subtotal' => $saleData['subtotal'],
                'tax_amount' => $saleData['tax_amount'],
                'discount_amount' => $saleData['discount_amount'] ?? 0,
                'total_amount' => $saleData['total_amount'],
                'paid_amount' => $saleData['paid_amount'] ?? $saleData['total_amount'],
                'change_amount' => $saleData['change_amount'] ?? 0,
                'remaining_amount' => $saleData['remaining_amount'] ?? 0,
                'notes' => $saleData['notes'] ?? null,
                'metadata' => array_merge($saleData['metadata'] ?? [], [
                    'synced_from_offline' => true,
                    'offline_id' => $saleData['offline_id'],
                    'offline_timestamp' => $saleData['offline_timestamp']
                ])
            ]);

            // Create sale items
            foreach ($saleData['items'] as $itemData) {
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'item_type' => $itemData['type'],
                    'part_id' => $itemData['part_id'] ?? null,
                    'item_name' => $itemData['name'],
                    'item_code' => $itemData['code'] ?? null,
                    'item_description' => $itemData['description'] ?? null,
                    'item_category' => $itemData['category'] ?? null,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'original_price' => $itemData['original_price'] ?? $itemData['unit_price'],
                    'discount_amount' => $itemData['discount_amount'] ?? 0,
                    'line_total' => $itemData['line_total'],
                    'tax_rate' => $itemData['tax_rate'] ?? 15,
                    'tax_amount' => $itemData['tax_amount'] ?? 0,
                    'affects_inventory' => $itemData['affects_inventory'] ?? true,
                ]);
            }

            // Create payments
            if (isset($saleData['payments'])) {
                foreach ($saleData['payments'] as $paymentData) {
                    SalePayment::create([
                        'sale_id' => $sale->id,
                        'payment_number' => SalePayment::generatePaymentNumber(),
                        'payment_method' => $paymentData['method'],
                        'amount' => $paymentData['amount'],
                        'status' => 'completed',
                        'processed_by' => $saleData['user_id'],
                        'processed_at' => $saleData['sale_date'] ?? now(),
                        'reference_number' => $paymentData['reference_number'] ?? null,
                        'notes' => $paymentData['notes'] ?? null,
                    ]);
                }
            }

            // Process inventory movements
            $inventoryService = app(InventoryIntegrationService::class);
            $inventoryService->processInventoryMovements($sale);

            return [
                'success' => true,
                'sale_id' => $sale->id,
                'sale_number' => $sale->sale_number
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'فشل في مزامنة المبيعة: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get offline sync status
     */
    public function getSyncStatus()
    {
        $syncQueue = Cache::get(self::SYNC_QUEUE_KEY, []);
        
        return [
            'pending_items' => count($syncQueue),
            'last_sync' => Cache::get(self::CACHE_PREFIX . 'last_sync'),
            'queue_items' => collect($syncQueue)->map(function ($item) {
                return [
                    'type' => $item['type'],
                    'offline_id' => $item['offline_id'],
                    'created_at' => $item['created_at'],
                    'age_minutes' => (time() - $item['created_at']) / 60,
                ];
            })
        ];
    }

    /**
     * Clear offline data
     */
    public function clearOfflineData($locationId = null)
    {
        $keys = [
            self::CACHE_PREFIX . 'data_' . ($locationId ?? 'main'),
            self::SYNC_QUEUE_KEY,
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }

        // Clear individual sale caches
        $syncQueue = Cache::get(self::SYNC_QUEUE_KEY, []);
        foreach ($syncQueue as $item) {
            Cache::forget(self::CACHE_PREFIX . 'sale_' . $item['offline_id']);
        }

        return true;
    }

    /**
     * Get parts data for offline use
     */
    private function getPartsData($locationId = null)
    {
        return Part::where('is_active', true)
                  ->where('stock_quantity', '>', 0)
                  ->select([
                      'id', 'name', 'part_number', 'description', 'category',
                      'price', 'cost_price', 'stock_quantity', 'min_stock_level'
                  ])
                  ->get()
                  ->toArray();
    }

    /**
     * Get customers data for offline use
     */
    private function getCustomersData()
    {
        return Customer::where('is_active', true)
                      ->select(['id', 'full_name', 'phone', 'email'])
                      ->limit(1000) // Limit for performance
                      ->get()
                      ->toArray();
    }

    /**
     * Get location data
     */
    private function getLocationData($locationId)
    {
        if (!$locationId) {
            return Location::getMainBranch()?->toArray();
        }

        return Location::find($locationId)?->toArray();
    }

    /**
     * Get offline settings
     */
    private function getOfflineSettings($locationId)
    {
        $location = $locationId ? Location::find($locationId) : Location::getMainBranch();
        
        return [
            'tax_rate' => $location?->getTaxRate() ?? 15,
            'payment_methods' => $location?->getDefaultPaymentMethods() ?? ['cash'],
            'auto_sync' => true,
            'sync_interval' => 300, // 5 minutes
        ];
    }

    /**
     * Get last sale number for offline numbering
     */
    private function getLastSaleNumber($locationId)
    {
        $location = $locationId ? Location::find($locationId) : Location::getMainBranch();
        $locationCode = $location?->code ?? 'MAIN';
        
        $lastSale = Sale::where('sale_number', 'like', "{$locationCode}-%")
                       ->orderBy('sale_number', 'desc')
                       ->first();

        return $lastSale?->sale_number;
    }

    /**
     * Generate sale number for synced offline sale
     */
    private function generateSaleNumber($locationId)
    {
        $location = $locationId ? Location::find($locationId) : Location::getMainBranch();
        return Sale::generateSaleNumber($location?->code);
    }
}
