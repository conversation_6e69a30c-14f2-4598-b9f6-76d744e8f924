@extends('layouts.main')

@section('title', 'التقارير المالية')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">التقارير المالية</h1>
            <p class="text-gray-600 dark:text-gray-400">تحليل شامل للأداء المالي</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('reports.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة للتقارير
            </a>
            <a href="{{ route('reports.export', ['type' => 'financial', 'format' => 'pdf']) }}" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-file-pdf mr-2"></i>
                تصدير PDF
            </a>
            <a href="{{ route('reports.export', ['type' => 'financial', 'format' => 'excel']) }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-file-excel mr-2"></i>
                تصدير Excel
            </a>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <form method="GET" action="{{ route('reports.financial') }}" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" name="start_date" value="{{ $startDate->format('Y-m-d') }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" name="end_date" value="{{ $endDate->format('Y-m-d') }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفترة</label>
                <select name="period" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="daily" {{ $period == 'daily' ? 'selected' : '' }}>يومي</option>
                    <option value="weekly" {{ $period == 'weekly' ? 'selected' : '' }}>أسبوعي</option>
                    <option value="monthly" {{ $period == 'monthly' ? 'selected' : '' }}>شهري</option>
                </select>
            </div>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-search mr-2"></i>
                تطبيق
            </button>
        </form>
    </div>

    <!-- Financial Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100">إجمالي الإيرادات</p>
                    <p class="text-3xl font-bold">{{ number_format($revenue->sum('amount'), 2) }}</p>
                    <p class="text-green-100 text-sm">ريال سعودي</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-arrow-up"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-red-500 to-red-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-red-100">إجمالي المصروفات</p>
                    <p class="text-3xl font-bold">{{ number_format($expenses->sum('amount'), 2) }}</p>
                    <p class="text-red-100 text-sm">ريال سعودي</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-arrow-down"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100">صافي الربح</p>
                    <p class="text-3xl font-bold">{{ number_format($profit->sum('amount'), 2) }}</p>
                    <p class="text-blue-100 text-sm">ريال سعودي</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue vs Expenses Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الإيرادات مقابل المصروفات</h3>
        </div>
        <div class="p-6">
            <canvas id="revenueExpensesChart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Profit Trend -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">اتجاه الأرباح</h3>
        </div>
        <div class="p-6">
            <canvas id="profitTrendChart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Payment Methods Breakdown -->
    @if($paymentMethods->count() > 0)
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">طرق الدفع</h3>
            </div>
            <div class="p-6">
                <canvas id="paymentMethodsChart" width="400" height="200"></canvas>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تفاصيل طرق الدفع</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    @foreach($paymentMethods as $method)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-4 h-4 rounded-full mr-3" style="background-color: {{ getPaymentMethodColor($method->payment_method) }}"></div>
                                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                    {{ getPaymentMethodLabel($method->payment_method) }}
                                </span>
                            </div>
                            <div class="text-left">
                                <p class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ number_format($method->total, 2) }} ريال</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ $method->count }} معاملة</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Financial Metrics Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">المؤشرات المالية التفصيلية</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفترة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإيرادات</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المصروفات</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الربح</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">هامش الربح</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($revenue as $index => $revenueItem)
                        @php
                            $expenseItem = $expenses->get($index);
                            $profitItem = $profit->get($index);
                            $margin = $revenueItem->amount > 0 ? (($profitItem->amount ?? 0) / $revenueItem->amount) * 100 : 0;
                        @endphp
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                {{ $revenueItem->period }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                {{ number_format($revenueItem->amount, 2) }} ريال
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                {{ number_format($expenseItem->amount ?? 0, 2) }} ريال
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm {{ ($profitItem->amount ?? 0) >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                {{ number_format($profitItem->amount ?? 0, 2) }} ريال
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ number_format($margin, 1) }}%
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>



@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue vs Expenses Chart
const revenueData = @json($revenue);
const expensesData = @json($expenses);

const ctx1 = document.getElementById('revenueExpensesChart').getContext('2d');
const revenueExpensesChart = new Chart(ctx1, {
    type: 'bar',
    data: {
        labels: revenueData.map(item => item.period),
        datasets: [{
            label: 'الإيرادات',
            data: revenueData.map(item => item.amount),
            backgroundColor: 'rgba(34, 197, 94, 0.8)',
            borderColor: 'rgb(34, 197, 94)',
            borderWidth: 1
        }, {
            label: 'المصروفات',
            data: expensesData.map(item => item.amount),
            backgroundColor: 'rgba(239, 68, 68, 0.8)',
            borderColor: 'rgb(239, 68, 68)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        }
    }
});

// Profit Trend Chart
const profitData = @json($profit);
const ctx2 = document.getElementById('profitTrendChart').getContext('2d');

const profitTrendChart = new Chart(ctx2, {
    type: 'line',
    data: {
        labels: profitData.map(item => item.period),
        datasets: [{
            label: 'صافي الربح',
            data: profitData.map(item => item.amount),
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        }
    }
});

// Payment Methods Chart
@if($paymentMethods->count() > 0)
const paymentMethodsData = @json($paymentMethods);
const ctx3 = document.getElementById('paymentMethodsChart').getContext('2d');

const paymentMethodsChart = new Chart(ctx3, {
    type: 'doughnut',
    data: {
        labels: paymentMethodsData.map(item => {
            switch(item.payment_method) {
                case 'cash': return 'نقدي';
                case 'card': return 'بطاقة ائتمان';
                case 'bank_transfer': return 'تحويل بنكي';
                case 'check': return 'شيك';
                default: return item.payment_method;
            }
        }),
        datasets: [{
            data: paymentMethodsData.map(item => item.total),
            backgroundColor: [
                '#10B981',
                '#3B82F6',
                '#8B5CF6',
                '#F59E0B',
                '#6B7280'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
            }
        }
    }
});
@endif
</script>
@endpush
@endsection
