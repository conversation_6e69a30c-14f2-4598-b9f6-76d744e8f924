<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WorkOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'work_order_number',
        'technician_id',
        'repair_id',
        'title',
        'description',
        'priority',
        'status',
        'assigned_date',
        'due_date',
        'completed_date',
        'estimated_hours',
        'actual_hours',
        'instructions',
        'notes'
    ];

    protected $casts = [
        'assigned_date' => 'datetime',
        'due_date' => 'datetime',
        'completed_date' => 'datetime',
        'estimated_hours' => 'decimal:2',
        'actual_hours' => 'decimal:2'
    ];

    // Relationships
    public function technician()
    {
        return $this->belongsTo(Technician::class);
    }

    public function repair()
    {
        return $this->belongsTo(Repair::class);
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'pending' => ['class' => 'badge-warning', 'text' => 'في الانتظار'],
            'assigned' => ['class' => 'badge-info', 'text' => 'معين'],
            'in_progress' => ['class' => 'badge-primary', 'text' => 'قيد التنفيذ'],
            'completed' => ['class' => 'badge-success', 'text' => 'مكتمل'],
            'cancelled' => ['class' => 'badge-danger', 'text' => 'ملغي']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getPriorityBadgeAttribute()
    {
        $priorities = [
            'low' => ['class' => 'badge-success', 'text' => 'منخفضة'],
            'normal' => ['class' => 'badge-info', 'text' => 'عادية'],
            'high' => ['class' => 'badge-warning', 'text' => 'عالية'],
            'urgent' => ['class' => 'badge-danger', 'text' => 'عاجلة']
        ];

        return $priorities[$this->priority] ?? ['class' => 'badge-info', 'text' => 'عادية'];
    }

    // Methods
    public function generateWorkOrderNumber()
    {
        $prefix = 'WO';
        $year = now()->year;
        $month = now()->format('m');
        
        $lastWorkOrder = static::whereYear('created_at', $year)
                              ->whereMonth('created_at', now()->month)
                              ->orderBy('id', 'desc')
                              ->first();
        
        $sequence = $lastWorkOrder ? (int)substr($lastWorkOrder->work_order_number, -4) + 1 : 1;
        
        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($workOrder) {
            if (!$workOrder->work_order_number) {
                $workOrder->work_order_number = $workOrder->generateWorkOrderNumber();
            }
        });
    }
}
