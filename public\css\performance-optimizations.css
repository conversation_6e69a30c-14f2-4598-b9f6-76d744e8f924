/* ==========================================================================
   تحسينات الأداء وسرعة التحميل
   ========================================================================== */

/* تحسين الخطوط */
@font-face {
    font-family: 'Cairo';
    font-display: swap;
    src: local('Cairo'), url('/fonts/cairo.woff2') format('woff2');
}

@font-face {
    font-family: 'Tajawal';
    font-display: swap;
    src: local('Tajawal'), url('/fonts/tajawal.woff2') format('woff2');
}

/* تحسين التحميل التدريجي */
.lazy-load {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.lazy-load.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* تحسين الصور */
.optimized-image {
    object-fit: cover;
    object-position: center;
    loading: lazy;
    decoding: async;
}

.image-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* تحسين الجداول الكبيرة */
.virtual-scroll {
    height: 400px;
    overflow-y: auto;
    contain: layout style paint;
}

.table-row {
    contain: layout style;
    will-change: transform;
}

/* تحسين الرسوم المتحركة */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

.smooth-animation {
    animation-fill-mode: both;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين التفاعلات */
.interactive-element {
    touch-action: manipulation;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

/* تحسين الذاكرة */
.memory-efficient {
    contain: layout style paint;
    content-visibility: auto;
    contain-intrinsic-size: 200px;
}

/* تحسين CSS Grid و Flexbox */
.optimized-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    contain: layout;
}

.optimized-flex {
    display: flex;
    contain: layout;
}

/* تحسين الظلال والتأثيرات */
.efficient-shadow {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    transition: box-shadow 0.2s ease;
}

.efficient-shadow:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

/* تحسين الألوان والتدرجات */
.efficient-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    background-attachment: fixed;
}

/* تحسين النصوص */
.optimized-text {
    text-rendering: optimizeSpeed;
    font-feature-settings: "liga" 1, "kern" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسين التمرير */
.smooth-scroll {
    scroll-behavior: smooth;
    overflow-anchor: auto;
}

/* تحسين الأزرار */
.efficient-button {
    contain: layout style;
    isolation: isolate;
    transform: translateZ(0);
}

/* تحسين النماذج */
.optimized-form {
    contain: layout style;
}

.form-field {
    contain: layout;
    isolation: isolate;
}

/* تحسين القوائم */
.efficient-list {
    contain: layout;
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-item {
    contain: layout style;
    display: flex;
    align-items: center;
    padding: 0.5rem;
}

/* تحسين الشبكات */
.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    contain: layout;
}

/* تحسين الصور المتجاوبة */
.responsive-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    loading: lazy;
    decoding: async;
}

/* تحسين الفيديو */
.optimized-video {
    loading: lazy;
    preload: metadata;
    poster: data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjI0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+;
}

/* تحسين الخلفيات */
.efficient-background {
    background-attachment: scroll;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

/* تحسين الحدود */
.efficient-border {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    contain: layout;
}

/* تحسين الانتقالات */
.fast-transition {
    transition: all 0.15s ease-out;
    will-change: transform, opacity;
}

.fast-transition:hover {
    transform: translateY(-2px);
}

/* تحسين الشفافية */
.efficient-opacity {
    opacity: 0.9;
    transition: opacity 0.2s ease;
}

/* تحسين التحويلات */
.efficient-transform {
    transform: translateZ(0);
    transition: transform 0.2s ease;
}

/* تحسين الفلاتر */
.efficient-filter {
    filter: brightness(1) contrast(1);
    transition: filter 0.2s ease;
}

/* تحسين الشبكة المرنة */
.flexible-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(250px, 100%), 1fr));
    gap: clamp(1rem, 2vw, 2rem);
    contain: layout;
}

/* تحسين النصوص الطويلة */
.efficient-text-content {
    line-height: 1.6;
    word-wrap: break-word;
    hyphens: auto;
    text-align: justify;
}

/* تحسين القوائم المنسدلة */
.efficient-dropdown {
    contain: layout style;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* تحسين الأيقونات */
.efficient-icon {
    width: 1em;
    height: 1em;
    display: inline-block;
    vertical-align: middle;
    contain: layout size;
}

/* تحسين الشارات */
.efficient-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    contain: layout style;
    isolation: isolate;
}

/* تحسين البطاقات */
.efficient-card {
    contain: layout style;
    isolation: isolate;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* تحسين الشريط الجانبي */
.efficient-sidebar {
    contain: layout style;
    transform: translateZ(0);
    will-change: transform;
}

/* تحسين المحتوى الرئيسي */
.efficient-main-content {
    contain: layout style;
    isolation: isolate;
}

/* تحسين التذييل */
.efficient-footer {
    contain: layout style;
    transform: translateZ(0);
}

/* تحسين الرأس */
.efficient-header {
    contain: layout style;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

/* تحسين التنقل */
.efficient-navigation {
    contain: layout style;
    transform: translateZ(0);
}

/* تحسين الروابط */
.efficient-link {
    text-decoration: none;
    color: var(--link-color);
    transition: color 0.15s ease;
    contain: layout;
}

.efficient-link:hover {
    color: var(--link-hover-color);
}

/* تحسين الإدخال */
.efficient-input {
    contain: layout;
    transition: border-color 0.15s ease, box-shadow 0.15s ease;
}

.efficient-input:focus {
    outline: none;
    border-color: var(--focus-color);
    box-shadow: 0 0 0 3px rgba(var(--focus-color-rgb), 0.1);
}

/* تحسين الجداول */
.efficient-table {
    contain: layout;
    border-collapse: collapse;
    width: 100%;
}

.efficient-table th,
.efficient-table td {
    contain: layout;
    padding: 0.75rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

/* تحسين الصفحات */
.efficient-pagination {
    contain: layout style;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
}

/* تحسين البحث */
.efficient-search {
    contain: layout style;
    position: relative;
    display: flex;
    align-items: center;
}

/* تحسين الفلاتر */
.efficient-filters {
    contain: layout style;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* تحسين الإحصائيات */
.efficient-stats {
    contain: layout style;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

/* تحسين الرسوم البيانية */
.efficient-chart {
    contain: layout size;
    width: 100%;
    height: 300px;
    position: relative;
}

/* تحسين النوافذ المنبثقة */
.efficient-modal {
    contain: layout style;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

/* تحسين التحميل */
.loading-spinner {
    contain: layout size;
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .mobile-optimized {
        contain: layout style;
        transform: translateZ(0);
    }
    
    .mobile-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .mobile-text {
        font-size: 14px;
        line-height: 1.4;
    }
}

/* تحسين الطباعة */
@media print {
    .print-optimized {
        color: black !important;
        background: white !important;
        box-shadow: none !important;
    }
    
    .no-print {
        display: none !important;
    }
}

/* تحسين الحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .respect-motion-preference {
        animation: none !important;
        transition: none !important;
    }
}

/* تحسين الألوان عالية التباين */
@media (prefers-contrast: high) {
    .high-contrast {
        border: 2px solid currentColor;
        background: white;
        color: black;
    }
}
