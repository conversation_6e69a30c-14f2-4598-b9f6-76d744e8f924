<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('parts', function (Blueprint $table) {
            $table->id();
            $table->string('part_number')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('category_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('supplier_id')->nullable()->constrained()->onDelete('set null');
            $table->string('brand')->nullable();
            $table->string('model')->nullable();
            $table->json('compatibility')->nullable();
            $table->string('part_type')->nullable();
            $table->enum('condition', ['new', 'refurbished', 'used', 'damaged'])->default('new');
            $table->string('location')->nullable();
            $table->string('shelf_location')->nullable();
            $table->string('barcode')->nullable();
            $table->string('sku')->nullable();
            $table->decimal('cost_price', 10, 2)->default(0);
            $table->decimal('selling_price', 10, 2)->default(0);
            $table->decimal('markup_percentage', 5, 2)->default(0);
            $table->integer('stock_quantity')->default(0);
            $table->integer('minimum_stock')->default(0);
            $table->integer('maximum_stock')->default(0);
            $table->integer('reorder_point')->default(0);
            $table->integer('reorder_quantity')->default(0);
            $table->string('unit_of_measure')->default('piece');
            $table->decimal('weight', 8, 3)->nullable();
            $table->json('dimensions')->nullable();
            $table->integer('warranty_period')->nullable(); // days
            $table->string('warranty_type')->nullable();
            $table->string('supplier_part_number')->nullable();
            $table->string('manufacturer_part_number')->nullable();
            $table->text('notes')->nullable();
            $table->json('images')->nullable();
            $table->json('specifications')->nullable();
            $table->json('installation_instructions')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_serialized')->default(false);
            $table->boolean('is_returnable')->default(true);
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['part_number']);
            $table->index(['name']);
            $table->index(['brand']);
            $table->index(['condition']);
            $table->index(['stock_quantity']);
            $table->index(['is_active']);
            $table->index(['barcode']);
            $table->index(['sku']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('parts');
    }
};
