<?php

/**
 * Routes Testing Script
 * 
 * This script helps debug route issues.
 */

echo "🛣️  اختبار Routes\n";
echo "================\n\n";

// Check if routes file exists and is readable
if (!file_exists('routes/web.php')) {
    echo "❌ ملف routes/web.php غير موجود\n";
    exit(1);
}

$routesContent = file_get_contents('routes/web.php');

echo "1️⃣ فحص تعريف routes الجدولة...\n";

// Check for schedule route
if (strpos($routesContent, "Route::get('technicians/schedule'") !== false) {
    echo "   ✅ Route الجدولة مُعرف خارج المجموعة\n";
} elseif (strpos($routesContent, "Route::get('/schedule'") !== false) {
    echo "   ⚠️  Route الجدولة مُعرف داخل المجموعة\n";
} else {
    echo "   ❌ Route الجدولة غير مُعرف\n";
}

// Check for TechnicianController reference
if (strpos($routesContent, 'TechnicianController::class') !== false) {
    echo "   ✅ TechnicianController مُستخدم في routes\n";
} else {
    echo "   ❌ TechnicianController غير مُستخدم في routes\n";
}

// Check for schedule method reference
if (strpos($routesContent, "'schedule'") !== false) {
    echo "   ✅ schedule method مُشار إليه في routes\n";
} else {
    echo "   ❌ schedule method غير مُشار إليه في routes\n";
}

echo "\n";

echo "2️⃣ فحص TechnicianController...\n";

if (!file_exists('app/Http/Controllers/TechnicianController.php')) {
    echo "   ❌ TechnicianController غير موجود\n";
} else {
    $controllerContent = file_get_contents('app/Http/Controllers/TechnicianController.php');
    
    if (strpos($controllerContent, 'public function schedule()') !== false) {
        echo "   ✅ schedule method موجود في Controller\n";
    } else {
        echo "   ❌ schedule method غير موجود في Controller\n";
    }
    
    if (strpos($controllerContent, "return view('technicians.schedule'") !== false) {
        echo "   ✅ schedule method يعيد view صحيح\n";
    } else {
        echo "   ❌ schedule method لا يعيد view صحيح\n";
    }
}

echo "\n";

echo "3️⃣ فحص schedule view...\n";

if (!file_exists('resources/views/technicians/schedule.blade.php')) {
    echo "   ❌ schedule.blade.php غير موجود\n";
} else {
    $viewContent = file_get_contents('resources/views/technicians/schedule.blade.php');
    
    if (strpos($viewContent, '@extends') !== false) {
        echo "   ✅ schedule view يمتد من layout\n";
    } else {
        echo "   ❌ schedule view لا يمتد من layout\n";
    }
    
    if (strpos($viewContent, 'جدولة الفنيين') !== false) {
        echo "   ✅ schedule view يحتوي على محتوى عربي\n";
    } else {
        echo "   ❌ schedule view لا يحتوي على محتوى عربي\n";
    }
}

echo "\n";

echo "4️⃣ فحص ترتيب Routes...\n";

// Extract route definitions
preg_match_all('/Route::(get|post|put|delete|resource)\s*\([^)]+\)/', $routesContent, $matches);
$routes = $matches[0];

$scheduleRouteIndex = -1;
$resourceRouteIndex = -1;

foreach ($routes as $index => $route) {
    if (strpos($route, 'technicians/schedule') !== false) {
        $scheduleRouteIndex = $index;
    }
    if (strpos($route, "resource('technicians'") !== false) {
        $resourceRouteIndex = $index;
    }
}

if ($scheduleRouteIndex !== -1 && $resourceRouteIndex !== -1) {
    if ($scheduleRouteIndex < $resourceRouteIndex) {
        echo "   ✅ schedule route مُعرف قبل resource route\n";
    } else {
        echo "   ⚠️  schedule route مُعرف بعد resource route (قد يسبب تضارب)\n";
    }
} else {
    echo "   ❌ لم يتم العثور على أحد الـ routes\n";
}

echo "\n";

echo "5️⃣ اقتراحات الحل...\n";

if ($scheduleRouteIndex === -1) {
    echo "   💡 أضف route الجدولة:\n";
    echo "      Route::get('technicians/schedule', [TechnicianController::class, 'schedule'])->name('technicians.schedule');\n";
}

if ($scheduleRouteIndex > $resourceRouteIndex) {
    echo "   💡 انقل route الجدولة قبل resource route\n";
}

if (!file_exists('app/Http/Controllers/TechnicianController.php')) {
    echo "   💡 تأكد من وجود TechnicianController\n";
}

if (!file_exists('resources/views/technicians/schedule.blade.php')) {
    echo "   💡 تأكد من وجود schedule view\n";
}

echo "\n";

echo "6️⃣ Routes المُعرفة للفنيين...\n";

$technicianRoutes = [];
foreach ($routes as $route) {
    if (strpos($route, 'technician') !== false) {
        $technicianRoutes[] = $route;
    }
}

if (!empty($technicianRoutes)) {
    foreach ($technicianRoutes as $route) {
        echo "   📍 $route\n";
    }
} else {
    echo "   ❌ لم يتم العثور على routes للفنيين\n";
}

echo "\n";

echo "📋 الخلاصة\n";
echo "=========\n";

$issues = 0;

if ($scheduleRouteIndex === -1) $issues++;
if (!file_exists('app/Http/Controllers/TechnicianController.php')) $issues++;
if (!file_exists('resources/views/technicians/schedule.blade.php')) $issues++;

if ($issues === 0) {
    echo "🎉 جميع المكونات موجودة!\n";
    echo "✅ Route مُعرف\n";
    echo "✅ Controller موجود\n";
    echo "✅ View موجود\n\n";
    
    echo "🔧 إذا كان لا يزال يظهر 404:\n";
    echo "1. امسح cache الـ routes: php artisan route:clear\n";
    echo "2. امسح cache التطبيق: php artisan cache:clear\n";
    echo "3. أعد تشغيل الخادم: php artisan serve\n";
    echo "4. جرب الرابط: http://tareq.test/technicians/schedule\n";
} else {
    echo "❌ يوجد $issues مشكلة تحتاج إلى حل\n";
    echo "📝 راجع الاقتراحات أعلاه\n";
}

echo "\n🏁 انتهى فحص Routes\n";
