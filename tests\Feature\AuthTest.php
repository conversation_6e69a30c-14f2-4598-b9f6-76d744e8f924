<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class AuthTest extends TestCase
{
    use RefreshDatabase;

    /**
     * اختبار عرض صفحة تسجيل الدخول
     */
    public function test_login_page_can_be_rendered()
    {
        $response = $this->get('/login');

        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
    }

    /**
     * اختبار تسجيل دخول ناجح
     */
    public function test_users_can_authenticate_using_the_login_screen()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'is_active' => true,
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $this->assertAuthenticated();
        $response->assertRedirect('/dashboard');
    }

    /**
     * اختبار تسجيل دخول بكلمة مرور خاطئة
     */
    public function test_users_can_not_authenticate_with_invalid_password()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'is_active' => true,
        ]);

        $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
        ]);

        $this->assertGuest();
    }

    /**
     * اختبار منع تسجيل دخول المستخدم غير المفعل
     */
    public function test_inactive_users_cannot_login()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'is_active' => false,
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $this->assertGuest();
        $response->assertSessionHasErrors(['email']);
    }

    /**
     * اختبار التحقق من صحة البيانات
     */
    public function test_login_validation()
    {
        // اختبار البريد الإلكتروني المطلوب
        $response = $this->post('/login', [
            'email' => '',
            'password' => 'password',
        ]);

        $response->assertSessionHasErrors(['email']);

        // اختبار كلمة المرور المطلوبة
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => '',
        ]);

        $response->assertSessionHasErrors(['password']);

        // اختبار صيغة البريد الإلكتروني
        $response = $this->post('/login', [
            'email' => 'invalid-email',
            'password' => 'password',
        ]);

        $response->assertSessionHasErrors(['email']);
    }

    /**
     * اختبار وظيفة "تذكرني"
     */
    public function test_remember_me_functionality()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'is_active' => true,
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
            'remember' => true,
        ]);

        $this->assertAuthenticated();
        $response->assertRedirect('/dashboard');
        
        // التحقق من وجود remember token
        $this->assertNotNull(auth()->user()->getRememberToken());
    }

    /**
     * اختبار تسجيل الخروج
     */
    public function test_users_can_logout()
    {
        $user = User::factory()->create([
            'is_active' => true,
        ]);

        $this->actingAs($user);

        $response = $this->post('/logout');

        $this->assertGuest();
        $response->assertRedirect('/');
    }

    /**
     * اختبار إعادة التوجيه بعد تسجيل الدخول
     */
    public function test_redirect_after_login()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'is_active' => true,
        ]);

        // محاولة الوصول لصفحة محمية
        $this->get('/dashboard');

        // تسجيل الدخول
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        // التحقق من إعادة التوجيه للصفحة المطلوبة
        $response->assertRedirect('/dashboard');
    }

    /**
     * اختبار Rate Limiting (يتطلب تفعيل Cache)
     */
    public function test_rate_limiting()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'is_active' => true,
        ]);

        // محاولات متعددة بكلمة مرور خاطئة
        for ($i = 0; $i < 6; $i++) {
            $response = $this->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'wrong-password',
            ]);
        }

        // المحاولة السادسة يجب أن تفشل بسبب Rate Limiting
        $response->assertSessionHasErrors(['email']);
        $this->assertStringContains('تم تجاوز عدد المحاولات', $response->getSession()->get('errors')->first('email'));
    }

    /**
     * اختبار تحديث آخر تسجيل دخول
     */
    public function test_last_login_update()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'is_active' => true,
            'last_login_at' => null,
            'login_count' => 0,
        ]);

        $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $user->refresh();

        $this->assertNotNull($user->last_login_at);
        $this->assertEquals(1, $user->login_count);
    }
}
