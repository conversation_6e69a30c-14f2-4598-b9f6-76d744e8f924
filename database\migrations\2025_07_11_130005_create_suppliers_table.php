<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if suppliers table already exists
        if (!Schema::hasTable('suppliers')) {
            Schema::create('suppliers', function (Blueprint $table) {
                $table->id();
                $table->string('supplier_code', 20)->unique()->comment('رمز المورد');
                $table->string('company_name')->comment('اسم الشركة');
                $table->string('contact_person')->nullable()->comment('الشخص المسؤول');
                $table->string('email')->nullable();
                $table->string('phone')->nullable();
                $table->string('mobile')->nullable();
                $table->text('address')->nullable();
                $table->string('city')->nullable();
                $table->string('country')->default('المملكة العربية السعودية');
                $table->string('tax_number')->nullable()->comment('الرقم الضريبي');
                $table->string('commercial_register')->nullable()->comment('السجل التجاري');
                $table->enum('payment_terms', ['cash', '30_days', '60_days', '90_days'])->default('30_days')->comment('شروط الدفع');
                $table->decimal('credit_limit', 15, 2)->default(0)->comment('حد الائتمان');
                $table->decimal('current_balance', 15, 2)->default(0)->comment('الرصيد الحالي');
                $table->boolean('is_active')->default(true);
                $table->text('notes')->nullable();
                $table->json('settings')->nullable();
                $table->timestamps();

                // Indexes
                $table->index(['is_active']);
                $table->index(['supplier_code']);
                $table->index(['company_name']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('suppliers');
    }
};
