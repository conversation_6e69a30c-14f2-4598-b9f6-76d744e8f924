@extends('layouts.main')

@section('title', 'تواصل العميل')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">سجل التواصل مع العميل</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $customer->display_name }} - {{ $customer->customer_number }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('customers.show', $customer) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة لتفاصيل العميل
            </a>
            <button onclick="showAddCommunicationModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                إضافة تواصل
            </button>
        </div>
    </div>

    <!-- Communication Summary -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $communications->total() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">إجمالي التواصل</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $communications->where('direction', 'outbound')->count() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">تواصل صادر</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $communications->where('direction', 'inbound')->count() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">تواصل وارد</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    @if($customer->last_contact_date)
                        {{ $customer->last_contact_date->diffInDays() }}
                    @else
                        -
                    @endif
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">أيام منذ آخر تواصل</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات سريعة</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button onclick="quickCall()" class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                <svg class="w-6 h-6 text-green-600 dark:text-green-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">اتصال هاتفي</span>
            </button>

            <button onclick="quickEmail()" class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">بريد إلكتروني</span>
            </button>

            <button onclick="quickSMS()" class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">رسالة نصية</span>
            </button>

            <button onclick="quickWhatsApp()" class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                <svg class="w-6 h-6 text-green-600 dark:text-green-400 ml-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097"/>
                </svg>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">واتساب</span>
            </button>
        </div>
    </div>

    <!-- Communications Timeline -->
    @if($communications->count() > 0)
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">سجل التواصل</h3>
            </div>

            <div class="p-6">
                <div class="flow-root">
                    <ul class="-mb-8">
                        @foreach($communications as $index => $communication)
                            <li>
                                <div class="relative pb-8">
                                    @if(!$loop->last)
                                        <span class="absolute top-4 right-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700" aria-hidden="true"></span>
                                    @endif
                                    <div class="relative flex space-x-3 space-x-reverse">
                                        <div>
                                            <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800
                                                @switch($communication->type)
                                                    @case('call') bg-green-500 @break
                                                    @case('email') bg-blue-500 @break
                                                    @case('sms') bg-yellow-500 @break
                                                    @case('whatsapp') bg-green-600 @break
                                                    @case('visit') bg-purple-500 @break
                                                    @case('note') bg-gray-500 @break
                                                    @default bg-gray-500
                                                @endswitch
                                            ">
                                                @switch($communication->type)
                                                    @case('call')
                                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                                        </svg>
                                                        @break
                                                    @case('email')
                                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                        </svg>
                                                        @break
                                                    @case('sms')
                                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                                        </svg>
                                                        @break
                                                    @case('whatsapp')
                                                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097"/>
                                                        </svg>
                                                        @break
                                                    @case('visit')
                                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        </svg>
                                                        @break
                                                    @default
                                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                @endswitch
                                            </span>
                                        </div>
                                        <div class="min-w-0 flex-1 pt-1.5">
                                            <div class="flex justify-between items-start">
                                                <div class="flex-1">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{ $communication->subject }}
                                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2
                                                            {{ $communication->direction == 'inbound' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' }}
                                                        ">
                                                            {{ $communication->direction == 'inbound' ? 'وارد' : 'صادر' }}
                                                        </span>
                                                    </p>
                                                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                                        {{ $communication->content }}
                                                    </p>
                                                    <div class="mt-2 flex items-center space-x-2 space-x-reverse text-xs text-gray-500 dark:text-gray-400">
                                                        <span>
                                                            @switch($communication->type)
                                                                @case('call') مكالمة هاتفية @break
                                                                @case('email') بريد إلكتروني @break
                                                                @case('sms') رسالة نصية @break
                                                                @case('whatsapp') واتساب @break
                                                                @case('visit') زيارة @break
                                                                @case('note') ملاحظة @break
                                                                @default {{ $communication->type }}
                                                            @endswitch
                                                        </span>
                                                        <span>•</span>
                                                        <span>{{ $communication->created_at->format('Y-m-d H:i') }}</span>
                                                        @if($communication->createdBy)
                                                            <span>•</span>
                                                            <span>{{ $communication->createdBy->name }}</span>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="text-left">
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                        @switch($communication->status)
                                                            @case('completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @break
                                                            @case('scheduled') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 @break
                                                            @case('cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @break
                                                            @default bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                                        @endswitch
                                                    ">
                                                        @switch($communication->status)
                                                            @case('completed') مكتمل @break
                                                            @case('scheduled') مجدول @break
                                                            @case('cancelled') ملغي @break
                                                            @default {{ $communication->status }}
                                                        @endswitch
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $communications->links() }}
            </div>
        </div>
    @else
        <div class="bg-white dark:bg-gray-800 rounded-lg p-12 shadow-sm text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا يوجد تواصل</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لم يتم تسجيل أي تواصل مع هذا العميل بعد.</p>
            <div class="mt-6">
                <button onclick="showAddCommunicationModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <svg class="-mr-1 ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة تواصل
                </button>
            </div>
        </div>
    @endif
</div>

<!-- Add Communication Modal -->
<div id="addCommunicationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إضافة تواصل جديد</h3>

            <form action="{{ route('customers.add-communication', $customer) }}" method="POST" class="space-y-4">
                @csrf

                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع التواصل *</label>
                    <select name="type" id="type" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="">اختر نوع التواصل</option>
                        <option value="call">مكالمة هاتفية</option>
                        <option value="email">بريد إلكتروني</option>
                        <option value="sms">رسالة نصية</option>
                        <option value="whatsapp">واتساب</option>
                        <option value="visit">زيارة</option>
                        <option value="note">ملاحظة</option>
                    </select>
                </div>

                <div>
                    <label for="direction" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاتجاه *</label>
                    <select name="direction" id="direction" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="">اختر الاتجاه</option>
                        <option value="outbound">صادر</option>
                        <option value="inbound">وارد</option>
                    </select>
                </div>

                <div>
                    <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموضوع *</label>
                    <input type="text" name="subject" id="subject" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المحتوى *</label>
                    <textarea name="content" id="content" rows="4" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة *</label>
                    <select name="status" id="status" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="completed">مكتمل</option>
                        <option value="scheduled">مجدول</option>
                        <option value="cancelled">ملغي</option>
                    </select>
                </div>

                <div id="scheduledDateField" class="hidden">
                    <label for="scheduled_at" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">موعد التواصل</label>
                    <input type="datetime-local" name="scheduled_at" id="scheduled_at" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div class="flex justify-end space-x-2 space-x-reverse pt-4">
                    <button type="button" onclick="hideAddCommunicationModal()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        حفظ التواصل
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showAddCommunicationModal() {
    document.getElementById('addCommunicationModal').classList.remove('hidden');
}

function hideAddCommunicationModal() {
    document.getElementById('addCommunicationModal').classList.add('hidden');
}

function quickCall() {
    showAddCommunicationModal();
    document.getElementById('type').value = 'call';
    document.getElementById('direction').value = 'outbound';
    document.getElementById('subject').value = 'مكالمة هاتفية';
}

function quickEmail() {
    showAddCommunicationModal();
    document.getElementById('type').value = 'email';
    document.getElementById('direction').value = 'outbound';
    document.getElementById('subject').value = 'بريد إلكتروني';
}

function quickSMS() {
    showAddCommunicationModal();
    document.getElementById('type').value = 'sms';
    document.getElementById('direction').value = 'outbound';
    document.getElementById('subject').value = 'رسالة نصية';
}

function quickWhatsApp() {
    showAddCommunicationModal();
    document.getElementById('type').value = 'whatsapp';
    document.getElementById('direction').value = 'outbound';
    document.getElementById('subject').value = 'رسالة واتساب';
}

// Show/hide scheduled date field based on status
document.getElementById('status').addEventListener('change', function() {
    const scheduledField = document.getElementById('scheduledDateField');
    if (this.value === 'scheduled') {
        scheduledField.classList.remove('hidden');
        document.getElementById('scheduled_at').required = true;
    } else {
        scheduledField.classList.add('hidden');
        document.getElementById('scheduled_at').required = false;
    }
});

// Close modal when clicking outside
document.getElementById('addCommunicationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideAddCommunicationModal();
    }
});
</script>
@endsection