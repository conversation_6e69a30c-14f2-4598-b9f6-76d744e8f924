<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sale_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sale_id')->constrained()->onDelete('cascade');
            
            // Item details
            $table->enum('item_type', ['repair_service', 'part', 'accessory', 'labor', 'other']);
            $table->foreignId('part_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('repair_id')->nullable()->constrained()->onDelete('set null');
            
            // Product information
            $table->string('item_name');
            $table->string('item_code')->nullable();
            $table->text('item_description')->nullable();
            $table->string('item_category')->nullable();
            
            // Pricing and quantity
            $table->integer('quantity');
            $table->decimal('unit_price', 8, 2);
            $table->decimal('original_price', 8, 2)->nullable(); // Before discount
            $table->decimal('discount_amount', 8, 2)->default(0);
            $table->decimal('line_total', 10, 2); // quantity * (unit_price - discount)
            
            // Tax information
            $table->decimal('tax_rate', 5, 2)->default(0);
            $table->decimal('tax_amount', 8, 2)->default(0);
            
            // Inventory tracking
            $table->boolean('affects_inventory')->default(true);
            $table->json('inventory_movements')->nullable(); // Track inventory changes
            
            // Additional information
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['sale_id', 'item_type']);
            $table->index('part_id');
            $table->index('repair_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sale_items');
    }
};
