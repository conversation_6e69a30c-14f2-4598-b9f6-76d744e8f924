/* ===================================
   التفاعلات والرسوم المتحركة المتقدمة
   Advanced Animations & Interactions CSS
   =================================== */

/* ===================================
   الرسوم المتحركة الأساسية
   =================================== */

/* تأثيرات الدخول */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 0.3);
    }
    50% {
        opacity: 1;
    }
    to {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
}

/* تأثيرات التحميل */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        transform: translateY(0);
    }
    40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        transform: translateY(-30px);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        transform: translateY(-15px);
    }
    90% {
        transform: translateY(-4px);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

@keyframes wobble {
    0% {
        transform: translateX(0%);
    }
    15% {
        transform: translateX(-25%) rotate(-5deg);
    }
    30% {
        transform: translateX(20%) rotate(3deg);
    }
    45% {
        transform: translateX(-15%) rotate(-3deg);
    }
    60% {
        transform: translateX(10%) rotate(2deg);
    }
    75% {
        transform: translateX(-5%) rotate(-1deg);
    }
    100% {
        transform: translateX(0%);
    }
}

/* تأثيرات الخروج */
@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes slideOutUp {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(-100%);
    }
}

@keyframes slideOutDown {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(100%);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
    50% {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 0.3);
    }
    to {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 0.3);
    }
}

/* ===================================
   فئات الرسوم المتحركة
   =================================== */

/* تأثيرات الدخول */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

.animate-slide-in-down {
    animation: slideInDown 0.5s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.4s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
}

.animate-zoom-in {
    animation: zoomIn 0.5s ease-out;
}

/* تأثيرات التحميل */
.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-wobble {
    animation: wobble 1s ease-in-out;
}

/* تأثيرات الخروج */
.animate-fade-out {
    animation: fadeOut 0.3s ease-out;
}

.animate-slide-out-up {
    animation: slideOutUp 0.3s ease-out;
}

.animate-slide-out-down {
    animation: slideOutDown 0.3s ease-out;
}

.animate-zoom-out {
    animation: zoomOut 0.3s ease-out;
}

/* تأخيرات الرسوم المتحركة */
.animate-delay-100 {
    animation-delay: 0.1s;
}

.animate-delay-200 {
    animation-delay: 0.2s;
}

.animate-delay-300 {
    animation-delay: 0.3s;
}

.animate-delay-500 {
    animation-delay: 0.5s;
}

.animate-delay-700 {
    animation-delay: 0.7s;
}

.animate-delay-1000 {
    animation-delay: 1s;
}

/* ===================================
   التفاعلات المتقدمة
   =================================== */

/* تأثيرات التمرير */
.hover-lift {
    transition: transform var(--transition-bounce), box-shadow var(--transition-bounce);
}

.hover-lift:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.hover-scale {
    transition: transform var(--transition-bounce);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform var(--transition-bounce);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: box-shadow var(--transition-bounce);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.hover-slide-right {
    transition: transform var(--transition-bounce);
}

.hover-slide-right:hover {
    transform: translateX(5px);
}

.hover-slide-left {
    transition: transform var(--transition-bounce);
}

.hover-slide-left:hover {
    transform: translateX(-5px);
}

/* تأثيرات الضغط */
.active-scale {
    transition: transform var(--transition-fast);
}

.active-scale:active {
    transform: scale(0.95);
}

.active-bounce {
    transition: transform var(--transition-fast);
}

.active-bounce:active {
    animation: bounce 0.3s ease-out;
}

/* تأثيرات التركيز */
.focus-ring {
    transition: box-shadow var(--transition-normal);
}

.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.focus-glow {
    transition: box-shadow var(--transition-normal);
}

.focus-glow:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color), 0 0 20px rgba(59, 130, 246, 0.2);
}

/* ===================================
   تأثيرات خاصة
   =================================== */

/* تأثير الموجة */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* تأثير التدرج المتحرك */
.gradient-animation {
    background: linear-gradient(-45deg, var(--accent-color), var(--success-color), var(--info-color), var(--warning-color));
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* تأثير الكتابة */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--accent-color);
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--accent-color);
    }
}

/* تأثير الجسيمات */
.particles {
    position: relative;
    overflow: hidden;
}

.particles::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: particleFloat 20s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(0) translateX(0);
    }
    100% {
        transform: translateY(-100px) translateX(50px);
    }
}

/* ===================================
   تحسينات الأداء
   =================================== */

/* تحسين الرسوم المتحركة */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.will-change-auto {
    will-change: auto;
}

/* تحسين GPU */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* ===================================
   استجابة للحركة المخفضة
   =================================== */

@media (prefers-reduced-motion: reduce) {
    .animate-fade-in,
    .animate-fade-in-up,
    .animate-fade-in-down,
    .animate-fade-in-left,
    .animate-fade-in-right,
    .animate-slide-in-up,
    .animate-slide-in-down,
    .animate-scale-in,
    .animate-bounce-in,
    .animate-zoom-in,
    .animate-pulse,
    .animate-spin,
    .animate-bounce,
    .animate-shake,
    .animate-wobble,
    .gradient-animation,
    .typewriter,
    .particles::before {
        animation: none !important;
    }
    
    .hover-lift,
    .hover-scale,
    .hover-rotate,
    .hover-glow,
    .hover-slide-right,
    .hover-slide-left,
    .active-scale,
    .active-bounce {
        transition: none !important;
    }
    
    .hover-lift:hover,
    .hover-scale:hover,
    .hover-rotate:hover,
    .hover-slide-right:hover,
    .hover-slide-left:hover,
    .active-scale:active,
    .active-bounce:active {
        transform: none !important;
    }
}
