@extends('layouts.main')

@section('title', 'مولد الباركود والطباعة')

@section('content')
<div class="space-y-6" x-data="barcodeGenerator()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">مولد الباركود والطباعة</h1>
            <p class="text-gray-600 dark:text-gray-400">إنشاء وطباعة الباركود للمنتجات وطلبات الصيانة</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="printLabels()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                طباعة الملصقات
            </button>
            <button @click="exportBarcodes()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير
            </button>
        </div>
    </div>

    <!-- Barcode Generator -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Generator Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إنشاء باركود جديد</h3>

            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الباركود</label>
                    <select x-model="barcodeType" @change="generateBarcode()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="repair">طلب صيانة</option>
                        <option value="product">منتج</option>
                        <option value="customer">عميل</option>
                        <option value="invoice">فاتورة</option>
                        <option value="custom">مخصص</option>
                    </select>
                </div>

                <div x-show="barcodeType === 'repair'">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم طلب الصيانة</label>
                    <input type="text" x-model="repairId" @input="generateBarcode()" placeholder="REP-001" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div x-show="barcodeType === 'product'">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كود المنتج</label>
                    <input type="text" x-model="productCode" @input="generateBarcode()" placeholder="PRD-001" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div x-show="barcodeType === 'customer'">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم العميل</label>
                    <input type="text" x-model="customerId" @input="generateBarcode()" placeholder="CUS-001" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div x-show="barcodeType === 'invoice'">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الفاتورة</label>
                    <input type="text" x-model="invoiceId" @input="generateBarcode()" placeholder="INV-001" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div x-show="barcodeType === 'custom'">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">النص المخصص</label>
                    <input type="text" x-model="customText" @input="generateBarcode()" placeholder="أدخل النص المطلوب" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تنسيق الباركود</label>
                    <select x-model="barcodeFormat" @change="generateBarcode()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="CODE128">CODE128</option>
                        <option value="CODE39">CODE39</option>
                        <option value="EAN13">EAN13</option>
                        <option value="EAN8">EAN8</option>
                        <option value="UPC">UPC</option>
                        <option value="QR">QR Code</option>
                    </select>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العرض</label>
                        <input type="number" x-model="barcodeWidth" @input="generateBarcode()" min="100" max="500" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الارتفاع</label>
                        <input type="number" x-model="barcodeHeight" @input="generateBarcode()" min="50" max="200" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    </div>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" x-model="showText" @change="generateBarcode()" id="showText" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="showText" class="mr-2 block text-sm text-gray-900 dark:text-gray-100">إظهار النص أسفل الباركود</label>
                </div>

                <button @click="generateBarcode()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg">
                    إنشاء الباركود
                </button>
            </div>
        </div>

        <!-- Barcode Preview -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معاينة الباركود</h3>

            <div class="text-center">
                <div id="barcodePreview" class="mb-4 p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg min-h-[200px] flex items-center justify-center">
                    <p class="text-gray-500 dark:text-gray-400">سيظهر الباركود هنا</p>
                </div>

                <div class="space-y-2">
                    <p class="text-sm text-gray-600 dark:text-gray-400">النص: <span x-text="currentBarcodeText" class="font-mono"></span></p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">التنسيق: <span x-text="barcodeFormat"></span></p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">الأبعاد: <span x-text="barcodeWidth + ' × ' + barcodeHeight"></span></p>
                </div>

                <div class="mt-4 flex space-x-2 space-x-reverse justify-center">
                    <button @click="downloadBarcode()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
                        تحميل PNG
                    </button>
                    <button @click="copyBarcode()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm">
                        نسخ
                    </button>
                    <button @click="addToQueue()" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm">
                        إضافة للطباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Print Queue -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">قائمة الطباعة</h3>
            <div class="flex space-x-2 space-x-reverse">
                <button @click="clearQueue()" class="text-red-600 hover:text-red-800 dark:text-red-400 text-sm">مسح الكل</button>
                <span class="text-sm text-gray-500 dark:text-gray-400" x-text="printQueue.length + ' عنصر'"></span>
            </div>
        </div>

        <div x-show="printQueue.length === 0" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">قائمة الطباعة فارغة</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">أضف باركود للطباعة</p>
        </div>

        <div x-show="printQueue.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <template x-for="(item, index) in printQueue" :key="index">
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="item.text"></p>
                            <p class="text-xs text-gray-500 dark:text-gray-400" x-text="item.format"></p>
                        </div>
                        <button @click="removeFromQueue(index)" class="text-red-600 hover:text-red-800 dark:text-red-400">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="text-center">
                        <div x-html="item.svg" class="inline-block"></div>
                    </div>
                    <div class="mt-2 text-center">
                        <span class="text-xs text-gray-500 dark:text-gray-400" x-text="item.width + ' × ' + item.height"></span>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Barcode Scanner -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">ماسح الباركود</h3>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
                <div class="mb-4">
                    <button @click="startScanner()" :disabled="scannerActive" class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg mr-2">
                        <span x-show="!scannerActive">بدء المسح</span>
                        <span x-show="scannerActive">جاري المسح...</span>
                    </button>
                    <button @click="stopScanner()" :disabled="!scannerActive" class="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg">
                        إيقاف المسح
                    </button>
                </div>

                <div id="scanner" class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg h-64 flex items-center justify-center">
                    <p class="text-gray-500 dark:text-gray-400">ضع الباركود أمام الكاميرا</p>
                </div>
            </div>

            <div>
                <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">نتائج المسح</h4>
                <div class="space-y-2 max-h-64 overflow-y-auto">
                    <template x-for="(result, index) in scanResults" :key="index">
                        <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="flex justify-between items-start">
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="result.text"></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400" x-text="result.format"></p>
                                    <p class="text-xs text-gray-400" x-text="result.timestamp"></p>
                                </div>
                                <button @click="searchByBarcode(result.text)" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 text-sm">
                                    بحث
                                </button>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include JsBarcode library -->
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
<!-- Include QuaggaJS for barcode scanning -->
<script src="https://cdn.jsdelivr.net/npm/quagga@0.12.1/dist/quagga.min.js"></script>

@push('scripts')
<script>
function barcodeGenerator() {
    return {
        barcodeType: 'repair',
        repairId: '',
        productCode: '',
        customerId: '',
        invoiceId: '',
        customText: '',
        barcodeFormat: 'CODE128',
        barcodeWidth: 200,
        barcodeHeight: 100,
        showText: true,
        currentBarcodeText: '',
        printQueue: [],
        scannerActive: false,
        scanResults: [],

        init() {
            this.generateBarcode();
        },

        generateBarcode() {
            let text = '';

            switch(this.barcodeType) {
                case 'repair':
                    text = this.repairId || 'REP-001';
                    break;
                case 'product':
                    text = this.productCode || 'PRD-001';
                    break;
                case 'customer':
                    text = this.customerId || 'CUS-001';
                    break;
                case 'invoice':
                    text = this.invoiceId || 'INV-001';
                    break;
                case 'custom':
                    text = this.customText || 'SAMPLE';
                    break;
            }

            this.currentBarcodeText = text;

            try {
                const preview = document.getElementById('barcodePreview');
                preview.innerHTML = '';

                if (this.barcodeFormat === 'QR') {
                    // For QR codes, we'd use a different library
                    preview.innerHTML = '<p class="text-gray-500">QR Code generation requires additional library</p>';
                } else {
                    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                    JsBarcode(svg, text, {
                        format: this.barcodeFormat,
                        width: 2,
                        height: this.barcodeHeight,
                        displayValue: this.showText,
                        fontSize: 14,
                        textMargin: 5
                    });
                    preview.appendChild(svg);
                }
            } catch (error) {
                console.error('Barcode generation error:', error);
                document.getElementById('barcodePreview').innerHTML = '<p class="text-red-500">خطأ في إنشاء الباركود</p>';
            }
        },

        downloadBarcode() {
            const svg = document.querySelector('#barcodePreview svg');
            if (!svg) return;

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const data = new XMLSerializer().serializeToString(svg);
            const img = new Image();

            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);

                const link = document.createElement('a');
                link.download = `barcode_${this.currentBarcodeText}.png`;
                link.href = canvas.toDataURL();
                link.click();
            };

            img.src = 'data:image/svg+xml;base64,' + btoa(data);
        },

        copyBarcode() {
            navigator.clipboard.writeText(this.currentBarcodeText).then(() => {
                alert('تم نسخ النص إلى الحافظة');
            });
        },

        addToQueue() {
            const svg = document.querySelector('#barcodePreview svg');
            if (!svg) return;

            this.printQueue.push({
                text: this.currentBarcodeText,
                format: this.barcodeFormat,
                width: this.barcodeWidth,
                height: this.barcodeHeight,
                svg: svg.outerHTML
            });
        },

        removeFromQueue(index) {
            this.printQueue.splice(index, 1);
        },

        clearQueue() {
            if (confirm('هل أنت متأكد من مسح قائمة الطباعة؟')) {
                this.printQueue = [];
            }
        },

        printLabels() {
            if (this.printQueue.length === 0) {
                alert('قائمة الطباعة فارغة');
                return;
            }

            const printWindow = window.open('', '_blank');
            let content = `
                <html>
                <head>
                    <title>طباعة الباركود</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .barcode-item {
                            display: inline-block;
                            margin: 10px;
                            text-align: center;
                            page-break-inside: avoid;
                        }
                        @media print {
                            .barcode-item { margin: 5px; }
                        }
                    </style>
                </head>
                <body>
            `;

            this.printQueue.forEach(item => {
                content += `<div class="barcode-item">${item.svg}</div>`;
            });

            content += '</body></html>';

            printWindow.document.write(content);
            printWindow.document.close();
            printWindow.print();
        },

        exportBarcodes() {
            alert('سيتم تصدير الباركود كملف PDF');
        },

        startScanner() {
            this.scannerActive = true;

            Quagga.init({
                inputStream: {
                    name: "Live",
                    type: "LiveStream",
                    target: document.querySelector('#scanner')
                },
                decoder: {
                    readers: ["code_128_reader", "ean_reader", "ean_8_reader", "code_39_reader"]
                }
            }, (err) => {
                if (err) {
                    console.error('Scanner initialization error:', err);
                    this.scannerActive = false;
                    return;
                }
                Quagga.start();
            });

            Quagga.onDetected((data) => {
                this.scanResults.unshift({
                    text: data.codeResult.code,
                    format: data.codeResult.format,
                    timestamp: new Date().toLocaleTimeString('ar-EG')
                });

                // Keep only last 10 results
                if (this.scanResults.length > 10) {
                    this.scanResults = this.scanResults.slice(0, 10);
                }
            });
        },

        stopScanner() {
            Quagga.stop();
            this.scannerActive = false;
        },

        searchByBarcode(code) {
            alert(`البحث عن: ${code}`);
            // Here you would implement the actual search functionality
        }
    }
}
</script>
@endpush
@endsection