<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PerformanceReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'technician_id',
        'reviewer_id',
        'review_period_start',
        'review_period_end',
        'technical_skills',
        'communication_skills',
        'punctuality',
        'customer_service',
        'problem_solving',
        'overall_rating',
        'strengths',
        'areas_for_improvement',
        'goals',
        'comments',
        'status'
    ];

    protected $casts = [
        'review_period_start' => 'date',
        'review_period_end' => 'date',
        'technical_skills' => 'decimal:1',
        'communication_skills' => 'decimal:1',
        'punctuality' => 'decimal:1',
        'customer_service' => 'decimal:1',
        'problem_solving' => 'decimal:1',
        'overall_rating' => 'decimal:1'
    ];

    // Relationships
    public function technician()
    {
        return $this->belongsTo(Technician::class);
    }

    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'draft' => ['class' => 'badge-secondary', 'text' => 'مسودة'],
            'completed' => ['class' => 'badge-success', 'text' => 'مكتمل'],
            'pending' => ['class' => 'badge-warning', 'text' => 'في الانتظار']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getAverageRatingAttribute()
    {
        $ratings = [
            $this->technical_skills,
            $this->communication_skills,
            $this->punctuality,
            $this->customer_service,
            $this->problem_solving
        ];

        $validRatings = array_filter($ratings, function($rating) {
            return $rating !== null && $rating > 0;
        });

        return count($validRatings) > 0 ? array_sum($validRatings) / count($validRatings) : 0;
    }
}
