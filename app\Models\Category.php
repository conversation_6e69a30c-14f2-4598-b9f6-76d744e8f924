<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Category extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'parent_id',
        'name',
        'name_en',
        'description',
        'image',
        'sort_order',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع التصنيف الأب
     */
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    /**
     * العلاقة مع التصنيفات الفرعية
     */
    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * العلاقة مع المنتجات
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * العلاقة مع قطع الغيار
     */
    public function parts()
    {
        return $this->hasMany(Part::class);
    }

    /**
     * العلاقة مع المصروفات
     */
    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * العلاقة مع العملاء (many-to-many)
     */
    public function customers()
    {
        return $this->belongsToMany(Customer::class, 'customer_categories');
    }

    /**
     * العلاقة مع الموردين (many-to-many)
     */
    public function suppliers()
    {
        return $this->belongsToMany(Supplier::class, 'supplier_categories');
    }

    /**
     * العلاقة مع الفنيين (many-to-many)
     */
    public function technicians()
    {
        return $this->belongsToMany(Technician::class, 'technician_categories');
    }

    /**
     * العلاقة مع طلبات الصيانة (many-to-many)
     */
    public function repairs()
    {
        return $this->belongsToMany(Repair::class, 'repair_categories');
    }

    /**
     * الحصول على جميع التصنيفات الفرعية (متداخلة)
     */
    public function getAllChildren()
    {
        $children = collect();
        
        foreach ($this->children as $child) {
            $children->push($child);
            $children = $children->merge($child->getAllChildren());
        }
        
        return $children;
    }

    /**
     * الحصول على مسار التصنيف
     */
    public function getPathAttribute()
    {
        $path = collect([$this->name]);
        $parent = $this->parent;
        
        while ($parent) {
            $path->prepend($parent->name);
            $parent = $parent->parent;
        }
        
        return $path->implode(' > ');
    }

    /**
     * الحصول على عدد المنتجات (شامل التصنيفات الفرعية)
     */
    public function getTotalProductsCountAttribute()
    {
        $count = $this->products()->count();
        
        foreach ($this->children as $child) {
            $count += $child->total_products_count;
        }
        
        return $count;
    }

    /**
     * scope للتصنيفات الرئيسية
     */
    public function scopeParent($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * scope للتصنيفات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * scope للترتيب
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}

class Brand extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'name',
        'name_en',
        'description',
        'logo',
        'website',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع المنتجات
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * scope للعلامات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}

class Unit extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'name',
        'name_en',
        'short_name',
        'short_name_en',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع المنتجات
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * scope للوحدات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
