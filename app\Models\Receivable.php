<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Receivable extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'sale_id',
        'repair_id',
        'invoice_number',
        'invoice_date',
        'due_date',
        'original_amount',
        'paid_amount',
        'remaining_amount',
        'status',
        'notes',
        'payment_history',
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'original_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'payment_history' => 'array',
    ];

    /**
     * Get the customer
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the sale
     */
    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }

    /**
     * Get the repair
     */
    public function repair(): BelongsTo
    {
        return $this->belongsTo(Repair::class);
    }

    /**
     * Scope for pending receivables
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for overdue receivables
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now()->toDateString())
                    ->whereIn('status', ['pending', 'partial']);
    }

    /**
     * Scope for paid receivables
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Check if receivable is overdue
     */
    public function isOverdue()
    {
        return $this->due_date < now()->toDateString() && 
               in_array($this->status, ['pending', 'partial']);
    }

    /**
     * Get days overdue
     */
    public function getDaysOverdue()
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return Carbon::parse($this->due_date)->diffInDays(now());
    }

    /**
     * Get days until due
     */
    public function getDaysUntilDue()
    {
        if ($this->isOverdue()) {
            return 0;
        }

        return now()->diffInDays(Carbon::parse($this->due_date));
    }

    /**
     * Record payment
     */
    public function recordPayment($amount, $paymentMethod = 'cash', $notes = null)
    {
        if ($amount <= 0) {
            throw new \Exception('مبلغ الدفع يجب أن يكون أكبر من صفر');
        }

        if ($amount > $this->remaining_amount) {
            throw new \Exception('مبلغ الدفع أكبر من المبلغ المتبقي');
        }

        // Update amounts
        $this->paid_amount += $amount;
        $this->remaining_amount -= $amount;

        // Update status
        if ($this->remaining_amount <= 0.01) {
            $this->status = 'paid';
            $this->remaining_amount = 0;
        } else {
            $this->status = 'partial';
        }

        // Add to payment history
        $paymentHistory = $this->payment_history ?? [];
        $paymentHistory[] = [
            'date' => now()->toDateString(),
            'amount' => $amount,
            'method' => $paymentMethod,
            'notes' => $notes,
            'recorded_at' => now()->toDateTimeString(),
        ];
        $this->payment_history = $paymentHistory;

        $this->save();

        return true;
    }

    /**
     * Get status label
     */
    public function getStatusLabel()
    {
        $statuses = [
            'pending' => 'معلق',
            'partial' => 'مدفوع جزئياً',
            'paid' => 'مدفوع',
            'overdue' => 'متأخر',
            'cancelled' => 'ملغي',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Get status color
     */
    public function getStatusColor()
    {
        if ($this->isOverdue()) {
            return 'bg-red-100 text-red-800';
        }

        $colors = [
            'pending' => 'bg-yellow-100 text-yellow-800',
            'partial' => 'bg-blue-100 text-blue-800',
            'paid' => 'bg-green-100 text-green-800',
            'cancelled' => 'bg-gray-100 text-gray-800',
        ];

        return $colors[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    /**
     * Get payment percentage
     */
    public function getPaymentPercentage()
    {
        if ($this->original_amount <= 0) {
            return 0;
        }

        return ($this->paid_amount / $this->original_amount) * 100;
    }

    /**
     * Update status based on due date
     */
    public function updateStatus()
    {
        if ($this->isOverdue() && in_array($this->status, ['pending', 'partial'])) {
            // Don't change the status to overdue, just mark it as such in queries
            // The status field keeps track of payment status, not due status
        }
    }

    /**
     * Static method to update all overdue receivables
     */
    public static function updateOverdueStatus()
    {
        static::where('due_date', '<', now()->toDateString())
              ->whereIn('status', ['pending', 'partial'])
              ->chunk(100, function ($receivables) {
                  foreach ($receivables as $receivable) {
                      $receivable->updateStatus();
                  }
              });
    }
}
