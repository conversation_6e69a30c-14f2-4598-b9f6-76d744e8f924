<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerCommunication extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'type',
        'subject',
        'content',
        'method',
        'status',
        'sent_at',
        'delivered_at',
        'read_at',
        'response_received_at',
        'sent_by',
        'metadata'
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
        'response_received_at' => 'datetime',
        'metadata' => 'array'
    ];

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function sentBy()
    {
        return $this->belongsTo(User::class, 'sent_by');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'draft' => ['class' => 'badge-secondary', 'text' => 'مسودة'],
            'sent' => ['class' => 'badge-info', 'text' => 'مرسل'],
            'delivered' => ['class' => 'badge-success', 'text' => 'تم التسليم'],
            'read' => ['class' => 'badge-primary', 'text' => 'مقروء'],
            'failed' => ['class' => 'badge-danger', 'text' => 'فشل'],
            'responded' => ['class' => 'badge-success', 'text' => 'تم الرد']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getMethodBadgeAttribute()
    {
        $methods = [
            'email' => ['class' => 'badge-info', 'text' => 'بريد إلكتروني'],
            'sms' => ['class' => 'badge-success', 'text' => 'رسالة نصية'],
            'phone' => ['class' => 'badge-warning', 'text' => 'هاتف'],
            'whatsapp' => ['class' => 'badge-success', 'text' => 'واتساب'],
            'system' => ['class' => 'badge-primary', 'text' => 'النظام']
        ];

        return $methods[$this->method] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getTypeBadgeAttribute()
    {
        $types = [
            'notification' => ['class' => 'badge-info', 'text' => 'إشعار'],
            'reminder' => ['class' => 'badge-warning', 'text' => 'تذكير'],
            'marketing' => ['class' => 'badge-success', 'text' => 'تسويق'],
            'support' => ['class' => 'badge-primary', 'text' => 'دعم'],
            'system' => ['class' => 'badge-secondary', 'text' => 'نظام']
        ];

        return $types[$this->type] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    // Scopes
    public function scopeSent($query)
    {
        return $query->whereNotNull('sent_at');
    }

    public function scopeDelivered($query)
    {
        return $query->whereNotNull('delivered_at');
    }

    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    public function scopeByMethod($query, $method)
    {
        return $query->where('method', $method);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Methods
    public function markAsDelivered()
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now()
        ]);
    }

    public function markAsRead()
    {
        $this->update([
            'status' => 'read',
            'read_at' => now()
        ]);
    }

    public function markAsResponded()
    {
        $this->update([
            'status' => 'responded',
            'response_received_at' => now()
        ]);
    }
}
