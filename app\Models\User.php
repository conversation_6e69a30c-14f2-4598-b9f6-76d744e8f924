<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'role_id',
        'name',
        'email',
        'password',
        'phone',
        'avatar',
        'employee_id',
        'department',
        'hire_date',
        'salary',
        'commission_rate',
        'is_active',
        'last_login_at',
        'login_count',
        'settings'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'hire_date' => 'date',
        'last_login_at' => 'datetime',
        'is_active' => 'boolean',
        'salary' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'settings' => 'array'
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع الدور
     */
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * العلاقة مع الفني (إذا كان المستخدم فني)
     */
    public function technician()
    {
        return $this->hasOne(Technician::class);
    }

    /**
     * العلاقة مع المبيعات كمندوب مبيعات
     */
    public function sales()
    {
        return $this->hasMany(Sale::class, 'salesperson_id');
    }

    /**
     * العلاقة مع طلبات الصيانة المنشأة
     */
    public function createdRepairs()
    {
        return $this->hasMany(Repair::class, 'created_by');
    }

    /**
     * العلاقة مع طلبات الصيانة المحدثة
     */
    public function updatedRepairs()
    {
        return $this->hasMany(Repair::class, 'updated_by');
    }

    /**
     * العلاقة مع المصروفات المعتمدة
     */
    public function approvedExpenses()
    {
        return $this->hasMany(Expense::class, 'approved_by');
    }

    /**
     * العلاقة مع المصروفات المنشأة
     */
    public function createdExpenses()
    {
        return $this->hasMany(Expense::class, 'created_by');
    }

    /**
     * العلاقة مع الإشعارات
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * العلاقة مع الإشعارات المنشأة
     */
    public function createdNotifications()
    {
        return $this->hasMany(Notification::class, 'created_by');
    }

    /**
     * العلاقة مع الإعدادات المنشأة
     */
    public function createdSettings()
    {
        return $this->hasMany(Setting::class, 'created_by');
    }

    /**
     * العلاقة مع الإعدادات المحدثة
     */
    public function updatedSettings()
    {
        return $this->hasMany(Setting::class, 'updated_by');
    }

    /**
     * العلاقة مع المواقع
     */
    public function locations()
    {
        return $this->belongsToMany(Location::class, 'user_locations');
    }



    /**
     * العلاقة مع سجل الأنشطة
     */
    public function activities()
    {
        return $this->hasMany(ActivityLog::class);
    }

    /**
     * التحقق من وجود صلاحية معينة
     */
    public function hasPermission($permission)
    {
        return $this->role && $this->role->hasPermission($permission);
    }

    /**
     * التحقق من إمكانية الوصول لموقع معين
     */
    public function canAccessLocation($locationId)
    {
        return $this->locations()->where('location_id', $locationId)->exists();
    }

    /**
     * الحصول على الموقع الافتراضي للمستخدم
     */
    public function getDefaultLocationAttribute()
    {
        return $this->locations()->first();
    }

    /**
     * تسجيل نشاط المستخدم
     */
    public function logActivity($action, $description = null, $model = null)
    {
        return ActivityLog::create([
            'user_id' => $this->id,
            'action' => $action,
            'description' => $description,
            'model_type' => $model ? get_class($model) : null,
            'model_id' => $model ? $model->id : null,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * تحديث آخر تسجيل دخول
     */
    public function updateLastLogin()
    {
        $this->update([
            'last_login_at' => now(),
            'login_count' => $this->login_count + 1
        ]);
    }

    /**
     * scope للمستخدمين النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * الحصول على اسم الدور
     */
    public function getRoleNameAttribute()
    {
        return $this->role ? $this->role->name : 'غير محدد';
    }
}
