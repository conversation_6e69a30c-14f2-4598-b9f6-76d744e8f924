<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Account;

class ChartOfAccountsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing accounts
        Account::truncate();

        $accounts = [
            // الأصول (Assets) - 1xxxx
            [
                'account_code' => '10000',
                'account_name' => 'الأصول',
                'account_name_en' => 'Assets',
                'account_type' => 'asset',
                'balance_type' => 'debit',
                'is_system' => true,
                'children' => [
                    // الأصول المتداولة - 11xxx
                    [
                        'account_code' => '11000',
                        'account_name' => 'الأصول المتداولة',
                        'account_name_en' => 'Current Assets',
                        'account_type' => 'asset',
                        'account_category' => 'current_asset',
                        'balance_type' => 'debit',
                        'is_system' => true,
                        'children' => [
                            // النقدية والبنوك - 111xx
                            [
                                'account_code' => '11100',
                                'account_name' => 'النقدية والبنوك',
                                'account_name_en' => 'Cash and Banks',
                                'account_type' => 'asset',
                                'account_category' => 'cash',
                                'balance_type' => 'debit',
                                'is_system' => true,
                                'children' => [
                                    [
                                        'account_code' => '11101',
                                        'account_name' => 'الصندوق',
                                        'account_name_en' => 'Cash',
                                        'account_type' => 'asset',
                                        'account_category' => 'cash',
                                        'balance_type' => 'debit',
                                        'opening_balance' => 10000,
                                        'is_system' => true,
                                    ],
                                    [
                                        'account_code' => '11102',
                                        'account_name' => 'البنك الأهلي',
                                        'account_name_en' => 'National Bank',
                                        'account_type' => 'asset',
                                        'account_category' => 'bank',
                                        'balance_type' => 'debit',
                                        'opening_balance' => 50000,
                                        'is_system' => true,
                                    ],
                                    [
                                        'account_code' => '11103',
                                        'account_name' => 'البنك الراجحي',
                                        'account_name_en' => 'Al Rajhi Bank',
                                        'account_type' => 'asset',
                                        'account_category' => 'bank',
                                        'balance_type' => 'debit',
                                        'opening_balance' => 30000,
                                        'is_system' => true,
                                    ],
                                ]
                            ],
                            // الذمم المدينة - 112xx
                            [
                                'account_code' => '11200',
                                'account_name' => 'الذمم المدينة',
                                'account_name_en' => 'Accounts Receivable',
                                'account_type' => 'asset',
                                'account_category' => 'receivable',
                                'balance_type' => 'debit',
                                'is_system' => true,
                                'children' => [
                                    [
                                        'account_code' => '11201',
                                        'account_name' => 'ذمم العملاء',
                                        'account_name_en' => 'Customer Receivables',
                                        'account_type' => 'asset',
                                        'account_category' => 'receivable',
                                        'balance_type' => 'debit',
                                        'is_system' => true,
                                    ],
                                    [
                                        'account_code' => '11202',
                                        'account_name' => 'أوراق القبض',
                                        'account_name_en' => 'Notes Receivable',
                                        'account_type' => 'asset',
                                        'account_category' => 'receivable',
                                        'balance_type' => 'debit',
                                        'is_system' => true,
                                    ],
                                ]
                            ],
                            // المخزون - 113xx
                            [
                                'account_code' => '11300',
                                'account_name' => 'المخزون',
                                'account_name_en' => 'Inventory',
                                'account_type' => 'asset',
                                'account_category' => 'inventory',
                                'balance_type' => 'debit',
                                'is_system' => true,
                                'children' => [
                                    [
                                        'account_code' => '11301',
                                        'account_name' => 'مخزون قطع الغيار',
                                        'account_name_en' => 'Parts Inventory',
                                        'account_type' => 'asset',
                                        'account_category' => 'inventory',
                                        'balance_type' => 'debit',
                                        'opening_balance' => 25000,
                                        'is_system' => true,
                                    ],
                                    [
                                        'account_code' => '11302',
                                        'account_name' => 'مخزون الإكسسوارات',
                                        'account_name_en' => 'Accessories Inventory',
                                        'account_type' => 'asset',
                                        'account_category' => 'inventory',
                                        'balance_type' => 'debit',
                                        'opening_balance' => 15000,
                                        'is_system' => true,
                                    ],
                                ]
                            ],
                        ]
                    ],
                    // الأصول الثابتة - 12xxx
                    [
                        'account_code' => '12000',
                        'account_name' => 'الأصول الثابتة',
                        'account_name_en' => 'Fixed Assets',
                        'account_type' => 'asset',
                        'account_category' => 'fixed_asset',
                        'balance_type' => 'debit',
                        'is_system' => true,
                        'children' => [
                            [
                                'account_code' => '12100',
                                'account_name' => 'المعدات والأجهزة',
                                'account_name_en' => 'Equipment',
                                'account_type' => 'asset',
                                'account_category' => 'fixed_asset',
                                'balance_type' => 'debit',
                                'opening_balance' => 80000,
                                'is_system' => true,
                            ],
                            [
                                'account_code' => '12200',
                                'account_name' => 'الأثاث والمفروشات',
                                'account_name_en' => 'Furniture',
                                'account_type' => 'asset',
                                'account_category' => 'fixed_asset',
                                'balance_type' => 'debit',
                                'opening_balance' => 20000,
                                'is_system' => true,
                            ],
                            [
                                'account_code' => '12900',
                                'account_name' => 'مجمع الاستهلاك',
                                'account_name_en' => 'Accumulated Depreciation',
                                'account_type' => 'asset',
                                'account_category' => 'contra_asset',
                                'balance_type' => 'credit',
                                'opening_balance' => 15000,
                                'is_system' => true,
                            ],
                        ]
                    ],
                ]
            ],

            // الخصوم (Liabilities) - 2xxxx
            [
                'account_code' => '20000',
                'account_name' => 'الخصوم',
                'account_name_en' => 'Liabilities',
                'account_type' => 'liability',
                'balance_type' => 'credit',
                'is_system' => true,
                'children' => [
                    // الخصوم المتداولة - 21xxx
                    [
                        'account_code' => '21000',
                        'account_name' => 'الخصوم المتداولة',
                        'account_name_en' => 'Current Liabilities',
                        'account_type' => 'liability',
                        'account_category' => 'current_liability',
                        'balance_type' => 'credit',
                        'is_system' => true,
                        'children' => [
                            [
                                'account_code' => '21100',
                                'account_name' => 'الذمم الدائنة',
                                'account_name_en' => 'Accounts Payable',
                                'account_type' => 'liability',
                                'account_category' => 'payable',
                                'balance_type' => 'credit',
                                'is_system' => true,
                                'children' => [
                                    [
                                        'account_code' => '21101',
                                        'account_name' => 'ذمم الموردين',
                                        'account_name_en' => 'Supplier Payables',
                                        'account_type' => 'liability',
                                        'account_category' => 'payable',
                                        'balance_type' => 'credit',
                                        'is_system' => true,
                                    ],
                                ]
                            ],
                            [
                                'account_code' => '21200',
                                'account_name' => 'المصروفات المستحقة',
                                'account_name_en' => 'Accrued Expenses',
                                'account_type' => 'liability',
                                'account_category' => 'accrued',
                                'balance_type' => 'credit',
                                'is_system' => true,
                                'children' => [
                                    [
                                        'account_code' => '21201',
                                        'account_name' => 'رواتب مستحقة',
                                        'account_name_en' => 'Accrued Salaries',
                                        'account_type' => 'liability',
                                        'account_category' => 'accrued',
                                        'balance_type' => 'credit',
                                        'is_system' => true,
                                    ],
                                    [
                                        'account_code' => '21202',
                                        'account_name' => 'إيجار مستحق',
                                        'account_name_en' => 'Accrued Rent',
                                        'account_type' => 'liability',
                                        'account_category' => 'accrued',
                                        'balance_type' => 'credit',
                                        'is_system' => true,
                                    ],
                                ]
                            ],
                            [
                                'account_code' => '21300',
                                'account_name' => 'ضريبة القيمة المضافة',
                                'account_name_en' => 'VAT Payable',
                                'account_type' => 'liability',
                                'account_category' => 'tax',
                                'balance_type' => 'credit',
                                'is_system' => true,
                            ],
                        ]
                    ],
                ]
            ],

            // حقوق الملكية (Equity) - 3xxxx
            [
                'account_code' => '30000',
                'account_name' => 'حقوق الملكية',
                'account_name_en' => 'Equity',
                'account_type' => 'equity',
                'balance_type' => 'credit',
                'is_system' => true,
                'children' => [
                    [
                        'account_code' => '31000',
                        'account_name' => 'رأس المال',
                        'account_name_en' => 'Capital',
                        'account_type' => 'equity',
                        'account_category' => 'capital',
                        'balance_type' => 'credit',
                        'opening_balance' => 200000,
                        'is_system' => true,
                    ],
                    [
                        'account_code' => '32000',
                        'account_name' => 'الأرباح المحتجزة',
                        'account_name_en' => 'Retained Earnings',
                        'account_type' => 'equity',
                        'account_category' => 'retained_earnings',
                        'balance_type' => 'credit',
                        'is_system' => true,
                    ],
                    [
                        'account_code' => '33000',
                        'account_name' => 'أرباح العام الجاري',
                        'account_name_en' => 'Current Year Earnings',
                        'account_type' => 'equity',
                        'account_category' => 'current_earnings',
                        'balance_type' => 'credit',
                        'is_system' => true,
                    ],
                ]
            ],

            // الإيرادات (Revenues) - 4xxxx
            [
                'account_code' => '40000',
                'account_name' => 'الإيرادات',
                'account_name_en' => 'Revenues',
                'account_type' => 'revenue',
                'balance_type' => 'credit',
                'is_system' => true,
                'children' => [
                    [
                        'account_code' => '41000',
                        'account_name' => 'إيرادات المبيعات',
                        'account_name_en' => 'Sales Revenue',
                        'account_type' => 'revenue',
                        'account_category' => 'sales',
                        'balance_type' => 'credit',
                        'is_system' => true,
                        'children' => [
                            [
                                'account_code' => '41100',
                                'account_name' => 'مبيعات قطع الغيار',
                                'account_name_en' => 'Parts Sales',
                                'account_type' => 'revenue',
                                'account_category' => 'sales',
                                'balance_type' => 'credit',
                                'is_system' => true,
                            ],
                            [
                                'account_code' => '41200',
                                'account_name' => 'مبيعات الإكسسوارات',
                                'account_name_en' => 'Accessories Sales',
                                'account_type' => 'revenue',
                                'account_category' => 'sales',
                                'balance_type' => 'credit',
                                'is_system' => true,
                            ],
                        ]
                    ],
                    [
                        'account_code' => '42000',
                        'account_name' => 'إيرادات الخدمات',
                        'account_name_en' => 'Service Revenue',
                        'account_type' => 'revenue',
                        'account_category' => 'service',
                        'balance_type' => 'credit',
                        'is_system' => true,
                        'children' => [
                            [
                                'account_code' => '42100',
                                'account_name' => 'خدمات الإصلاح',
                                'account_name_en' => 'Repair Services',
                                'account_type' => 'revenue',
                                'account_category' => 'service',
                                'balance_type' => 'credit',
                                'is_system' => true,
                            ],
                            [
                                'account_code' => '42200',
                                'account_name' => 'خدمات الصيانة',
                                'account_name_en' => 'Maintenance Services',
                                'account_type' => 'revenue',
                                'account_category' => 'service',
                                'balance_type' => 'credit',
                                'is_system' => true,
                            ],
                        ]
                    ],
                ]
            ],

            // المصروفات (Expenses) - 5xxxx
            [
                'account_code' => '50000',
                'account_name' => 'المصروفات',
                'account_name_en' => 'Expenses',
                'account_type' => 'expense',
                'balance_type' => 'debit',
                'is_system' => true,
                'children' => [
                    [
                        'account_code' => '51000',
                        'account_name' => 'تكلفة البضاعة المباعة',
                        'account_name_en' => 'Cost of Goods Sold',
                        'account_type' => 'expense',
                        'account_category' => 'cogs',
                        'balance_type' => 'debit',
                        'is_system' => true,
                    ],
                    [
                        'account_code' => '52000',
                        'account_name' => 'مصروفات التشغيل',
                        'account_name_en' => 'Operating Expenses',
                        'account_type' => 'expense',
                        'account_category' => 'operating',
                        'balance_type' => 'debit',
                        'is_system' => true,
                        'children' => [
                            [
                                'account_code' => '52100',
                                'account_name' => 'الرواتب والأجور',
                                'account_name_en' => 'Salaries and Wages',
                                'account_type' => 'expense',
                                'account_category' => 'operating',
                                'balance_type' => 'debit',
                                'is_system' => true,
                            ],
                            [
                                'account_code' => '52200',
                                'account_name' => 'الإيجار',
                                'account_name_en' => 'Rent',
                                'account_type' => 'expense',
                                'account_category' => 'operating',
                                'balance_type' => 'debit',
                                'is_system' => true,
                            ],
                            [
                                'account_code' => '52300',
                                'account_name' => 'الكهرباء والماء',
                                'account_name_en' => 'Utilities',
                                'account_type' => 'expense',
                                'account_category' => 'operating',
                                'balance_type' => 'debit',
                                'is_system' => true,
                            ],
                            [
                                'account_code' => '52400',
                                'account_name' => 'الاتصالات',
                                'account_name_en' => 'Communications',
                                'account_type' => 'expense',
                                'account_category' => 'operating',
                                'balance_type' => 'debit',
                                'is_system' => true,
                            ],
                        ]
                    ],
                ]
            ],
        ];

        $this->createAccountsRecursively($accounts);

        // Update current balances for all accounts
        $this->updateAccountBalances();

        $this->command->info('تم إنشاء دليل الحسابات بنجاح');
    }

    /**
     * Create accounts recursively
     */
    private function createAccountsRecursively($accounts, $parentId = null)
    {
        foreach ($accounts as $accountData) {
            $children = $accountData['children'] ?? [];
            unset($accountData['children']);

            $accountData['parent_id'] = $parentId;
            $accountData['current_balance'] = $accountData['opening_balance'] ?? 0;

            $account = Account::create($accountData);

            if (!empty($children)) {
                $this->createAccountsRecursively($children, $account->id);
            }
        }
    }

    /**
     * Update account balances
     */
    private function updateAccountBalances()
    {
        $accounts = Account::all();
        foreach ($accounts as $account) {
            $account->current_balance = $account->opening_balance;
            $account->save();
        }
    }
}
