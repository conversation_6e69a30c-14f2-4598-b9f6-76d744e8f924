<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SalePayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'sale_id',
        'payment_number',
        'payment_method',
        'amount',
        'status',
        'reference_number',
        'card_last_four',
        'card_type',
        'bank_name',
        'account_number',
        'installment_number',
        'total_installments',
        'due_date',
        'processed_by',
        'processed_at',
        'processing_notes',
        'refunded_amount',
        'refunded_at',
        'refunded_by',
        'refund_reason',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'refunded_amount' => 'decimal:2',
        'due_date' => 'date',
        'processed_at' => 'datetime',
        'refunded_at' => 'datetime',
        'metadata' => 'array',
    ];

    // Relationships
    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    public function refundedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'refunded_by');
    }

    // Accessors
    public function getPaymentMethodLabelAttribute()
    {
        $methods = [
            'cash' => 'نقدي',
            'card' => 'بطاقة ائتمان',
            'bank_transfer' => 'تحويل بنكي',
            'check' => 'شيك',
            'digital_wallet' => 'محفظة رقمية',
            'installment' => 'تقسيط',
        ];

        return $methods[$this->payment_method] ?? $this->payment_method;
    }

    public function getStatusLabelAttribute()
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'processing' => 'قيد المعالجة',
            'completed' => 'مكتمل',
            'failed' => 'فشل',
            'cancelled' => 'ملغي',
            'refunded' => 'مسترد',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'bg-yellow-100 text-yellow-800',
            'processing' => 'bg-blue-100 text-blue-800',
            'completed' => 'bg-green-100 text-green-800',
            'failed' => 'bg-red-100 text-red-800',
            'cancelled' => 'bg-gray-100 text-gray-800',
            'refunded' => 'bg-purple-100 text-purple-800',
        ];

        return $colors[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    public function getNetAmountAttribute()
    {
        return $this->amount - $this->refunded_amount;
    }

    public function getIsRefundableAttribute()
    {
        return $this->status === 'completed' && $this->refunded_amount < $this->amount;
    }

    public function getIsInstallmentAttribute()
    {
        return $this->payment_method === 'installment' && $this->total_installments > 1;
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeByMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    public function scopeInstallments($query)
    {
        return $query->where('payment_method', 'installment');
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereIn('status', ['pending', 'processing']);
    }

    // Methods
    public static function generatePaymentNumber()
    {
        $date = now()->format('Ymd');
        $lastPayment = static::where('payment_number', 'like', "PAY-{$date}-%")
                            ->orderBy('payment_number', 'desc')
                            ->first();

        if ($lastPayment) {
            $lastNumber = (int) substr($lastPayment->payment_number, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return "PAY-{$date}-{$newNumber}";
    }

    public function markAsCompleted($processedBy = null)
    {
        $this->update([
            'status' => 'completed',
            'processed_by' => $processedBy ?? auth()->id(),
            'processed_at' => now(),
        ]);
    }

    public function markAsFailed($reason = null)
    {
        $this->update([
            'status' => 'failed',
            'processing_notes' => $this->processing_notes . "\nFailed: " . $reason,
        ]);
    }

    public function processRefund($amount, $reason = null, $refundedBy = null)
    {
        if ($amount > ($this->amount - $this->refunded_amount)) {
            throw new \Exception('Refund amount cannot exceed the remaining payment amount.');
        }

        $this->update([
            'refunded_amount' => $this->refunded_amount + $amount,
            'refunded_at' => now(),
            'refunded_by' => $refundedBy ?? auth()->id(),
            'refund_reason' => $reason,
            'status' => ($this->refunded_amount + $amount >= $this->amount) ? 'refunded' : 'completed',
        ]);

        return $this;
    }

    public function canBeRefunded()
    {
        return $this->status === 'completed' && $this->refunded_amount < $this->amount;
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['pending', 'processing']);
    }

    public function getRefundableAmount()
    {
        return $this->amount - $this->refunded_amount;
    }

    // Static methods for creating payments
    public static function createCashPayment($saleId, $amount, $notes = null)
    {
        return static::create([
            'sale_id' => $saleId,
            'payment_number' => static::generatePaymentNumber(),
            'payment_method' => 'cash',
            'amount' => $amount,
            'status' => 'completed',
            'processed_by' => auth()->id(),
            'processed_at' => now(),
            'notes' => $notes,
        ]);
    }

    public static function createCardPayment($saleId, $amount, $cardDetails = [])
    {
        return static::create([
            'sale_id' => $saleId,
            'payment_number' => static::generatePaymentNumber(),
            'payment_method' => 'card',
            'amount' => $amount,
            'status' => 'completed',
            'processed_by' => auth()->id(),
            'processed_at' => now(),
            'reference_number' => $cardDetails['reference_number'] ?? null,
            'card_last_four' => $cardDetails['last_four'] ?? null,
            'card_type' => $cardDetails['card_type'] ?? null,
            'notes' => $cardDetails['notes'] ?? null,
        ]);
    }

    public static function createBankTransferPayment($saleId, $amount, $bankDetails = [])
    {
        return static::create([
            'sale_id' => $saleId,
            'payment_number' => static::generatePaymentNumber(),
            'payment_method' => 'bank_transfer',
            'amount' => $amount,
            'status' => 'pending', // Usually needs verification
            'reference_number' => $bankDetails['reference_number'] ?? null,
            'bank_name' => $bankDetails['bank_name'] ?? null,
            'account_number' => $bankDetails['account_number'] ?? null,
            'notes' => $bankDetails['notes'] ?? null,
        ]);
    }

    public static function createInstallmentPayment($saleId, $amount, $installmentDetails = [])
    {
        return static::create([
            'sale_id' => $saleId,
            'payment_number' => static::generatePaymentNumber(),
            'payment_method' => 'installment',
            'amount' => $amount,
            'status' => 'pending',
            'installment_number' => $installmentDetails['installment_number'] ?? 1,
            'total_installments' => $installmentDetails['total_installments'] ?? 1,
            'due_date' => $installmentDetails['due_date'] ?? now()->addMonth(),
            'notes' => $installmentDetails['notes'] ?? null,
        ]);
    }
}
