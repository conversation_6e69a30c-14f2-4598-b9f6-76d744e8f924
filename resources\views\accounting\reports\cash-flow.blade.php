@extends('layouts.main')

@section('title', 'قائمة التدفقات النقدية')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">قائمة التدفقات النقدية</h1>
            <p class="text-gray-600 dark:text-gray-400">التدفقات النقدية من {{ $startDate }} إلى {{ $endDate }}</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <button onclick="printReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-print mr-2"></i>
                طباعة
            </button>
            
            <button onclick="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير
            </button>
            
            <a href="{{ route('accounting-reports.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" name="start_date" value="{{ $startDate }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" name="end_date" value="{{ $endDate }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                <i class="fas fa-search mr-2"></i>
                تحديث
            </button>
        </form>
    </div>

    <!-- Cash Flow Statement -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden" id="cash-flow-report">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="text-center">
                <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100">قائمة التدفقات النقدية</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">للفترة من {{ $startDate }} إلى {{ $endDate }}</p>
            </div>
        </div>
        
        <div class="p-6 space-y-6">
            <!-- Cash Balance Summary -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <div class="text-center">
                        <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">الرصيد النقدي الافتتاحي</h4>
                        <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {{ number_format($openingCashBalance, 2) }} ريال
                        </p>
                    </div>
                </div>
                
                <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                    <div class="text-center">
                        <h4 class="text-sm font-medium text-green-800 dark:text-green-200 mb-2">صافي التدفق النقدي</h4>
                        <p class="text-2xl font-bold {{ $netCashFlow >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ number_format($netCashFlow, 2) }} ريال
                        </p>
                    </div>
                </div>
                
                <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                    <div class="text-center">
                        <h4 class="text-sm font-medium text-purple-800 dark:text-purple-200 mb-2">الرصيد النقدي الختامي</h4>
                        <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                            {{ number_format($closingCashBalance, 2) }} ريال
                        </p>
                    </div>
                </div>
            </div>

            <!-- Operating Activities -->
            <div>
                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                    التدفقات النقدية من الأنشطة التشغيلية
                </h3>
                
                <div class="space-y-3 mr-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700 dark:text-gray-300">صافي الدخل</span>
                        <span class="font-medium {{ $netIncome >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ number_format($netIncome, 2) }}
                        </span>
                    </div>
                    
                    <div class="text-sm text-gray-600 dark:text-gray-400 mr-4">
                        <p>تعديلات لتسوية صافي الدخل مع النقد المحصل من الأنشطة التشغيلية:</p>
                    </div>
                    
                    <div class="flex justify-between items-center mr-4">
                        <span class="text-gray-600 dark:text-gray-400">الاستهلاك والإطفاء</span>
                        <span class="text-gray-800 dark:text-gray-200">
                            {{ number_format($depreciation, 2) }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center mr-4">
                        <span class="text-gray-600 dark:text-gray-400">التغير في الذمم المدينة</span>
                        <span class="text-gray-800 dark:text-gray-200">
                            {{ number_format($receivablesChange, 2) }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center mr-4">
                        <span class="text-gray-600 dark:text-gray-400">التغير في المخزون</span>
                        <span class="text-gray-800 dark:text-gray-200">
                            {{ number_format($inventoryChange, 2) }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center mr-4">
                        <span class="text-gray-600 dark:text-gray-400">التغير في الذمم الدائنة</span>
                        <span class="text-gray-800 dark:text-gray-200">
                            {{ number_format($payablesChange, 2) }}
                        </span>
                    </div>
                </div>
                
                <div class="border-t border-gray-200 dark:border-gray-700 mt-4 pt-3">
                    <div class="flex justify-between items-center font-bold text-lg">
                        <span class="text-gray-900 dark:text-gray-100">صافي النقد من الأنشطة التشغيلية</span>
                        <span class="{{ $operatingCashFlow >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ number_format($operatingCashFlow, 2) }} ريال
                        </span>
                    </div>
                </div>
            </div>

            <!-- Investing Activities -->
            <div>
                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                    التدفقات النقدية من الأنشطة الاستثمارية
                </h3>
                
                <div class="space-y-3 mr-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700 dark:text-gray-300">شراء أصول ثابتة</span>
                        <span class="text-red-600 dark:text-red-400">
                            ({{ number_format(abs($fixedAssetsAdditions), 2) }})
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700 dark:text-gray-300">بيع أصول ثابتة</span>
                        <span class="text-green-600 dark:text-green-400">
                            {{ number_format($fixedAssetsDisposals, 2) }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700 dark:text-gray-300">استثمارات أخرى</span>
                        <span class="text-gray-800 dark:text-gray-200">
                            {{ number_format($otherInvestments, 2) }}
                        </span>
                    </div>
                </div>
                
                <div class="border-t border-gray-200 dark:border-gray-700 mt-4 pt-3">
                    <div class="flex justify-between items-center font-bold text-lg">
                        <span class="text-gray-900 dark:text-gray-100">صافي النقد من الأنشطة الاستثمارية</span>
                        <span class="{{ $investingCashFlow >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ number_format($investingCashFlow, 2) }} ريال
                        </span>
                    </div>
                </div>
            </div>

            <!-- Financing Activities -->
            <div>
                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                    التدفقات النقدية من الأنشطة التمويلية
                </h3>
                
                <div class="space-y-3 mr-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700 dark:text-gray-300">زيادة رأس المال</span>
                        <span class="text-green-600 dark:text-green-400">
                            {{ number_format($capitalIncrease, 2) }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700 dark:text-gray-300">قروض جديدة</span>
                        <span class="text-green-600 dark:text-green-400">
                            {{ number_format($newLoans, 2) }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700 dark:text-gray-300">سداد قروض</span>
                        <span class="text-red-600 dark:text-red-400">
                            ({{ number_format(abs($loanRepayments), 2) }})
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700 dark:text-gray-300">توزيعات أرباح</span>
                        <span class="text-red-600 dark:text-red-400">
                            ({{ number_format(abs($dividendsPaid), 2) }})
                        </span>
                    </div>
                </div>
                
                <div class="border-t border-gray-200 dark:border-gray-700 mt-4 pt-3">
                    <div class="flex justify-between items-center font-bold text-lg">
                        <span class="text-gray-900 dark:text-gray-100">صافي النقد من الأنشطة التمويلية</span>
                        <span class="{{ $financingCashFlow >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ number_format($financingCashFlow, 2) }} ريال
                        </span>
                    </div>
                </div>
            </div>

            <!-- Net Cash Flow -->
            <div class="border-t-2 border-gray-300 dark:border-gray-600 pt-4">
                <div class="space-y-3">
                    <div class="flex justify-between items-center font-bold text-lg">
                        <span class="text-gray-900 dark:text-gray-100">صافي الزيادة (النقص) في النقد</span>
                        <span class="{{ $netCashFlow >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ number_format($netCashFlow, 2) }} ريال
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700 dark:text-gray-300">النقد في بداية الفترة</span>
                        <span class="text-gray-900 dark:text-gray-100">
                            {{ number_format($openingCashBalance, 2) }} ريال
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center font-bold text-xl border-t border-gray-200 dark:border-gray-700 pt-2">
                        <span class="text-gray-900 dark:text-gray-100">النقد في نهاية الفترة</span>
                        <span class="text-blue-600 dark:text-blue-400">
                            {{ number_format($closingCashBalance, 2) }} ريال
                        </span>
                    </div>
                </div>
            </div>

            <!-- Cash Flow Analysis -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3">تحليل التدفقات النقدية</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <div class="text-gray-600 dark:text-gray-400">نسبة التدفق التشغيلي</div>
                        <div class="font-bold text-gray-900 dark:text-gray-100">
                            {{ $netIncome != 0 ? number_format(($operatingCashFlow / abs($netIncome)) * 100, 1) : 0 }}%
                        </div>
                    </div>
                    <div>
                        <div class="text-gray-600 dark:text-gray-400">معدل دوران النقد</div>
                        <div class="font-bold text-gray-900 dark:text-gray-100">
                            {{ $openingCashBalance != 0 ? number_format($netCashFlow / $openingCashBalance, 2) : 0 }}
                        </div>
                    </div>
                </div>
                
                @if($operatingCashFlow > 0 && $investingCashFlow < 0)
                    <div class="mt-3 p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                        <p class="text-green-800 dark:text-green-200 text-sm">
                            <i class="fas fa-check-circle mr-1"></i>
                            الشركة تحقق تدفقات نقدية إيجابية من العمليات وتستثمر في النمو
                        </p>
                    </div>
                @elseif($operatingCashFlow < 0)
                    <div class="mt-3 p-3 bg-red-100 dark:bg-red-900/20 rounded-lg">
                        <p class="text-red-800 dark:text-red-200 text-sm">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            تحذير: التدفقات النقدية التشغيلية سالبة - يجب مراجعة العمليات
                        </p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function printReport() {
    window.print();
}

function exportReport() {
    alert('ميزة التصدير قيد التطوير');
}

// Print styles
const printStyles = `
    @media print {
        body * {
            visibility: hidden;
        }
        #cash-flow-report, #cash-flow-report * {
            visibility: visible;
        }
        #cash-flow-report {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none !important;
        }
    }
`;

// Add print styles to head
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
@endpush
@endsection
