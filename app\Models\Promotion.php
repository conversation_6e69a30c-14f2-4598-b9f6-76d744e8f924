<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Promotion extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'type',
        'value',
        'max_discount',
        'min_purchase',
        'buy_quantity',
        'get_quantity',
        'applicable_items',
        'is_active',
        'start_date',
        'end_date',
        'usage_limit',
        'usage_limit_per_customer',
        'used_count',
        'conditions',
        'applicable_locations',
        'applicable_customer_types',
        'created_by',
    ];

    protected $casts = [
        'applicable_items' => 'array',
        'conditions' => 'array',
        'applicable_locations' => 'array',
        'applicable_customer_types' => 'array',
        'is_active' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
        'value' => 'decimal:2',
        'max_discount' => 'decimal:2',
        'min_purchase' => 'decimal:2',
    ];

    // Relationships
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    // Accessors
    public function getTypeLabelAttribute()
    {
        $types = [
            'percentage' => 'نسبة مئوية',
            'fixed_amount' => 'مبلغ ثابت',
            'buy_x_get_y' => 'اشتري X واحصل على Y',
            'free_shipping' => 'شحن مجاني',
        ];

        return $types[$this->type] ?? $this->type;
    }

    public function getStatusLabelAttribute()
    {
        if (!$this->is_active) {
            return 'غير نشط';
        }

        $now = now()->toDateString();
        
        if ($this->start_date > $now) {
            return 'لم يبدأ بعد';
        }
        
        if ($this->end_date < $now) {
            return 'منتهي الصلاحية';
        }
        
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return 'تم استنفاد الاستخدامات';
        }

        return 'نشط';
    }

    public function getStatusColorAttribute()
    {
        $status = $this->status_label;
        
        $colors = [
            'نشط' => 'bg-green-100 text-green-800',
            'غير نشط' => 'bg-gray-100 text-gray-800',
            'لم يبدأ بعد' => 'bg-blue-100 text-blue-800',
            'منتهي الصلاحية' => 'bg-red-100 text-red-800',
            'تم استنفاد الاستخدامات' => 'bg-orange-100 text-orange-800',
        ];

        return $colors[$status] ?? 'bg-gray-100 text-gray-800';
    }

    public function getUsagePercentageAttribute()
    {
        if (!$this->usage_limit) {
            return 0;
        }

        return min(($this->used_count / $this->usage_limit) * 100, 100);
    }

    public function getRemainingUsageAttribute()
    {
        if (!$this->usage_limit) {
            return null; // Unlimited
        }

        return max(0, $this->usage_limit - $this->used_count);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    public function scopeByCode($query, $code)
    {
        return $query->where('code', $code);
    }

    public function scopeByLocation($query, $locationId)
    {
        return $query->where(function($q) use ($locationId) {
            $q->whereNull('applicable_locations')
              ->orWhereJsonContains('applicable_locations', $locationId);
        });
    }

    public function scopeByCustomerType($query, $customerType)
    {
        return $query->where(function($q) use ($customerType) {
            $q->whereNull('applicable_customer_types')
              ->orWhereJsonContains('applicable_customer_types', $customerType);
        });
    }

    public function scopeAvailable($query)
    {
        return $query->active()
                    ->where(function($q) {
                        $q->whereNull('usage_limit')
                          ->orWhereRaw('used_count < usage_limit');
                    });
    }

    // Methods
    public function isValid($saleAmount = null, $locationId = null, $customerType = null)
    {
        // Check if promotion is active
        if (!$this->is_active) {
            return false;
        }

        // Check date range
        $now = now()->toDateString();
        if ($this->start_date > $now || $this->end_date < $now) {
            return false;
        }

        // Check usage limit
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        // Check minimum purchase amount
        if ($this->min_purchase && $saleAmount && $saleAmount < $this->min_purchase) {
            return false;
        }

        // Check location applicability
        if ($this->applicable_locations && $locationId) {
            if (!in_array($locationId, $this->applicable_locations)) {
                return false;
            }
        }

        // Check customer type applicability
        if ($this->applicable_customer_types && $customerType) {
            if (!in_array($customerType, $this->applicable_customer_types)) {
                return false;
            }
        }

        return true;
    }

    public function calculateDiscount($saleAmount, $items = [])
    {
        if (!$this->isValid($saleAmount)) {
            return 0;
        }

        switch ($this->type) {
            case 'percentage':
                $discount = ($saleAmount * $this->value) / 100;
                return $this->max_discount ? min($discount, $this->max_discount) : $discount;

            case 'fixed_amount':
                return min($this->value, $saleAmount);

            case 'buy_x_get_y':
                return $this->calculateBuyXGetYDiscount($items);

            case 'free_shipping':
                // This would be handled differently based on shipping costs
                return 0;

            default:
                return 0;
        }
    }

    private function calculateBuyXGetYDiscount($items)
    {
        if (!$this->applicable_items || empty($items)) {
            return 0;
        }

        $applicableItems = collect($items)->filter(function($item) {
            return in_array($item['part_id'] ?? null, $this->applicable_items) ||
                   in_array($item['item_category'] ?? null, $this->applicable_items);
        });

        $totalQuantity = $applicableItems->sum('quantity');
        $freeItems = intval($totalQuantity / $this->buy_quantity) * $this->get_quantity;

        if ($freeItems > 0) {
            // Calculate discount based on cheapest items
            $sortedItems = $applicableItems->sortBy('unit_price');
            $discount = 0;
            $remainingFreeItems = $freeItems;

            foreach ($sortedItems as $item) {
                if ($remainingFreeItems <= 0) break;

                $freeFromThisItem = min($remainingFreeItems, $item['quantity']);
                $discount += $freeFromThisItem * $item['unit_price'];
                $remainingFreeItems -= $freeFromThisItem;
            }

            return $discount;
        }

        return 0;
    }

    public function canBeUsedBy($customerId)
    {
        if (!$this->usage_limit_per_customer) {
            return true;
        }

        $customerUsage = $this->sales()
                             ->where('customer_id', $customerId)
                             ->count();

        return $customerUsage < $this->usage_limit_per_customer;
    }

    public function incrementUsage()
    {
        $this->increment('used_count');
    }

    public function decrementUsage()
    {
        if ($this->used_count > 0) {
            $this->decrement('used_count');
        }
    }

    public function activate()
    {
        $this->update(['is_active' => true]);
    }

    public function deactivate()
    {
        $this->update(['is_active' => false]);
    }

    // Static methods
    public static function findByCode($code)
    {
        return static::where('code', $code)->first();
    }

    public static function getAvailablePromotions($locationId = null, $customerType = null)
    {
        $query = static::available();

        if ($locationId) {
            $query->byLocation($locationId);
        }

        if ($customerType) {
            $query->byCustomerType($customerType);
        }

        return $query->get();
    }

    public static function generateCode($name)
    {
        $baseCode = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $name), 0, 6));
        $code = $baseCode;
        $counter = 1;

        while (static::where('code', $code)->exists()) {
            $code = $baseCode . $counter;
            $counter++;
        }

        return $code;
    }
}
