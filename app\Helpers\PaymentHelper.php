<?php

namespace App\Helpers;

class PaymentHelper
{
    /**
     * Get payment method color
     */
    public static function getPaymentMethodColor($method)
    {
        return match($method) {
            'cash' => '#10B981',
            'card' => '#3B82F6',
            'bank_transfer' => '#8B5CF6',
            'check' => '#F59E0B',
            'digital_wallet' => '#06B6D4',
            'installment' => '#F97316',
            default => '#6B7280'
        };
    }

    /**
     * Get payment method label
     */
    public static function getPaymentMethodLabel($method)
    {
        return match($method) {
            'cash' => 'نقدي',
            'card' => 'بطاقة ائتمان',
            'bank_transfer' => 'تحويل بنكي',
            'check' => 'شيك',
            'digital_wallet' => 'محفظة رقمية',
            'installment' => 'تقسيط',
            default => $method
        };
    }

    /**
     * Get payment method icon
     */
    public static function getPaymentMethodIcon($method)
    {
        return match($method) {
            'cash' => 'fas fa-money-bill-wave',
            'card' => 'fas fa-credit-card',
            'bank_transfer' => 'fas fa-university',
            'check' => 'fas fa-money-check',
            'digital_wallet' => 'fas fa-mobile-alt',
            'installment' => 'fas fa-calendar-alt',
            default => 'fas fa-dollar-sign'
        };
    }

    /**
     * Get payment status color
     */
    public static function getPaymentStatusColor($status)
    {
        return match($status) {
            'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
            'processing' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
            'completed' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
            'failed' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
            'cancelled' => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
            'refunded' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
            default => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
        };
    }

    /**
     * Get payment status label
     */
    public static function getPaymentStatusLabel($status)
    {
        return match($status) {
            'pending' => 'في الانتظار',
            'processing' => 'قيد المعالجة',
            'completed' => 'مكتمل',
            'failed' => 'فشل',
            'cancelled' => 'ملغي',
            'refunded' => 'مسترد',
            default => $status
        };
    }

    /**
     * Get all available payment methods
     */
    public static function getAvailablePaymentMethods()
    {
        return [
            'cash' => [
                'label' => self::getPaymentMethodLabel('cash'),
                'icon' => self::getPaymentMethodIcon('cash'),
                'color' => self::getPaymentMethodColor('cash'),
            ],
            'card' => [
                'label' => self::getPaymentMethodLabel('card'),
                'icon' => self::getPaymentMethodIcon('card'),
                'color' => self::getPaymentMethodColor('card'),
            ],
            'bank_transfer' => [
                'label' => self::getPaymentMethodLabel('bank_transfer'),
                'icon' => self::getPaymentMethodIcon('bank_transfer'),
                'color' => self::getPaymentMethodColor('bank_transfer'),
            ],
            'check' => [
                'label' => self::getPaymentMethodLabel('check'),
                'icon' => self::getPaymentMethodIcon('check'),
                'color' => self::getPaymentMethodColor('check'),
            ],
            'digital_wallet' => [
                'label' => self::getPaymentMethodLabel('digital_wallet'),
                'icon' => self::getPaymentMethodIcon('digital_wallet'),
                'color' => self::getPaymentMethodColor('digital_wallet'),
            ],
            'installment' => [
                'label' => self::getPaymentMethodLabel('installment'),
                'icon' => self::getPaymentMethodIcon('installment'),
                'color' => self::getPaymentMethodColor('installment'),
            ],
        ];
    }

    /**
     * Validate payment method
     */
    public static function isValidPaymentMethod($method)
    {
        return in_array($method, array_keys(self::getAvailablePaymentMethods()));
    }

    /**
     * Get payment method account code
     */
    public static function getPaymentMethodAccountCode($method)
    {
        return match($method) {
            'cash' => '11101',           // الصندوق
            'card' => '11102',           // البنك الأهلي
            'bank_transfer' => '11103',  // البنك الراجحي
            'check' => '11104',          // الشيكات تحت التحصيل
            'digital_wallet' => '11105', // المحافظ الرقمية
            'installment' => '11201',    // ذمم العملاء (للتقسيط)
            default => '11101'           // الصندوق كافتراضي
        };
    }
}
