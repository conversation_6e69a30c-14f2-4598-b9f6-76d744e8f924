@extends('layouts.main')

@section('title', 'حركات المخزون')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">حركات المخزون</h1>
            <p class="text-gray-600 dark:text-gray-400">تتبع جميع حركات المخزون والتغييرات</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('inventory.adjust-stock') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-plus mr-2"></i>
                تعديل مخزون
            </a>
            <a href="{{ route('inventory.transfer-stock') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-exchange-alt mr-2"></i>
                نقل مخزون
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي الحركات</h3>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $movementStats['total_movements'] ?? 0 }}</p>
                </div>
                <div class="text-2xl text-blue-600">
                    <i class="fas fa-exchange-alt"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">كمية الإدخال</h3>
                    <p class="text-2xl font-bold text-green-600">{{ $movementStats['incoming_quantity'] ?? 0 }}</p>
                </div>
                <div class="text-2xl text-green-600">
                    <i class="fas fa-arrow-down"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">كمية الإخراج</h3>
                    <p class="text-2xl font-bold text-red-600">{{ $movementStats['outgoing_quantity'] ?? 0 }}</p>
                </div>
                <div class="text-2xl text-red-600">
                    <i class="fas fa-arrow-up"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">عمليات النقل</h3>
                    <p class="text-2xl font-bold text-yellow-600">{{ $movementStats['transfers_count'] ?? 0 }}</p>
                </div>
                <div class="text-2xl text-yellow-600">
                    <i class="fas fa-truck"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تصفية الحركات</h3>
        </div>
        <div class="p-6">
            <form method="GET" action="{{ route('inventory.movements') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الحركة</label>
                    <select name="type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="">جميع الأنواع</option>
                        <option value="in" {{ request('type') == 'in' ? 'selected' : '' }}>إدخال</option>
                        <option value="out" {{ request('type') == 'out' ? 'selected' : '' }}>إخراج</option>
                        <option value="transfer" {{ request('type') == 'transfer' ? 'selected' : '' }}>نقل</option>
                        <option value="adjustment" {{ request('type') == 'adjustment' ? 'selected' : '' }}>تعديل</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                    <input type="date" name="date_from" value="{{ request('date_from') }}" 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                    <input type="date" name="date_to" value="{{ request('date_to') }}" 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                    <div class="flex gap-2">
                        <input type="text" name="search" value="{{ request('search') }}" placeholder="البحث في المنتجات..."
                               class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Movements Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">سجل الحركات</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المنتج</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">نوع الحركة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">السبب</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المستخدم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($movements as $movement)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                                        <span class="text-sm font-medium text-gray-600 dark:text-gray-300">
                                            {{ substr($movement->inventory->product->name ?? 'N/A', 0, 1) }}
                                        </span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                            {{ $movement->inventory->product->name ?? 'منتج محذوف' }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $movement->inventory->location->name ?? 'موقع غير محدد' }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    @if($movement->type == 'in') bg-green-100 text-green-800
                                    @elseif($movement->type == 'out') bg-red-100 text-red-800
                                    @elseif($movement->type == 'transfer') bg-blue-100 text-blue-800
                                    @else bg-yellow-100 text-yellow-800 @endif">
                                    @if($movement->type == 'in') إدخال
                                    @elseif($movement->type == 'out') إخراج
                                    @elseif($movement->type == 'transfer') نقل
                                    @else تعديل @endif
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                <span class="font-medium
                                    @if($movement->type == 'in') text-green-600
                                    @elseif($movement->type == 'out') text-red-600
                                    @else text-gray-600 @endif">
                                    @if($movement->type == 'in') +{{ $movement->quantity }}
                                    @elseif($movement->type == 'out') -{{ $movement->quantity }}
                                    @else {{ $movement->quantity }} @endif
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                {{ $movement->reason }}
                                @if($movement->notes)
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ $movement->notes }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $movement->user->name ?? 'مستخدم محذوف' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                <div>{{ $movement->created_at->format('Y-m-d') }}</div>
                                <div class="text-xs">{{ $movement->created_at->format('H:i') }}</div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-exchange-alt text-4xl mb-4"></i>
                                    <p>لا توجد حركات مخزون</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        @if($movements->hasPages())
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $movements->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
