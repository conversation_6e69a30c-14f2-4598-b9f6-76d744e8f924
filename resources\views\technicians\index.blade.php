@extends('layouts.main')

@section('title', 'إدارة الفنيين')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إدارة الفنيين</h1>
            <p class="text-gray-600 dark:text-gray-400">إدارة الفنيين ومهاراتهم وأعباء العمل</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('technicians.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                إضافة فني
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الفنيين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $technicians->total() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متاحين الآن</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ $technicians->where('availability', 'available')->count() }}
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                    <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مشغولين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ $technicians->where('availability', 'busy')->count() }}
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">نشطين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ $technicians->where('status', 'active')->count() }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <input type="text" x-model="filters.search" @input="filterTechnicians()" placeholder="اسم الفني..." 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التخصص</label>
                <select x-model="filters.specialization" @change="filterTechnicians()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع التخصصات</option>
                    <option value="mobile">هواتف محمولة</option>
                    <option value="laptop">لابتوب</option>
                    <option value="desktop">حاسوب مكتبي</option>
                    <option value="tablet">تابلت</option>
                    <option value="gaming">أجهزة ألعاب</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                <select x-model="filters.status" @change="filterTechnicians()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="available">متاح</option>
                    <option value="busy">مشغول</option>
                    <option value="break">استراحة</option>
                    <option value="offline">غير متصل</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المستوى</label>
                <select x-model="filters.level" @change="filterTechnicians()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع المستويات</option>
                    <option value="junior">مبتدئ</option>
                    <option value="intermediate">متوسط</option>
                    <option value="senior">خبير</option>
                    <option value="expert">محترف</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Technicians Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <template x-for="technician in filteredTechnicians" :key="technician.id">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <div class="flex items-start justify-between">
                    <div class="flex items-center">
                        <img class="h-12 w-12 rounded-full" :src="technician.avatar" :alt="technician.name">
                        <div class="mr-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" x-text="technician.name"></h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400" x-text="technician.email"></p>
                        </div>
                    </div>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          :class="{
                              'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': technician.status === 'available',
                              'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200': technician.status === 'busy',
                              'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': technician.status === 'break',
                              'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200': technician.status === 'offline'
                          }"
                          x-text="getStatusText(technician.status)">
                    </span>
                </div>

                <div class="mt-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">التقييم:</span>
                        <div class="flex items-center">
                            <template x-for="i in 5" :key="i">
                                <svg class="w-4 h-4" :class="i <= technician.rating ? 'text-yellow-400' : 'text-gray-300'" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            </template>
                            <span class="mr-1 text-sm text-gray-600 dark:text-gray-400" x-text="technician.rating"></span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">المستوى:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="getLevelText(technician.level)"></span>
                    </div>

                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">المهام النشطة:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="technician.active_tasks"></span>
                    </div>

                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm text-gray-600 dark:text-gray-400">المهام المكتملة:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="technician.completed_tasks"></span>
                    </div>

                    <!-- Specializations -->
                    <div class="mb-4">
                        <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التخصصات:</p>
                        <div class="flex flex-wrap gap-1">
                            <template x-for="spec in technician.specializations" :key="spec">
                                <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" x-text="getSpecializationText(spec)"></span>
                            </template>
                        </div>
                    </div>

                    <!-- Skills -->
                    <div class="mb-4">
                        <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المهارات:</p>
                        <div class="space-y-2">
                            <template x-for="skill in technician.skills" :key="skill.name">
                                <div>
                                    <div class="flex justify-between text-xs">
                                        <span x-text="skill.name"></span>
                                        <span x-text="skill.level + '%'"></span>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                        <div class="bg-blue-600 h-1.5 rounded-full" :style="`width: ${skill.level}%`"></div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex space-x-2 space-x-reverse">
                        <button @click="viewTechnician(technician.id)" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm">
                            عرض التفاصيل
                        </button>
                        <button @click="assignTask(technician.id)" class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded text-sm">
                            تعيين مهمة
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- Workload Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">توزيع أعباء العمل</h3>
        <div class="space-y-4">
            <template x-for="technician in technicians" :key="technician.id">
                <div class="flex items-center">
                    <div class="w-32 text-sm text-gray-900 dark:text-gray-100" x-text="technician.name"></div>
                    <div class="flex-1 mx-4">
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4">
                            <div class="bg-blue-600 h-4 rounded-full flex items-center justify-center text-xs text-white" 
                                 :style="`width: ${technician.workload}%`"
                                 x-text="technician.workload + '%'">
                            </div>
                        </div>
                    </div>
                    <div class="w-20 text-sm text-gray-600 dark:text-gray-400" x-text="technician.active_tasks + ' مهام'"></div>
                </div>
            </template>
        </div>
    </div>
</div>

@push('scripts')
<script>
function techniciansManager() {
    return {
        technicians: [
            {
                id: 1,
                name: 'محمد الفني',
                email: '<EMAIL>',
                avatar: 'https://ui-avatars.com/api/?name=محمد+الفني&background=3B82F6&color=fff&size=48',
                status: 'available',
                level: 'senior',
                rating: 4.8,
                active_tasks: 3,
                completed_tasks: 127,
                workload: 60,
                specializations: ['mobile', 'tablet'],
                skills: [
                    { name: 'إصلاح الشاشات', level: 95 },
                    { name: 'استبدال البطاريات', level: 90 },
                    { name: 'إصلاح اللوحات الأم', level: 85 }
                ]
            },
            {
                id: 2,
                name: 'علي الفني',
                email: '<EMAIL>',
                avatar: 'https://ui-avatars.com/api/?name=علي+الفني&background=10B981&color=fff&size=48',
                status: 'busy',
                level: 'intermediate',
                rating: 4.5,
                active_tasks: 5,
                completed_tasks: 89,
                workload: 85,
                specializations: ['laptop', 'desktop'],
                skills: [
                    { name: 'إصلاح الهارد ديسك', level: 88 },
                    { name: 'تركيب الرام', level: 92 },
                    { name: 'إصلاح المراوح', level: 80 }
                ]
            },
            {
                id: 3,
                name: 'أحمد الفني',
                email: '<EMAIL>',
                avatar: 'https://ui-avatars.com/api/?name=أحمد+الفني&background=F59E0B&color=fff&size=48',
                status: 'break',
                level: 'junior',
                rating: 4.2,
                active_tasks: 2,
                completed_tasks: 45,
                workload: 40,
                specializations: ['mobile'],
                skills: [
                    { name: 'تنظيف الأجهزة', level: 75 },
                    { name: 'استبدال الشواحن', level: 85 },
                    { name: 'فحص البرمجيات', level: 70 }
                ]
            }
        ],
        filteredTechnicians: [],
        filters: {
            search: '',
            specialization: '',
            status: '',
            level: ''
        },
        stats: {
            totalTechnicians: 8,
            availableNow: 5,
            busyNow: 3,
            avgRating: 4.7
        },

        init() {
            this.filteredTechnicians = [...this.technicians];
        },

        filterTechnicians() {
            this.filteredTechnicians = this.technicians.filter(tech => {
                const matchesSearch = !this.filters.search || 
                    tech.name.toLowerCase().includes(this.filters.search.toLowerCase());
                
                const matchesSpecialization = !this.filters.specialization || 
                    tech.specializations.includes(this.filters.specialization);
                
                const matchesStatus = !this.filters.status || tech.status === this.filters.status;
                const matchesLevel = !this.filters.level || tech.level === this.filters.level;

                return matchesSearch && matchesSpecialization && matchesStatus && matchesLevel;
            });
        },

        getStatusText(status) {
            const statuses = {
                'available': 'متاح',
                'busy': 'مشغول',
                'break': 'استراحة',
                'offline': 'غير متصل'
            };
            return statuses[status] || status;
        },

        getLevelText(level) {
            const levels = {
                'junior': 'مبتدئ',
                'intermediate': 'متوسط',
                'senior': 'خبير',
                'expert': 'محترف'
            };
            return levels[level] || level;
        },

        getSpecializationText(spec) {
            const specs = {
                'mobile': 'هواتف',
                'laptop': 'لابتوب',
                'desktop': 'مكتبي',
                'tablet': 'تابلت',
                'gaming': 'ألعاب'
            };
            return specs[spec] || spec;
        },

        addTechnician() {
            alert('سيتم إضافة نافذة إضافة فني جديد');
        },

        viewTechnician(id) {
            alert(`عرض تفاصيل الفني ${id}`);
        },

        assignTask(id) {
            alert(`تعيين مهمة للفني ${id}`);
        },

        exportTechnicians() {
            alert('سيتم تصدير بيانات الفنيين');
        }
    }
}
</script>
@endpush
@endsection
