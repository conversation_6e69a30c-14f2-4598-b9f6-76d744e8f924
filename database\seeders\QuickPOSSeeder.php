<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Location;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Part;
use App\Models\Customer;
use App\Models\User;

class QuickPOSSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default location if none exists
        if (Location::count() === 0) {
            Location::create([
                'name' => 'الفرع الرئيسي',
                'code' => 'MAIN',
                'type' => 'store',
                'description' => 'الفرع الرئيسي لمركز الصيانة',
                'email' => '<EMAIL>',
                'phone' => '+966501234567',
                'address' => 'شارع الملك فهد، الرياض',
                'city' => 'الرياض',
                'state' => 'الرياض',
                'country' => 'المملكة العربية السعودية',
                'postal_code' => '12345',
                'is_active' => true,
                'is_default' => true,
                'is_main_branch' => true,
                'business_hours' => [
                    'sunday' => ['open' => '09:00', 'close' => '18:00'],
                    'monday' => ['open' => '09:00', 'close' => '18:00'],
                    'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                    'thursday' => ['open' => '09:00', 'close' => '18:00'],
                    'friday' => ['open' => '14:00', 'close' => '18:00'],
                    'saturday' => ['open' => '09:00', 'close' => '18:00'],
                ],
                'services_offered' => [
                    'mobile_repair',
                    'computer_repair',
                    'tablet_repair',
                    'parts_sales',
                    'accessories_sales'
                ],
                'pos_settings' => [
                    'allow_sales' => true,
                    'tax_rate' => 15,
                    'payment_methods' => ['cash', 'card', 'bank_transfer'],
                    'auto_print_receipt' => true,
                    'require_customer_info' => false,
                ],
                'receipt_header' => 'مركز الصيانة المتقدم',
                'receipt_footer' => 'شكراً لثقتكم - نتطلع لخدمتكم مرة أخرى',
                'tax_number' => '***************',
            ]);

            $this->command->info('تم إنشاء الموقع الافتراضي');
        }

        // Create sample sales if we have parts and customers
        $location = Location::first();
        $user = User::first();
        
        if ($location && $user && Part::count() > 0) {
            $this->createSampleSales($location, $user);
        }
    }

    private function createSampleSales($location, $user)
    {
        $parts = Part::limit(5)->get();
        $customers = Customer::limit(3)->get();

        // Create 3 sample sales
        for ($i = 1; $i <= 3; $i++) {
            $customer = $customers->count() > 0 ? $customers->random() : null;
            
            $sale = Sale::create([
                'sale_number' => 'MAIN-' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'customer_id' => $customer?->id,
                'user_id' => $user->id,
                'location_id' => $location->id,
                'sale_type' => 'parts_only',
                'status' => 'completed',
                'payment_status' => 'paid',
                'sale_date' => now()->subDays(rand(0, 7)),
                'subtotal' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'total_amount' => 0,
                'paid_amount' => 0,
                'remaining_amount' => 0,
                'completed_at' => now()->subDays(rand(0, 7)),
            ]);

            // Add 1-3 items to each sale
            $itemCount = rand(1, 3);
            $subtotal = 0;

            for ($j = 1; $j <= $itemCount; $j++) {
                if ($parts->count() > 0) {
                    $part = $parts->random();
                    $quantity = rand(1, 2);
                    $unitPrice = $part->price;
                    $lineTotal = $quantity * $unitPrice;
                    $subtotal += $lineTotal;

                    SaleItem::create([
                        'sale_id' => $sale->id,
                        'item_type' => 'part',
                        'part_id' => $part->id,
                        'item_name' => $part->name,
                        'item_code' => $part->part_number,
                        'item_description' => $part->description,
                        'item_category' => $part->category,
                        'quantity' => $quantity,
                        'unit_price' => $unitPrice,
                        'original_price' => $part->price,
                        'discount_amount' => 0,
                        'line_total' => $lineTotal,
                        'tax_rate' => 15,
                        'tax_amount' => $lineTotal * 0.15,
                        'affects_inventory' => true,
                    ]);
                }
            }

            // Calculate totals
            $taxAmount = $subtotal * 0.15;
            $totalAmount = $subtotal + $taxAmount;

            $sale->update([
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'paid_amount' => $totalAmount,
                'remaining_amount' => 0,
            ]);
        }

        $this->command->info('تم إنشاء ' . Sale::count() . ' مبيعة تجريبية');
    }
}
