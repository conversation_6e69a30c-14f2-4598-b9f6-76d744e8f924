<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 لوحة اختبار النظام</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-card { transition: all 0.3s ease; }
        .test-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; display: inline-block; margin-left: 8px; }
        .status-pending { background-color: #fbbf24; }
        .status-success { background-color: #10b981; }
        .status-error { background-color: #ef4444; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">🧪 لوحة اختبار النظام</h1>
            <p class="text-gray-600">اختبر جميع مكونات نظام إدارة مراكز الصيانة</p>
            <div class="mt-4 p-4 bg-blue-100 rounded-lg">
                <p class="text-blue-800 font-medium">
                    🎯 الهدف الرئيسي: التأكد من عمل 
                    <a href="/technicians/schedule" class="underline font-bold">جدولة الفنيين</a>
                    بشكل مثالي
                </p>
            </div>
        </div>

        <!-- Test Categories -->
        @foreach($tests as $categoryKey => $category)
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-4 flex items-center">
                @if($categoryKey === 'basic')
                    🔧
                @elseif($categoryKey === 'schedule')
                    ⭐
                @elseif($categoryKey === 'technicians')
                    👨‍🔧
                @else
                    🛠️
                @endif
                {{ $category['name'] }}
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($category['routes'] as $route => $description)
                <div class="test-card bg-white rounded-lg shadow-md p-6 border-r-4 
                    @if($route === '/technicians/schedule') border-yellow-400 bg-yellow-50
                    @else border-blue-400 @endif">
                    
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-800">{{ $description }}</h3>
                        <span class="status-indicator status-pending" id="status-{{ md5($route) }}"></span>
                    </div>
                    
                    <p class="text-sm text-gray-600 mb-4">{{ $route }}</p>
                    
                    @if($route === '/technicians/schedule')
                    <div class="mb-4 p-3 bg-yellow-100 rounded border-r-4 border-yellow-400">
                        <p class="text-yellow-800 text-sm font-medium">
                            🌟 هذا هو الاختبار الأهم! يجب أن يعرض:
                        </p>
                        <ul class="text-yellow-700 text-xs mt-2 space-y-1">
                            <li>• عنوان "جدولة الفنيين"</li>
                            <li>• بطاقات إحصائيات</li>
                            <li>• قائمة الفنيين مع أعباء العمل</li>
                            <li>• قائمة الطلبات غير المُعينة</li>
                        </ul>
                    </div>
                    @endif
                    
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="{{ $route }}" target="_blank" 
                           class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-center text-sm font-medium transition-colors"
                           onclick="markAsTested('{{ md5($route) }}')">
                            🔗 اختبر الآن
                        </a>
                        <button onclick="markAsSuccess('{{ md5($route) }}')" 
                                class="bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded text-sm">
                            ✅
                        </button>
                        <button onclick="markAsError('{{ md5($route) }}')" 
                                class="bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded text-sm">
                            ❌
                        </button>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endforeach

        <!-- Progress Summary -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">📊 ملخص التقدم</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600" id="pending-count">8</div>
                    <div class="text-yellow-800">في الانتظار</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="success-count">0</div>
                    <div class="text-green-800">نجح</div>
                </div>
                <div class="text-center p-4 bg-red-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-600" id="error-count">0</div>
                    <div class="text-red-800">فشل</div>
                </div>
            </div>
            
            <div class="w-full bg-gray-200 rounded-full h-4">
                <div class="bg-blue-600 h-4 rounded-full transition-all duration-300" style="width: 0%" id="progress-bar"></div>
            </div>
            <p class="text-center mt-2 text-gray-600" id="progress-text">0% مكتمل</p>
        </div>

        <!-- Instructions -->
        <div class="mt-8 bg-blue-50 rounded-lg p-6 border-r-4 border-blue-400">
            <h3 class="text-lg font-bold text-blue-800 mb-3">📋 تعليمات الاختبار</h3>
            <ol class="text-blue-700 space-y-2">
                <li><strong>1.</strong> اضغط على "🔗 اختبر الآن" لفتح الرابط في تبويب جديد</li>
                <li><strong>2.</strong> تحقق من أن الصفحة تحمل بدون أخطاء</li>
                <li><strong>3.</strong> تأكد من ظهور المحتوى باللغة العربية</li>
                <li><strong>4.</strong> اضغط ✅ إذا نجح الاختبار أو ❌ إذا فشل</li>
                <li><strong>5.</strong> ركز على اختبار "جدولة الفنيين" ⭐ - هذا هو الأهم!</li>
            </ol>
        </div>

        <!-- Troubleshooting -->
        <div class="mt-6 bg-red-50 rounded-lg p-6 border-r-4 border-red-400">
            <h3 class="text-lg font-bold text-red-800 mb-3">🐛 إذا واجهت مشاكل</h3>
            <div class="text-red-700 space-y-2">
                <p><strong>خطأ 404:</strong> امسح cache بـ <code class="bg-red-100 px-2 py-1 rounded">php artisan route:clear</code></p>
                <p><strong>صفحة فارغة:</strong> تحقق من logs في <code class="bg-red-100 px-2 py-1 rounded">storage/logs/laravel.log</code></p>
                <p><strong>خطأ 500:</strong> تأكد من قاعدة البيانات بـ <code class="bg-red-100 px-2 py-1 rounded">php artisan migrate:status</code></p>
            </div>
        </div>
    </div>

    <script>
        let pendingCount = 8;
        let successCount = 0;
        let errorCount = 0;

        function updateProgress() {
            document.getElementById('pending-count').textContent = pendingCount;
            document.getElementById('success-count').textContent = successCount;
            document.getElementById('error-count').textContent = errorCount;
            
            const total = pendingCount + successCount + errorCount;
            const completed = successCount + errorCount;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
            
            document.getElementById('progress-bar').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = percentage + '% مكتمل';
            
            if (percentage === 100) {
                if (errorCount === 0) {
                    showCelebration();
                } else {
                    showWarning();
                }
            }
        }

        function markAsTested(id) {
            // Just visual feedback that the link was clicked
            const indicator = document.getElementById('status-' + id);
            indicator.style.backgroundColor = '#3b82f6'; // Blue for "testing"
        }

        function markAsSuccess(id) {
            const indicator = document.getElementById('status-' + id);
            if (indicator.classList.contains('status-pending')) {
                indicator.classList.remove('status-pending');
                indicator.classList.add('status-success');
                pendingCount--;
                successCount++;
                updateProgress();
            } else if (indicator.classList.contains('status-error')) {
                indicator.classList.remove('status-error');
                indicator.classList.add('status-success');
                errorCount--;
                successCount++;
                updateProgress();
            }
        }

        function markAsError(id) {
            const indicator = document.getElementById('status-' + id);
            if (indicator.classList.contains('status-pending')) {
                indicator.classList.remove('status-pending');
                indicator.classList.add('status-error');
                pendingCount--;
                errorCount++;
                updateProgress();
            } else if (indicator.classList.contains('status-success')) {
                indicator.classList.remove('status-success');
                indicator.classList.add('status-error');
                successCount--;
                errorCount++;
                updateProgress();
            }
        }

        function showCelebration() {
            alert('🎉 تهانينا! جميع الاختبارات نجحت!\n\nالنظام جاهز للاستخدام بالكامل.');
        }

        function showWarning() {
            alert('⚠️ بعض الاختبارات فشلت.\n\nراجع الاختبارات المُعلمة بـ ❌ وأصلح المشاكل.');
        }

        // Initialize
        updateProgress();
    </script>
</body>
</html>
