@extends('layouts.main')

@section('title', 'تعديل المخزون')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تعديل المخزون</h1>
            <p class="text-gray-600 dark:text-gray-400">تعديل كميات المخزون للمنتجات</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('inventory.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة للمخزون
            </a>
        </div>
    </div>

    <!-- Form -->
    <form action="{{ route('inventory.process-adjustment') }}" method="POST" class="space-y-6">
        @csrf

        <!-- Product Selection -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">اختيار المنتج</h3>
            
            @if($inventory)
                <!-- Pre-selected inventory item -->
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-gray-100">{{ $inventory->product->name }}</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                الموقع: {{ $inventory->location->name ?? 'غير محدد' }}
                            </p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                الكمية الحالية: <span class="font-medium">{{ $inventory->quantity }}</span>
                            </p>
                        </div>
                    </div>
                </div>
                <input type="hidden" name="inventory_id" value="{{ $inventory->id }}">
            @else
                <!-- Product and Location Selection -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="product_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المنتج *</label>
                        <select name="product_id" id="product_id" required onchange="loadInventoryData()"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            <option value="">اختر المنتج</option>
                            @foreach($products as $product)
                                <option value="{{ $product->id }}">{{ $product->name }}</option>
                            @endforeach
                        </select>
                        @error('product_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="location_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموقع *</label>
                        <select name="location_id" id="location_id" required onchange="loadInventoryData()"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            <option value="">اختر الموقع</option>
                            @foreach($locations as $location)
                                <option value="{{ $location->id }}">{{ $location->name }}</option>
                            @endforeach
                        </select>
                        @error('location_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Current Stock Display -->
                <div id="current-stock-display" class="mt-4 hidden">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            الكمية الحالية: <span id="current-quantity" class="font-medium text-gray-900 dark:text-gray-100">0</span>
                        </p>
                    </div>
                </div>
                <input type="hidden" name="inventory_id" id="inventory_id">
            @endif
        </div>

        <!-- Adjustment Details -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">تفاصيل التعديل</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="new_quantity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الكمية الجديدة *</label>
                    <input type="number" name="new_quantity" id="new_quantity" min="0" required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    @error('new_quantity')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="reason" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">سبب التعديل *</label>
                    <select name="reason" id="reason" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="">اختر السبب</option>
                        <option value="جرد دوري">جرد دوري</option>
                        <option value="تلف">تلف</option>
                        <option value="فقدان">فقدان</option>
                        <option value="إضافة مخزون">إضافة مخزون</option>
                        <option value="تصحيح خطأ">تصحيح خطأ</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                    @error('reason')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mt-6">
                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات</label>
                <textarea name="notes" id="notes" rows="3" 
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                          placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                @error('notes')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Summary -->
        <div id="adjustment-summary" class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm hidden">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">ملخص التعديل</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p class="text-sm text-gray-500 dark:text-gray-400">الكمية الحالية</p>
                    <p id="summary-current" class="text-2xl font-bold text-gray-900 dark:text-gray-100">0</p>
                </div>
                
                <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p class="text-sm text-gray-500 dark:text-gray-400">الكمية الجديدة</p>
                    <p id="summary-new" class="text-2xl font-bold text-blue-600">0</p>
                </div>
                
                <div class="text-center p-4 rounded-lg" id="summary-change-container">
                    <p class="text-sm text-gray-500 dark:text-gray-400">التغيير</p>
                    <p id="summary-change" class="text-2xl font-bold">0</p>
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-2 space-x-reverse">
            <a href="{{ route('inventory.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg">
                إلغاء
            </a>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                تأكيد التعديل
            </button>
        </div>
    </form>
</div>

<script>
let currentQuantity = {{ $inventory->quantity ?? 0 }};

function loadInventoryData() {
    const productId = document.getElementById('product_id').value;
    const locationId = document.getElementById('location_id').value;
    
    if (productId && locationId) {
        // Here you would make an AJAX call to get inventory data
        // For now, we'll simulate it
        fetch(`/api/inventory/find?product_id=${productId}&location_id=${locationId}`)
            .then(response => response.json())
            .then(data => {
                if (data.inventory) {
                    currentQuantity = data.inventory.quantity;
                    document.getElementById('current-quantity').textContent = currentQuantity;
                    document.getElementById('current-stock-display').classList.remove('hidden');
                    document.getElementById('inventory_id').value = data.inventory.id;
                } else {
                    currentQuantity = 0;
                    document.getElementById('current-quantity').textContent = '0 (جديد)';
                    document.getElementById('current-stock-display').classList.remove('hidden');
                    document.getElementById('inventory_id').value = '';
                }
                updateSummary();
            })
            .catch(error => {
                console.error('Error:', error);
            });
    }
}

function updateSummary() {
    const newQuantity = parseInt(document.getElementById('new_quantity').value) || 0;
    const change = newQuantity - currentQuantity;
    
    document.getElementById('summary-current').textContent = currentQuantity;
    document.getElementById('summary-new').textContent = newQuantity;
    document.getElementById('summary-change').textContent = change > 0 ? `+${change}` : change;
    
    const changeContainer = document.getElementById('summary-change-container');
    const changeElement = document.getElementById('summary-change');
    
    if (change > 0) {
        changeContainer.className = 'text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg';
        changeElement.className = 'text-2xl font-bold text-green-600';
    } else if (change < 0) {
        changeContainer.className = 'text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg';
        changeElement.className = 'text-2xl font-bold text-red-600';
    } else {
        changeContainer.className = 'text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg';
        changeElement.className = 'text-2xl font-bold text-gray-600';
    }
    
    document.getElementById('adjustment-summary').classList.remove('hidden');
}

document.getElementById('new_quantity').addEventListener('input', updateSummary);

// Initialize if inventory is pre-selected
@if($inventory)
    updateSummary();
@endif
</script>
@endsection
