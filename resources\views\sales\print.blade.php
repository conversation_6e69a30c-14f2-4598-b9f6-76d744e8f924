<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة فاتورة - INV-{{ $id }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: white;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }

        .invoice-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3B82F6;
            padding-bottom: 20px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #3B82F6;
            margin-bottom: 5px;
        }

        .company-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }

        .invoice-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-top: 15px;
        }

        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 40px;
        }

        .invoice-info, .customer-info {
            flex: 1;
        }

        .info-title {
            font-size: 16px;
            font-weight: bold;
            color: #3B82F6;
            margin-bottom: 10px;
            border-bottom: 1px solid #E5E7EB;
            padding-bottom: 5px;
        }

        .info-row {
            display: flex;
            margin-bottom: 5px;
        }

        .info-label {
            font-weight: bold;
            width: 100px;
            color: #666;
        }

        .info-value {
            flex: 1;
            color: #333;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 1px solid #E5E7EB;
        }

        .items-table th,
        .items-table td {
            padding: 12px 8px;
            text-align: right;
            border-bottom: 1px solid #E5E7EB;
        }

        .items-table th {
            background-color: #F9FAFB;
            font-weight: bold;
            color: #374151;
            border-bottom: 2px solid #E5E7EB;
        }

        .items-table tr:nth-child(even) {
            background-color: #F9FAFB;
        }

        .item-name {
            font-weight: bold;
            color: #333;
        }

        .item-sku {
            font-size: 12px;
            color: #666;
        }

        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 30px;
        }

        .totals-table {
            width: 300px;
            border-collapse: collapse;
        }

        .totals-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #E5E7EB;
        }

        .totals-label {
            font-weight: bold;
            color: #666;
            text-align: right;
        }

        .totals-value {
            text-align: left;
            font-weight: bold;
            color: #333;
        }

        .total-final {
            background-color: #3B82F6;
            color: white;
            font-size: 16px;
        }

        .notes-section {
            margin-bottom: 30px;
        }

        .notes-title {
            font-size: 16px;
            font-weight: bold;
            color: #3B82F6;
            margin-bottom: 10px;
        }

        .notes-content {
            background-color: #F9FAFB;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #E5E7EB;
            color: #666;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #E5E7EB;
            color: #666;
            font-size: 12px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-completed {
            background-color: #D1FAE5;
            color: #065F46;
        }

        .status-pending {
            background-color: #FEF3C7;
            color: #92400E;
        }

        .status-cancelled {
            background-color: #FEE2E2;
            color: #991B1B;
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .invoice-container {
                max-width: none;
                margin: 0;
                padding: 15px;
            }
            
            .no-print {
                display: none !important;
            }
        }

        @page {
            margin: 1cm;
            size: A4;
        }
    </style>
</head>
<body>
    <div class="invoice-container" x-data="printInvoice({{ $id }})">
        <!-- Print Button -->
        <div class="no-print" style="text-align: center; margin-bottom: 20px;">
            <button onclick="window.print()" style="background: #3B82F6; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">
                طباعة الفاتورة
            </button>
            <button onclick="window.close()" style="background: #6B7280; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; margin-right: 10px;">
                إغلاق
            </button>
        </div>

        <!-- Invoice Header -->
        <div class="invoice-header">
            <div class="company-name">مركز الطارق لصيانة الحاسوب والموبايل</div>
            <div class="company-info">
                شارع عمر المختار، رام الله، فلسطين<br>
                هاتف: +970-2-123-4567 | البريد الإلكتروني: <EMAIL>
            </div>
            <div class="invoice-title">فاتورة مبيعات</div>
        </div>

        <!-- Invoice Details -->
        <div class="invoice-details">
            <div class="invoice-info">
                <div class="info-title">معلومات الفاتورة</div>
                <div class="info-row">
                    <span class="info-label">رقم الفاتورة:</span>
                    <span class="info-value" x-text="invoice.number"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">التاريخ:</span>
                    <span class="info-value" x-text="invoice.date"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">طريقة الدفع:</span>
                    <span class="info-value" x-text="getPaymentMethodText(invoice.payment_method)"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">
                        <span class="status-badge" 
                              :class="{
                                  'status-completed': invoice.status === 'completed',
                                  'status-pending': invoice.status === 'pending',
                                  'status-cancelled': invoice.status === 'cancelled'
                              }"
                              x-text="getStatusText(invoice.status)">
                        </span>
                    </span>
                </div>
            </div>

            <div class="customer-info">
                <div class="info-title">معلومات العميل</div>
                <div class="info-row">
                    <span class="info-label">الاسم:</span>
                    <span class="info-value" x-text="invoice.customer.name"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value" x-text="invoice.customer.phone"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">البريد:</span>
                    <span class="info-value" x-text="invoice.customer.email"></span>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 50px;">#</th>
                    <th>المنتج</th>
                    <th style="width: 100px;">السعر</th>
                    <th style="width: 80px;">الكمية</th>
                    <th style="width: 100px;">الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                <template x-for="(item, index) in invoice.items" :key="index">
                    <tr>
                        <td x-text="index + 1"></td>
                        <td>
                            <div class="item-name" x-text="item.name"></div>
                            <div class="item-sku" x-text="item.sku"></div>
                        </td>
                        <td>₪<span x-text="item.price.toFixed(2)"></span></td>
                        <td x-text="item.quantity"></td>
                        <td>₪<span x-text="item.total.toFixed(2)"></span></td>
                    </tr>
                </template>
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="totals-label">المجموع الفرعي:</td>
                    <td class="totals-value">₪<span x-text="invoice.subtotal.toFixed(2)"></span></td>
                </tr>
                <tr x-show="invoice.discount > 0">
                    <td class="totals-label">الخصم:</td>
                    <td class="totals-value" style="color: #DC2626;">-₪<span x-text="invoice.discount.toFixed(2)"></span></td>
                </tr>
                <tr>
                    <td class="totals-label">الضريبة (16%):</td>
                    <td class="totals-value">₪<span x-text="invoice.tax.toFixed(2)"></span></td>
                </tr>
                <tr class="total-final">
                    <td class="totals-label">الإجمالي:</td>
                    <td class="totals-value">₪<span x-text="invoice.total.toFixed(2)"></span></td>
                </tr>
            </table>
        </div>

        <!-- Notes -->
        <div class="notes-section" x-show="invoice.notes">
            <div class="notes-title">ملاحظات</div>
            <div class="notes-content" x-text="invoice.notes"></div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام الطارق لإدارة مراكز الصيانة</p>
            <p style="margin-top: 10px; font-size: 10px;">
                تاريخ الطباعة: <span x-text="new Date().toLocaleDateString('ar-EG')"></span>
            </p>
        </div>
    </div>

    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        function printInvoice(invoiceId) {
            return {
                invoice: {
                    id: invoiceId,
                    number: 'INV-001',
                    date: '2024-07-09',
                    status: 'completed',
                    payment_method: 'cash',
                    customer: {
                        name: 'أحمد محمد علي',
                        phone: '0599123456',
                        email: '<EMAIL>'
                    },
                    items: [
                        {
                            name: 'كابل USB-C',
                            sku: 'CAB-001',
                            price: 25.00,
                            quantity: 2,
                            total: 50.00
                        },
                        {
                            name: 'شاحن سريع 20W',
                            sku: 'CHR-001',
                            price: 85.00,
                            quantity: 1,
                            total: 85.00
                        }
                    ],
                    subtotal: 135.00,
                    discount: 10.00,
                    tax: 20.00,
                    total: 145.00,
                    notes: 'فاتورة تجريبية لعرض النظام'
                },

                init() {
                    // Load invoice data
                    this.loadInvoiceData();
                    
                    // Auto print after 1 second
                    setTimeout(() => {
                        window.print();
                    }, 1000);
                },

                loadInvoiceData() {
                    // In real implementation, fetch data from API
                    console.log('Loading invoice data for print:', this.invoice.id);
                },

                getPaymentMethodText(method) {
                    const methods = {
                        'cash': 'نقدي',
                        'card': 'بطاقة',
                        'credit': 'آجل'
                    };
                    return methods[method] || method;
                },

                getStatusText(status) {
                    const statuses = {
                        'completed': 'مكتملة',
                        'pending': 'معلقة',
                        'cancelled': 'ملغية'
                    };
                    return statuses[status] || status;
                }
            }
        }
    </script>
</body>
</html>
