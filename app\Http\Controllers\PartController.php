<?php

namespace App\Http\Controllers;

use App\Models\Part;
use App\Models\Category;
use App\Models\Supplier;
use App\Models\StockMovement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class PartController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // TODO: Add permission middleware when role system is implemented
        // $this->middleware('permission:parts.view')->only(['index', 'show']);
        // $this->middleware('permission:parts.create')->only(['create', 'store']);
        // $this->middleware('permission:parts.edit')->only(['edit', 'update']);
        // $this->middleware('permission:parts.delete')->only(['destroy']);
    }

    public function index(Request $request)
    {
        $query = Part::with(['category', 'supplier']);

        // Apply filters
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('part_number', 'like', '%' . $request->search . '%')
                  ->orWhere('brand', 'like', '%' . $request->search . '%')
                  ->orWhere('model', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        if ($request->filled('condition')) {
            $query->where('condition', $request->condition);
        }

        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'in_stock':
                    $query->where('stock_quantity', '>', 0);
                    break;
                case 'low_stock':
                    $query->whereColumn('stock_quantity', '<=', 'reorder_point');
                    break;
                case 'out_of_stock':
                    $query->where('stock_quantity', '<=', 0);
                    break;
            }
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $parts = $query->paginate(15)->appends($request->query());

        // Get filter options
        $categories = Category::orderBy('name')->get();
        $suppliers = Supplier::orderBy('name')->get();
        $conditions = ['new', 'refurbished', 'used', 'damaged'];

        // Statistics
        $stats = [
            'total_parts' => Part::count(),
            'active_parts' => Part::where('is_active', true)->count(),
            'low_stock_parts' => Part::lowStock()->count(),
            'out_of_stock_parts' => Part::outOfStock()->count(),
            'total_value' => Part::sum(DB::raw('stock_quantity * cost_price')),
        ];

        return view('parts.index', compact('parts', 'categories', 'suppliers', 'conditions', 'stats'));
    }

    public function create()
    {
        $categories = Category::orderBy('name')->get();
        $suppliers = Supplier::orderBy('name')->get();
        $conditions = ['new', 'refurbished', 'used', 'damaged'];
        $partTypes = ['original', 'compatible', 'aftermarket', 'refurbished'];
        $unitsOfMeasure = ['piece', 'meter', 'kilogram', 'liter', 'box', 'set'];

        return view('parts.create', compact('categories', 'suppliers', 'conditions', 'partTypes', 'unitsOfMeasure'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:categories,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'brand' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'part_type' => 'nullable|string|max:255',
            'condition' => 'required|in:new,refurbished,used,damaged',
            'location' => 'nullable|string|max:255',
            'shelf_location' => 'nullable|string|max:255',
            'barcode' => 'nullable|string|max:255|unique:parts',
            'sku' => 'nullable|string|max:255|unique:parts',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'markup_percentage' => 'nullable|numeric|min:0|max:1000',
            'stock_quantity' => 'required|integer|min:0',
            'minimum_stock' => 'nullable|integer|min:0',
            'maximum_stock' => 'nullable|integer|min:0',
            'reorder_point' => 'nullable|integer|min:0',
            'reorder_quantity' => 'nullable|integer|min:0',
            'unit_of_measure' => 'nullable|string|max:50',
            'weight' => 'nullable|numeric|min:0',
            'warranty_period' => 'nullable|integer|min:0',
            'warranty_type' => 'nullable|string|max:255',
            'supplier_part_number' => 'nullable|string|max:255',
            'manufacturer_part_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
            'is_serialized' => 'boolean',
            'is_returnable' => 'boolean',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'compatibility' => 'nullable|array',
            'dimensions' => 'nullable|array',
            'specifications' => 'nullable|array',
            'installation_instructions' => 'nullable|array',
        ]);

        $data = $request->except(['images']);
        
        // Handle images upload
        if ($request->hasFile('images')) {
            $images = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('parts', 'public');
                $images[] = $path;
            }
            $data['images'] = $images;
        }

        // Calculate markup percentage if not provided
        if (!$request->filled('markup_percentage') && $request->cost_price > 0) {
            $data['markup_percentage'] = (($request->selling_price - $request->cost_price) / $request->cost_price) * 100;
        }

        $part = Part::create($data);

        // Create initial stock movement
        if ($part->stock_quantity > 0) {
            StockMovement::create([
                'part_id' => $part->id,
                'type' => 'initial_stock',
                'quantity' => $part->stock_quantity,
                'reference_type' => 'initial',
                'reference_id' => $part->id,
                'notes' => 'مخزون أولي',
                'created_by' => auth()->id(),
            ]);
        }

        return redirect()->route('parts.index')
            ->with('success', 'تم إضافة قطعة الغيار بنجاح.');
    }

    public function show(Part $part)
    {
        $part->load(['category', 'supplier', 'repairs', 'stockMovements' => function ($query) {
            $query->latest()->limit(10);
        }]);

        // Get recent stock movements
        $recentMovements = $part->stockMovements()
            ->with(['createdBy'])
            ->latest()
            ->limit(10)
            ->get();

        // Get compatible repairs
        $compatibleRepairs = $part->repairs()
            ->with(['customer', 'technician'])
            ->latest()
            ->limit(5)
            ->get();

        // Calculate statistics
        $stats = [
            'total_used' => $part->repairs()->sum('repair_parts.quantity'),
            'total_movements' => $part->stockMovements()->count(),
            'average_monthly_usage' => $this->calculateAverageMonthlyUsage($part),
            'estimated_reorder_date' => $this->estimateReorderDate($part),
        ];

        return view('parts.show', compact('part', 'recentMovements', 'compatibleRepairs', 'stats'));
    }

    public function edit(Part $part)
    {
        $categories = Category::orderBy('name')->get();
        $suppliers = Supplier::orderBy('name')->get();
        $conditions = ['new', 'refurbished', 'used', 'damaged'];
        $partTypes = ['original', 'compatible', 'aftermarket', 'refurbished'];
        $unitsOfMeasure = ['piece', 'meter', 'kilogram', 'liter', 'box', 'set'];

        return view('parts.edit', compact('part', 'categories', 'suppliers', 'conditions', 'partTypes', 'unitsOfMeasure'));
    }

    public function update(Request $request, Part $part)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:categories,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'brand' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'part_type' => 'nullable|string|max:255',
            'condition' => 'required|in:new,refurbished,used,damaged',
            'location' => 'nullable|string|max:255',
            'shelf_location' => 'nullable|string|max:255',
            'barcode' => 'nullable|string|max:255|unique:parts,barcode,' . $part->id,
            'sku' => 'nullable|string|max:255|unique:parts,sku,' . $part->id,
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'markup_percentage' => 'nullable|numeric|min:0|max:1000',
            'stock_quantity' => 'required|integer|min:0',
            'minimum_stock' => 'nullable|integer|min:0',
            'maximum_stock' => 'nullable|integer|min:0',
            'reorder_point' => 'nullable|integer|min:0',
            'reorder_quantity' => 'nullable|integer|min:0',
            'unit_of_measure' => 'nullable|string|max:50',
            'weight' => 'nullable|numeric|min:0',
            'warranty_period' => 'nullable|integer|min:0',
            'warranty_type' => 'nullable|string|max:255',
            'supplier_part_number' => 'nullable|string|max:255',
            'manufacturer_part_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
            'is_serialized' => 'boolean',
            'is_returnable' => 'boolean',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'compatibility' => 'nullable|array',
            'dimensions' => 'nullable|array',
            'specifications' => 'nullable|array',
            'installation_instructions' => 'nullable|array',
        ]);

        $data = $request->except(['images']);
        
        // Handle images upload
        if ($request->hasFile('images')) {
            // Delete old images
            if ($part->images) {
                foreach ($part->images as $oldImage) {
                    Storage::disk('public')->delete($oldImage);
                }
            }
            
            $images = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('parts', 'public');
                $images[] = $path;
            }
            $data['images'] = $images;
        }

        // Calculate markup percentage if not provided
        if (!$request->filled('markup_percentage') && $request->cost_price > 0) {
            $data['markup_percentage'] = (($request->selling_price - $request->cost_price) / $request->cost_price) * 100;
        }

        // Track stock changes
        $oldQuantity = $part->stock_quantity;
        $newQuantity = $request->stock_quantity;
        
        $part->update($data);

        // Create stock movement if quantity changed
        if ($oldQuantity != $newQuantity) {
            $difference = $newQuantity - $oldQuantity;
            StockMovement::create([
                'part_id' => $part->id,
                'type' => $difference > 0 ? 'adjustment_in' : 'adjustment_out',
                'quantity' => abs($difference),
                'reference_type' => 'manual_adjustment',
                'reference_id' => $part->id,
                'notes' => 'تعديل يدوي للمخزون',
                'created_by' => auth()->id(),
            ]);
        }

        return redirect()->route('parts.show', $part)
            ->with('success', 'تم تحديث قطعة الغيار بنجاح.');
    }

    public function destroy(Part $part)
    {
        // Check if part is used in any repairs
        if ($part->repairs()->count() > 0) {
            return redirect()->route('parts.index')
                ->with('error', 'لا يمكن حذف قطعة الغيار لأنها مستخدمة في طلبات صيانة.');
        }

        // Delete images
        if ($part->images) {
            foreach ($part->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $part->delete();

        return redirect()->route('parts.index')
            ->with('success', 'تم حذف قطعة الغيار بنجاح.');
    }

    private function calculateAverageMonthlyUsage(Part $part)
    {
        $totalUsed = $part->repairs()->sum('repair_parts.quantity');
        $firstUsage = $part->repairs()->oldest()->first();
        
        if (!$firstUsage || $totalUsed == 0) {
            return 0;
        }
        
        $monthsSinceFirstUsage = $firstUsage->created_at->diffInMonths(now()) ?: 1;
        return round($totalUsed / $monthsSinceFirstUsage, 2);
    }

    private function estimateReorderDate(Part $part)
    {
        $averageUsage = $this->calculateAverageMonthlyUsage($part);
        
        if ($averageUsage <= 0 || $part->stock_quantity <= 0) {
            return null;
        }
        
        $daysUntilReorder = ($part->stock_quantity - $part->reorder_point) / ($averageUsage / 30);
        
        return $daysUntilReorder > 0 ? now()->addDays($daysUntilReorder) : now();
    }

    public function updateStock(Request $request, Part $part)
    {
        $request->validate([
            'type' => 'required|in:in,out,adjustment',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
            'reference_type' => 'nullable|string|max:255',
            'reference_id' => 'nullable|integer',
        ]);

        $type = $request->type;
        $quantity = $request->quantity;
        $notes = $request->notes ?? '';

        // Validate stock out operation
        if ($type === 'out' && $part->stock_quantity < $quantity) {
            return response()->json([
                'success' => false,
                'message' => 'الكمية المطلوبة أكبر من المتوفر في المخزون.'
            ], 400);
        }

        // Update stock quantity
        $newQuantity = $part->stock_quantity;
        switch ($type) {
            case 'in':
                $newQuantity += $quantity;
                $movementType = 'stock_in';
                break;
            case 'out':
                $newQuantity -= $quantity;
                $movementType = 'stock_out';
                break;
            case 'adjustment':
                $newQuantity = $quantity;
                $movementType = $quantity > $part->stock_quantity ? 'adjustment_in' : 'adjustment_out';
                $quantity = abs($quantity - $part->stock_quantity);
                break;
        }

        $part->update(['stock_quantity' => $newQuantity]);

        // Create stock movement record
        StockMovement::create([
            'part_id' => $part->id,
            'type' => $movementType,
            'quantity' => $quantity,
            'reference_type' => $request->reference_type ?? 'manual',
            'reference_id' => $request->reference_id,
            'notes' => $notes,
            'created_by' => auth()->id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث المخزون بنجاح.',
            'new_quantity' => $newQuantity,
            'stock_status' => $part->stock_status
        ]);
    }

    public function stockMovements(Part $part)
    {
        $movements = $part->stockMovements()
            ->with(['createdBy'])
            ->latest()
            ->paginate(20);

        return view('parts.stock-movements', compact('part', 'movements'));
    }

    public function lowStock()
    {
        $parts = Part::lowStock()
            ->with(['category', 'supplier'])
            ->orderBy('stock_quantity', 'asc')
            ->paginate(20);

        return view('parts.low-stock', compact('parts'));
    }

    public function outOfStock()
    {
        $parts = Part::outOfStock()
            ->with(['category', 'supplier'])
            ->orderBy('updated_at', 'desc')
            ->paginate(20);

        return view('parts.out-of-stock', compact('parts'));
    }

    public function inventory()
    {
        $totalParts = Part::count();
        $activeParts = Part::where('is_active', true)->count();
        $lowStockParts = Part::lowStock()->count();
        $outOfStockParts = Part::outOfStock()->count();
        $totalValue = Part::sum(DB::raw('stock_quantity * cost_price'));
        $totalSellingValue = Part::sum(DB::raw('stock_quantity * selling_price'));

        // Top categories by value
        $topCategories = Category::withSum(['parts' => function ($query) {
            $query->selectRaw('SUM(stock_quantity * cost_price)');
        }], 'stock_quantity')
            ->orderByDesc('parts_sum_stock_quantity')
            ->limit(10)
            ->get();

        // Recent movements - simplified for parts
        $recentMovements = collect(); // Empty collection for now
        // Note: Parts have their own stock tracking separate from inventory system

        // Parts needing reorder
        $reorderParts = Part::whereColumn('stock_quantity', '<=', 'reorder_point')
            ->where('is_active', true)
            ->with(['category', 'supplier'])
            ->orderBy('stock_quantity', 'asc')
            ->limit(10)
            ->get();

        return view('parts.inventory', compact(
            'totalParts',
            'activeParts',
            'lowStockParts',
            'outOfStockParts',
            'totalValue',
            'totalSellingValue',
            'topCategories',
            'recentMovements',
            'reorderParts'
        ));
    }

    public function generateBarcode(Part $part)
    {
        if (!$part->barcode) {
            $part->update(['barcode' => 'PART-' . str_pad($part->id, 8, '0', STR_PAD_LEFT)]);
        }

        return response()->json([
            'success' => true,
            'barcode' => $part->barcode
        ]);
    }

    public function printLabels(Request $request)
    {
        $request->validate([
            'part_ids' => 'required|array',
            'part_ids.*' => 'exists:parts,id'
        ]);

        $parts = Part::whereIn('id', $request->part_ids)->get();

        return view('parts.print-labels', compact('parts'));
    }

    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'part_ids' => 'required|array',
            'part_ids.*' => 'exists:parts,id',
            'action' => 'required|in:activate,deactivate,update_supplier,update_category',
            'supplier_id' => 'required_if:action,update_supplier|exists:suppliers,id',
            'category_id' => 'required_if:action,update_category|exists:categories,id',
        ]);

        $parts = Part::whereIn('id', $request->part_ids);
        $count = $parts->count();

        switch ($request->action) {
            case 'activate':
                $parts->update(['is_active' => true]);
                $message = "تم تفعيل {$count} قطعة غيار.";
                break;
            case 'deactivate':
                $parts->update(['is_active' => false]);
                $message = "تم إلغاء تفعيل {$count} قطعة غيار.";
                break;
            case 'update_supplier':
                $parts->update(['supplier_id' => $request->supplier_id]);
                $message = "تم تحديث المورد لـ {$count} قطعة غيار.";
                break;
            case 'update_category':
                $parts->update(['category_id' => $request->category_id]);
                $message = "تم تحديث الفئة لـ {$count} قطعة غيار.";
                break;
        }

        return redirect()->route('parts.index')
            ->with('success', $message);
    }

    public function export(Request $request)
    {
        $query = Part::with(['category', 'supplier']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('part_number', 'like', '%' . $request->search . '%')
                  ->orWhere('brand', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        $parts = $query->get();

        return view('parts.export', compact('parts'));
    }
}
