-- إعداد النظام المحاسبي المتكامل
-- تشغيل هذا الملف بعد إنشاء الجداول الأساسية

-- إنشاء الجداول المحاسبية
CREATE TABLE IF NOT EXISTS `accounts` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `account_code` varchar(10) NOT NULL COMMENT 'رقم الحساب',
  `account_name` varchar(255) NOT NULL COMMENT 'اسم الحساب',
  `account_name_en` varchar(255) DEFAULT NULL COMMENT 'اسم الحساب بالإنجليزية',
  `parent_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'الحساب الأب',
  `account_type` enum('asset','liability','equity','revenue','expense') NOT NULL COMMENT 'نوع الحساب',
  `account_category` varchar(50) DEFAULT NULL COMMENT 'فئة الحساب',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط/غير نشط',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'حساب نظام',
  `balance_type` enum('debit','credit') NOT NULL COMMENT 'طبيعة الرصيد',
  `opening_balance` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'الرصيد الافتتاحي',
  `current_balance` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'الرصيد الحالي',
  `description` text DEFAULT NULL COMMENT 'وصف الحساب',
  `settings` json DEFAULT NULL COMMENT 'إعدادات إضافية',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `accounts_account_code_unique` (`account_code`),
  KEY `accounts_account_type_is_active_index` (`account_type`,`is_active`),
  KEY `accounts_parent_id_index` (`parent_id`),
  KEY `accounts_account_code_index` (`account_code`),
  CONSTRAINT `accounts_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `accounts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `journal_entries` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `entry_number` varchar(20) NOT NULL COMMENT 'رقم القيد',
  `entry_date` date NOT NULL COMMENT 'تاريخ القيد',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع',
  `reference_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'معرف المرجع',
  `description` text NOT NULL COMMENT 'وصف القيد',
  `total_debit` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'إجمالي المدين',
  `total_credit` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'إجمالي الدائن',
  `status` enum('draft','posted','reversed') NOT NULL DEFAULT 'draft' COMMENT 'حالة القيد',
  `created_by` bigint(20) UNSIGNED NOT NULL COMMENT 'المستخدم المنشئ',
  `posted_by` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'المستخدم المرحل',
  `posted_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الترحيل',
  `reversed_by` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'المستخدم المعكس',
  `reversed_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ العكس',
  `reversal_reason` text DEFAULT NULL COMMENT 'سبب العكس',
  `metadata` json DEFAULT NULL COMMENT 'بيانات إضافية',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `journal_entries_entry_number_unique` (`entry_number`),
  KEY `journal_entries_entry_date_status_index` (`entry_date`,`status`),
  KEY `journal_entries_reference_type_reference_id_index` (`reference_type`,`reference_id`),
  KEY `journal_entries_status_index` (`status`),
  KEY `journal_entries_created_by_index` (`created_by`),
  CONSTRAINT `journal_entries_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `journal_entries_posted_by_foreign` FOREIGN KEY (`posted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `journal_entries_reversed_by_foreign` FOREIGN KEY (`reversed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `account_transactions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `journal_entry_id` bigint(20) UNSIGNED NOT NULL COMMENT 'معرف القيد',
  `account_id` bigint(20) UNSIGNED NOT NULL COMMENT 'معرف الحساب',
  `debit_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'المبلغ المدين',
  `credit_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'المبلغ الدائن',
  `description` text DEFAULT NULL COMMENT 'وصف العملية',
  `reference_number` varchar(50) DEFAULT NULL COMMENT 'رقم المرجع',
  `metadata` json DEFAULT NULL COMMENT 'بيانات إضافية',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_transactions_journal_entry_id_index` (`journal_entry_id`),
  KEY `account_transactions_account_id_index` (`account_id`),
  KEY `account_transactions_journal_entry_id_account_id_index` (`journal_entry_id`,`account_id`),
  CONSTRAINT `account_transactions_journal_entry_id_foreign` FOREIGN KEY (`journal_entry_id`) REFERENCES `journal_entries` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_transactions_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `receivables` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `customer_id` bigint(20) UNSIGNED NOT NULL COMMENT 'معرف العميل',
  `sale_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'معرف المبيعة',
  `repair_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'معرف الإصلاح',
  `invoice_number` varchar(50) NOT NULL COMMENT 'رقم الفاتورة',
  `invoice_date` date NOT NULL COMMENT 'تاريخ الفاتورة',
  `due_date` date NOT NULL COMMENT 'تاريخ الاستحقاق',
  `original_amount` decimal(15,2) NOT NULL COMMENT 'المبلغ الأصلي',
  `paid_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'المبلغ المدفوع',
  `remaining_amount` decimal(15,2) NOT NULL COMMENT 'المبلغ المتبقي',
  `status` enum('pending','partial','paid','overdue','cancelled') NOT NULL DEFAULT 'pending' COMMENT 'الحالة',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `payment_history` json DEFAULT NULL COMMENT 'تاريخ المدفوعات',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `receivables_customer_id_status_index` (`customer_id`,`status`),
  KEY `receivables_due_date_status_index` (`due_date`,`status`),
  KEY `receivables_invoice_number_index` (`invoice_number`),
  KEY `receivables_sale_id_index` (`sale_id`),
  KEY `receivables_repair_id_index` (`repair_id`),
  CONSTRAINT `receivables_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `receivables_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE SET NULL,
  CONSTRAINT `receivables_repair_id_foreign` FOREIGN KEY (`repair_id`) REFERENCES `repairs` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `payables` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `supplier_id` bigint(20) UNSIGNED NOT NULL COMMENT 'معرف المورد',
  `purchase_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT 'معرف المشتريات',
  `invoice_number` varchar(50) NOT NULL COMMENT 'رقم فاتورة المورد',
  `our_reference` varchar(50) DEFAULT NULL COMMENT 'مرجعنا',
  `invoice_date` date NOT NULL COMMENT 'تاريخ الفاتورة',
  `due_date` date NOT NULL COMMENT 'تاريخ الاستحقاق',
  `original_amount` decimal(15,2) NOT NULL COMMENT 'المبلغ الأصلي',
  `paid_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT 'المبلغ المدفوع',
  `remaining_amount` decimal(15,2) NOT NULL COMMENT 'المبلغ المتبقي',
  `status` enum('pending','partial','paid','overdue','cancelled') NOT NULL DEFAULT 'pending' COMMENT 'الحالة',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `payment_history` json DEFAULT NULL COMMENT 'تاريخ المدفوعات',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `payables_supplier_id_status_index` (`supplier_id`,`status`),
  KEY `payables_due_date_status_index` (`due_date`,`status`),
  KEY `payables_invoice_number_index` (`invoice_number`),
  KEY `payables_purchase_id_index` (`purchase_id`),
  CONSTRAINT `payables_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج دليل الحسابات الأساسي
INSERT IGNORE INTO `accounts` (`id`, `account_code`, `account_name`, `account_name_en`, `parent_id`, `account_type`, `account_category`, `is_active`, `is_system`, `balance_type`, `opening_balance`, `current_balance`, `description`, `created_at`, `updated_at`) VALUES
-- الأصول الرئيسية
(1, '10000', 'الأصول', 'Assets', NULL, 'asset', NULL, 1, 1, 'debit', 0.00, 0.00, 'مجموعة الأصول الرئيسية', NOW(), NOW()),
(2, '11000', 'الأصول المتداولة', 'Current Assets', 1, 'asset', 'current_asset', 1, 1, 'debit', 0.00, 0.00, 'الأصول المتداولة', NOW(), NOW()),
(3, '11100', 'النقدية والبنوك', 'Cash and Banks', 2, 'asset', 'cash', 1, 1, 'debit', 0.00, 0.00, 'النقدية والحسابات البنكية', NOW(), NOW()),
(4, '11101', 'الصندوق', 'Cash', 3, 'asset', 'cash', 1, 1, 'debit', 10000.00, 10000.00, 'النقدية في الصندوق', NOW(), NOW()),
(5, '11102', 'البنك الأهلي', 'National Bank', 3, 'asset', 'bank', 1, 1, 'debit', 50000.00, 50000.00, 'حساب البنك الأهلي', NOW(), NOW()),
(6, '11103', 'البنك الراجحي', 'Al Rajhi Bank', 3, 'asset', 'bank', 1, 1, 'debit', 30000.00, 30000.00, 'حساب البنك الراجحي', NOW(), NOW()),

-- الذمم المدينة
(7, '11200', 'الذمم المدينة', 'Accounts Receivable', 2, 'asset', 'receivable', 1, 1, 'debit', 0.00, 0.00, 'الذمم المدينة', NOW(), NOW()),
(8, '11201', 'ذمم العملاء', 'Customer Receivables', 7, 'asset', 'receivable', 1, 1, 'debit', 0.00, 0.00, 'ذمم العملاء', NOW(), NOW()),
(9, '11202', 'أوراق القبض', 'Notes Receivable', 7, 'asset', 'receivable', 1, 1, 'debit', 0.00, 0.00, 'أوراق القبض', NOW(), NOW()),

-- المخزون
(10, '11300', 'المخزون', 'Inventory', 2, 'asset', 'inventory', 1, 1, 'debit', 0.00, 0.00, 'المخزون', NOW(), NOW()),
(11, '11301', 'مخزون قطع الغيار', 'Parts Inventory', 10, 'asset', 'inventory', 1, 1, 'debit', 25000.00, 25000.00, 'مخزون قطع الغيار', NOW(), NOW()),
(12, '11302', 'مخزون الإكسسوارات', 'Accessories Inventory', 10, 'asset', 'inventory', 1, 1, 'debit', 15000.00, 15000.00, 'مخزون الإكسسوارات', NOW(), NOW()),

-- الأصول الثابتة
(13, '12000', 'الأصول الثابتة', 'Fixed Assets', 1, 'asset', 'fixed_asset', 1, 1, 'debit', 0.00, 0.00, 'الأصول الثابتة', NOW(), NOW()),
(14, '12100', 'المعدات والأجهزة', 'Equipment', 13, 'asset', 'fixed_asset', 1, 1, 'debit', 80000.00, 80000.00, 'المعدات والأجهزة', NOW(), NOW()),
(15, '12200', 'الأثاث والمفروشات', 'Furniture', 13, 'asset', 'fixed_asset', 1, 1, 'debit', 20000.00, 20000.00, 'الأثاث والمفروشات', NOW(), NOW()),
(16, '12900', 'مجمع الاستهلاك', 'Accumulated Depreciation', 13, 'asset', 'contra_asset', 1, 1, 'credit', 15000.00, 15000.00, 'مجمع الاستهلاك', NOW(), NOW()),

-- الخصوم
(17, '20000', 'الخصوم', 'Liabilities', NULL, 'liability', NULL, 1, 1, 'credit', 0.00, 0.00, 'مجموعة الخصوم الرئيسية', NOW(), NOW()),
(18, '21000', 'الخصوم المتداولة', 'Current Liabilities', 17, 'liability', 'current_liability', 1, 1, 'credit', 0.00, 0.00, 'الخصوم المتداولة', NOW(), NOW()),
(19, '21100', 'الذمم الدائنة', 'Accounts Payable', 18, 'liability', 'payable', 1, 1, 'credit', 0.00, 0.00, 'الذمم الدائنة', NOW(), NOW()),
(20, '21101', 'ذمم الموردين', 'Supplier Payables', 19, 'liability', 'payable', 1, 1, 'credit', 0.00, 0.00, 'ذمم الموردين', NOW(), NOW()),
(21, '21200', 'المصروفات المستحقة', 'Accrued Expenses', 18, 'liability', 'accrued', 1, 1, 'credit', 0.00, 0.00, 'المصروفات المستحقة', NOW(), NOW()),
(22, '21201', 'رواتب مستحقة', 'Accrued Salaries', 21, 'liability', 'accrued', 1, 1, 'credit', 0.00, 0.00, 'رواتب مستحقة', NOW(), NOW()),
(23, '21202', 'إيجار مستحق', 'Accrued Rent', 21, 'liability', 'accrued', 1, 1, 'credit', 0.00, 0.00, 'إيجار مستحق', NOW(), NOW()),
(24, '21300', 'ضريبة القيمة المضافة', 'VAT Payable', 18, 'liability', 'tax', 1, 1, 'credit', 0.00, 0.00, 'ضريبة القيمة المضافة', NOW(), NOW()),

-- حقوق الملكية
(25, '30000', 'حقوق الملكية', 'Equity', NULL, 'equity', NULL, 1, 1, 'credit', 0.00, 0.00, 'مجموعة حقوق الملكية', NOW(), NOW()),
(26, '31000', 'رأس المال', 'Capital', 25, 'equity', 'capital', 1, 1, 'credit', 200000.00, 200000.00, 'رأس المال', NOW(), NOW()),
(27, '32000', 'الأرباح المحتجزة', 'Retained Earnings', 25, 'equity', 'retained_earnings', 1, 1, 'credit', 0.00, 0.00, 'الأرباح المحتجزة', NOW(), NOW()),
(28, '33000', 'أرباح العام الجاري', 'Current Year Earnings', 25, 'equity', 'current_earnings', 1, 1, 'credit', 0.00, 0.00, 'أرباح العام الجاري', NOW(), NOW()),

-- الإيرادات
(29, '40000', 'الإيرادات', 'Revenues', NULL, 'revenue', NULL, 1, 1, 'credit', 0.00, 0.00, 'مجموعة الإيرادات', NOW(), NOW()),
(30, '41000', 'إيرادات المبيعات', 'Sales Revenue', 29, 'revenue', 'sales', 1, 1, 'credit', 0.00, 0.00, 'إيرادات المبيعات', NOW(), NOW()),
(31, '41100', 'مبيعات قطع الغيار', 'Parts Sales', 30, 'revenue', 'sales', 1, 1, 'credit', 0.00, 0.00, 'مبيعات قطع الغيار', NOW(), NOW()),
(32, '41200', 'مبيعات الإكسسوارات', 'Accessories Sales', 30, 'revenue', 'sales', 1, 1, 'credit', 0.00, 0.00, 'مبيعات الإكسسوارات', NOW(), NOW()),
(33, '42000', 'إيرادات الخدمات', 'Service Revenue', 29, 'revenue', 'service', 1, 1, 'credit', 0.00, 0.00, 'إيرادات الخدمات', NOW(), NOW()),
(34, '42100', 'خدمات الإصلاح', 'Repair Services', 33, 'revenue', 'service', 1, 1, 'credit', 0.00, 0.00, 'خدمات الإصلاح', NOW(), NOW()),
(35, '42200', 'خدمات الصيانة', 'Maintenance Services', 33, 'revenue', 'service', 1, 1, 'credit', 0.00, 0.00, 'خدمات الصيانة', NOW(), NOW()),

-- المصروفات
(36, '50000', 'المصروفات', 'Expenses', NULL, 'expense', NULL, 1, 1, 'debit', 0.00, 0.00, 'مجموعة المصروفات', NOW(), NOW()),
(37, '51000', 'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 36, 'expense', 'cogs', 1, 1, 'debit', 0.00, 0.00, 'تكلفة البضاعة المباعة', NOW(), NOW()),
(38, '52000', 'مصروفات التشغيل', 'Operating Expenses', 36, 'expense', 'operating', 1, 1, 'debit', 0.00, 0.00, 'مصروفات التشغيل', NOW(), NOW()),
(39, '52100', 'الرواتب والأجور', 'Salaries and Wages', 38, 'expense', 'operating', 1, 1, 'debit', 0.00, 0.00, 'الرواتب والأجور', NOW(), NOW()),
(40, '52200', 'الإيجار', 'Rent', 38, 'expense', 'operating', 1, 1, 'debit', 0.00, 0.00, 'الإيجار', NOW(), NOW()),
(41, '52300', 'الكهرباء والماء', 'Utilities', 38, 'expense', 'operating', 1, 1, 'debit', 0.00, 0.00, 'الكهرباء والماء', NOW(), NOW()),
(42, '52400', 'الاتصالات', 'Communications', 38, 'expense', 'operating', 1, 1, 'debit', 0.00, 0.00, 'الاتصالات', NOW(), NOW());

-- تحديث تسلسل الجداول
ALTER TABLE `accounts` AUTO_INCREMENT = 43;
ALTER TABLE `journal_entries` AUTO_INCREMENT = 1;
ALTER TABLE `account_transactions` AUTO_INCREMENT = 1;
ALTER TABLE `receivables` AUTO_INCREMENT = 1;
ALTER TABLE `payables` AUTO_INCREMENT = 1;
