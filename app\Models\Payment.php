<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'contact_id',
        'reference_id',
        'reference_type',
        'payment_number',
        'payment_date',
        'amount',
        'payment_method',
        'type',
        'notes'
    ];

    protected $casts = [
        'payment_date' => 'date',
        'amount' => 'decimal:2'
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع جهة الاتصال
     */
    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    /**
     * العلاقة مع المرجع (polymorphic)
     */
    public function reference()
    {
        return $this->morphTo();
    }

    /**
     * الحصول على نوع الدفع مترجم
     */
    public function getTypeNameAttribute()
    {
        $types = [
            'received' => 'مقبوض',
            'paid' => 'مدفوع'
        ];

        return $types[$this->type] ?? $this->type;
    }

    /**
     * الحصول على طريقة الدفع مترجمة
     */
    public function getPaymentMethodNameAttribute()
    {
        $methods = [
            'cash' => 'نقداً',
            'bank_transfer' => 'تحويل بنكي',
            'check' => 'شيك',
            'credit_card' => 'بطاقة ائتمان',
            'other' => 'أخرى'
        ];

        return $methods[$this->payment_method] ?? $this->payment_method;
    }

    /**
     * إنشاء رقم دفعة تلقائي
     */
    public static function generatePaymentNumber($companyId)
    {
        $prefix = 'PAY';
        $lastPayment = self::where('company_id', $companyId)
            ->orderBy('id', 'desc')
            ->first();

        $number = $lastPayment ? (int) substr($lastPayment->payment_number, strlen($prefix)) + 1 : 1;
        
        return $prefix . str_pad($number, 6, '0', STR_PAD_LEFT);
    }
}
