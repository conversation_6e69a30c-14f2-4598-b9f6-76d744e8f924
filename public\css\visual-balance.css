/* ==========================================================================
   تحسين التوازن البصري والتخطيط
   Visual Balance & Layout Optimization
   ========================================================================== */

/* تحسين المسافات والتوازن */
.content-wrapper {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 1rem;
    overflow-x: hidden;
}

.page-header {
    margin-bottom: 2rem;
    padding: 1.5rem 0;
}

.page-title {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.page-subtitle {
    font-size: clamp(0.875rem, 2vw, 1rem);
    opacity: 0.8;
    margin-bottom: 0;
}

/* تحسين الشبكات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* تحسين البطاقات */
.stat-card,
.card {
    background: var(--card-background);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card:hover,
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* تحسين النصوص */
.stat-value {
    font-size: clamp(1.75rem, 5vw, 2.5rem);
    font-weight: 800;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0.8;
    margin-bottom: 0.75rem;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    opacity: 0.7;
}

/* تحسين الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    min-height: 44px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--muted-color) 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #22c55e 100%);
    color: white;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

/* تحسين النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-size: 0.875rem;
}

.form-input,
.form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.875rem;
    background: var(--input-background);
    color: var(--text-color);
    transition: all 0.2s ease;
    min-height: 44px;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

/* تحسين البحث */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    padding-left: 2.5rem;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    color: var(--muted-color);
    pointer-events: none;
}

/* تحسين الجداول */
.table-container {
    background: var(--card-background);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
}

.table-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--header-background);
}

.table-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-color);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.data-table th {
    background: var(--header-background);
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--muted-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table tbody tr {
    transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
    background: var(--hover-background);
}

/* تحسين الشارات */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.badge-secondary {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

/* تحسين مجموعات الإجراءات */
.action-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.action-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.5rem;
    border-radius: 6px;
    border: none;
    background: transparent;
    color: var(--muted-color);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 36px;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: var(--hover-background);
    color: var(--primary-color);
    transform: scale(1.1);
}

/* تحسين التخطيط المتجاوب */
@media (max-width: 768px) {
    .content-wrapper {
        padding: 0 0.75rem;
    }
    
    .page-header {
        margin-bottom: 1.5rem;
        padding: 1rem 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .card-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .action-group {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .data-table {
        font-size: 0.875rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.75rem 0.5rem;
    }
}

@media (max-width: 480px) {
    .stat-card,
    .card {
        padding: 1rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .btn {
        padding: 0.625rem 1rem;
        font-size: 0.8125rem;
    }
    
    .form-input,
    .form-select {
        padding: 0.625rem 0.75rem;
    }
}

/* تحسين التباعد العمودي */
.section-spacing {
    margin-bottom: 2rem;
}

.section-spacing:last-child {
    margin-bottom: 0;
}

/* تحسين التحميل */
.loading-state {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading-state::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين التركيز */
.focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسين الانتقالات */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين الظلال */
.elevated {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.elevated-hover:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
