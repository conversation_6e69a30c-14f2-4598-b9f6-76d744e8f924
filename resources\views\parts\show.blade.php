@extends('layouts.main')

@section('title', 'تفاصيل قطعة الغيار - ' . $part->name)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $part->name }}</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $part->part_number ?? $part->sku }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('parts.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة للقائمة
            </a>
            <a href="{{ route('parts.edit', $part) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                تعديل
            </a>
            <button onclick="updateStock()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                تحديث المخزون
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Main Info -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">المعلومات الأساسية</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">اسم القطعة</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->name }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">رقم القطعة</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->part_number ?? 'غير محدد' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">العلامة التجارية</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->brand ?? 'غير محدد' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الموديل</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->model ?? 'غير محدد' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الفئة</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->category->name ?? 'غير محدد' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">المورد</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->supplier->name ?? 'غير محدد' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">نوع القطعة</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                            @switch($part->part_type)
                                @case('original') أصلي @break
                                @case('compatible') متوافق @break
                                @case('aftermarket') بديل @break
                                @case('refurbished') مجدد @break
                                @default {{ $part->part_type ?? 'غير محدد' }}
                            @endswitch
                        </p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الحالة</label>
                        <p class="mt-1">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                @switch($part->condition)
                                    @case('new') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @break
                                    @case('refurbished') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 @break
                                    @case('used') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 @break
                                    @case('damaged') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @break
                                    @default bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                @endswitch">
                                @switch($part->condition)
                                    @case('new') جديد @break
                                    @case('refurbished') مجدد @break
                                    @case('used') مستعمل @break
                                    @case('damaged') تالف @break
                                    @default {{ $part->condition }}
                                @endswitch
                            </span>
                        </p>
                    </div>
                </div>

                @if($part->description)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الوصف</label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->description }}</p>
                </div>
                @endif
            </div>

            <!-- Pricing Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات التسعير</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">سعر التكلفة</label>
                        <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-gray-100">{{ number_format($part->cost_price, 2) }} ر.س</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">سعر البيع</label>
                        <p class="mt-1 text-lg font-semibold text-green-600 dark:text-green-400">{{ number_format($part->selling_price, 2) }} ر.س</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">نسبة الربح</label>
                        <p class="mt-1 text-lg font-semibold text-blue-600 dark:text-blue-400">{{ number_format($part->markup_percentage, 2) }}%</p>
                    </div>
                </div>
            </div>

            <!-- Stock Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات المخزون</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الكمية الحالية</label>
                        <div class="mt-1 flex items-center">
                            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $part->stock_quantity }}</p>
                            <span class="mr-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $part->stock_status_badge['class'] }}">
                                {{ $part->stock_status_badge['text'] }}
                            </span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الحد الأدنى</label>
                        <p class="mt-1 text-lg text-gray-900 dark:text-gray-100">{{ $part->minimum_stock ?? 'غير محدد' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">نقطة إعادة الطلب</label>
                        <p class="mt-1 text-lg text-gray-900 dark:text-gray-100">{{ $part->reorder_point ?? 'غير محدد' }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">كمية إعادة الطلب</label>
                        <p class="mt-1 text-lg text-gray-900 dark:text-gray-100">{{ $part->reorder_quantity ?? 'غير محدد' }}</p>
                    </div>
                </div>

                @if($part->location || $part->shelf_location)
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    @if($part->location)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الموقع</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->location }}</p>
                    </div>
                    @endif

                    @if($part->shelf_location)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">موقع الرف</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->shelf_location }}</p>
                    </div>
                    @endif
                </div>
                @endif
            </div>

            <!-- Recent Stock Movements -->
            @if($recentMovements->count() > 0)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">حركات المخزون الأخيرة</h3>
                    <a href="{{ route('parts.stock-movements', $part) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm">
                        عرض الكل
                    </a>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">النوع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المستخدم</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($recentMovements as $movement)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    {{ $movement->created_at->format('Y-m-d H:i') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @if(str_contains($movement->type, 'in')) bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                        @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @endif">
                                        @switch($movement->type)
                                            @case('stock_in') إدخال @break
                                            @case('stock_out') إخراج @break
                                            @case('adjustment_in') تعديل زيادة @break
                                            @case('adjustment_out') تعديل نقص @break
                                            @case('initial_stock') مخزون أولي @break
                                            @default {{ $movement->type }}
                                        @endswitch
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    {{ $movement->quantity }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    {{ $movement->createdBy->name ?? 'غير محدد' }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif
        </div>

        <!-- Right Column - Sidebar -->
        <div class="space-y-6">
            <!-- Images -->
            @if($part->images && count($part->images) > 0)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الصور</h3>
                <div class="grid grid-cols-1 gap-4">
                    @foreach($part->images as $image)
                        <img src="{{ Storage::url($image) }}" alt="{{ $part->name }}" class="w-full h-48 object-cover rounded-lg">
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Statistics -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الإحصائيات</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">إجمالي المستخدم</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $stats['total_used'] }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">حركات المخزون</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $stats['total_movements'] }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">الاستخدام الشهري</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $stats['average_monthly_usage'] }}</span>
                    </div>

                    @if($stats['estimated_reorder_date'])
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">تاريخ إعادة الطلب المتوقع</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $stats['estimated_reorder_date']->format('Y-m-d') }}</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Additional Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات إضافية</h3>
                
                <div class="space-y-4">
                    @if($part->barcode)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الباركود</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->barcode }}</p>
                    </div>
                    @endif

                    @if($part->sku)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">SKU</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->sku }}</p>
                    </div>
                    @endif

                    @if($part->weight)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الوزن</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->weight }} كجم</p>
                    </div>
                    @endif

                    @if($part->warranty_period)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">فترة الضمان</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->warranty_period }} شهر</p>
                    </div>
                    @endif

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">الحالة</label>
                        <p class="mt-1">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $part->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                                {{ $part->is_active ? 'نشط' : 'غير نشط' }}
                            </span>
                        </p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">تاريخ الإنشاء</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->created_at->format('Y-m-d H:i') }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">آخر تحديث</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $part->updated_at->format('Y-m-d H:i') }}</p>
                    </div>
                </div>
            </div>

            @if($part->notes)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">ملاحظات</h3>
                <p class="text-sm text-gray-900 dark:text-gray-100">{{ $part->notes }}</p>
            </div>
            @endif
        </div>
    </div>
</div>

<script>
function updateStock() {
    // Implementation for stock update modal
    alert('سيتم إضافة نافذة تحديث المخزون قريباً');
}
</script>
@endsection
