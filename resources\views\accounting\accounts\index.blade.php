@extends('layouts.main')

@section('title', 'دليل الحسابات')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">دليل الحسابات</h1>
            <p class="text-gray-600 dark:text-gray-400">إدارة الحسابات المحاسبية والهيكل المالي</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('accounts.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-plus mr-2"></i>
                حساب جديد
            </a>
            
            <button onclick="exportAccounts()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الحساب</label>
                <select name="type" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الأنواع</option>
                    @foreach($accountTypes as $key => $label)
                        <option value="{{ $key }}" {{ request('type') == $key ? 'selected' : '' }}>{{ $label }}</option>
                    @endforeach
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                <select name="status" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشط</option>
                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                </select>
            </div>
            
            <div class="flex-1">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <input type="text" name="search" value="{{ request('search') }}" 
                       placeholder="البحث في رقم أو اسم الحساب..."
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                <i class="fas fa-search mr-2"></i>
                بحث
            </button>
            
            <a href="{{ route('accounts.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-times mr-2"></i>
                مسح
            </a>
        </form>
    </div>

    <!-- Accounts Tree -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الحسابات</h3>
                <div class="flex gap-2">
                    <button onclick="expandAll()" class="text-blue-600 hover:text-blue-700 text-sm">
                        <i class="fas fa-expand-arrows-alt mr-1"></i>
                        توسيع الكل
                    </button>
                    <button onclick="collapseAll()" class="text-blue-600 hover:text-blue-700 text-sm">
                        <i class="fas fa-compress-arrows-alt mr-1"></i>
                        طي الكل
                    </button>
                    <a href="{{ route('accounting.reports.trial-balance') }}" class="text-green-600 hover:text-green-700 text-sm">
                        <i class="fas fa-balance-scale mr-1"></i>
                        ميزان المراجعة
                    </a>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            @if($accounts->count() > 0)
                <div class="space-y-2" id="accounts-tree">
                    @foreach($accounts as $account)
                        @include('accounting.accounts.partials.account-tree-item', ['account' => $account, 'level' => 0])
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-list-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد حسابات</p>
                    <a href="{{ route('accounts.create') }}" class="text-blue-600 hover:text-blue-700 mt-2 inline-block">
                        إنشاء حساب جديد
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Account Actions Modal -->
<div id="accountModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4" id="modalTitle">إجراءات الحساب</h3>
        <div id="modalContent">
            <!-- Content will be loaded here -->
        </div>
        <div class="flex justify-end gap-2 mt-6">
            <button onclick="closeModal()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                إلغاء
            </button>
            <button id="confirmAction" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                تأكيد
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
let currentAccountId = null;
let currentAction = null;

function showAccountDetails(accountId) {
    window.location.href = `/accounts/${accountId}`;
}

function editAccount(accountId) {
    window.location.href = `/accounts/${accountId}/edit`;
}

function addSubAccount(parentId) {
    window.location.href = `/accounts/create?parent_id=${parentId}`;
}

function toggleAccountStatus(accountId) {
    fetch(`/accounts/${accountId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تغيير حالة الحساب');
    });
}

function deleteAccount(accountId) {
    currentAccountId = accountId;
    currentAction = 'delete';
    
    document.getElementById('modalTitle').textContent = 'حذف الحساب';
    document.getElementById('modalContent').innerHTML = '<p class="text-gray-700 dark:text-gray-300">هل أنت متأكد من حذف هذا الحساب؟ لا يمكن التراجع عن هذا الإجراء.</p>';
    document.getElementById('accountModal').classList.remove('hidden');
    document.getElementById('accountModal').classList.add('flex');
}

function confirmAction() {
    if (currentAction === 'delete' && currentAccountId) {
        fetch(`/accounts/${currentAccountId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
            closeModal();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الحساب');
            closeModal();
        });
    }
}

function closeModal() {
    document.getElementById('accountModal').classList.add('hidden');
    document.getElementById('accountModal').classList.remove('flex');
    currentAccountId = null;
    currentAction = null;
}

function toggleChildren(accountId) {
    const childrenContainer = document.getElementById(`children-${accountId}`);
    const toggleIcon = document.getElementById(`toggle-${accountId}`);
    
    if (childrenContainer.classList.contains('hidden')) {
        childrenContainer.classList.remove('hidden');
        toggleIcon.classList.remove('fa-plus');
        toggleIcon.classList.add('fa-minus');
    } else {
        childrenContainer.classList.add('hidden');
        toggleIcon.classList.remove('fa-minus');
        toggleIcon.classList.add('fa-plus');
    }
}

function expandAll() {
    document.querySelectorAll('[id^="children-"]').forEach(element => {
        element.classList.remove('hidden');
    });
    document.querySelectorAll('[id^="toggle-"]').forEach(element => {
        element.classList.remove('fa-plus');
        element.classList.add('fa-minus');
    });
}

function collapseAll() {
    document.querySelectorAll('[id^="children-"]').forEach(element => {
        element.classList.add('hidden');
    });
    document.querySelectorAll('[id^="toggle-"]').forEach(element => {
        element.classList.remove('fa-minus');
        element.classList.add('fa-plus');
    });
}

function exportAccounts() {
    alert('ميزة التصدير قيد التطوير');
}

// Event listeners
document.getElementById('confirmAction').addEventListener('click', confirmAction);

// Close modal when clicking outside
document.getElementById('accountModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
@endpush
@endsection
