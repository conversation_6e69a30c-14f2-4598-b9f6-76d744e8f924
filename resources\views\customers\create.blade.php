@extends('layouts.main')

@section('title', 'إضافة عميل جديد')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إضافة عميل جديد</h1>
            <p class="text-gray-600 dark:text-gray-400">أدخل بيانات العميل الجديد في النظام</p>
        </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400"></i>
                </div>
                <div class="mr-3">
                    <p class="text-sm font-medium text-green-800 dark:text-green-200">
                        {{ session('success') }}
                    </p>
                </div>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="mr-3">
                    <p class="text-sm font-medium text-red-800 dark:text-red-200">
                        {{ session('error') }}
                    </p>
                </div>
            </div>
        </div>
    @endif

    @if($errors->any())
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-red-400"></i>
                </div>
                <div class="mr-3">
                    <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                        يرجى تصحيح الأخطاء التالية:
                    </h3>
                    <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                        <ul class="list-disc list-inside space-y-1">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Form -->
    <form action="{{ route('customers.store') }}" method="POST" class="space-y-6">
        @csrf

        <!-- Customer Type -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">نوع العميل</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                        <input type="radio" name="type" value="individual" checked class="text-blue-600 focus:ring-blue-500">
                        <div class="mr-3">
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">عميل فردي</div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">شخص طبيعي</div>
                        </div>
                    </label>
                </div>

                <div>
                    <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                        <input type="radio" name="type" value="business" class="text-blue-600 focus:ring-blue-500">
                        <div class="mr-3">
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">عميل تجاري</div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">شركة أو مؤسسة</div>
                        </div>
                    </label>
                </div>
            </div>
        </div>

        <!-- Basic Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">المعلومات الأساسية</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأول *</label>
                    <input type="text" name="first_name" id="first_name" value="{{ old('first_name') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    @error('first_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأخير *</label>
                    <input type="text" name="last_name" id="last_name" value="{{ old('last_name') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    @error('last_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="company_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم الشركة</label>
                    <input type="text" name="company_name" id="company_name" value="{{ old('company_name') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    @error('company_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mobile" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الجوال *</label>
                    <input type="text" name="mobile" id="mobile" value="{{ old('mobile') }}" required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    @error('mobile')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات الاتصال</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                    <input type="email" name="email" id="email" value="{{ old('email') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                    <input type="text" name="phone" id="phone" value="{{ old('phone') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    @error('phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المدينة</label>
                    <input type="text" name="city" id="city" value="{{ old('city') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    @error('city')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العنوان</label>
                    <textarea name="address" id="address" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">{{ old('address') }}</textarea>
                    @error('address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Settings -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الإعدادات</h3>

            <div class="space-y-4">
                <div class="flex items-center">
                    <input type="checkbox" name="is_vip" id="is_vip" value="1" {{ old('is_vip') ? 'checked' : '' }}
                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <label for="is_vip" class="mr-2 text-sm text-gray-700 dark:text-gray-300">عميل VIP</label>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <label for="is_active" class="mr-2 text-sm text-gray-700 dark:text-gray-300">عميل نشط</label>
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-2 space-x-reverse">
            <a href="{{ route('customers.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg">
                إلغاء
            </a>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                حفظ العميل
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeRadios = document.querySelectorAll('input[name="type"]');
    const firstNameField = document.getElementById('first_name');
    const lastNameField = document.getElementById('last_name');
    const companyNameField = document.getElementById('company_name');
    const form = document.querySelector('form');

    function updateRequiredFields() {
        const selectedType = document.querySelector('input[name="type"]:checked').value;

        if (selectedType === 'individual') {
            firstNameField.required = true;
            lastNameField.required = true;
            companyNameField.required = false;

            // Update labels
            document.querySelector('label[for="first_name"]').innerHTML = 'الاسم الأول *';
            document.querySelector('label[for="last_name"]').innerHTML = 'الاسم الأخير *';
            document.querySelector('label[for="company_name"]').innerHTML = 'اسم الشركة';

            // Clear company name if switching from business
            if (companyNameField.value && !firstNameField.value) {
                companyNameField.value = '';
            }
        } else {
            firstNameField.required = false;
            lastNameField.required = false;
            companyNameField.required = true;

            // Update labels
            document.querySelector('label[for="first_name"]').innerHTML = 'الاسم الأول';
            document.querySelector('label[for="last_name"]').innerHTML = 'الاسم الأخير';
            document.querySelector('label[for="company_name"]').innerHTML = 'اسم الشركة *';
        }
    }

    // Form validation before submit
    form.addEventListener('submit', function(e) {
        const selectedType = document.querySelector('input[name="type"]:checked').value;
        let isValid = true;
        let errorMessage = '';

        if (selectedType === 'individual') {
            if (!firstNameField.value.trim()) {
                isValid = false;
                errorMessage = 'الاسم الأول مطلوب للعملاء الأفراد';
            } else if (!lastNameField.value.trim()) {
                isValid = false;
                errorMessage = 'الاسم الأخير مطلوب للعملاء الأفراد';
            }
        } else {
            if (!companyNameField.value.trim()) {
                isValid = false;
                errorMessage = 'اسم الشركة مطلوب للعملاء التجاريين';
            }
        }

        if (!document.getElementById('mobile').value.trim()) {
            isValid = false;
            errorMessage = 'رقم الجوال مطلوب';
        }

        if (!isValid) {
            e.preventDefault();
            alert(errorMessage);
            return false;
        }
    });

    typeRadios.forEach(radio => {
        radio.addEventListener('change', updateRequiredFields);
    });

    // Initialize on page load
    updateRequiredFields();
});
</script>
@endsection
