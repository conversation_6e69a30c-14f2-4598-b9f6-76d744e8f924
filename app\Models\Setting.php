<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label',
        'description',
        'options',
        'validation_rules',
        'is_public',
        'is_encrypted',
        'order',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'value' => 'json',
        'options' => 'array',
        'validation_rules' => 'array',
        'is_public' => 'boolean',
        'is_encrypted' => 'boolean',
        'order' => 'integer'
    ];

    // Relationships
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            return decrypt($value);
        }

        $decoded = json_decode($value, true);
        
        // Return decoded JSON if valid, otherwise return original value
        return json_last_error() === JSON_ERROR_NONE ? $decoded : $value;
    }

    public function getTypeBadgeAttribute()
    {
        $types = [
            'string' => ['class' => 'badge-primary', 'text' => 'نص'],
            'integer' => ['class' => 'badge-info', 'text' => 'رقم صحيح'],
            'float' => ['class' => 'badge-info', 'text' => 'رقم عشري'],
            'boolean' => ['class' => 'badge-success', 'text' => 'منطقي'],
            'array' => ['class' => 'badge-warning', 'text' => 'مصفوفة'],
            'json' => ['class' => 'badge-warning', 'text' => 'JSON'],
            'email' => ['class' => 'badge-secondary', 'text' => 'بريد إلكتروني'],
            'url' => ['class' => 'badge-secondary', 'text' => 'رابط'],
            'password' => ['class' => 'badge-danger', 'text' => 'كلمة مرور'],
            'file' => ['class' => 'badge-dark', 'text' => 'ملف'],
            'image' => ['class' => 'badge-dark', 'text' => 'صورة']
        ];

        return $types[$this->type] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getFormattedValueAttribute()
    {
        switch ($this->type) {
            case 'boolean':
                return $this->value ? 'نعم' : 'لا';
            case 'password':
                return '••••••••';
            case 'array':
            case 'json':
                return is_array($this->value) ? implode(', ', $this->value) : $this->value;
            default:
                return $this->value;
        }
    }

    // Mutators
    public function setValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            $this->attributes['value'] = encrypt($value);
        } else {
            $this->attributes['value'] = is_array($value) || is_object($value) 
                ? json_encode($value) 
                : $value;
        }
    }

    // Scopes
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopePrivate($query)
    {
        return $query->where('is_public', false);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('group')->orderBy('order')->orderBy('label');
    }

    // Static Methods
    public static function get($key, $default = null)
    {
        $cacheKey = 'setting_' . $key;
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    public static function set($key, $value, $type = 'string')
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'updated_by' => auth()->id()
            ]
        );

        // Clear cache
        Cache::forget('setting_' . $key);
        Cache::forget('all_settings');

        return $setting;
    }

    public static function getGroup($group)
    {
        $cacheKey = 'settings_group_' . $group;
        
        return Cache::remember($cacheKey, 3600, function () use ($group) {
            return static::byGroup($group)->ordered()->get()->pluck('value', 'key');
        });
    }

    public static function getAllSettings()
    {
        return Cache::remember('all_settings', 3600, function () {
            return static::all()->pluck('value', 'key');
        });
    }

    public static function getPublicSettings()
    {
        return Cache::remember('public_settings', 3600, function () {
            return static::public()->get()->pluck('value', 'key');
        });
    }

    public static function clearCache()
    {
        Cache::forget('all_settings');
        Cache::forget('public_settings');
        
        // Clear individual setting caches
        $keys = static::pluck('key');
        foreach ($keys as $key) {
            Cache::forget('setting_' . $key);
        }
        
        // Clear group caches
        $groups = static::distinct()->pluck('group');
        foreach ($groups as $group) {
            Cache::forget('settings_group_' . $group);
        }
    }

    public static function createDefaultSettings()
    {
        $defaults = [
            // Company Settings
            [
                'key' => 'company_name',
                'value' => 'مركز الصيانة',
                'type' => 'string',
                'group' => 'company',
                'label' => 'اسم الشركة',
                'description' => 'اسم الشركة أو المركز',
                'is_public' => true,
                'order' => 1
            ],
            [
                'key' => 'company_logo',
                'value' => null,
                'type' => 'image',
                'group' => 'company',
                'label' => 'شعار الشركة',
                'description' => 'شعار الشركة المعروض في النظام',
                'is_public' => true,
                'order' => 2
            ],
            [
                'key' => 'company_address',
                'value' => '',
                'type' => 'string',
                'group' => 'company',
                'label' => 'عنوان الشركة',
                'description' => 'العنوان الكامل للشركة',
                'is_public' => true,
                'order' => 3
            ],
            [
                'key' => 'company_phone',
                'value' => '',
                'type' => 'string',
                'group' => 'company',
                'label' => 'هاتف الشركة',
                'description' => 'رقم هاتف الشركة',
                'is_public' => true,
                'order' => 4
            ],
            [
                'key' => 'company_email',
                'value' => '',
                'type' => 'email',
                'group' => 'company',
                'label' => 'بريد الشركة الإلكتروني',
                'description' => 'البريد الإلكتروني الرسمي للشركة',
                'is_public' => true,
                'order' => 5
            ],

            // System Settings
            [
                'key' => 'default_language',
                'value' => 'ar',
                'type' => 'string',
                'group' => 'system',
                'label' => 'اللغة الافتراضية',
                'description' => 'اللغة الافتراضية للنظام',
                'options' => ['ar' => 'العربية', 'en' => 'English'],
                'is_public' => true,
                'order' => 1
            ],
            [
                'key' => 'default_currency',
                'value' => 'SAR',
                'type' => 'string',
                'group' => 'system',
                'label' => 'العملة الافتراضية',
                'description' => 'العملة المستخدمة في النظام',
                'options' => ['SAR' => 'ريال سعودي', 'USD' => 'دولار أمريكي', 'EUR' => 'يورو'],
                'is_public' => true,
                'order' => 2
            ],
            [
                'key' => 'timezone',
                'value' => 'Asia/Riyadh',
                'type' => 'string',
                'group' => 'system',
                'label' => 'المنطقة الزمنية',
                'description' => 'المنطقة الزمنية للنظام',
                'is_public' => true,
                'order' => 3
            ],
            [
                'key' => 'date_format',
                'value' => 'Y-m-d',
                'type' => 'string',
                'group' => 'system',
                'label' => 'تنسيق التاريخ',
                'description' => 'تنسيق عرض التاريخ في النظام',
                'options' => ['Y-m-d' => '2024-01-15', 'd/m/Y' => '15/01/2024', 'd-m-Y' => '15-01-2024'],
                'is_public' => true,
                'order' => 4
            ],

            // Notification Settings
            [
                'key' => 'email_notifications',
                'value' => true,
                'type' => 'boolean',
                'group' => 'notifications',
                'label' => 'إشعارات البريد الإلكتروني',
                'description' => 'تفعيل إشعارات البريد الإلكتروني',
                'is_public' => false,
                'order' => 1
            ],
            [
                'key' => 'sms_notifications',
                'value' => false,
                'type' => 'boolean',
                'group' => 'notifications',
                'label' => 'إشعارات الرسائل النصية',
                'description' => 'تفعيل إشعارات الرسائل النصية',
                'is_public' => false,
                'order' => 2
            ],
            [
                'key' => 'push_notifications',
                'value' => true,
                'type' => 'boolean',
                'group' => 'notifications',
                'label' => 'الإشعارات الفورية',
                'description' => 'تفعيل الإشعارات الفورية في المتصفح',
                'is_public' => false,
                'order' => 3
            ],

            // Security Settings
            [
                'key' => 'session_timeout',
                'value' => 120,
                'type' => 'integer',
                'group' => 'security',
                'label' => 'انتهاء الجلسة (بالدقائق)',
                'description' => 'مدة انتهاء الجلسة بالدقائق',
                'is_public' => false,
                'order' => 1
            ],
            [
                'key' => 'password_min_length',
                'value' => 8,
                'type' => 'integer',
                'group' => 'security',
                'label' => 'الحد الأدنى لطول كلمة المرور',
                'description' => 'الحد الأدنى لعدد أحرف كلمة المرور',
                'is_public' => false,
                'order' => 2
            ],
            [
                'key' => 'max_login_attempts',
                'value' => 5,
                'type' => 'integer',
                'group' => 'security',
                'label' => 'الحد الأقصى لمحاولات تسجيل الدخول',
                'description' => 'عدد محاولات تسجيل الدخول المسموحة قبل الحظر',
                'is_public' => false,
                'order' => 3
            ],

            // Backup Settings
            [
                'key' => 'auto_backup',
                'value' => true,
                'type' => 'boolean',
                'group' => 'backup',
                'label' => 'النسخ الاحتياطي التلقائي',
                'description' => 'تفعيل النسخ الاحتياطي التلقائي',
                'is_public' => false,
                'order' => 1
            ],
            [
                'key' => 'backup_frequency',
                'value' => 'daily',
                'type' => 'string',
                'group' => 'backup',
                'label' => 'تكرار النسخ الاحتياطي',
                'description' => 'تكرار إنشاء النسخ الاحتياطية',
                'options' => ['daily' => 'يومي', 'weekly' => 'أسبوعي', 'monthly' => 'شهري'],
                'is_public' => false,
                'order' => 2
            ]
        ];

        foreach ($defaults as $setting) {
            static::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($setting) {
            $setting->created_by = auth()->id();
        });
        
        static::updating(function ($setting) {
            $setting->updated_by = auth()->id();
        });
        
        static::saved(function ($setting) {
            static::clearCache();
        });
        
        static::deleted(function ($setting) {
            static::clearCache();
        });
    }
}
