<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Maintenance extends Model
{
    use HasFactory;

    protected $fillable = [
        'maintenance_number',
        'technician_id',
        'equipment_type',
        'equipment_id',
        'maintenance_type',
        'scheduled_date',
        'completed_date',
        'status',
        'description',
        'checklist',
        'parts_used',
        'labor_hours',
        'cost',
        'notes',
        'next_maintenance_date'
    ];

    protected $casts = [
        'scheduled_date' => 'date',
        'completed_date' => 'date',
        'next_maintenance_date' => 'date',
        'checklist' => 'array',
        'parts_used' => 'array',
        'labor_hours' => 'decimal:2',
        'cost' => 'decimal:2'
    ];

    // Relationships
    public function technician()
    {
        return $this->belongsTo(Technician::class);
    }

    public function equipment()
    {
        return $this->morphTo();
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'scheduled' => ['class' => 'badge-info', 'text' => 'مجدول'],
            'in_progress' => ['class' => 'badge-warning', 'text' => 'قيد التنفيذ'],
            'completed' => ['class' => 'badge-success', 'text' => 'مكتمل'],
            'cancelled' => ['class' => 'badge-danger', 'text' => 'ملغي'],
            'overdue' => ['class' => 'badge-danger', 'text' => 'متأخر']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getMaintenanceTypeBadgeAttribute()
    {
        $types = [
            'preventive' => ['class' => 'badge-success', 'text' => 'وقائية'],
            'corrective' => ['class' => 'badge-warning', 'text' => 'تصحيحية'],
            'emergency' => ['class' => 'badge-danger', 'text' => 'طارئة'],
            'routine' => ['class' => 'badge-info', 'text' => 'روتينية']
        ];

        return $types[$this->maintenance_type] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    // Scopes
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeOverdue($query)
    {
        return $query->where('scheduled_date', '<', today())
                    ->where('status', '!=', 'completed');
    }

    public function scopePreventive($query)
    {
        return $query->where('maintenance_type', 'preventive');
    }

    // Methods
    public function generateMaintenanceNumber()
    {
        $prefix = 'MNT';
        $year = now()->year;
        
        $lastMaintenance = static::whereYear('created_at', $year)
                                ->orderBy('id', 'desc')
                                ->first();
        
        $sequence = $lastMaintenance ? (int)substr($lastMaintenance->maintenance_number, -5) + 1 : 1;
        
        return $prefix . $year . str_pad($sequence, 5, '0', STR_PAD_LEFT);
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($maintenance) {
            if (!$maintenance->maintenance_number) {
                $maintenance->maintenance_number = $maintenance->generateMaintenanceNumber();
            }
        });
    }
}
