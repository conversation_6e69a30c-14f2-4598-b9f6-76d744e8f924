/**
 * ملف JavaScript لواجهة تسجيل الدخول المتطورة
 * Auth JavaScript - Version 1.0
 */

class AuthManager {
    constructor() {
        this.maxAttempts = 5;
        this.lockoutTime = 15 * 60 * 1000; // 15 دقيقة
        this.isSubmitting = false; // منع الإرسال المتعدد
        this.init();
    }

    init() {
        this.setupThemeToggle();
        this.setupPasswordToggle();
        this.setupFormValidation();
        this.setupDemoAccounts();
        this.setupKeyboardShortcuts();
        this.setupRateLimit();
        this.setupAnimations();
        this.setupPageUnload();
    }

    // إدارة تبديل الوضع الليلي/النهاري
    setupThemeToggle() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.setTheme(savedTheme);

        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    setTheme(theme) {
        const body = document.body;
        const themeToggle = document.getElementById('themeToggle');
        
        body.setAttribute('data-bs-theme', theme);
        
        if (themeToggle) {
            if (theme === 'dark') {
                themeToggle.innerHTML = '<i class="fas fa-sun me-2"></i><span>الوضع النهاري</span>';
            } else {
                themeToggle.innerHTML = '<i class="fas fa-moon me-2"></i><span>الوضع الليلي</span>';
            }
        }
    }

    toggleTheme() {
        const currentTheme = document.body.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        this.setTheme(newTheme);
        localStorage.setItem('theme', newTheme);
        
        // تأثير انتقال سلس
        document.body.style.transition = 'all 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    // إدارة إظهار/إخفاء كلمة المرور
    setupPasswordToggle() {
        const passwordField = document.getElementById('password');
        const toggleBtn = document.querySelector('.password-toggle');
        
        if (passwordField && toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.togglePassword();
            });
        }
    }

    togglePassword() {
        const passwordField = document.getElementById('password');
        const toggleIcon = document.getElementById('passwordToggleIcon');
        
        if (passwordField && toggleIcon) {
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    }

    // التحقق من صحة النموذج
    setupFormValidation() {
        const form = document.getElementById('loginForm');
        const emailField = document.getElementById('email');
        const passwordField = document.getElementById('password');

        if (emailField) {
            emailField.addEventListener('blur', () => {
                this.validateEmail(emailField);
            });

            emailField.addEventListener('input', () => {
                if (emailField.classList.contains('is-invalid')) {
                    this.validateEmail(emailField);
                }
            });
        }

        if (passwordField) {
            passwordField.addEventListener('input', () => {
                this.validatePassword(passwordField);
            });
        }

        if (form) {
            form.addEventListener('submit', (e) => {
                const result = this.handleFormSubmit(e);
                if (result === false) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        }
    }

    validateEmail(field) {
        const email = field.value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            field.classList.add('is-invalid');
            return false;
        } else {
            field.classList.remove('is-invalid');
            return true;
        }
    }

    validatePassword(field) {
        const password = field.value;
        
        if (password.length > 0 && password.length < 6) {
            field.classList.add('is-invalid');
            return false;
        } else {
            field.classList.remove('is-invalid');
            return true;
        }
    }

    // إدارة إرسال النموذج
    handleFormSubmit(e) {
        const emailField = document.getElementById('email');
        const passwordField = document.getElementById('password');
        const loginBtn = document.getElementById('loginBtn');
        const loginBtnText = document.getElementById('loginBtnText');

        // منع الإرسال المتعدد
        if (this.isSubmitting) {
            e.preventDefault();
            return false;
        }

        // التحقق من Rate Limiting
        if (!this.checkRateLimit()) {
            e.preventDefault();
            return false;
        }

        // التحقق من صحة البيانات
        const isEmailValid = this.validateEmail(emailField);
        const isPasswordValid = this.validatePassword(passwordField);

        if (!isEmailValid || !isPasswordValid) {
            e.preventDefault();
            return false;
        }

        // التحقق من أن الزر ليس في حالة تحميل بالفعل
        if (loginBtn && loginBtn.classList.contains('loading')) {
            e.preventDefault();
            return false;
        }

        // تعيين حالة الإرسال
        this.isSubmitting = true;

        // إضافة حالة التحميل
        if (loginBtn && loginBtnText) {
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;
            loginBtnText.textContent = 'جاري تسجيل الدخول...';

            // إزالة حالة التحميل بعد 10 ثوان كحد أقصى
            setTimeout(() => {
                if (loginBtn.classList.contains('loading')) {
                    loginBtn.classList.remove('loading');
                    loginBtn.disabled = false;
                    loginBtnText.textContent = 'تسجيل الدخول';
                    this.isSubmitting = false; // إعادة تعيين حالة الإرسال
                }
            }, 10000);
        }

        // السماح بإرسال النموذج - تسجيل المحاولات يتم server-side
        return true;
    }

    // إدارة الحسابات التجريبية
    setupDemoAccounts() {
        const demoAccounts = document.querySelectorAll('.demo-account');
        
        demoAccounts.forEach(account => {
            account.style.cursor = 'pointer';
            account.addEventListener('click', () => {
                this.fillDemoCredentials(account);
            });

            // تأثير hover
            account.addEventListener('mouseenter', () => {
                account.style.transform = 'translateY(-2px)';
            });

            account.addEventListener('mouseleave', () => {
                account.style.transform = 'translateY(0)';
            });
        });
    }

    fillDemoCredentials(accountElement) {
        const credentials = accountElement.querySelector('.demo-credentials');
        if (credentials) {
            const lines = credentials.textContent.trim().split('\n');
            if (lines.length >= 2) {
                const email = lines[0].trim();
                const password = lines[1].trim();
                
                const emailField = document.getElementById('email');
                const passwordField = document.getElementById('password');
                
                if (emailField && passwordField) {
                    emailField.value = email;
                    passwordField.value = password;
                    
                    // إزالة أي أخطاء validation
                    emailField.classList.remove('is-invalid');
                    passwordField.classList.remove('is-invalid');
                    
                    // تأثير بصري
                    emailField.style.background = 'rgba(34, 197, 94, 0.1)';
                    passwordField.style.background = 'rgba(34, 197, 94, 0.1)';
                    
                    setTimeout(() => {
                        emailField.style.background = '';
                        passwordField.style.background = '';
                    }, 1000);
                }
            }
        }
    }

    // اختصارات لوحة المفاتيح
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Alt + T لتبديل الوضع
            if (e.altKey && e.key.toLowerCase() === 't') {
                e.preventDefault();
                this.toggleTheme();
            }
            
            // Alt + P لإظهار/إخفاء كلمة المرور
            if (e.altKey && e.key.toLowerCase() === 'p') {
                e.preventDefault();
                this.togglePassword();
            }

            // Enter لإرسال النموذج
            if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.altKey) {
                const activeElement = document.activeElement;
                if (activeElement && (activeElement.id === 'email' || activeElement.id === 'password')) {
                    e.preventDefault();
                    const form = document.getElementById('loginForm');
                    const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                    form.dispatchEvent(submitEvent);
                }
            }
        });
    }

    // حماية Rate Limiting
    setupRateLimit() {
        this.loadLoginAttempts();
    }

    checkRateLimit() {
        // Rate limiting يتم التحقق منه server-side فقط
        // هذا للتوافق مع الكود الموجود
        return true;
    }

    // تم نقل تسجيل المحاولات إلى server-side للدقة

    getLoginAttempts() {
        return parseInt(localStorage.getItem('loginAttempts') || '0');
    }

    clearLoginAttempts() {
        localStorage.removeItem('loginAttempts');
        localStorage.removeItem('lastLoginAttempt');
    }

    loadLoginAttempts() {
        const attempts = this.getLoginAttempts();
        if (attempts > 0) {
            console.log(`محاولات تسجيل الدخول: ${attempts}/${this.maxAttempts}`);
        }
    }

    // الرسائل والتنبيهات
    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // الرسوم المتحركة
    setupAnimations() {
        // تأثير الدخول للبطاقة
        const authCard = document.querySelector('.auth-card');
        if (authCard) {
            authCard.style.opacity = '0';
            authCard.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                authCard.style.transition = 'all 0.8s ease-out';
                authCard.style.opacity = '1';
                authCard.style.transform = 'translateY(0)';
            }, 100);
        }

        // تأثير التركيز على الحقول
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                input.parentElement.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', () => {
                input.parentElement.style.transform = 'scale(1)';
            });
        });
    }

    // إزالة حالة التحميل عند مغادرة الصفحة
    setupPageUnload() {
        window.addEventListener('beforeunload', () => {
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');

            if (loginBtn && loginBtn.classList.contains('loading')) {
                loginBtn.classList.remove('loading');
                loginBtn.disabled = false;
                if (loginBtnText) {
                    loginBtnText.textContent = 'تسجيل الدخول';
                }
                this.isSubmitting = false;
            }
        });

        // إزالة حالة التحميل عند إعادة تحميل الصفحة
        window.addEventListener('pageshow', () => {
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');

            if (loginBtn && loginBtn.classList.contains('loading')) {
                loginBtn.classList.remove('loading');
                loginBtn.disabled = false;
                if (loginBtnText) {
                    loginBtnText.textContent = 'تسجيل الدخول';
                }
                this.isSubmitting = false;
            }
        });
    }

    // مسح محاولات تسجيل الدخول عند النجاح
    clearLoginAttempts() {
        localStorage.removeItem('loginAttempts');
        localStorage.removeItem('lastLoginAttempt');
    }
}

// تشغيل AuthManager عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new AuthManager();
});

// تصدير للاستخدام العام
window.AuthManager = AuthManager;
