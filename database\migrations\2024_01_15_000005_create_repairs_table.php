<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('repairs', function (Blueprint $table) {
            $table->id();
            $table->string('repair_number')->unique();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('technician_id')->nullable()->constrained()->onDelete('set null');
            $table->string('device_type');
            $table->string('device_brand');
            $table->string('device_model');
            $table->string('device_serial')->nullable();
            $table->string('device_imei')->nullable();
            $table->text('problem_description');
            $table->text('diagnosis')->nullable();
            $table->enum('repair_type', ['warranty', 'paid', 'internal'])->default('paid');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->enum('status', ['pending', 'diagnosed', 'in_progress', 'waiting_parts', 'completed', 'delivered', 'cancelled', 'on_hold'])->default('pending');
            $table->decimal('estimated_cost', 10, 2)->default(0);
            $table->decimal('actual_cost', 10, 2)->default(0);
            $table->decimal('labor_cost', 10, 2)->default(0);
            $table->decimal('parts_cost', 10, 2)->default(0);
            $table->decimal('total_cost', 10, 2)->default(0);
            $table->timestamp('estimated_completion')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->integer('warranty_period')->nullable(); // days
            $table->timestamp('warranty_expires_at')->nullable();
            $table->text('customer_notes')->nullable();
            $table->text('technician_notes')->nullable();
            $table->text('internal_notes')->nullable();
            $table->json('accessories')->nullable();
            $table->text('condition_on_arrival')->nullable();
            $table->text('test_results')->nullable();
            $table->json('repair_steps')->nullable();
            $table->text('quality_check')->nullable();
            $table->string('customer_signature')->nullable();
            $table->json('photos_before')->nullable();
            $table->json('photos_after')->nullable();
            $table->boolean('is_warranty_repair')->default(false);
            $table->foreignId('original_repair_id')->nullable()->constrained('repairs')->onDelete('set null');
            $table->enum('payment_status', ['pending', 'partial', 'paid', 'refunded'])->default('pending');
            $table->string('payment_method')->nullable();
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['repair_number']);
            $table->index(['status']);
            $table->index(['priority']);
            $table->index(['device_type']);
            $table->index(['device_brand']);
            $table->index(['payment_status']);
            $table->index(['estimated_completion']);
            $table->index(['warranty_expires_at']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('repairs');
    }
};
