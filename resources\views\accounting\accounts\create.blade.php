@extends('layouts.main')

@section('title', 'إنشاء حساب جديد')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إنشاء حساب جديد</h1>
            <p class="text-gray-600 dark:text-gray-400">إضافة حساب جديد إلى دليل الحسابات</p>
        </div>
        
        <a href="{{ route('accounts.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-arrow-right mr-2"></i>
            العودة لدليل الحسابات
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">معلومات الحساب</h3>
        </div>
        
        <form action="{{ route('accounts.store') }}" method="POST" class="p-6 space-y-6">
            @csrf

            <!-- Parent Account Info (if applicable) -->
            @if($parentAccount)
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">الحساب الأب</h4>
                    <p class="text-blue-800 dark:text-blue-200">
                        {{ $parentAccount->account_code }} - {{ $parentAccount->account_name }}
                    </p>
                    <input type="hidden" name="parent_id" value="{{ $parentAccount->id }}">
                </div>
            @endif

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Account Code -->
                <div>
                    <label for="account_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        رقم الحساب <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="account_code" 
                           name="account_code" 
                           value="{{ old('account_code') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('account_code') border-red-500 @enderror"
                           placeholder="مثال: 11101"
                           required>
                    @error('account_code')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Account Name (Arabic) -->
                <div>
                    <label for="account_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        اسم الحساب <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="account_name" 
                           name="account_name" 
                           value="{{ old('account_name') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('account_name') border-red-500 @enderror"
                           placeholder="مثال: الصندوق"
                           required>
                    @error('account_name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Account Name (English) -->
                <div>
                    <label for="account_name_en" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        اسم الحساب (إنجليزي)
                    </label>
                    <input type="text" 
                           id="account_name_en" 
                           name="account_name_en" 
                           value="{{ old('account_name_en') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('account_name_en') border-red-500 @enderror"
                           placeholder="Example: Cash">
                    @error('account_name_en')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Parent Account -->
                @if(!$parentAccount)
                <div>
                    <label for="parent_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        الحساب الأب
                    </label>
                    <select name="parent_id" 
                            id="parent_id"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('parent_id') border-red-500 @enderror">
                        <option value="">حساب رئيسي</option>
                        @foreach($parentAccounts as $parent)
                            <option value="{{ $parent->id }}" {{ old('parent_id') == $parent->id ? 'selected' : '' }}>
                                {{ $parent->account_code }} - {{ $parent->account_name }}
                            </option>
                        @endforeach
                    </select>
                    @error('parent_id')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                @endif

                <!-- Account Type -->
                <div>
                    <label for="account_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        نوع الحساب <span class="text-red-500">*</span>
                    </label>
                    <select name="account_type" 
                            id="account_type"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('account_type') border-red-500 @enderror"
                            required>
                        <option value="">اختر نوع الحساب</option>
                        @foreach($accountTypes as $key => $label)
                            <option value="{{ $key }}" {{ old('account_type') == $key ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('account_type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Account Category -->
                <div>
                    <label for="account_category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        فئة الحساب
                    </label>
                    <input type="text" 
                           id="account_category" 
                           name="account_category" 
                           value="{{ old('account_category') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('account_category') border-red-500 @enderror"
                           placeholder="مثال: current_asset">
                    @error('account_category')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Balance Type -->
                <div>
                    <label for="balance_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        طبيعة الرصيد <span class="text-red-500">*</span>
                    </label>
                    <select name="balance_type" 
                            id="balance_type"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('balance_type') border-red-500 @enderror"
                            required>
                        <option value="">اختر طبيعة الرصيد</option>
                        @foreach($balanceTypes as $key => $label)
                            <option value="{{ $key }}" {{ old('balance_type') == $key ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('balance_type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Opening Balance -->
                <div>
                    <label for="opening_balance" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        الرصيد الافتتاحي
                    </label>
                    <input type="number" 
                           id="opening_balance" 
                           name="opening_balance" 
                           value="{{ old('opening_balance', 0) }}"
                           step="0.01"
                           min="0"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('opening_balance') border-red-500 @enderror">
                    @error('opening_balance')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    وصف الحساب
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 @error('description') border-red-500 @enderror"
                          placeholder="وصف مختصر للحساب وغرض استخدامه">{{ old('description') }}</textarea>
                @error('description')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="{{ route('accounts.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg">
                    إلغاء
                </a>
                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                    <i class="fas fa-save mr-2"></i>
                    حفظ الحساب
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
// Auto-suggest balance type based on account type
document.getElementById('account_type').addEventListener('change', function() {
    const accountType = this.value;
    const balanceTypeSelect = document.getElementById('balance_type');
    
    // Default balance types for each account type
    const defaultBalanceTypes = {
        'asset': 'debit',
        'expense': 'debit',
        'liability': 'credit',
        'equity': 'credit',
        'revenue': 'credit'
    };
    
    if (defaultBalanceTypes[accountType]) {
        balanceTypeSelect.value = defaultBalanceTypes[accountType];
    }
});
</script>
@endpush
@endsection
