@extends('layouts.main')

@section('title', 'إنشاء فاتورة مبيعات')

@section('content')
<div class="space-y-6" x-data="createSale()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إنشاء فاتورة مبيعات</h1>
            <p class="text-gray-600 dark:text-gray-400">إنشاء فاتورة مبيعات جديدة</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('sales.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                إلغاء
            </a>
            <button @click="saveDraft()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg">
                حفظ كمسودة
            </button>
            <button @click="saveSale()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                حفظ الفاتورة
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Invoice Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Customer & Invoice Info -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات الفاتورة</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الفاتورة</label>
                        <input type="text" x-model="invoice.number" readonly 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 dark:text-gray-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التاريخ</label>
                        <input type="date" x-model="invoice.date" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العميل</label>
                        <div class="relative">
                            <input type="text" x-model="customerSearch" @input="searchCustomers()" @focus="showCustomerDropdown = true"
                                   placeholder="البحث عن عميل أو إضافة عميل جديد..."
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            
                            <!-- Customer Dropdown -->
                            <div x-show="showCustomerDropdown && filteredCustomers.length > 0" 
                                 @click.away="showCustomerDropdown = false"
                                 class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                                <template x-for="customer in filteredCustomers" :key="customer.id">
                                    <div @click="selectCustomer(customer)" 
                                         class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                                        <div class="font-medium text-gray-900 dark:text-gray-100" x-text="customer.name"></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400" x-text="customer.phone"></div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">المنتجات</h3>
                    <button @click="showProductModal = true" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                        إضافة منتج
                    </button>
                </div>

                <!-- Products Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">المنتج</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">السعر</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الكمية</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">الإجمالي</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <template x-for="(item, index) in invoice.items" :key="index">
                                <tr>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="item.name"></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400" x-text="item.sku"></div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <input type="number" x-model="item.price" @input="calculateItemTotal(index)" step="0.01"
                                               class="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-gray-100">
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <input type="number" x-model="item.quantity" @input="calculateItemTotal(index)" min="1"
                                               class="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-gray-100">
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        ₪<span x-text="item.total.toFixed(2)"></span>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <button @click="removeItem(index)" class="text-red-600 hover:text-red-900 dark:text-red-400">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                            </template>
                            <tr x-show="invoice.items.length === 0">
                                <td colspan="5" class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                                    لم يتم إضافة أي منتجات بعد
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Notes -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">ملاحظات</h3>
                <textarea x-model="invoice.notes" rows="3" placeholder="ملاحظات إضافية..."
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
            </div>
        </div>

        <!-- Invoice Summary -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm sticky top-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">ملخص الفاتورة</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">₪<span x-text="subtotal.toFixed(2)"></span></span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">الخصم:</span>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <input type="number" x-model="invoice.discount" @input="calculateTotals()" min="0" step="0.01"
                                   class="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-gray-100">
                            <select x-model="invoice.discountType" @change="calculateTotals()"
                                    class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm dark:bg-gray-700 dark:text-gray-100">
                                <option value="amount">₪</option>
                                <option value="percentage">%</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الضريبة (16%):</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">₪<span x-text="tax.toFixed(2)"></span></span>
                    </div>
                    
                    <hr class="border-gray-200 dark:border-gray-700">
                    
                    <div class="flex justify-between text-lg font-bold">
                        <span class="text-gray-900 dark:text-gray-100">الإجمالي:</span>
                        <span class="text-blue-600 dark:text-blue-400">₪<span x-text="total.toFixed(2)"></span></span>
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة الدفع</label>
                    <select x-model="invoice.paymentMethod" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="cash">نقدي</option>
                        <option value="card">بطاقة</option>
                        <option value="credit">آجل</option>
                    </select>
                </div>

                <!-- Payment Amount -->
                <div class="mt-4" x-show="invoice.paymentMethod !== 'credit'">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ المدفوع</label>
                    <input type="number" x-model="invoice.paidAmount" @input="calculateChange()" step="0.01"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    
                    <div x-show="change > 0" class="mt-2 p-2 bg-green-100 dark:bg-green-900 rounded text-green-800 dark:text-green-200 text-sm">
                        الباقي: ₪<span x-text="change.toFixed(2)"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Selection Modal -->
    <div x-show="showProductModal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" @click="showProductModal = false">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">اختيار منتج</h3>
                    
                    <div class="mb-4">
                        <input type="text" x-model="productSearch" @input="searchProducts()" 
                               placeholder="البحث في المنتجات..."
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    </div>

                    <div class="max-h-60 overflow-y-auto">
                        <template x-for="product in filteredProducts" :key="product.id">
                            <div @click="addProduct(product)" 
                                 class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg mb-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                <div class="font-medium text-gray-900 dark:text-gray-100" x-text="product.name"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <span x-text="product.sku"></span> - ₪<span x-text="product.price"></span>
                                </div>
                                <div class="text-xs text-gray-400 dark:text-gray-500">
                                    المخزون: <span x-text="product.stock"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="showProductModal = false" 
                            class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-100 dark:border-gray-500">
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function createSale() {
    return {
        invoice: {
            number: 'INV-' + Date.now(),
            date: new Date().toISOString().split('T')[0],
            customer: null,
            items: [],
            notes: '',
            discount: 0,
            discountType: 'amount',
            paymentMethod: 'cash',
            paidAmount: 0
        },
        customerSearch: '',
        showCustomerDropdown: false,
        filteredCustomers: [],
        customers: [
            { id: 1, name: 'أحمد محمد علي', phone: '0599123456', email: '<EMAIL>' },
            { id: 2, name: 'سارة أحمد خالد', phone: '0598765432', email: '<EMAIL>' },
            { id: 3, name: 'محمد عبدالله حسن', phone: '0597654321', email: '<EMAIL>' }
        ],
        showProductModal: false,
        productSearch: '',
        filteredProducts: [],
        products: [
            { id: 1, name: 'كابل USB-C', sku: 'CAB-001', price: 25.00, stock: 50 },
            { id: 2, name: 'شاحن سريع 20W', sku: 'CHR-001', price: 85.00, stock: 30 },
            { id: 3, name: 'جراب حماية iPhone', sku: 'CAS-001', price: 45.00, stock: 25 },
            { id: 4, name: 'سماعات بلوتوث', sku: 'HED-001', price: 120.00, stock: 15 }
        ],
        subtotal: 0,
        tax: 0,
        total: 0,
        change: 0,

        init() {
            this.filteredCustomers = [...this.customers];
            this.filteredProducts = [...this.products];
            this.calculateTotals();
        },

        searchCustomers() {
            if (this.customerSearch.length === 0) {
                this.filteredCustomers = [...this.customers];
            } else {
                this.filteredCustomers = this.customers.filter(customer =>
                    customer.name.toLowerCase().includes(this.customerSearch.toLowerCase()) ||
                    customer.phone.includes(this.customerSearch)
                );
            }
        },

        selectCustomer(customer) {
            this.invoice.customer = customer;
            this.customerSearch = customer.name;
            this.showCustomerDropdown = false;
        },

        searchProducts() {
            if (this.productSearch.length === 0) {
                this.filteredProducts = [...this.products];
            } else {
                this.filteredProducts = this.products.filter(product =>
                    product.name.toLowerCase().includes(this.productSearch.toLowerCase()) ||
                    product.sku.toLowerCase().includes(this.productSearch.toLowerCase())
                );
            }
        },

        addProduct(product) {
            const existingItem = this.invoice.items.find(item => item.id === product.id);
            
            if (existingItem) {
                existingItem.quantity += 1;
                this.calculateItemTotal(this.invoice.items.indexOf(existingItem));
            } else {
                this.invoice.items.push({
                    id: product.id,
                    name: product.name,
                    sku: product.sku,
                    price: product.price,
                    quantity: 1,
                    total: product.price
                });
            }
            
            this.showProductModal = false;
            this.productSearch = '';
            this.filteredProducts = [...this.products];
            this.calculateTotals();
        },

        removeItem(index) {
            this.invoice.items.splice(index, 1);
            this.calculateTotals();
        },

        calculateItemTotal(index) {
            const item = this.invoice.items[index];
            item.total = item.price * item.quantity;
            this.calculateTotals();
        },

        calculateTotals() {
            this.subtotal = this.invoice.items.reduce((sum, item) => sum + item.total, 0);
            
            let discountAmount = 0;
            if (this.invoice.discountType === 'percentage') {
                discountAmount = this.subtotal * (this.invoice.discount / 100);
            } else {
                discountAmount = this.invoice.discount;
            }
            
            const afterDiscount = this.subtotal - discountAmount;
            this.tax = afterDiscount * 0.16; // 16% tax
            this.total = afterDiscount + this.tax;
            
            this.calculateChange();
        },

        calculateChange() {
            if (this.invoice.paymentMethod !== 'credit') {
                this.change = Math.max(0, this.invoice.paidAmount - this.total);
            } else {
                this.change = 0;
            }
        },

        saveDraft() {
            // Save as draft logic
            alert('تم حفظ الفاتورة كمسودة');
        },

        saveSale() {
            if (this.invoice.items.length === 0) {
                alert('يجب إضافة منتج واحد على الأقل');
                return;
            }
            
            if (!this.invoice.customer) {
                alert('يجب اختيار عميل');
                return;
            }
            
            // Save sale logic
            alert('تم حفظ الفاتورة بنجاح');
            window.location.href = '/sales';
        }
    }
}
</script>
@endpush
@endsection
