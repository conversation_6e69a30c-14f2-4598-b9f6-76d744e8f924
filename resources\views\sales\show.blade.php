@extends('layouts.main')

@section('title', 'تفاصيل الفاتورة')

@section('content')
<div class="space-y-6" x-data="saleDetails({{ $id }})">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تفاصيل الفاتورة</h1>
            <p class="text-gray-600 dark:text-gray-400">عرض تفاصيل فاتورة المبيعات</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('sales.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة للقائمة
            </a>
            <button @click="printInvoice()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                طباعة
            </button>
            <a :href="`/sales/${sale.id}/edit`" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                تعديل
            </a>
        </div>
    </div>

    <!-- Invoice Card -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <!-- Invoice Header -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8 text-white">
            <div class="flex justify-between items-start">
                <div>
                    <h2 class="text-2xl font-bold">فاتورة مبيعات</h2>
                    <p class="text-blue-100 mt-1">مركز الطارق لصيانة الحاسوب والموبايل</p>
                </div>
                <div class="text-right">
                    <div class="text-3xl font-bold" x-text="sale.invoice_number"></div>
                    <div class="text-blue-100 mt-1" x-text="sale.date"></div>
                </div>
            </div>
        </div>

        <div class="p-6">
            <!-- Customer & Invoice Info -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">معلومات العميل</h3>
                    <div class="space-y-2">
                        <div class="flex">
                            <span class="text-gray-600 dark:text-gray-400 w-20">الاسم:</span>
                            <span class="text-gray-900 dark:text-gray-100 font-medium" x-text="sale.customer.name"></span>
                        </div>
                        <div class="flex">
                            <span class="text-gray-600 dark:text-gray-400 w-20">الهاتف:</span>
                            <span class="text-gray-900 dark:text-gray-100" x-text="sale.customer.phone"></span>
                        </div>
                        <div class="flex">
                            <span class="text-gray-600 dark:text-gray-400 w-20">البريد:</span>
                            <span class="text-gray-900 dark:text-gray-100" x-text="sale.customer.email"></span>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">معلومات الفاتورة</h3>
                    <div class="space-y-2">
                        <div class="flex">
                            <span class="text-gray-600 dark:text-gray-400 w-24">رقم الفاتورة:</span>
                            <span class="text-gray-900 dark:text-gray-100 font-medium" x-text="sale.invoice_number"></span>
                        </div>
                        <div class="flex">
                            <span class="text-gray-600 dark:text-gray-400 w-24">التاريخ:</span>
                            <span class="text-gray-900 dark:text-gray-100" x-text="sale.date"></span>
                        </div>
                        <div class="flex">
                            <span class="text-gray-600 dark:text-gray-400 w-24">طريقة الدفع:</span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                  :class="{
                                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': sale.payment_method === 'cash',
                                      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': sale.payment_method === 'card',
                                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': sale.payment_method === 'credit'
                                  }"
                                  x-text="getPaymentMethodText(sale.payment_method)">
                            </span>
                        </div>
                        <div class="flex">
                            <span class="text-gray-600 dark:text-gray-400 w-24">الحالة:</span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                  :class="{
                                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': sale.status === 'completed',
                                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': sale.status === 'pending',
                                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': sale.status === 'cancelled'
                                  }"
                                  x-text="getStatusText(sale.status)">
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Items Table -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">تفاصيل المنتجات</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">#</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المنتج</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">السعر</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <template x-for="(item, index) in sale.items" :key="index">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="index + 1"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="item.name"></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400" x-text="item.sku"></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        ₪<span x-text="item.price.toFixed(2)"></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="item.quantity"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                        ₪<span x-text="item.total.toFixed(2)"></span>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Totals -->
            <div class="flex justify-end">
                <div class="w-full max-w-sm">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                            <span class="font-medium text-gray-900 dark:text-gray-100">₪<span x-text="sale.subtotal.toFixed(2)"></span></span>
                        </div>
                        
                        <div class="flex justify-between" x-show="sale.discount > 0">
                            <span class="text-gray-600 dark:text-gray-400">الخصم:</span>
                            <span class="font-medium text-red-600 dark:text-red-400">-₪<span x-text="sale.discount.toFixed(2)"></span></span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">الضريبة (16%):</span>
                            <span class="font-medium text-gray-900 dark:text-gray-100">₪<span x-text="sale.tax.toFixed(2)"></span></span>
                        </div>
                        
                        <hr class="border-gray-200 dark:border-gray-600">
                        
                        <div class="flex justify-between text-lg font-bold">
                            <span class="text-gray-900 dark:text-gray-100">الإجمالي:</span>
                            <span class="text-blue-600 dark:text-blue-400">₪<span x-text="sale.total.toFixed(2)"></span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            <div x-show="sale.notes" class="mt-8">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">ملاحظات</h3>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <p class="text-gray-700 dark:text-gray-300" x-text="sale.notes"></p>
                </div>
            </div>

            <!-- Payment History -->
            <div class="mt-8" x-show="sale.payments && sale.payments.length > 0">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">تاريخ المدفوعات</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الطريقة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <template x-for="payment in sale.payments" :key="payment.id">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="payment.date"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                        ₪<span x-text="payment.amount.toFixed(2)"></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="getPaymentMethodText(payment.method)"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="payment.notes || '-'"></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function saleDetails(saleId) {
    return {
        sale: {
            id: saleId,
            invoice_number: 'INV-001',
            date: '2024-07-09',
            status: 'completed',
            payment_method: 'cash',
            customer: {
                name: 'أحمد محمد علي',
                phone: '0599123456',
                email: '<EMAIL>'
            },
            items: [
                {
                    name: 'كابل USB-C',
                    sku: 'CAB-001',
                    price: 25.00,
                    quantity: 2,
                    total: 50.00
                },
                {
                    name: 'شاحن سريع 20W',
                    sku: 'CHR-001',
                    price: 85.00,
                    quantity: 1,
                    total: 85.00
                }
            ],
            subtotal: 135.00,
            discount: 10.00,
            tax: 20.00,
            total: 145.00,
            notes: 'فاتورة تجريبية لعرض النظام',
            payments: [
                {
                    id: 1,
                    date: '2024-07-09',
                    amount: 145.00,
                    method: 'cash',
                    notes: 'دفع كامل نقدي'
                }
            ]
        },

        init() {
            // Load sale data based on saleId
            this.loadSaleData();
        },

        loadSaleData() {
            // In real implementation, this would fetch data from API
            console.log('Loading sale data for ID:', this.sale.id);
        },

        getPaymentMethodText(method) {
            const methods = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'credit': 'آجل'
            };
            return methods[method] || method;
        },

        getStatusText(status) {
            const statuses = {
                'completed': 'مكتملة',
                'pending': 'معلقة',
                'cancelled': 'ملغية'
            };
            return statuses[status] || status;
        },

        printInvoice() {
            window.open(`/sales/${this.sale.id}/print`, '_blank');
        }
    }
}
</script>
@endpush
@endsection
