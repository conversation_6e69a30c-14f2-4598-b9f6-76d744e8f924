<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Technician;
use App\Models\Repair;

class TechnicianControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
    }

    /** @test */
    public function it_can_display_technicians_index()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('technicians.index'));

        $response->assertStatus(200);
        $response->assertViewIs('technicians.index');
    }

    /** @test */
    public function it_can_display_technician_create_form()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('technicians.create'));

        $response->assertStatus(200);
        $response->assertViewIs('technicians.create');
    }

    /** @test */
    public function it_can_create_a_technician()
    {
        $technicianData = [
            'first_name' => 'أحمد',
            'last_name' => 'محمد',
            'email' => '<EMAIL>',
            'phone' => '0599123456',
            'department' => 'صيانة الهواتف',
            'position' => 'فني أول',
            'hire_date' => '2024-01-01',
            'skill_level' => 'advanced',
            'experience_years' => 5,
            'specializations' => ['Mobile Repair', 'Computer Repair']
        ];

        $response = $this->actingAs($this->user)
                         ->post(route('technicians.store'), $technicianData);

        $response->assertRedirect(route('technicians.index'));
        $this->assertDatabaseHas('technicians', [
            'first_name' => 'أحمد',
            'last_name' => 'محمد',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function it_can_display_a_technician()
    {
        $technician = Technician::factory()->create();

        $response = $this->actingAs($this->user)
                         ->get(route('technicians.show', $technician));

        $response->assertStatus(200);
        $response->assertViewIs('technicians.show');
        $response->assertViewHas('technician', $technician);
    }

    /** @test */
    public function it_can_display_technician_edit_form()
    {
        $technician = Technician::factory()->create();

        $response = $this->actingAs($this->user)
                         ->get(route('technicians.edit', $technician));

        $response->assertStatus(200);
        $response->assertViewIs('technicians.edit');
        $response->assertViewHas('technician', $technician);
    }

    /** @test */
    public function it_can_update_a_technician()
    {
        $technician = Technician::factory()->create();

        $updateData = [
            'first_name' => 'علي',
            'last_name' => 'أحمد',
            'email' => '<EMAIL>',
            'phone' => '0598765432',
            'department' => 'صيانة الحاسوب',
            'position' => 'فني خبير',
            'hire_date' => $technician->hire_date->format('Y-m-d'),
            'skill_level' => 'expert',
            'experience_years' => 8,
            'specializations' => ['Computer Repair', 'Tablet Repair']
        ];

        $response = $this->actingAs($this->user)
                         ->put(route('technicians.update', $technician), $updateData);

        $response->assertRedirect(route('technicians.show', $technician));
        $this->assertDatabaseHas('technicians', [
            'id' => $technician->id,
            'first_name' => 'علي',
            'last_name' => 'أحمد',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function it_can_update_technician_availability()
    {
        $technician = Technician::factory()->create(['availability' => 'available']);

        $response = $this->actingAs($this->user)
                         ->post(route('technicians.update-availability', $technician), [
                             'availability' => 'busy'
                         ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('technicians', [
            'id' => $technician->id,
            'availability' => 'busy'
        ]);
    }

    /** @test */
    public function it_can_display_technician_schedule()
    {
        $response = $this->actingAs($this->user)
                         ->get(route('technicians.schedule'));

        $response->assertStatus(200);
        $response->assertViewIs('technicians.schedule');
    }

    /** @test */
    public function it_can_assign_repair_to_technician()
    {
        $technician = Technician::factory()->create(['availability' => 'available']);
        $repair = Repair::factory()->create(['technician_id' => null]);

        $response = $this->actingAs($this->user)
                         ->post(route('technicians.assign-repair', $technician), [
                             'repair_id' => $repair->id,
                             'notes' => 'Assigned for repair'
                         ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('repairs', [
            'id' => $repair->id,
            'technician_id' => $technician->id
        ]);
    }

    /** @test */
    public function it_can_auto_assign_repairs()
    {
        $technician = Technician::factory()->create([
            'availability' => 'available',
            'specializations' => ['Mobile Repair']
        ]);
        
        $repair1 = Repair::factory()->create([
            'technician_id' => null,
            'device_type' => 'mobile'
        ]);
        
        $repair2 = Repair::factory()->create([
            'technician_id' => null,
            'device_type' => 'mobile'
        ]);

        $response = $this->actingAs($this->user)
                         ->post(route('technicians.auto-assign'), [
                             'repair_ids' => [$repair1->id, $repair2->id]
                         ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
    }

    /** @test */
    public function it_can_delete_a_technician()
    {
        $technician = Technician::factory()->create();

        $response = $this->actingAs($this->user)
                         ->delete(route('technicians.destroy', $technician));

        $response->assertRedirect(route('technicians.index'));
        $this->assertSoftDeleted('technicians', ['id' => $technician->id]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_technician()
    {
        $response = $this->actingAs($this->user)
                         ->post(route('technicians.store'), []);

        $response->assertSessionHasErrors([
            'first_name',
            'last_name',
            'email',
            'department',
            'position',
            'hire_date',
            'skill_level'
        ]);
    }

    /** @test */
    public function it_can_filter_technicians_by_status()
    {
        Technician::factory()->create(['status' => 'active']);
        Technician::factory()->create(['status' => 'inactive']);

        $response = $this->actingAs($this->user)
                         ->get(route('technicians.index', ['status' => 'active']));

        $response->assertStatus(200);
        $response->assertViewIs('technicians.index');
    }

    /** @test */
    public function it_can_search_technicians()
    {
        $technician = Technician::factory()->create(['first_name' => 'أحمد']);

        $response = $this->actingAs($this->user)
                         ->get(route('technicians.index', ['search' => 'أحمد']));

        $response->assertStatus(200);
        $response->assertViewIs('technicians.index');
    }

    /** @test */
    public function it_can_display_technician_workload()
    {
        $technician = Technician::factory()->create();

        $response = $this->actingAs($this->user)
                         ->get(route('technicians.workload', $technician));

        $response->assertStatus(200);
        $response->assertViewIs('technicians.workload');
        $response->assertViewHas('technician', $technician);
    }

    /** @test */
    public function it_can_display_technician_performance()
    {
        $technician = Technician::factory()->create();

        $response = $this->actingAs($this->user)
                         ->get(route('technicians.performance', $technician));

        $response->assertStatus(200);
        $response->assertViewIs('technicians.performance');
        $response->assertViewHas('technician', $technician);
    }
}
