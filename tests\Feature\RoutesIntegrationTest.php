<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\Repair;
use App\Models\Technician;
use App\Models\Customer;

class RoutesIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
    }

    /** @test */
    public function all_repair_routes_are_accessible()
    {
        $repair = Repair::factory()->create();

        // Test repair routes
        $routes = [
            ['GET', route('repairs.index')],
            ['GET', route('repairs.create')],
            ['GET', route('repairs.show', $repair)],
            ['GET', route('repairs.edit', $repair)],
            ['GET', route('repairs.print', $repair)],
        ];

        foreach ($routes as [$method, $url]) {
            $response = $this->actingAs($this->user)->call($method, $url);
            $this->assertTrue(
                $response->isSuccessful(),
                "Route {$method} {$url} failed with status {$response->getStatusCode()}"
            );
        }
    }

    /** @test */
    public function all_technician_routes_are_accessible()
    {
        $technician = Technician::factory()->create();

        // Test technician routes
        $routes = [
            ['GET', route('technicians.index')],
            ['GET', route('technicians.create')],
            ['GET', route('technicians.show', $technician)],
            ['GET', route('technicians.edit', $technician)],
            ['GET', route('technicians.schedule')],
            ['GET', route('technicians.workload', $technician)],
            ['GET', route('technicians.performance', $technician)],
        ];

        foreach ($routes as [$method, $url]) {
            $response = $this->actingAs($this->user)->call($method, $url);
            $this->assertTrue(
                $response->isSuccessful(),
                "Route {$method} {$url} failed with status {$response->getStatusCode()}"
            );
        }
    }

    /** @test */
    public function repair_crud_operations_work_correctly()
    {
        $customer = Customer::factory()->create();
        $technician = Technician::factory()->create();

        // Test CREATE
        $createData = [
            'customer_id' => $customer->id,
            'device_type' => 'mobile',
            'device_brand' => 'Samsung',
            'device_model' => 'Galaxy S21',
            'problem_description' => 'Screen is broken',
            'repair_type' => 'paid',
            'priority' => 'normal',
            'estimated_cost' => 150.00,
            'technician_id' => $technician->id,
        ];

        $response = $this->actingAs($this->user)
                         ->post(route('repairs.store'), $createData);

        $response->assertRedirect();
        $repair = Repair::where('device_brand', 'Samsung')->first();
        $this->assertNotNull($repair);

        // Test READ
        $response = $this->actingAs($this->user)
                         ->get(route('repairs.show', $repair));
        $response->assertSuccessful();

        // Test UPDATE
        $updateData = [
            'customer_id' => $customer->id,
            'device_type' => 'mobile',
            'device_brand' => 'iPhone',
            'device_model' => '13 Pro',
            'problem_description' => 'Battery issue',
            'repair_type' => 'warranty',
            'priority' => 'high',
            'estimated_cost' => 200.00,
        ];

        $response = $this->actingAs($this->user)
                         ->put(route('repairs.update', $repair), $updateData);

        $response->assertRedirect();
        $repair->refresh();
        $this->assertEquals('iPhone', $repair->device_brand);

        // Test DELETE
        $response = $this->actingAs($this->user)
                         ->delete(route('repairs.destroy', $repair));

        $response->assertRedirect();
        $this->assertSoftDeleted('repairs', ['id' => $repair->id]);
    }

    /** @test */
    public function technician_crud_operations_work_correctly()
    {
        // Test CREATE
        $createData = [
            'first_name' => 'أحمد',
            'last_name' => 'محمد',
            'email' => '<EMAIL>',
            'phone' => '0599123456',
            'department' => 'صيانة الهواتف',
            'position' => 'فني أول',
            'hire_date' => '2024-01-01',
            'skill_level' => 'advanced',
            'experience_years' => 5,
            'specializations' => ['Mobile Repair', 'Computer Repair']
        ];

        $response = $this->actingAs($this->user)
                         ->post(route('technicians.store'), $createData);

        $response->assertRedirect();
        $technician = Technician::where('email', '<EMAIL>')->first();
        $this->assertNotNull($technician);

        // Test READ
        $response = $this->actingAs($this->user)
                         ->get(route('technicians.show', $technician));
        $response->assertSuccessful();

        // Test UPDATE
        $updateData = [
            'first_name' => 'علي',
            'last_name' => 'أحمد',
            'email' => '<EMAIL>',
            'phone' => '0598765432',
            'department' => 'صيانة الحاسوب',
            'position' => 'فني خبير',
            'hire_date' => '2024-01-01',
            'skill_level' => 'expert',
            'experience_years' => 8,
            'specializations' => ['Computer Repair']
        ];

        $response = $this->actingAs($this->user)
                         ->put(route('technicians.update', $technician), $updateData);

        $response->assertRedirect();
        $technician->refresh();
        $this->assertEquals('علي', $technician->first_name);

        // Test DELETE
        $response = $this->actingAs($this->user)
                         ->delete(route('technicians.destroy', $technician));

        $response->assertRedirect();
        $this->assertSoftDeleted('technicians', ['id' => $technician->id]);
    }

    /** @test */
    public function repair_status_update_works_correctly()
    {
        $repair = Repair::factory()->create(['status' => 'pending']);

        $response = $this->actingAs($this->user)
                         ->post(route('repairs.update-status', $repair), [
                             'status' => 'in_progress',
                             'notes' => 'Started working on the repair'
                         ]);

        $response->assertRedirect();
        $repair->refresh();
        $this->assertEquals('in_progress', $repair->status);

        // Check if status history was created
        $this->assertDatabaseHas('repair_status_histories', [
            'repair_id' => $repair->id,
            'old_status' => 'pending',
            'new_status' => 'in_progress',
            'notes' => 'Started working on the repair'
        ]);
    }

    /** @test */
    public function technician_availability_update_works_correctly()
    {
        $technician = Technician::factory()->create(['availability' => 'available']);

        $response = $this->actingAs($this->user)
                         ->post(route('technicians.update-availability', $technician), [
                             'availability' => 'busy'
                         ]);

        $response->assertSuccessful();
        $technician->refresh();
        $this->assertEquals('busy', $technician->availability);
    }

    /** @test */
    public function repair_assignment_to_technician_works()
    {
        $technician = Technician::factory()->create(['availability' => 'available']);
        $repair = Repair::factory()->create(['technician_id' => null]);

        $response = $this->actingAs($this->user)
                         ->post(route('technicians.assign-repair', $technician), [
                             'repair_id' => $repair->id,
                             'notes' => 'Assigned for immediate repair'
                         ]);

        $response->assertSuccessful();
        $repair->refresh();
        $this->assertEquals($technician->id, $repair->technician_id);
    }

    /** @test */
    public function auto_assignment_of_repairs_works()
    {
        // Create technicians with different specializations
        $mobileTech = Technician::factory()->create([
            'availability' => 'available',
            'specializations' => ['Mobile Repair']
        ]);
        
        $computerTech = Technician::factory()->create([
            'availability' => 'available',
            'specializations' => ['Computer Repair']
        ]);

        // Create unassigned repairs
        $mobileRepair = Repair::factory()->create([
            'technician_id' => null,
            'device_type' => 'mobile'
        ]);
        
        $laptopRepair = Repair::factory()->create([
            'technician_id' => null,
            'device_type' => 'laptop'
        ]);

        $response = $this->actingAs($this->user)
                         ->post(route('technicians.auto-assign'), [
                             'repair_ids' => [$mobileRepair->id, $laptopRepair->id]
                         ]);

        $response->assertSuccessful();
        $response->assertJson(['success' => true]);
    }

    /** @test */
    public function filtering_and_searching_works_correctly()
    {
        // Create test data
        $pendingRepair = Repair::factory()->create(['status' => 'pending']);
        $completedRepair = Repair::factory()->create(['status' => 'completed']);
        $iphoneRepair = Repair::factory()->create(['device_brand' => 'iPhone']);

        // Test repair filtering by status
        $response = $this->actingAs($this->user)
                         ->get(route('repairs.index', ['status' => 'pending']));
        $response->assertSuccessful();

        // Test repair search
        $response = $this->actingAs($this->user)
                         ->get(route('repairs.index', ['search' => 'iPhone']));
        $response->assertSuccessful();

        // Create technician test data
        $activeTech = Technician::factory()->create(['status' => 'active']);
        $inactiveTech = Technician::factory()->create(['status' => 'inactive']);
        $ahmedTech = Technician::factory()->create(['first_name' => 'أحمد']);

        // Test technician filtering by status
        $response = $this->actingAs($this->user)
                         ->get(route('technicians.index', ['status' => 'active']));
        $response->assertSuccessful();

        // Test technician search
        $response = $this->actingAs($this->user)
                         ->get(route('technicians.index', ['search' => 'أحمد']));
        $response->assertSuccessful();
    }

    /** @test */
    public function dashboard_and_main_routes_are_accessible()
    {
        $routes = [
            ['GET', route('dashboard')],
            ['GET', '/'],
        ];

        foreach ($routes as [$method, $url]) {
            $response = $this->actingAs($this->user)->call($method, $url);
            $this->assertTrue(
                $response->isSuccessful(),
                "Route {$method} {$url} failed with status {$response->getStatusCode()}"
            );
        }
    }

    /** @test */
    public function authentication_is_required_for_protected_routes()
    {
        $repair = Repair::factory()->create();
        $technician = Technician::factory()->create();

        $protectedRoutes = [
            ['GET', route('repairs.index')],
            ['GET', route('repairs.create')],
            ['GET', route('repairs.show', $repair)],
            ['GET', route('technicians.index')],
            ['GET', route('technicians.create')],
            ['GET', route('technicians.show', $technician)],
            ['GET', route('technicians.schedule')],
        ];

        foreach ($protectedRoutes as [$method, $url]) {
            $response = $this->call($method, $url);
            $this->assertEquals(302, $response->getStatusCode(), "Route {$method} {$url} should redirect to login");
        }
    }
}
