<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة - {{ $sale->sale_number }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .company-info {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .receipt-body {
            padding: 30px;
        }
        
        .receipt-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .info-section h3 {
            color: #495057;
            font-size: 16px;
            margin-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 5px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .info-label {
            font-weight: 600;
            color: #6c757d;
        }
        
        .info-value {
            color: #495057;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .items-table th {
            background: #495057;
            color: white;
            padding: 15px 10px;
            text-align: right;
            font-weight: 600;
            font-size: 14px;
        }
        
        .items-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }
        
        .items-table tr:hover {
            background: #f8f9fa;
        }
        
        .item-name {
            font-weight: 600;
            color: #495057;
        }
        
        .item-details {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
        }
        
        .totals-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .total-row.grand-total {
            font-size: 18px;
            font-weight: 700;
            color: #495057;
            border-top: 2px solid #dee2e6;
            padding-top: 15px;
            margin-top: 15px;
        }
        
        .payment-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .payment-section h3 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .payment-method {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .receipt-footer {
            background: #495057;
            color: white;
            padding: 20px 30px;
            text-align: center;
        }
        
        .thank-you {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .footer-info {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .qr-section {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .qr-code {
            width: 120px;
            height: 120px;
            border: 2px solid #dee2e6;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 8px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .receipt-container {
                box-shadow: none;
                border-radius: 0;
            }
            
            .no-print {
                display: none;
            }
        }
        
        @media (max-width: 768px) {
            .receipt-info {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .items-table {
                font-size: 12px;
            }
            
            .items-table th,
            .items-table td {
                padding: 8px 5px;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Receipt Header -->
        <div class="receipt-header">
            <div class="company-name">{{ $location->getReceiptHeader() }}</div>
            <div class="company-info">
                @if($location->address)
                    <div>{{ $location->address }}</div>
                @endif
                @if($location->phone)
                    <div>هاتف: {{ $location->phone }} | </div>
                @endif
                @if($location->email)
                    <span>{{ $location->email }}</span>
                @endif
                @if($location->tax_number)
                    <div>الرقم الضريبي: {{ $location->tax_number }}</div>
                @endif
            </div>
        </div>

        <div class="receipt-body">
            <!-- Receipt Info -->
            <div class="receipt-info">
                <div class="info-section">
                    <h3>معلومات الفاتورة</h3>
                    <div class="info-item">
                        <span class="info-label">رقم الفاتورة:</span>
                        <span class="info-value">{{ $sale->sale_number }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">تاريخ الإصدار:</span>
                        <span class="info-value">{{ $sale->sale_date->format('Y-m-d H:i') }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">نوع المبيعة:</span>
                        <span class="info-value">{{ $sale->sale_type_label }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الحالة:</span>
                        <span class="status-badge status-{{ $sale->status }}">{{ $sale->status_label }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الكاشير:</span>
                        <span class="info-value">{{ $sale->user->name }}</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3>معلومات العميل</h3>
                    @if($customer)
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">{{ $customer->full_name }}</span>
                        </div>
                        @if($customer->phone)
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">{{ $customer->phone }}</span>
                        </div>
                        @endif
                        @if($customer->email)
                        <div class="info-item">
                            <span class="info-label">البريد الإلكتروني:</span>
                            <span class="info-value">{{ $customer->email }}</span>
                        </div>
                        @endif
                        @if($customer->address)
                        <div class="info-item">
                            <span class="info-label">العنوان:</span>
                            <span class="info-value">{{ $customer->address }}</span>
                        </div>
                        @endif
                    @else
                        <div class="info-item">
                            <span class="info-value">عميل مجهول</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Items Table -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 40%">الصنف</th>
                        <th style="width: 15%">الكمية</th>
                        <th style="width: 15%">سعر الوحدة</th>
                        <th style="width: 15%">الخصم</th>
                        <th style="width: 15%">المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($items as $item)
                    <tr>
                        <td>
                            <div class="item-name">{{ $item->item_name }}</div>
                            @if($item->item_code)
                                <div class="item-details">كود: {{ $item->item_code }}</div>
                            @endif
                            @if($item->item_description)
                                <div class="item-details">{{ $item->item_description }}</div>
                            @endif
                            @if($item->item_category)
                                <div class="item-details">التصنيف: {{ $item->item_category }}</div>
                            @endif
                        </td>
                        <td style="text-align: center">{{ $item->quantity }}</td>
                        <td style="text-align: left">{{ number_format($item->unit_price, 2) }} ريال</td>
                        <td style="text-align: left">{{ number_format($item->discount_amount, 2) }} ريال</td>
                        <td style="text-align: left; font-weight: 600;">{{ number_format($item->line_total, 2) }} ريال</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            <!-- Totals Section -->
            <div class="totals-section">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>{{ number_format($sale->subtotal, 2) }} ريال</span>
                </div>
                
                @if($sale->discount_amount > 0)
                <div class="total-row">
                    <span>إجمالي الخصم:</span>
                    <span style="color: #dc3545;">-{{ number_format($sale->discount_amount, 2) }} ريال</span>
                </div>
                @endif
                
                @if($sale->tax_amount > 0)
                <div class="total-row">
                    <span>ضريبة القيمة المضافة ({{ $location->getTaxRate() }}%):</span>
                    <span>{{ number_format($sale->tax_amount, 2) }} ريال</span>
                </div>
                @endif
                
                <div class="total-row grand-total">
                    <span>المجموع الكلي:</span>
                    <span>{{ number_format($sale->total_amount, 2) }} ريال</span>
                </div>
            </div>

            <!-- Payment Section -->
            <div class="payment-section">
                <h3>تفاصيل الدفع</h3>
                @foreach($payments as $payment)
                <div class="payment-method">
                    <span>
                        {{ $payment->payment_method_label }}
                        @if($payment->reference_number)
                            ({{ $payment->reference_number }})
                        @endif
                    </span>
                    <span>
                        {{ number_format($payment->amount, 2) }} ريال
                        <span class="status-badge status-{{ $payment->status }}">{{ $payment->status_label }}</span>
                    </span>
                </div>
                @endforeach
                
                <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                    <div class="payment-method" style="font-weight: 600;">
                        <span>إجمالي المدفوع:</span>
                        <span>{{ number_format($sale->paid_amount, 2) }} ريال</span>
                    </div>
                    
                    @if($sale->change_amount > 0)
                    <div class="payment-method" style="color: #28a745; font-weight: 600;">
                        <span>المبلغ المرتجع:</span>
                        <span>{{ number_format($sale->change_amount, 2) }} ريال</span>
                    </div>
                    @endif
                    
                    @if($sale->remaining_amount > 0)
                    <div class="payment-method" style="color: #dc3545; font-weight: 600;">
                        <span>المبلغ المتبقي:</span>
                        <span>{{ number_format($sale->remaining_amount, 2) }} ريال</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="qr-section">
                <div class="qr-code">
                    <span style="font-size: 12px; color: #6c757d;">QR CODE</span>
                </div>
                <div style="font-size: 12px; color: #6c757d;">
                    امسح الكود للتحقق من صحة الفاتورة
                </div>
            </div>

            @if($sale->notes)
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <strong>ملاحظات:</strong> {{ $sale->notes }}
            </div>
            @endif
        </div>

        <!-- Receipt Footer -->
        <div class="receipt-footer">
            <div class="thank-you">{{ $location->getReceiptFooter() }}</div>
            <div class="footer-info">
                <div>تاريخ الطباعة: {{ $generated_at->format('Y-m-d H:i:s') }}</div>
                <div style="margin-top: 5px;">سياسة الاسترداد: يمكن استرداد المنتجات خلال 7 أيام من تاريخ الشراء</div>
            </div>
        </div>
    </div>

    <!-- Print Button -->
    <div class="no-print" style="text-align: center; margin: 20px 0;">
        <button onclick="window.print()" style="background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 16px;">
            طباعة الفاتورة
        </button>
    </div>
</body>
</html>
