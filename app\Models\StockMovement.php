<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockMovement extends Model
{
    use HasFactory;

    protected $fillable = [
        'part_id',
        'type',
        'quantity',
        'old_quantity',
        'new_quantity',
        'reason',
        'reference_type',
        'reference_id',
        'user_id',
        'notes'
    ];

    protected $casts = [
        'quantity' => 'integer',
        'old_quantity' => 'integer',
        'new_quantity' => 'integer'
    ];

    // Relationships
    public function part()
    {
        return $this->belongsTo(Part::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function reference()
    {
        return $this->morphTo();
    }

    // Accessors
    public function getTypeBadgeAttribute()
    {
        $types = [
            'in' => ['class' => 'badge-success', 'text' => 'دخول'],
            'out' => ['class' => 'badge-danger', 'text' => 'خروج'],
            'adjustment' => ['class' => 'badge-warning', 'text' => 'تعديل'],
            'transfer' => ['class' => 'badge-info', 'text' => 'نقل']
        ];

        return $types[$this->type] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getQuantityChangeAttribute()
    {
        return $this->new_quantity - $this->old_quantity;
    }

    // Scopes
    public function scopeIn($query)
    {
        return $query->where('type', 'in');
    }

    public function scopeOut($query)
    {
        return $query->where('type', 'out');
    }

    public function scopeAdjustment($query)
    {
        return $query->where('type', 'adjustment');
    }

    public function scopeByPart($query, $partId)
    {
        return $query->where('part_id', $partId);
    }
}
