<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use App\Http\Middleware\LoginRateLimit;

class AuthController extends Controller
{
    /**
     * عرض صفحة تسجيل الدخول
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * معالجة تسجيل الدخول مع الحماية المتقدمة
     */
    public function login(Request $request)
    {
        // التحقق من Rate Limiting يتم من خلال middleware

        $request->validate([
            'email' => 'required|email|max:255',
            'password' => 'required|min:6|max:255',
        ], [
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.max' => 'البريد الإلكتروني طويل جداً',
            'password.required' => 'كلمة المرور مطلوبة',
            'password.min' => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
            'password.max' => 'كلمة المرور طويلة جداً',
        ]);

        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        if (Auth::attempt($credentials, $remember)) {
            $request->session()->regenerate();

            // مسح محاولات تسجيل الدخول الفاشلة - تأكيد مضاعف
            LoginRateLimit::clearFailedAttempts($request);

            // تسجيل نجاح تسجيل الدخول في log
            Log::info('تسجيل دخول ناجح: ' . $request->email . ' - IP: ' . $request->ip());

            // التحقق من أن المستخدم نشط
            if (!Auth::user()->is_active) {
                Auth::logout();
                throw ValidationException::withMessages([
                    'email' => 'حسابك غير مفعل. يرجى التواصل مع المدير.',
                ]);
            }

            // تحديث آخر تسجيل دخول
            $user = Auth::user();
            $user->update([
                'last_login_at' => now(),
                'login_count' => ($user->login_count ?? 0) + 1
            ]);

            return redirect()->intended(route('dashboard'));
        }

        // تسجيل محاولة تسجيل دخول فاشلة
        LoginRateLimit::recordFailedAttempt($request);

        throw ValidationException::withMessages([
            'email' => 'بيانات تسجيل الدخول غير صحيحة.',
        ]);
    }

    /**
     * تسجيل الخروج
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login');
    }

    /**
     * عرض صفحة نسيت كلمة المرور
     */
    public function showForgotPasswordForm()
    {
        return view('auth.forgot-password');
    }

    /**
     * إرسال رابط إعادة تعيين كلمة المرور
     */
    public function sendResetLinkEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email|exists:users,email',
        ], [
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.exists' => 'البريد الإلكتروني غير موجود في النظام',
        ]);

        // هنا يمكن إضافة منطق إرسال البريد الإلكتروني
        // لكن سنكتفي برسالة نجاح للآن

        return back()->with('status', 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.');
    }
}
