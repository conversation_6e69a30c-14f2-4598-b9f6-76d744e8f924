<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Company;
use App\Models\Location;
use App\Models\Role;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الشركة الافتراضية
        $company = Company::create([
            'name' => 'مركز الطارق لصيانة الحاسوب والموبايل',
            'name_en' => 'Al-Tareq Computer & Mobile Repair Center',
            'email' => '<EMAIL>',
            'phone' => '+970-2-123-4567',
            'address' => 'شارع عمر المختار، رام الله',
            'city' => 'رام الله',
            'country' => 'فلسطين',
            'currency' => 'ILS',
            'timezone' => 'Asia/Gaza',
            'fiscal_year_start' => '2024-01-01',
            'default_profit_margin' => 25.00,
            'is_active' => true,
            'settings' => [
                'locale' => 'ar',
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i',
                'invoice_prefix' => 'INV',
                'purchase_prefix' => 'PUR',
                'maintenance_prefix' => 'MNT'
            ]
        ]);

        // إنشاء الأدوار الافتراضية
        Role::createDefaultRoles($company->id);

        // الحصول على الأدوار
        $adminRole = Role::where('company_id', $company->id)->where('name_en', 'System Administrator')->first();
        $managerRole = Role::where('company_id', $company->id)->where('name_en', 'Manager')->first();
        $cashierRole = Role::where('company_id', $company->id)->where('name_en', 'Cashier')->first();
        $technicianRole = Role::where('company_id', $company->id)->where('name_en', 'Technician')->first();

        // إنشاء الموقع الافتراضي
        $mainLocation = Location::create([
            'company_id' => $company->id,
            'name' => 'الفرع الرئيسي',
            'name_en' => 'Main Branch',
            'type' => 'store',
            'code' => 'MAIN001',
            'email' => '<EMAIL>',
            'phone' => '+970-2-123-4567',
            'address' => 'شارع عمر المختار، رام الله',
            'city' => 'رام الله',
            'country' => 'فلسطين',
            'is_active' => true,
            'is_default' => true,
            'invoice_settings' => [
                'template' => 'default',
                'show_logo' => true,
                'show_company_details' => true,
                'footer_text' => 'شكراً لتعاملكم معنا'
            ],
            'pos_settings' => [
                'default_customer' => 'Walk-In Customer',
                'auto_print' => false,
                'receipt_template' => 'thermal'
            ]
        ]);

        // إنشاء المستخدمين
        $admin = User::create([
            'company_id' => $company->id,
            'role_id' => $adminRole->id,
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '0599123456',
            'employee_id' => 'EMP001',
            'department' => 'الإدارة',
            'hire_date' => now()->subYear(),
            'is_active' => true,
            'email_verified_at' => now()
        ]);

        $manager = User::create([
            'company_id' => $company->id,
            'role_id' => $managerRole->id,
            'name' => 'أحمد محمد علي',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '0599234567',
            'employee_id' => 'EMP002',
            'department' => 'المبيعات',
            'hire_date' => now()->subMonths(6),
            'salary' => 3000.00,
            'commission_rate' => 2.5,
            'is_active' => true,
            'email_verified_at' => now()
        ]);

        $cashier = User::create([
            'company_id' => $company->id,
            'role_id' => $cashierRole->id,
            'name' => 'فاطمة أحمد حسن',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '0599345678',
            'employee_id' => 'EMP003',
            'department' => 'المبيعات',
            'hire_date' => now()->subMonths(3),
            'salary' => 2000.00,
            'commission_rate' => 1.5,
            'is_active' => true,
            'email_verified_at' => now()
        ]);

        $technician = User::create([
            'company_id' => $company->id,
            'role_id' => $technicianRole->id,
            'name' => 'علي حسين محمد',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '0599456789',
            'employee_id' => 'EMP004',
            'department' => 'الصيانة',
            'hire_date' => now()->subMonths(8),
            'salary' => 2500.00,
            'is_active' => true,
            'email_verified_at' => now()
        ]);

        // ربط المستخدمين بالموقع الرئيسي
        $mainLocation->users()->attach([
            $admin->id => ['is_default' => true],
            $manager->id => ['is_default' => true],
            $cashier->id => ['is_default' => true],
            $technician->id => ['is_default' => true]
        ]);

        // تحديث مدير الموقع
        $mainLocation->update(['manager_id' => $manager->id]);
    }
}
