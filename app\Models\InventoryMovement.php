<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryMovement extends Model
{
    use HasFactory;

    protected $fillable = [
        'inventory_id',
        'type',
        'quantity',
        'reason',
        'user_id',
        'reference_id',
        'reference_type',
        'notes'
    ];

    protected $casts = [
        'quantity' => 'integer'
    ];

    /**
     * العلاقة مع المخزون
     */
    public function inventory()
    {
        return $this->belongsTo(Inventory::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع المرجع (polymorphic)
     */
    public function reference()
    {
        return $this->morphTo();
    }

    /**
     * الحصول على نوع الحركة مترجم
     */
    public function getTypeNameAttribute()
    {
        $types = [
            'in' => 'إدخال',
            'out' => 'إخراج',
            'transfer' => 'نقل',
            'adjustment' => 'تعديل'
        ];

        return $types[$this->type] ?? $this->type;
    }

    /**
     * scope للحركات الواردة
     */
    public function scopeIncoming($query)
    {
        return $query->where('type', 'in');
    }

    /**
     * scope للحركات الصادرة
     */
    public function scopeOutgoing($query)
    {
        return $query->where('type', 'out');
    }

    /**
     * scope لحركات النقل
     */
    public function scopeTransfers($query)
    {
        return $query->where('type', 'transfer');
    }

    /**
     * scope للتعديلات
     */
    public function scopeAdjustments($query)
    {
        return $query->where('type', 'adjustment');
    }

    /**
     * scope للحركات في فترة معينة
     */
    public function scopeInPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * تسجيل حركة مخزون جديدة
     */
    public static function logMovement($inventoryId, $type, $quantity, $reason, $reference = null, $notes = null)
    {
        return self::create([
            'inventory_id' => $inventoryId,
            'type' => $type,
            'quantity' => $quantity,
            'reason' => $reason,
            'user_id' => auth()->id(),
            'reference_id' => $reference ? $reference->id : null,
            'reference_type' => $reference ? get_class($reference) : null,
            'notes' => $notes
        ]);
    }

    /**
     * الحصول على تقرير حركات المخزون
     */
    public static function getMovementReport($filters = [])
    {
        $query = self::with(['inventory.product', 'inventory.location', 'user']);

        if (isset($filters['product_id'])) {
            $query->whereHas('inventory', function ($q) use ($filters) {
                $q->where('product_id', $filters['product_id']);
            });
        }

        if (isset($filters['location_id'])) {
            $query->whereHas('inventory', function ($q) use ($filters) {
                $q->where('location_id', $filters['location_id']);
            });
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * الحصول على إحصائيات حركات المخزون
     */
    public static function getMovementStats($period = 30)
    {
        $startDate = now()->subDays($period);

        return [
            'total_movements' => self::where('created_at', '>=', $startDate)->count(),
            'incoming_quantity' => self::where('created_at', '>=', $startDate)
                                      ->where('type', 'in')
                                      ->sum('quantity'),
            'outgoing_quantity' => self::where('created_at', '>=', $startDate)
                                      ->where('type', 'out')
                                      ->sum('quantity'),
            'transfers_count' => self::where('created_at', '>=', $startDate)
                                    ->where('type', 'transfer')
                                    ->count(),
            'adjustments_count' => self::where('created_at', '>=', $startDate)
                                      ->where('type', 'adjustment')
                                      ->count()
        ];
    }
}
