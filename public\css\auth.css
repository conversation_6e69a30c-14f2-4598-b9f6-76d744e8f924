/* ===================================
   تصميم واجهة تسجيل الدخول المتطورة
   Auth CSS - Version 1.0
   =================================== */

/* متغيرات الألوان */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --shadow-light: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* الوضع الليلي */
[data-bs-theme="dark"] {
    --glass-bg: rgba(30, 41, 59, 0.4);
    --glass-border: rgba(148, 163, 184, 0.2);
    --shadow-light: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

/* الخطوط العربية */
body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* خلفية متحركة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* حاوي التسجيل */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

/* بطاقة التسجيل */
.auth-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 24px;
    box-shadow: var(--shadow-light), var(--shadow-glow);
    max-width: 450px;
    width: 100%;
    overflow: hidden;
    position: relative;
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* خط علوي متوهج */
.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

/* رأس البطاقة */
.auth-header {
    text-align: center;
    padding: 3rem 2rem 2rem;
    position: relative;
}

/* حاوي الشعار */
.logo-container {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 
        0 10px 25px rgba(37, 99, 235, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* تأثير اللمعان */
.logo-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* أيقونة الشعار */
.logo-icon {
    font-size: 2rem;
    color: white;
    z-index: 1;
}

/* عنوان التطبيق */
.auth-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* العنوان الفرعي */
.auth-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    margin-bottom: 0;
}

/* نموذج التسجيل */
.auth-form {
    padding: 0 2rem 3rem;
}

/* حقول الإدخال */
.form-floating {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    padding: 1rem 1rem 1rem 3rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
    color: white;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* أيقونات الحقول */
.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
    z-index: 5;
}

/* زر إظهار/إخفاء كلمة المرور */
.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    z-index: 5;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: rgba(255, 255, 255, 0.8);
}

/* مربع التذكر */
.form-check {
    margin-bottom: 1.5rem;
}

.form-check-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
}

/* زر تسجيل الدخول */
.btn-login {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    padding: 1rem 2rem;
    width: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.4);
}

.btn-login:active {
    transform: translateY(0);
}

/* رابط نسيان كلمة المرور */
.forgot-password {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: white;
    text-decoration: underline;
}

/* الحسابات التجريبية */
.demo-accounts {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
}

.demo-title {
    color: white;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.demo-account {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.demo-account:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.demo-account:last-child {
    margin-bottom: 0;
}

.demo-role {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    display: block;
}

.demo-credentials {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

/* زر تبديل الوضع */
.theme-toggle {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 0.5rem 1rem;
    color: white;
    text-decoration: none;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    z-index: 1000;
    cursor: pointer;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.theme-toggle:focus {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* رسائل الخطأ */
.alert {
    background: rgba(220, 38, 38, 0.1);
    border: 1px solid rgba(220, 38, 38, 0.3);
    border-radius: 12px;
    color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

/* نص التذييل */
.footer-text {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
    z-index: 100;
    width: 100%;
    pointer-events: none;
}

/* دعم RTL */
[dir="rtl"] .input-icon {
    left: auto;
    right: 1rem;
}

[dir="rtl"] .password-toggle {
    right: auto;
    left: 1rem;
}

[dir="rtl"] .form-control {
    padding: 1rem 3rem 1rem 1rem;
}

/* التصميم المتجاوب */
@media (max-width: 576px) {
    .auth-container {
        padding: 1rem;
    }
    
    .auth-header {
        padding: 2rem 1.5rem 1.5rem;
    }
    
    .auth-form {
        padding: 0 1.5rem 2rem;
    }
    
    .theme-toggle {
        top: 1rem;
        right: 1rem;
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}

/* تحميل الزر */
.btn-login.loading {
    pointer-events: none;
}

.btn-login.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
