<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use App\Http\Middleware\LoginRateLimit;
use Illuminate\Http\Request;

class TestRateLimit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:rate-limit {action=status} {--ip=127.0.0.1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'اختبار وإدارة نظام Rate Limiting';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');
        $ip = $this->option('ip');

        switch ($action) {
            case 'status':
                $this->showStatus($ip);
                break;
            case 'clear':
                $this->clearAttempts($ip);
                break;
            case 'simulate':
                $this->simulateAttempts($ip);
                break;
            case 'test':
                $this->runFullTest();
                break;
            default:
                $this->error('الإجراء غير صحيح. استخدم: status, clear, simulate, test');
        }
    }

    private function showStatus($ip)
    {
        $this->info("🔍 فحص حالة Rate Limiting للـ IP: {$ip}");
        $this->line('');

        $key = 'login_attempts:' . $ip;
        $lockoutKey = $key . ':lockout_until';

        $attempts = Cache::get($key, 0);
        $lockoutUntil = Cache::get($lockoutKey);

        $this->table(['المعلومة', 'القيمة'], [
            ['عدد المحاولات', $attempts],
            ['الحد الأقصى', config('auth.login_rate_limit.max_attempts', 5)],
            ['مدة الحظر', config('auth.login_rate_limit.decay_minutes', 15) . ' دقيقة'],
            ['محظور حتى', $lockoutUntil ? $lockoutUntil->format('Y-m-d H:i:s') : 'غير محظور'],
            ['Cache Key', $key],
            ['Lockout Key', $lockoutKey]
        ]);

        if ($attempts >= config('auth.login_rate_limit.max_attempts', 5)) {
            if ($lockoutUntil && now()->lessThan($lockoutUntil)) {
                $remaining = now()->diffInMinutes($lockoutUntil);
                $this->error("🚫 IP محظور! الوقت المتبقي: {$remaining} دقيقة");
            } else {
                $this->warn("⚠️ تم تجاوز الحد الأقصى ولكن انتهت مدة الحظر");
            }
        } else {
            $this->info("✅ IP غير محظور");
        }
    }

    private function clearAttempts($ip)
    {
        $this->info("🧹 مسح محاولات تسجيل الدخول للـ IP: {$ip}");

        $request = new Request();
        $request->server->set('REMOTE_ADDR', $ip);

        LoginRateLimit::clearFailedAttempts($request);

        $this->info("✅ تم مسح المحاولات بنجاح");
        $this->showStatus($ip);
    }

    private function simulateAttempts($ip)
    {
        $this->info("🎭 محاكاة محاولات فاشلة للـ IP: {$ip}");

        $request = new Request();
        $request->server->set('REMOTE_ADDR', $ip);

        $maxAttempts = config('auth.login_rate_limit.max_attempts', 5);

        for ($i = 1; $i <= $maxAttempts + 1; $i++) {
            LoginRateLimit::recordFailedAttempt($request);
            $this->line("محاولة {$i}: تم تسجيل محاولة فاشلة");
        }

        $this->warn("⚠️ تم تسجيل {$maxAttempts} محاولة فاشلة + 1 إضافية");
        $this->showStatus($ip);
    }

    private function runFullTest()
    {
        $this->info("🧪 تشغيل اختبار شامل لنظام Rate Limiting");
        $this->line('');

        $testIP = '*************'; // IP اختبار

        // 1. مسح البيانات السابقة
        $this->line("1️⃣ مسح البيانات السابقة...");
        $this->clearAttempts($testIP);

        // 2. اختبار المحاولات الفاشلة
        $this->line("2️⃣ اختبار المحاولات الفاشلة...");
        $this->simulateAttempts($testIP);

        // 3. اختبار مسح المحاولات
        $this->line("3️⃣ اختبار مسح المحاولات...");
        $this->clearAttempts($testIP);

        // 4. فحص إعدادات النظام
        $this->line("4️⃣ فحص إعدادات النظام...");
        $this->checkSystemConfig();

        $this->info("✅ انتهى الاختبار الشامل");
    }

    private function checkSystemConfig()
    {
        $this->table(['الإعداد', 'القيمة'], [
            ['Max Attempts', config('auth.login_rate_limit.max_attempts', 'غير محدد')],
            ['Decay Minutes', config('auth.login_rate_limit.decay_minutes', 'غير محدد')],
            ['Rate Limit Enabled', config('auth.login_rate_limit.enabled', 'غير محدد') ? 'نعم' : 'لا'],
            ['Cache Driver', config('cache.default')],
            ['Cache Store', config('cache.stores.' . config('cache.default') . '.driver', 'غير محدد')]
        ]);

        // فحص middleware
        $kernel = app(\Illuminate\Contracts\Http\Kernel::class);
        $middlewares = $kernel->getMiddleware();
        
        $this->line('');
        $this->info('Middleware المسجلة:');
        foreach ($middlewares as $alias => $class) {
            if (str_contains($class, 'LoginRateLimit')) {
                $this->line("✅ {$alias}: {$class}");
            }
        }
    }
}
