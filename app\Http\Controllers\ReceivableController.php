<?php

namespace App\Http\Controllers;

use App\Models\Receivable;
use App\Models\Customer;
use App\Services\AccountingService;
use Illuminate\Http\Request;

class ReceivableController extends Controller
{
    protected $accountingService;

    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }

    /**
     * Display receivables
     */
    public function index(Request $request)
    {
        $query = Receivable::with(['customer', 'sale', 'repair']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by customer
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // Filter by due date
        if ($request->filled('due_date_from')) {
            $query->where('due_date', '>=', $request->due_date_from);
        }
        if ($request->filled('due_date_to')) {
            $query->where('due_date', '<=', $request->due_date_to);
        }

        // Filter overdue
        if ($request->get('overdue') === '1') {
            $query->overdue();
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('first_name', 'like', "%{$search}%")
                                   ->orWhere('last_name', 'like', "%{$search}%");
                  });
            });
        }

        $receivables = $query->orderBy('due_date', 'asc')
                            ->paginate(20);

        // Summary statistics
        $totalPending = Receivable::whereIn('status', ['pending', 'partial'])->sum('remaining_amount');
        $totalOverdue = Receivable::overdue()->sum('remaining_amount');
        $totalPaid = Receivable::where('status', 'paid')->sum('original_amount');

        $customers = Customer::where('is_active', true)->orderBy('first_name')->get();

        return view('accounting.receivables.index', compact(
            'receivables', 'totalPending', 'totalOverdue', 'totalPaid', 'customers'
        ));
    }

    /**
     * Show receivable details
     */
    public function show(Receivable $receivable)
    {
        $receivable->load(['customer', 'sale', 'repair']);
        
        return view('accounting.receivables.show', compact('receivable'));
    }

    /**
     * Record payment for receivable
     */
    public function recordPayment(Request $request, Receivable $receivable)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . $receivable->remaining_amount,
            'payment_method' => 'required|in:cash,card,bank_transfer',
            'notes' => 'nullable|string|max:255',
        ]);

        try {
            // Record payment in receivable
            $receivable->recordPayment(
                $request->amount,
                $request->payment_method,
                $request->notes
            );

            // Create accounting entry
            $this->accountingService->createPaymentReceivedEntry(
                $request->amount,
                $receivable->customer_id,
                $request->payment_method,
                'تحصيل من فاتورة ' . $receivable->invoice_number
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل الدفعة بنجاح',
                'receivable' => [
                    'paid_amount' => $receivable->fresh()->paid_amount,
                    'remaining_amount' => $receivable->fresh()->remaining_amount,
                    'status' => $receivable->fresh()->status,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
