<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="مركز الصيانة">
    <title>تطبيق مركز الصيانة</title>

    <!-- PWA Meta Tags -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#3B82F6">
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Custom Mobile Styles -->
    <style>
        /* Hide scrollbars but keep functionality */
        ::-webkit-scrollbar { width: 0px; background: transparent; }

        /* Mobile-first responsive design */
        .mobile-container { max-width: 100vw; overflow-x: hidden; }
        .mobile-card { border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .mobile-button { min-height: 44px; border-radius: 8px; }

        /* Touch-friendly spacing */
        .touch-target { min-height: 44px; min-width: 44px; }

        /* Safe area for notched devices */
        .safe-area-top { padding-top: env(safe-area-inset-top); }
        .safe-area-bottom { padding-bottom: env(safe-area-inset-bottom); }

        /* Smooth animations */
        .slide-up { transform: translateY(100%); transition: transform 0.3s ease-out; }
        .slide-up.active { transform: translateY(0); }
    </style>
</head>
<body class="bg-gray-50 mobile-container" x-data="mobileApp()">
    <!-- Status Bar Spacer -->
    <div class="safe-area-top bg-blue-600"></div>

    <!-- App Header -->
    <header class="bg-blue-600 text-white px-4 py-3 shadow-lg">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <button @click="toggleSidebar()" class="touch-target flex items-center justify-center ml-2">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <h1 class="text-lg font-bold" x-text="currentPage.title">مركز الصيانة</h1>
            </div>
            <div class="flex items-center space-x-2 space-x-reverse">
                <button @click="showNotifications()" class="touch-target flex items-center justify-center relative">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                    </svg>
                    <span x-show="notifications.unread > 0" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" x-text="notifications.unread"></span>
                </button>
                <button @click="showProfile()" class="touch-target flex items-center justify-center">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-sm font-bold">م</span>
                    </div>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-y-auto pb-20">
        <!-- Dashboard View -->
        <div x-show="currentPage.id === 'dashboard'" class="p-4 space-y-4">
            <!-- Quick Stats -->
            <div class="grid grid-cols-2 gap-4">
                <div class="mobile-card bg-white p-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center ml-3">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">طلبات اليوم</p>
                            <p class="text-lg font-bold text-gray-900" x-text="stats.todayRepairs">12</p>
                        </div>
                    </div>
                </div>

                <div class="mobile-card bg-white p-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center ml-3">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">مكتملة</p>
                            <p class="text-lg font-bold text-gray-900" x-text="stats.completed">8</p>
                        </div>
                    </div>
                </div>

                <div class="mobile-card bg-white p-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center ml-3">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">قيد الانتظار</p>
                            <p class="text-lg font-bold text-gray-900" x-text="stats.pending">23</p>
                        </div>
                    </div>
                </div>

                <div class="mobile-card bg-white p-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center ml-3">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">إيرادات اليوم</p>
                            <p class="text-lg font-bold text-gray-900">₪<span x-text="stats.revenue">2,450</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mobile-card bg-white p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">إجراءات سريعة</h3>
                <div class="grid grid-cols-2 gap-3">
                    <button @click="navigateTo('new-repair')" class="mobile-button bg-blue-600 text-white p-3 flex flex-col items-center">
                        <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span class="text-sm">طلب جديد</span>
                    </button>

                    <button @click="navigateTo('scan-qr')" class="mobile-button bg-green-600 text-white p-3 flex flex-col items-center">
                        <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                        </svg>
                        <span class="text-sm">مسح QR</span>
                    </button>

                    <button @click="navigateTo('search')" class="mobile-button bg-purple-600 text-white p-3 flex flex-col items-center">
                        <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <span class="text-sm">البحث</span>
                    </button>

                    <button @click="navigateTo('reports')" class="mobile-button bg-orange-600 text-white p-3 flex flex-col items-center">
                        <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span class="text-sm">التقارير</span>
                    </button>
                </div>
            </div>

            <!-- Recent Repairs -->
            <div class="mobile-card bg-white p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">آخر الطلبات</h3>
                <div class="space-y-3">
                    <template x-for="repair in recentRepairs" :key="repair.id">
                        <div @click="viewRepair(repair.id)" class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center ml-3" :class="{
                                'bg-green-100': repair.status === 'completed',
                                'bg-yellow-100': repair.status === 'in_progress',
                                'bg-blue-100': repair.status === 'pending'
                            }">
                                <svg class="w-5 h-5" :class="{
                                    'text-green-600': repair.status === 'completed',
                                    'text-yellow-600': repair.status === 'in_progress',
                                    'text-blue-600': repair.status === 'pending'
                                }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900" x-text="repair.customer"></p>
                                <p class="text-xs text-gray-500" x-text="repair.device"></p>
                                <p class="text-xs text-gray-400" x-text="repair.date"></p>
                            </div>
                            <div class="text-left">
                                <span class="text-xs px-2 py-1 rounded-full" :class="{
                                    'bg-green-100 text-green-800': repair.status === 'completed',
                                    'bg-yellow-100 text-yellow-800': repair.status === 'in_progress',
                                    'bg-blue-100 text-blue-800': repair.status === 'pending'
                                }" x-text="getStatusText(repair.status)"></span>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- Other Views (Repairs, Search, etc.) -->
        <div x-show="currentPage.id === 'repairs'" class="p-4">
            <h2 class="text-xl font-bold text-gray-900 mb-4">طلبات الصيانة</h2>
            <!-- Repairs content will go here -->
        </div>

        <div x-show="currentPage.id === 'search'" class="p-4">
            <h2 class="text-xl font-bold text-gray-900 mb-4">البحث</h2>
            <!-- Search content will go here -->
        </div>
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 safe-area-bottom">
        <div class="grid grid-cols-4 h-16">
            <button @click="navigateTo('dashboard')" class="flex flex-col items-center justify-center" :class="currentPage.id === 'dashboard' ? 'text-blue-600' : 'text-gray-400'">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4M16 5v4"></path>
                </svg>
                <span class="text-xs">الرئيسية</span>
            </button>

            <button @click="navigateTo('repairs')" class="flex flex-col items-center justify-center" :class="currentPage.id === 'repairs' ? 'text-blue-600' : 'text-gray-400'">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-xs">الصيانة</span>
            </button>

            <button @click="navigateTo('search')" class="flex flex-col items-center justify-center" :class="currentPage.id === 'search' ? 'text-blue-600' : 'text-gray-400'">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <span class="text-xs">البحث</span>
            </button>

            <button @click="navigateTo('profile')" class="flex flex-col items-center justify-center" :class="currentPage.id === 'profile' ? 'text-blue-600' : 'text-gray-400'">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs">الملف الشخصي</span>
            </button>
        </div>
    </nav>

    <!-- Sidebar Overlay -->
    <div x-show="sidebarOpen" @click="toggleSidebar()" class="fixed inset-0 bg-black bg-opacity-50 z-40" x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"></div>

    <!-- Sidebar -->
    <div x-show="sidebarOpen" class="fixed inset-y-0 right-0 w-64 bg-white shadow-xl z-50 transform" x-transition:enter="transition ease-in-out duration-300 transform" x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out duration-300 transform" x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full">
        <div class="safe-area-top bg-blue-600"></div>
        <div class="bg-blue-600 text-white p-4">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-bold">القائمة</h2>
                <button @click="toggleSidebar()" class="touch-target">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="p-4 space-y-2">
            <a href="#" class="block p-3 rounded-lg hover:bg-gray-100">لوحة التحكم</a>
            <a href="#" class="block p-3 rounded-lg hover:bg-gray-100">طلبات الصيانة</a>
            <a href="#" class="block p-3 rounded-lg hover:bg-gray-100">العملاء</a>
            <a href="#" class="block p-3 rounded-lg hover:bg-gray-100">التقارير</a>
            <a href="#" class="block p-3 rounded-lg hover:bg-gray-100">الإعدادات</a>
        </div>
    </div>

    <script>
        function mobileApp() {
            return {
                currentPage: { id: 'dashboard', title: 'لوحة التحكم' },
                sidebarOpen: false,
                notifications: { unread: 3 },
                stats: {
                    todayRepairs: 12,
                    completed: 8,
                    pending: 23,
                    revenue: '2,450'
                },
                recentRepairs: [
                    { id: 1, customer: 'أحمد محمد', device: 'iPhone 13', status: 'completed', date: 'اليوم 14:30' },
                    { id: 2, customer: 'سارة أحمد', device: 'Samsung S21', status: 'in_progress', date: 'اليوم 12:15' },
                    { id: 3, customer: 'محمد علي', device: 'iPad Pro', status: 'pending', date: 'أمس 16:45' }
                ],

                navigateTo(pageId) {
                    const pages = {
                        'dashboard': { id: 'dashboard', title: 'لوحة التحكم' },
                        'repairs': { id: 'repairs', title: 'طلبات الصيانة' },
                        'search': { id: 'search', title: 'البحث' },
                        'profile': { id: 'profile', title: 'الملف الشخصي' },
                        'new-repair': { id: 'new-repair', title: 'طلب صيانة جديد' },
                        'scan-qr': { id: 'scan-qr', title: 'مسح QR' },
                        'reports': { id: 'reports', title: 'التقارير' }
                    };

                    this.currentPage = pages[pageId] || pages['dashboard'];
                    this.sidebarOpen = false;
                },

                toggleSidebar() {
                    this.sidebarOpen = !this.sidebarOpen;
                },

                showNotifications() {
                    alert('عرض الإشعارات');
                },

                showProfile() {
                    this.navigateTo('profile');
                },

                viewRepair(id) {
                    alert(`عرض تفاصيل الطلب ${id}`);
                },

                getStatusText(status) {
                    const statuses = {
                        'completed': 'مكتمل',
                        'in_progress': 'قيد التنفيذ',
                        'pending': 'في الانتظار'
                    };
                    return statuses[status] || status;
                }
            }
        }

        // PWA Installation
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            // Show install button
        });

        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>
</html>