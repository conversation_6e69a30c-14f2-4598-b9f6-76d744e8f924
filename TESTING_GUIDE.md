# 🧪 دليل اختبار النظام الشامل

## 🎯 الهدف من الاختبار
التأكد من أن جميع مكونات نظام إدارة مراكز الصيانة تعمل بشكل صحيح، خاصة بعد إصلاح مشكلة route الجدولة.

## 📋 قائمة الاختبارات

### المرحلة 1: اختبارات أساسية (Critical Tests)
- [ ] تشغيل الخادم
- [ ] اختبار الاتصال بقاعدة البيانات
- [ ] اختبار routes الأساسية
- [ ] اختبار تسجيل الدخول

### المرحلة 2: اختبار Routes الجدولة (Schedule Routes)
- [ ] `/test-schedule` - اختبار بسيط
- [ ] `/simple-schedule` - اختبار view فارغ
- [ ] `/technicians/schedule` - الجدولة الكاملة

### المرحلة 3: اختبار إدارة الفنيين (Technicians)
- [ ] قائمة الفنيين
- [ ] إضافة فني جديد
- [ ] عرض تفاصيل فني
- [ ] تعديل فني
- [ ] تحديث حالة التوفر

### المرحلة 4: اختبار إدارة طلبات الصيانة (Repairs)
- [ ] قائمة طلبات الصيانة
- [ ] إضافة طلب جديد
- [ ] عرض تفاصيل طلب
- [ ] تعديل طلب
- [ ] تحديث حالة طلب

### المرحلة 5: اختبار الوظائف المتقدمة
- [ ] البحث والفلترة
- [ ] التوزيع التلقائي للمهام
- [ ] طباعة التقارير
- [ ] إدارة قطع الغيار

## 🚀 خطوات الاختبار التفصيلية

### الخطوة 1: إعداد البيئة

```bash
# 1. امسح جميع أنواع Cache
php artisan route:clear
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# 2. تأكد من قاعدة البيانات
php artisan migrate:status

# 3. أعد تشغيل الخادم
php artisan serve
```

### الخطوة 2: اختبارات أساسية

#### 🔗 الروابط للاختبار:

1. **اختبار النظام الأساسي:**
   ```
   http://tareq.test/test
   ```
   **النتيجة المتوقعة:** "النظام يعمل بشكل صحيح!"

2. **اختبار route الجدولة:**
   ```
   http://tareq.test/test-schedule
   ```
   **النتيجة المتوقعة:** "Schedule route يعمل بشكل صحيح!"

3. **اختبار view الجدولة (فارغ):**
   ```
   http://tareq.test/simple-schedule
   ```
   **النتيجة المتوقعة:** صفحة جدولة فارغة بدون أخطاء

### الخطوة 3: اختبار الجدولة الكاملة

4. **جدولة الفنيين (الهدف الرئيسي):**
   ```
   http://tareq.test/technicians/schedule
   ```
   **النتيجة المتوقعة:** 
   - صفحة تحتوي على "جدولة الفنيين"
   - بطاقات إحصائيات (فنيين متاحين، طلبات غير مُعينة، إلخ)
   - قائمة الفنيين مع أعباء العمل
   - قائمة الطلبات غير المُعينة

### الخطوة 4: اختبار إدارة الفنيين

5. **قائمة الفنيين:**
   ```
   http://tareq.test/technicians
   ```
   **النتيجة المتوقعة:** قائمة الفنيين مع إحصائيات

6. **إضافة فني جديد:**
   ```
   http://tareq.test/technicians/create
   ```
   **النتيجة المتوقعة:** نموذج إضافة فني شامل

### الخطوة 5: اختبار إدارة طلبات الصيانة

7. **قائمة طلبات الصيانة:**
   ```
   http://tareq.test/repairs
   ```
   **النتيجة المتوقعة:** قائمة الطلبات مع فلترة

8. **إضافة طلب جديد:**
   ```
   http://tareq.test/repairs/create
   ```
   **النتيجة المتوقعة:** نموذج إضافة طلب شامل

## ✅ معايير النجاح

### لكل صفحة يجب أن:
- [ ] تحمل بدون أخطاء (لا 404، لا 500)
- [ ] تعرض المحتوى باللغة العربية
- [ ] تكون متجاوبة (تعمل على الجوال)
- [ ] تحتوي على navigation صحيح
- [ ] لا تظهر أخطاء في console المتصفح

### للنماذج يجب أن:
- [ ] تعرض جميع الحقول المطلوبة
- [ ] تحتوي على validation
- [ ] تعرض رسائل خطأ واضحة
- [ ] تحفظ البيانات بشكل صحيح

### للقوائم يجب أن:
- [ ] تعرض البيانات بشكل منظم
- [ ] تحتوي على بحث وفلترة
- [ ] تدعم pagination
- [ ] تحتوي على أزرار العمليات

## 🐛 الأخطاء الشائعة وحلولها

### خطأ 404 في /technicians/schedule
**السبب:** Route order خاطئ
**الحل:** تأكد من أن schedule route قبل resource route

### خطأ 500 في صفحات الفنيين
**السبب:** مشكلة في قاعدة البيانات أو Model
**الحل:** تحقق من migrations والعلاقات

### صفحة فارغة أو بيضاء
**السبب:** خطأ في View أو Controller
**الحل:** تحقق من logs في storage/logs/laravel.log

### خطأ Permission Middleware
**السبب:** Middleware غير مُعرف
**الحل:** تأكد من تسجيل middleware في Kernel.php

## 📊 تقرير الاختبار

### نموذج تقرير:
```
✅ اختبار النظام الأساسي: نجح
✅ اختبار route الجدولة: نجح
✅ اختبار view الجدولة: نجح
✅ جدولة الفنيين الكاملة: نجح
✅ قائمة الفنيين: نجح
✅ إضافة فني جديد: نجح
✅ قائمة طلبات الصيانة: نجح
✅ إضافة طلب جديد: نجح

النتيجة: 8/8 اختبارات نجحت (100%)
```

## 🔧 أدوات التشخيص

### فحص Routes:
```bash
php artisan route:list | grep technician
```

### فحص Logs:
```bash
tail -f storage/logs/laravel.log
```

### فحص Database:
```bash
php artisan tinker
>>> App\Models\Technician::count()
>>> App\Models\Repair::count()
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من الـ logs
2. تأكد من إعدادات قاعدة البيانات
3. امسح جميع أنواع cache
4. أعد تشغيل الخادم
5. اتصل بالدعم الفني مع تفاصيل الخطأ

---

**ابدأ الاختبار الآن واتبع الخطوات بالترتيب!** 🚀
