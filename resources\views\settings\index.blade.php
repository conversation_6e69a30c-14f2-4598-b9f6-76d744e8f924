@extends('layouts.main')

@section('title', 'الإعدادات العامة')

@section('content')
<div class="content-wrapper animate-fade-in-up" x-data="settingsManager()">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">الإعدادات العامة</h1>
                    <p class="page-subtitle">إدارة إعدادات النظام والتطبيق بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="saveSettings()" class="btn btn-primary hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        حفظ الإعدادات
                    </button>
                    <button @click="resetSettings()" class="btn btn-danger hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="card animate-fade-in-up animate-delay-200">
        <div class="border-b border-border-light dark:border-border-dark">
            <nav class="flex space-x-8 space-x-reverse px-6" aria-label="Tabs">
                <button @click="activeTab = 'general'"
                        :class="activeTab === 'general' ? 'border-accent-color text-accent-color' : 'border-transparent text-muted-light dark:text-muted-dark hover:text-primary-light dark:hover:text-primary-dark hover:border-border-light dark:hover:border-border-dark'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 hover-scale">
                    <svg class="w-4 h-4 inline-block ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    الإعدادات العامة
                </button>
                <button @click="activeTab = 'company'"
                        :class="activeTab === 'company' ? 'border-accent-color text-accent-color' : 'border-transparent text-muted-light dark:text-muted-dark hover:text-primary-light dark:hover:text-primary-dark hover:border-border-light dark:hover:border-border-dark'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 hover-scale">
                    <svg class="w-4 h-4 inline-block ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    معلومات الشركة
                </button>
                <button @click="activeTab = 'notifications'"
                        :class="activeTab === 'notifications' ? 'border-accent-color text-accent-color' : 'border-transparent text-muted-light dark:text-muted-dark hover:text-primary-light dark:hover:text-primary-dark hover:border-border-light dark:hover:border-border-dark'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 hover-scale">
                    <svg class="w-4 h-4 inline-block ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V11H1V7.414a2 2 0 01.586-1.414l.707-.707a5 5 0 013.535-1.465z"></path>
                    </svg>
                    الإشعارات
                </button>
                <button @click="activeTab = 'security'"
                        :class="activeTab === 'security' ? 'border-accent-color text-accent-color' : 'border-transparent text-muted-light dark:text-muted-dark hover:text-primary-light dark:hover:text-primary-dark hover:border-border-light dark:hover:border-border-dark'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 hover-scale">
                    <svg class="w-4 h-4 inline-block ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    الأمان
                </button>
                <button @click="activeTab = 'backup'"
                        :class="activeTab === 'backup' ? 'border-accent-color text-accent-color' : 'border-transparent text-muted-light dark:text-muted-dark hover:text-primary-light dark:hover:text-primary-dark hover:border-border-light dark:hover:border-border-dark'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 hover-scale">
                    <svg class="w-4 h-4 inline-block ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    النسخ الاحتياطي
                </button>
            </nav>
        </div>

        <!-- General Settings -->
        <div x-show="activeTab === 'general'" class="p-6 animate-fade-in-up">
            <div class="card-header mb-6">
                <h3 class="card-title">الإعدادات العامة</h3>
                <div class="action-group">
                    <button @click="resetGeneralSettings()" class="btn btn-ghost btn-sm hover-scale">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        إعادة تعيين
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    <label class="form-label">اسم النظام</label>
                    <input type="text" x-model="settings.general.systemName" class="form-input focus-glow" placeholder="أدخل اسم النظام">
                </div>

                <div class="form-group">
                    <label class="form-label">المنطقة الزمنية</label>
                    <select x-model="settings.general.timezone" class="form-select focus-glow">
                        <option value="Asia/Jerusalem">آسيا/القدس</option>
                        <option value="Asia/Riyadh">آسيا/الرياض</option>
                        <option value="Asia/Dubai">آسيا/دبي</option>
                        <option value="Africa/Cairo">أفريقيا/القاهرة</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">العملة الافتراضية</label>
                    <select x-model="settings.general.currency" class="form-select focus-glow">
                        <option value="ILS">شيكل إسرائيلي (₪)</option>
                        <option value="USD">دولار أمريكي ($)</option>
                        <option value="EUR">يورو (€)</option>
                        <option value="SAR">ريال سعودي (ر.س)</option>
                        <option value="AED">درهم إماراتي (د.إ)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">اللغة الافتراضية</label>
                    <select x-model="settings.general.language" class="form-select focus-glow">
                        <option value="ar">العربية</option>
                        <option value="en">الإنجليزية</option>
                        <option value="he">العبرية</option>
                    </select>
                </div>

                <div class="md:col-span-2 form-group">
                    <div class="toggle-switch">
                        <input type="checkbox" x-model="settings.general.maintenanceMode" id="maintenanceMode" class="toggle-input">
                        <label for="maintenanceMode" class="toggle-label">
                            <span class="toggle-slider"></span>
                            <span class="toggle-text">وضع الصيانة</span>
                        </label>
                    </div>
                    <p class="text-xs text-muted-light dark:text-muted-dark mt-2">تفعيل وضع الصيانة سيمنع الوصول للنظام مؤقتاً</p>
                </div>
            </div>
        </div>

        <!-- Company Settings -->
        <div x-show="activeTab === 'company'" class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات الشركة</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم الشركة</label>
                    <input type="text" x-model="settings.company.name" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم التسجيل</label>
                    <input type="text" x-model="settings.company.registrationNumber" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العنوان</label>
                    <textarea x-model="settings.company.address" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                    <input type="text" x-model="settings.company.phone" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                    <input type="email" x-model="settings.company.email" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموقع الإلكتروني</label>
                    <input type="url" x-model="settings.company.website" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الرقم الضريبي</label>
                    <input type="text" x-model="settings.company.taxNumber" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div x-show="activeTab === 'notifications'" class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إعدادات الإشعارات</h3>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-sm font-medium text-gray-900 dark:text-gray-100">إشعارات البريد الإلكتروني</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">إرسال إشعارات عبر البريد الإلكتروني</p>
                    </div>
                    <input type="checkbox" x-model="settings.notifications.email" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-sm font-medium text-gray-900 dark:text-gray-100">إشعارات SMS</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">إرسال إشعارات عبر الرسائل النصية</p>
                    </div>
                    <input type="checkbox" x-model="settings.notifications.sms" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-sm font-medium text-gray-900 dark:text-gray-100">إشعارات سطح المكتب</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">إظهار إشعارات في المتصفح</p>
                    </div>
                    <input type="checkbox" x-model="settings.notifications.desktop" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-sm font-medium text-gray-900 dark:text-gray-100">الأصوات</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">تشغيل أصوات مع الإشعارات</p>
                    </div>
                    <input type="checkbox" x-model="settings.notifications.sound" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div x-show="activeTab === 'security'" class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إعدادات الأمان</h3>
            
            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">مدة انتهاء الجلسة (بالدقائق)</label>
                    <input type="number" x-model="settings.security.sessionTimeout" min="5" max="1440" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد محاولات تسجيل الدخول المسموحة</label>
                    <input type="number" x-model="settings.security.maxLoginAttempts" min="3" max="10" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-sm font-medium text-gray-900 dark:text-gray-100">المصادقة الثنائية</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">تفعيل المصادقة الثنائية للمستخدمين</p>
                    </div>
                    <input type="checkbox" x-model="settings.security.twoFactorAuth" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-sm font-medium text-gray-900 dark:text-gray-100">تسجيل العمليات</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">تسجيل جميع العمليات في النظام</p>
                    </div>
                    <input type="checkbox" x-model="settings.security.auditLog" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>
            </div>
        </div>

        <!-- Backup Settings -->
        <div x-show="activeTab === 'backup'" class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إعدادات النسخ الاحتياطي</h3>
            
            <div class="space-y-6">
                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-sm font-medium text-gray-900 dark:text-gray-100">النسخ الاحتياطي التلقائي</label>
                        <p class="text-xs text-gray-500 dark:text-gray-400">تفعيل النسخ الاحتياطي التلقائي</p>
                    </div>
                    <input type="checkbox" x-model="settings.backup.autoBackup" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>

                <div x-show="settings.backup.autoBackup">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تكرار النسخ الاحتياطي</label>
                    <select x-model="settings.backup.frequency" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="daily">يومياً</option>
                        <option value="weekly">أسبوعياً</option>
                        <option value="monthly">شهرياً</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">عدد النسخ المحفوظة</label>
                    <input type="number" x-model="settings.backup.retentionCount" min="1" max="30" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">مسار التخزين</label>
                    <input type="text" x-model="settings.backup.storagePath" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function settingsManager() {
    return {
        activeTab: 'general',
        settings: {
            general: {
                systemName: 'نظام إدارة مراكز الصيانة',
                timezone: 'Asia/Jerusalem',
                currency: 'ILS',
                language: 'ar',
                maintenanceMode: false
            },
            company: {
                name: 'مركز الصيانة المتقدم',
                registrationNumber: '*********',
                address: 'شارع الملك فيصل، رام الله، فلسطين',
                phone: '+970-2-1234567',
                email: '<EMAIL>',
                website: 'https://repaircenters.com',
                taxNumber: 'TAX123456'
            },
            notifications: {
                email: true,
                sms: false,
                desktop: true,
                sound: true
            },
            security: {
                sessionTimeout: 120,
                maxLoginAttempts: 5,
                twoFactorAuth: false,
                auditLog: true
            },
            backup: {
                autoBackup: true,
                frequency: 'daily',
                retentionCount: 7,
                storagePath: '/backups'
            }
        },

        saveSettings() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري الحفظ...
            `;
            button.disabled = true;

            // محاكاة عملية الحفظ
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                if (window.showNotification) {
                    window.showNotification('تم حفظ الإعدادات بنجاح!', 'success');
                }
            }, 2000);
        },

        resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
                // Reset to default values
                this.settings = {
                    general: {
                        systemName: 'نظام إدارة مراكز الصيانة',
                        timezone: 'Asia/Jerusalem',
                        currency: 'ILS',
                        language: 'ar',
                        maintenanceMode: false
                    },
                    company: {
                        name: '',
                        registrationNumber: '',
                        address: '',
                        phone: '',
                        email: '',
                        website: '',
                        taxNumber: ''
                    },
                    notifications: {
                        email: true,
                        sms: false,
                        desktop: true,
                        sound: true
                    },
                    security: {
                        sessionTimeout: 120,
                        maxLoginAttempts: 5,
                        twoFactorAuth: false,
                        auditLog: true
                    },
                    backup: {
                        autoBackup: true,
                        frequency: 'daily',
                        retentionCount: 7,
                        storagePath: '/backups'
                    }
                };

                if (window.showNotification) {
                    window.showNotification('تم إعادة تعيين الإعدادات بنجاح', 'info');
                }
            }
        },

        resetGeneralSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات العامة؟')) {
                this.settings.general = {
                    systemName: 'نظام إدارة مراكز الصيانة',
                    timezone: 'Asia/Jerusalem',
                    currency: 'ILS',
                    language: 'ar',
                    maintenanceMode: false
                };

                if (window.showNotification) {
                    window.showNotification('تم إعادة تعيين الإعدادات العامة', 'info');
                }
            }
        }
    }
}
</script>
@endpush
@endsection
