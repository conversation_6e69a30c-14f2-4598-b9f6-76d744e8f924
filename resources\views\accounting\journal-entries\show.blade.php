@extends('layouts.main')

@section('title', 'تفاصيل القيد - ' . $journalEntry->entry_number)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تفاصيل القيد المحاسبي</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $journalEntry->entry_number }} - {{ $journalEntry->entry_date->format('Y-m-d') }}</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            @if($journalEntry->canBeEdited())
                <a href="{{ route('journal-entries.edit', $journalEntry) }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-edit mr-2"></i>
                    تعديل
                </a>
            @endif
            
            @if($journalEntry->canBePosted())
                <button onclick="postEntry()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-check mr-2"></i>
                    ترحيل
                </button>
            @endif
            
            @if($journalEntry->canBeReversed())
                <button onclick="reverseEntry()" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-undo mr-2"></i>
                    عكس القيد
                </button>
            @endif
            
            <button onclick="printEntry()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-print mr-2"></i>
                طباعة
            </button>
            
            <a href="{{ route('journal-entries.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- Entry Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Basic Information -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">معلومات القيد</h3>
                </div>
                
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم القيد</label>
                            <div class="text-lg font-mono bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $journalEntry->entry_number }}
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ القيد</label>
                            <div class="text-lg font-medium text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $journalEntry->entry_date->format('Y-m-d') }}
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                            <div class="bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $journalEntry->getStatusColor() }}">
                                    {{ $journalEntry->getStatusLabel() }}
                                </span>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المنشئ</label>
                            <div class="text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $journalEntry->creator->name ?? 'غير محدد' }}
                            </div>
                        </div>
                        
                        @if($journalEntry->reference_type)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع المرجع</label>
                            <div class="text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $journalEntry->reference_type }}
                            </div>
                        </div>
                        @endif
                        
                        @if($journalEntry->reference_id)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">معرف المرجع</label>
                            <div class="text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $journalEntry->reference_id }}
                            </div>
                        </div>
                        @endif
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">وصف القيد</label>
                        <div class="text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                            {{ $journalEntry->description }}
                        </div>
                    </div>
                    
                    @if($journalEntry->posted_at)
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ الترحيل</label>
                            <div class="text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $journalEntry->posted_at->format('Y-m-d H:i') }}
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المرحل بواسطة</label>
                            <div class="text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $journalEntry->poster->name ?? 'غير محدد' }}
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    @if($journalEntry->reversed_at)
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                        <h4 class="font-medium text-red-800 dark:text-red-200 mb-2">معلومات العكس</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-red-600 dark:text-red-400">تاريخ العكس:</span>
                                <span class="text-red-800 dark:text-red-200 mr-2">
                                    {{ $journalEntry->reversed_at->format('Y-m-d H:i') }}
                                </span>
                            </div>
                            <div>
                                <span class="text-red-600 dark:text-red-400">معكوس بواسطة:</span>
                                <span class="text-red-800 dark:text-red-200 mr-2">
                                    {{ $journalEntry->reverser->name ?? 'غير محدد' }}
                                </span>
                            </div>
                        </div>
                        @if($journalEntry->reversal_reason)
                        <div class="mt-2">
                            <span class="text-red-600 dark:text-red-400">سبب العكس:</span>
                            <span class="text-red-800 dark:text-red-200 mr-2">{{ $journalEntry->reversal_reason }}</span>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Summary Information -->
        <div class="space-y-6">
            <!-- Totals -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">ملخص المبالغ</h3>
                </div>
                
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">إجمالي المدين</label>
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {{ number_format($journalEntry->total_debit, 2) }} ريال
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">إجمالي الدائن</label>
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format($journalEntry->total_credit, 2) }} ريال
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">حالة التوازن</label>
                        <div class="text-lg font-medium {{ $journalEntry->isBalanced() ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ $journalEntry->isBalanced() ? 'متوازن' : 'غير متوازن' }}
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">عدد السطور</label>
                        <div class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            {{ $journalEntry->transactions->count() }} سطر
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden" id="journal-entry-details">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تفاصيل القيد</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            رقم الحساب
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            اسم الحساب
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الوصف
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            مدين
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            دائن
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($journalEntry->transactions as $transaction)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-mono text-gray-900 dark:text-gray-100">
                                    {{ $transaction->account->account_code }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                    <a href="{{ route('accounts.show', $transaction->account) }}" 
                                       class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                                        {{ $transaction->account->account_name }}
                                    </a>
                                </div>
                                @if($transaction->account->account_name_en)
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $transaction->account->account_name_en }}
                                    </div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 dark:text-gray-100">
                                    {{ $transaction->description ?: '-' }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-left">
                                @if($transaction->debit_amount > 0)
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        {{ number_format($transaction->debit_amount, 2) }}
                                    </div>
                                @else
                                    <div class="text-sm text-gray-400">-</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-left">
                                @if($transaction->credit_amount > 0)
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        {{ number_format($transaction->credit_amount, 2) }}
                                    </div>
                                @else
                                    <div class="text-sm text-gray-400">-</div>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
                <tfoot class="bg-gray-50 dark:bg-gray-700">
                    <tr class="font-bold">
                        <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                            الإجمالي
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-bold text-gray-900 dark:text-gray-100">
                            {{ number_format($journalEntry->total_debit, 2) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-bold text-gray-900 dark:text-gray-100">
                            {{ number_format($journalEntry->total_credit, 2) }}
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
function postEntry() {
    if (confirm('هل أنت متأكد من ترحيل هذا القيد؟ لا يمكن التراجع عن هذا الإجراء.')) {
        fetch(`/journal-entries/{{ $journalEntry->id }}/post`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء ترحيل القيد');
        });
    }
}

function reverseEntry() {
    const reason = prompt('يرجى إدخال سبب عكس القيد:');
    if (reason && reason.trim()) {
        fetch(`/journal-entries/{{ $journalEntry->id }}/reverse`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ reason: reason.trim() })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء عكس القيد');
        });
    }
}

function printEntry() {
    window.print();
}

// Print styles
const printStyles = `
    @media print {
        body * {
            visibility: hidden;
        }
        #journal-entry-details, #journal-entry-details * {
            visibility: visible;
        }
        #journal-entry-details {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none !important;
        }
    }
`;

// Add print styles to head
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
@endpush
@endsection
