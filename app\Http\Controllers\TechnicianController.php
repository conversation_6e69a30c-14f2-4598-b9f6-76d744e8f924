<?php

namespace App\Http\Controllers;

use App\Models\Technician;
use App\Models\User;
use App\Models\Repair;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class TechnicianController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // TODO: Add permission middleware when role system is implemented
        // $this->middleware('permission:technicians.view')->only(['index', 'show']);
        // $this->middleware('permission:technicians.create')->only(['create', 'store']);
        // $this->middleware('permission:technicians.edit')->only(['edit', 'update']);
        // $this->middleware('permission:technicians.delete')->only(['destroy']);
    }

    public function index(Request $request)
    {
        $query = Technician::with(['user', 'repairs']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('employee_id', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by availability
        if ($request->filled('availability')) {
            $query->where('availability', $request->availability);
        }

        // Filter by specialization
        if ($request->filled('specialization')) {
            $query->bySpecialization($request->specialization);
        }

        // Filter by skill level
        if ($request->filled('skill_level')) {
            $query->bySkillLevel($request->skill_level);
        }

        // Filter by department
        if ($request->filled('department')) {
            $query->where('department', $request->department);
        }

        $technicians = $query->latest()->paginate(15);

        // Get filter options
        $departments = Technician::distinct()->pluck('department')->filter();
        $specializations = Technician::whereNotNull('specializations')
            ->get()
            ->pluck('specializations')
            ->flatten()
            ->unique()
            ->values();

        return view('technicians.index', compact('technicians', 'departments', 'specializations'));
    }

    public function schedule()
    {
        try {
            $technicians = Technician::where('status', 'active')
                ->withCount(['repairs as current_repairs_count' => function ($query) {
                    $query->whereIn('status', ['pending', 'in_progress', 'waiting_parts']);
                }])
                ->get();

            $unassignedRepairsList = \App\Models\Repair::whereNull('technician_id')
                ->whereIn('status', ['pending', 'diagnosed'])
                ->with('customer')
                ->orderBy('priority', 'desc')
                ->orderBy('created_at', 'asc')
                ->get();

            $availableTechnicians = $technicians->where('availability', 'available')->count();
            $unassignedRepairs = $unassignedRepairsList->count();
            $activeRepairs = \App\Models\Repair::whereIn('status', ['in_progress', 'waiting_parts'])->count();
            $averageWorkload = $technicians->avg('current_repairs_count') ?? 0;

            $availableTechniciansList = $technicians->where('availability', 'available');

            return view('technicians.schedule', compact(
                'technicians',
                'unassignedRepairsList',
                'availableTechnicians',
                'unassignedRepairs',
                'activeRepairs',
                'averageWorkload',
                'availableTechniciansList'
            ));
        } catch (\Exception $e) {
            // For debugging - in production, log this error
            return response()->json([
                'error' => 'حدث خطأ في تحميل صفحة الجدولة',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    public function create()
    {
        $departments = Technician::distinct()->pluck('department')->filter();
        $specializations = [
            'Mobile Repair',
            'Computer Repair',
            'Tablet Repair',
            'Gaming Console Repair',
            'Smart Watch Repair',
            'Audio Equipment Repair',
            'Camera Repair',
            'Printer Repair'
        ];

        return view('technicians.create', compact('departments', 'specializations'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:technicians,email',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'employee_id' => 'nullable|string|unique:technicians,employee_id',
            'department' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'hire_date' => 'required|date',
            'salary' => 'nullable|numeric|min:0',
            'hourly_rate' => 'nullable|numeric|min:0',
            'specializations' => 'nullable|array',
            'specializations.*' => 'string',
            'certifications' => 'nullable|array',
            'certifications.*' => 'string',
            'experience_years' => 'nullable|integer|min:0',
            'skill_level' => 'required|in:beginner,intermediate,advanced,expert',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:255',
            'date_of_birth' => 'nullable|date|before:today',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'working_hours' => 'nullable|array',
            'notes' => 'nullable|string',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'create_user_account' => 'boolean'
        ]);

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            $validated['profile_image'] = $request->file('profile_image')->store('technicians', 'public');
        }

        // Set default values
        $validated['status'] = 'active';
        $validated['availability'] = 'available';
        $validated['is_active'] = true;

        // Create user account if requested
        $user = null;
        if ($request->boolean('create_user_account')) {
            $user = User::create([
                'name' => $validated['first_name'] . ' ' . $validated['last_name'],
                'email' => $validated['email'],
                'password' => Hash::make('password123'), // Default password
                'role' => 'technician'
            ]);
            $validated['user_id'] = $user->id;
        }

        $technician = Technician::create($validated);

        return redirect()->route('technicians.index')
            ->with('success', 'تم إنشاء الفني بنجاح.');
    }

    public function show(Technician $technician)
    {
        $technician->load(['user', 'repairs.customer', 'schedules', 'maintenances']);

        // Get performance metrics
        $metrics = [
            'total_repairs' => $technician->repairs()->count(),
            'completed_repairs' => $technician->repairs()->where('status', 'completed')->count(),
            'pending_repairs' => $technician->repairs()->whereIn('status', ['pending', 'in_progress'])->count(),
            'average_rating' => $technician->getAverageRating(),
            'repairs_this_month' => $technician->getTotalRepairsThisMonth(),
            'completed_this_month' => $technician->getCompletedRepairsThisMonth(),
            'average_repair_time' => $technician->getAverageRepairTime(),
            'current_workload' => $technician->getCurrentWorkload()
        ];

        // Get recent repairs
        $recentRepairs = $technician->repairs()
            ->with('customer')
            ->latest()
            ->take(10)
            ->get();

        return view('technicians.show', compact('technician', 'metrics', 'recentRepairs'));
    }

    public function edit(Technician $technician)
    {
        $departments = Technician::distinct()->pluck('department')->filter();
        $specializations = [
            'Mobile Repair',
            'Computer Repair',
            'Tablet Repair',
            'Gaming Console Repair',
            'Smart Watch Repair',
            'Audio Equipment Repair',
            'Camera Repair',
            'Printer Repair'
        ];

        return view('technicians.edit', compact('technician', 'departments', 'specializations'));
    }

    public function update(Request $request, Technician $technician)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('technicians')->ignore($technician->id)],
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'employee_id' => ['nullable', 'string', Rule::unique('technicians')->ignore($technician->id)],
            'department' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'hire_date' => 'required|date',
            'salary' => 'nullable|numeric|min:0',
            'hourly_rate' => 'nullable|numeric|min:0',
            'specializations' => 'nullable|array',
            'specializations.*' => 'string',
            'certifications' => 'nullable|array',
            'certifications.*' => 'string',
            'experience_years' => 'nullable|integer|min:0',
            'skill_level' => 'required|in:beginner,intermediate,advanced,expert',
            'status' => 'required|in:active,inactive,on_leave,terminated',
            'availability' => 'required|in:available,busy,unavailable,on_break',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:255',
            'date_of_birth' => 'nullable|date|before:today',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'working_hours' => 'nullable|array',
            'notes' => 'nullable|string',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean'
        ]);

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            // Delete old image
            if ($technician->profile_image) {
                Storage::disk('public')->delete($technician->profile_image);
            }
            $validated['profile_image'] = $request->file('profile_image')->store('technicians', 'public');
        }

        $technician->update($validated);

        // Update associated user account if exists
        if ($technician->user) {
            $technician->user->update([
                'name' => $validated['first_name'] . ' ' . $validated['last_name'],
                'email' => $validated['email']
            ]);
        }

        return redirect()->route('technicians.show', $technician)
            ->with('success', 'تم تحديث بيانات الفني بنجاح.');
    }

    public function destroy(Technician $technician)
    {
        // Check if technician has active repairs
        if ($technician->repairs()->whereIn('status', ['pending', 'in_progress'])->exists()) {
            return redirect()->route('technicians.index')
                ->with('error', 'لا يمكن حذف الفني لوجود طلبات صيانة نشطة.');
        }

        // Delete profile image
        if ($technician->profile_image) {
            Storage::disk('public')->delete($technician->profile_image);
        }

        $technician->delete();

        return redirect()->route('technicians.index')
            ->with('success', 'تم حذف الفني بنجاح.');
    }

    public function updateSchedule(Request $request, Technician $technician)
    {
        $request->validate([
            'working_hours' => 'required|array',
            'working_hours.*.day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'working_hours.*.start_time' => 'required|date_format:H:i',
            'working_hours.*.end_time' => 'required|date_format:H:i|after:working_hours.*.start_time',
            'working_hours.*.is_working' => 'boolean'
        ]);

        $technician->update([
            'working_hours' => $request->working_hours
        ]);

        return redirect()->route('technicians.show', $technician)
            ->with('success', 'تم تحديث جدول العمل بنجاح.');
    }

    public function updateAvailability(Request $request, Technician $technician)
    {
        $request->validate([
            'availability' => 'required|in:available,busy,unavailable,on_break'
        ]);

        $technician->updateAvailability($request->availability);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة التوفر بنجاح.',
            'availability' => $technician->availability_badge
        ]);
    }

    /**
     * Display technician performance analytics
     */
    public function analytics(Technician $technician)
    {
        $startDate = request('start_date', now()->subMonth());
        $endDate = request('end_date', now());

        // Performance metrics
        $metrics = [
            'total_repairs' => $technician->repairs()->whereBetween('created_at', [$startDate, $endDate])->count(),
            'completed_repairs' => $technician->repairs()->where('status', 'completed')->whereBetween('completed_at', [$startDate, $endDate])->count(),
            'average_completion_time' => $technician->getAverageCompletionTime($startDate, $endDate),
            'customer_satisfaction' => $technician->getAverageRating($startDate, $endDate),
            'revenue_generated' => $technician->getRevenueGenerated($startDate, $endDate),
            'efficiency_score' => $technician->getEfficiencyScore($startDate, $endDate),
        ];

        // Daily performance data for charts
        $dailyPerformance = $technician->getDailyPerformance($startDate, $endDate);

        // Repair status breakdown
        $statusBreakdown = $technician->getRepairStatusBreakdown($startDate, $endDate);

        // Skills assessment
        $skillsAssessment = $technician->getSkillsAssessment();

        return view('technicians.analytics', compact(
            'technician', 'metrics', 'dailyPerformance', 'statusBreakdown', 'skillsAssessment', 'startDate', 'endDate'
        ));
    }

    /**
     * Display technician workload management
     */
    public function workload()
    {
        $technicians = Technician::active()
            ->with(['repairs' => function($query) {
                $query->whereIn('status', ['pending', 'in_progress', 'diagnosed']);
            }])
            ->get();

        $workloadData = $technicians->map(function($technician) {
            return [
                'id' => $technician->id,
                'name' => $technician->full_name,
                'current_repairs' => $technician->repairs->count(),
                'availability' => $technician->availability,
                'skill_level' => $technician->skill_level,
                'specializations' => $technician->specializations,
                'workload_percentage' => $technician->getWorkloadPercentage(),
                'estimated_completion' => $technician->getEstimatedCompletionTime(),
            ];
        });

        return view('technicians.workload', compact('workloadData'));
    }

    /**
     * Assign multiple repairs to technician
     */
    public function bulkAssign(Request $request)
    {
        $request->validate([
            'technician_id' => 'required|exists:technicians,id',
            'repair_ids' => 'required|array',
            'repair_ids.*' => 'exists:repairs,id',
            'notes' => 'nullable|string'
        ]);

        $technician = Technician::find($request->technician_id);
        $assignedCount = 0;

        foreach ($request->repair_ids as $repairId) {
            if ($technician->assignToRepair($repairId)) {
                $assignedCount++;
            }
        }

        return redirect()->back()
            ->with('success', "تم تعيين {$assignedCount} طلب صيانة للفني {$technician->full_name} بنجاح.");
    }

    /**
     * Display technician skills management
     */
    public function skills(Technician $technician)
    {
        $availableSkills = [
            'Mobile Repair' => [
                'Screen Replacement',
                'Battery Replacement',
                'Charging Port Repair',
                'Camera Repair',
                'Speaker Repair',
                'Motherboard Repair'
            ],
            'Computer Repair' => [
                'Hardware Diagnostics',
                'Software Installation',
                'Virus Removal',
                'Data Recovery',
                'Network Setup',
                'Component Replacement'
            ],
            'Tablet Repair' => [
                'Screen Replacement',
                'Battery Replacement',
                'Charging Issues',
                'Software Problems'
            ],
            'Gaming Console Repair' => [
                'Hardware Repair',
                'Software Issues',
                'Controller Repair',
                'Disc Drive Repair'
            ]
        ];

        $currentSkills = $technician->getSkillsWithProficiency();

        return view('technicians.skills', compact('technician', 'availableSkills', 'currentSkills'));
    }

    /**
     * Update technician skills
     */
    public function updateSkills(Request $request, Technician $technician)
    {
        $request->validate([
            'skills' => 'required|array',
            'skills.*.name' => 'required|string',
            'skills.*.proficiency' => 'required|integer|min:1|max:100',
            'skills.*.certified' => 'boolean'
        ]);

        $technician->updateSkills($request->skills);

        return redirect()->route('technicians.skills', $technician)
            ->with('success', 'تم تحديث مهارات الفني بنجاح.');
    }

    /**
     * Generate technician performance report
     */
    public function performanceReport(Technician $technician)
    {
        $startDate = request('start_date', now()->subMonth());
        $endDate = request('end_date', now());

        $report = [
            'technician' => $technician,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'metrics' => $technician->getPerformanceMetrics($startDate, $endDate),
            'repairs' => $technician->repairs()
                ->whereBetween('created_at', [$startDate, $endDate])
                ->with(['customer', 'parts'])
                ->get(),
            'revenue' => $technician->getRevenueBreakdown($startDate, $endDate),
            'efficiency' => $technician->getEfficiencyMetrics($startDate, $endDate)
        ];

        return view('technicians.performance-report', compact('report'));
    }

    /**
     * Export technicians data
     */
    public function export(Request $request)
    {
        $query = Technician::with(['repairs', 'user']);

        // Apply filters
        if ($request->filled('department')) {
            $query->where('department', $request->department);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('skill_level')) {
            $query->where('skill_level', $request->skill_level);
        }

        $technicians = $query->get();

        return view('technicians.export', compact('technicians'));
    }

    /**
     * Display technician scheduling interface
     */
    public function scheduling()
    {
        $technicians = Technician::active()
            ->with(['schedules' => function($query) {
                $query->whereBetween('scheduled_date', [now()->startOfWeek(), now()->endOfWeek()]);
            }])
            ->get();

        $repairs = Repair::whereIn('status', ['pending', 'diagnosed'])
            ->with(['customer', 'technician'])
            ->get();

        return view('technicians.scheduling', compact('technicians', 'repairs'));
    }

    /**
     * Assign repair to technician
     */
    public function assignRepair(Request $request, Technician $technician)
    {
        $request->validate([
            'repair_id' => 'required|exists:repairs,id'
        ]);

        $success = $technician->assignToRepair($request->repair_id);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'تم تعيين طلب الصيانة للفني بنجاح.'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'فشل في تعيين طلب الصيانة. تأكد من أن الفني متاح.'
        ], 400);
    }

    public function autoAssignRepairs(Request $request)
    {
        $request->validate([
            'repair_ids' => 'required|array',
            'repair_ids.*' => 'exists:repairs,id'
        ]);

        $assignedCount = 0;
        $failedAssignments = [];

        foreach ($request->repair_ids as $repairId) {
            $repair = \App\Models\Repair::find($repairId);

            if ($repair && !$repair->technician_id) {
                // Find the best available technician
                $technician = $this->findBestTechnician($repair);

                if ($technician && $technician->assignToRepair($repairId)) {
                    $assignedCount++;
                } else {
                    $failedAssignments[] = $repair->repair_number;
                }
            }
        }

        $message = "تم تعيين {$assignedCount} طلب صيانة بنجاح.";
        if (!empty($failedAssignments)) {
            $message .= " فشل في تعيين: " . implode(', ', $failedAssignments);
        }

        return response()->json([
            'success' => $assignedCount > 0,
            'message' => $message,
            'assigned_count' => $assignedCount,
            'failed_assignments' => $failedAssignments
        ]);
    }

    private function findBestTechnician(\App\Models\Repair $repair)
    {
        return Technician::where('status', 'active')
            ->where('availability', 'available')
            ->whereJsonContains('specializations', $repair->device_type)
            ->withCount(['repairs' => function ($query) {
                $query->whereIn('status', ['pending', 'in_progress']);
            }])
            ->orderBy('repairs_count', 'asc')
            ->orderBy('skill_level', 'desc')
            ->first();
    }

    /**
     * Get technician individual workload data
     */
    public function getWorkloadData(Technician $technician)
    {
        $workload = [
            'current_repairs' => $technician->repairs()
                ->whereIn('status', ['pending', 'in_progress'])
                ->with('customer')
                ->get(),
            'completed_today' => $technician->repairs()
                ->where('status', 'completed')
                ->whereDate('completed_at', today())
                ->count(),
            'scheduled_repairs' => $technician->schedules()
                ->whereDate('scheduled_date', '>=', today())
                ->with('repair.customer')
                ->get()
        ];

        return response()->json($workload);
    }

    public function performance(Technician $technician)
    {
        $performance = [
            'total_repairs' => $technician->repairs()->count(),
            'completed_repairs' => $technician->repairs()->where('status', 'completed')->count(),
            'average_rating' => $technician->getAverageRating(),
            'average_repair_time' => $technician->getAverageRepairTime(),
            'repairs_this_month' => $technician->getTotalRepairsThisMonth(),
            'completed_this_month' => $technician->getCompletedRepairsThisMonth(),
            'monthly_performance' => $this->getMonthlyPerformance($technician)
        ];

        return response()->json($performance);
    }

    private function getMonthlyPerformance(Technician $technician)
    {
        $months = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $months[] = [
                'month' => $date->format('Y-m'),
                'label' => $date->format('M Y'),
                'repairs' => $technician->repairs()
                    ->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
                'completed' => $technician->repairs()
                    ->where('status', 'completed')
                    ->whereYear('completed_at', $date->year)
                    ->whereMonth('completed_at', $date->month)
                    ->count()
            ];
        }

        return $months;
    }
}
