-- إدراج بيانات تجريبية سريعة لنظام نقطة البيع

-- إدراج عملاء تجريبيين (إذا لم يكونوا موجودين)
INSERT IGNORE INTO `customers` (
    `id`, `customer_number`, `type`, `first_name`, `last_name`, `company_name`, 
    `email`, `phone`, `mobile`, `address`, `city`, `country`, 
    `is_active`, `created_by`, `created_at`, `updated_at`
) VALUES 
(1, 'CUST-001', 'individual', 'أحمد', 'محمد علي', NULL,
    '<EMAIL>', '0599123456', '0599123456', 
    'شارع الملك فهد، الرياض', 'الرياض', 'المملكة العربية السعودية',
    1, 1, NOW(), NOW()),
(2, 'CUST-002', 'individual', 'سارة', 'أحمد خالد', NULL,
    '<EMAIL>', '0598765432', '0598765432',
    'شارع العليا، الرياض', 'الرياض', 'المملكة العربية السعودية',
    1, 1, NOW(), NOW()),
(3, 'CUST-003', 'company', NULL, NULL, 'شركة التقنية المتقدمة',
    '<EMAIL>', '0596543210', '0596543210',
    'طريق الملك عبدالعزيز، الدمام', 'الدمام', 'المملكة العربية السعودية',
    1, 1, NOW(), NOW());

-- إدراج قطع غيار تجريبية (إذا لم تكن موجودة)
INSERT IGNORE INTO `parts` (
    `id`, `part_number`, `name`, `description`, `category`, `brand`, `model`,
    `price`, `cost_price`, `stock_quantity`, `min_stock_level`, `max_stock_level`,
    `unit`, `location`, `is_active`, `created_at`, `updated_at`
) VALUES 
(1, 'PART-001', 'شاشة iPhone 13', 'شاشة أصلية لجهاز iPhone 13', 
    'screens', 'Apple', 'iPhone 13', 450.00, 350.00, 25, 5, 100,
    'piece', 'A1-01', 1, NOW(), NOW()),
(2, 'PART-002', 'بطارية iPhone 12', 'بطارية أصلية لجهاز iPhone 12',
    'batteries', 'Apple', 'iPhone 12', 120.00, 80.00, 40, 10, 100,
    'piece', 'B2-05', 1, NOW(), NOW()),
(3, 'ACC-001', 'كابل USB-C', 'كابل شحن USB-C عالي الجودة',
    'cables', 'Generic', 'Universal', 25.00, 15.00, 100, 20, 200,
    'piece', 'C3-10', 1, NOW(), NOW()),
(4, 'ACC-002', 'شاحن سريع 20W', 'شاحن سريع 20 واط متوافق مع iPhone',
    'chargers', 'Apple', 'Universal', 85.00, 60.00, 30, 10, 100,
    'piece', 'C3-15', 1, NOW(), NOW()),
(5, 'ACC-003', 'جراب iPhone 13 Pro', 'جراب حماية شفاف لجهاز iPhone 13 Pro',
    'cases', 'Generic', 'iPhone 13 Pro', 35.00, 20.00, 50, 15, 150,
    'piece', 'D4-20', 1, NOW(), NOW()),
(6, 'ACC-004', 'سماعات AirPods', 'سماعات AirPods الجيل الثالث',
    'accessories', 'Apple', 'AirPods 3rd Gen', 650.00, 500.00, 12, 3, 30,
    'piece', 'E5-01', 1, NOW(), NOW());

-- إدراج مستخدم افتراضي (إذا لم يكن موجوداً)
INSERT IGNORE INTO `users` (
    `id`, `name`, `email`, `email_verified_at`, `password`, 
    `created_at`, `updated_at`
) VALUES 
(1, 'مدير النظام', '<EMAIL>', NOW(), 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    NOW(), NOW());

-- تحديث تسلسل الجداول
ALTER TABLE `customers` AUTO_INCREMENT = 4;
ALTER TABLE `parts` AUTO_INCREMENT = 7;
ALTER TABLE `users` AUTO_INCREMENT = 2;
