@extends('layouts.main')

@section('title', 'جدولة الفنيين')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">جدولة الفنيين</h1>
            <p class="text-gray-600 dark:text-gray-400">إدارة جداول العمل وتوزيع المهام</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('technicians.workload') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-chart-bar mr-2"></i>
                أعباء العمل
            </a>
            <button onclick="openScheduleModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-plus mr-2"></i>
                جدولة جديدة
            </button>
            <a href="{{ route('technicians.export') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير
            </a>
        </div>
    </div>

    <!-- Week Navigation -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <button class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-chevron-right mr-2"></i>
                الأسبوع السابق
            </button>
            
            <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {{ now()->startOfWeek()->format('d M') }} - {{ now()->endOfWeek()->format('d M Y') }}
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">الأسبوع الحالي</p>
            </div>
            
            <button class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200">
                الأسبوع التالي
                <i class="fas fa-chevron-left mr-2"></i>
            </button>
        </div>
    </div>

    <!-- Scheduling Grid -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">جدول الأسبوع</h3>
        </div>
        
        <!-- Days Header -->
        <div class="grid grid-cols-8 border-b border-gray-200 dark:border-gray-700">
            <div class="p-4 bg-gray-50 dark:bg-gray-700 font-medium text-gray-900 dark:text-gray-100">الفني</div>
            @php
                $days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
                $currentWeek = collect(range(0, 6))->map(function($day) {
                    return now()->startOfWeek()->addDays($day);
                });
            @endphp
            @foreach($currentWeek as $index => $date)
                <div class="p-4 bg-gray-50 dark:bg-gray-700 text-center">
                    <div class="font-medium text-gray-900 dark:text-gray-100">{{ $days[$index] }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ $date->format('d/m') }}</div>
                </div>
            @endforeach
        </div>

        <!-- Technicians Schedule -->
        @foreach($technicians as $technician)
            <div class="grid grid-cols-8 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                <!-- Technician Info -->
                <div class="p-4 border-l border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mr-3">
                            <span class="text-sm font-medium text-blue-600">{{ substr($technician->full_name, 0, 1) }}</span>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $technician->full_name }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                <span class="inline-flex px-2 py-1 rounded-full text-xs
                                    @if($technician->availability == 'available') bg-green-100 text-green-800
                                    @elseif($technician->availability == 'busy') bg-orange-100 text-orange-800
                                    @else bg-red-100 text-red-800 @endif">
                                    @switch($technician->availability)
                                        @case('available') متاح @break
                                        @case('busy') مشغول @break
                                        @case('on_break') استراحة @break
                                        @default غير متاح
                                    @endswitch
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Daily Schedule -->
                @foreach($currentWeek as $date)
                    <div class="p-2 border-l border-gray-200 dark:border-gray-700 min-h-[80px]">
                        @php
                            $daySchedules = $technician->schedules->where('scheduled_date', $date->format('Y-m-d'));
                        @endphp
                        
                        @if($daySchedules->count() > 0)
                            @foreach($daySchedules as $schedule)
                                <div class="mb-1 p-2 bg-blue-100 dark:bg-blue-900/20 rounded text-xs">
                                    <div class="font-medium text-blue-800 dark:text-blue-200">
                                        {{ $schedule->start_time }} - {{ $schedule->end_time }}
                                    </div>
                                    @if($schedule->repair)
                                        <div class="text-blue-600 dark:text-blue-300">
                                            {{ $schedule->repair->repair_number }}
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <div class="h-full flex items-center justify-center">
                                <button onclick="scheduleDay('{{ $technician->id }}', '{{ $date->format('Y-m-d') }}')" 
                                        class="text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        @endforeach
    </div>

    <!-- Unassigned Repairs -->
    @if($repairs->count() > 0)
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">طلبات الصيانة غير المجدولة</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($repairs as $repair)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-move" 
                         draggable="true" 
                         data-repair-id="{{ $repair->id }}">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $repair->repair_number }}</span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                @if($repair->priority == 'urgent') bg-red-100 text-red-800
                                @elseif($repair->priority == 'high') bg-orange-100 text-orange-800
                                @elseif($repair->priority == 'normal') bg-blue-100 text-blue-800
                                @else bg-gray-100 text-gray-800 @endif">
                                {{ $repair->priority_label }}
                            </span>
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <div>{{ $repair->customer->full_name }}</div>
                            <div>{{ $repair->device_brand }} {{ $repair->device_model }}</div>
                        </div>
                        <div class="mt-2 flex justify-between items-center">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $repair->created_at->diffForHumans() }}
                            </span>
                            <button onclick="quickAssign({{ $repair->id }})" 
                                    class="text-blue-600 hover:text-blue-700 text-xs">
                                تعيين سريع
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي الفنيين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $technicians->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">متاح</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $technicians->where('availability', 'available')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-clock text-orange-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">مشغول</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $technicians->where('availability', 'busy')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-tools text-purple-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">طلبات غير مجدولة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $repairs->count() }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Modal -->
<div id="scheduleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">جدولة جديدة</h3>
                <button onclick="closeScheduleModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="scheduleForm" method="POST" action="{{ route('technicians.update-schedule', 1) }}">
                @csrf
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفني</label>
                        <select name="technician_id" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            <option value="">اختر الفني</option>
                            @foreach($technicians as $technician)
                                <option value="{{ $technician->id }}">{{ $technician->full_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التاريخ</label>
                        <input type="date" name="scheduled_date" required 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وقت البداية</label>
                            <input type="time" name="start_time" required 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وقت النهاية</label>
                            <input type="time" name="end_time" required 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طلب الصيانة (اختياري)</label>
                        <select name="repair_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            <option value="">بدون طلب محدد</option>
                            @foreach($repairs as $repair)
                                <option value="{{ $repair->id }}">{{ $repair->repair_number }} - {{ $repair->customer->full_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات</label>
                        <textarea name="notes" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                                  placeholder="ملاحظات إضافية..."></textarea>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-2 space-x-reverse mt-6">
                    <button type="button" onclick="closeScheduleModal()" 
                            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function openScheduleModal() {
    document.getElementById('scheduleModal').classList.remove('hidden');
}

function closeScheduleModal() {
    document.getElementById('scheduleModal').classList.add('hidden');
}

function scheduleDay(technicianId, date) {
    document.querySelector('select[name="technician_id"]').value = technicianId;
    document.querySelector('input[name="scheduled_date"]').value = date;
    openScheduleModal();
}

function quickAssign(repairId) {
    // Implementation for quick assignment
    alert('ميزة التعيين السريع قيد التطوير');
}

// Drag and drop functionality for repairs
document.addEventListener('DOMContentLoaded', function() {
    const repairs = document.querySelectorAll('[data-repair-id]');
    
    repairs.forEach(repair => {
        repair.addEventListener('dragstart', function(e) {
            e.dataTransfer.setData('text/plain', this.dataset.repairId);
        });
    });
    
    // Add drop zones for schedule cells
    const scheduleCells = document.querySelectorAll('.grid-cols-8 > div:not(:first-child)');
    
    scheduleCells.forEach(cell => {
        cell.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('bg-blue-50', 'dark:bg-blue-900/10');
        });
        
        cell.addEventListener('dragleave', function(e) {
            this.classList.remove('bg-blue-50', 'dark:bg-blue-900/10');
        });
        
        cell.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('bg-blue-50', 'dark:bg-blue-900/10');
            
            const repairId = e.dataTransfer.getData('text/plain');
            // Implementation for dropping repair into schedule
            console.log('Dropped repair', repairId, 'into cell');
        });
    });
});
</script>
@endpush
@endsection
