@extends('layouts.main')

@section('title', 'التقارير التشغيلية')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">التقارير التشغيلية</h1>
            <p class="text-gray-600 dark:text-gray-400">تحليل الأداء التشغيلي والإنتاجية</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('reports.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة للتقارير
            </a>
            
            <button onclick="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير التقرير
            </button>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" name="start_date" value="{{ $startDate->format('Y-m-d') }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" name="end_date" value="{{ $endDate->format('Y-m-d') }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                <i class="fas fa-filter mr-2"></i>
                تطبيق الفلتر
            </button>
        </form>
    </div>

    <!-- Repair Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100">إجمالي الإصلاحات</p>
                    <p class="text-3xl font-bold">{{ $repairStats['total'] ?? 0 }}</p>
                    <p class="text-blue-100 text-sm">طلب إصلاح</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-tools"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100">مكتملة</p>
                    <p class="text-3xl font-bold">{{ $repairStats['completed'] ?? 0 }}</p>
                    <p class="text-green-100 text-sm">إصلاح مكتمل</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100">قيد التنفيذ</p>
                    <p class="text-3xl font-bold">{{ $repairStats['in_progress'] ?? 0 }}</p>
                    <p class="text-orange-100 text-sm">إصلاح جاري</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-cog fa-spin"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-red-500 to-red-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-red-100">معلقة</p>
                    <p class="text-3xl font-bold">{{ $repairStats['pending'] ?? 0 }}</p>
                    <p class="text-red-100 text-sm">في الانتظار</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Technician Performance -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">أداء الفنيين</h3>
            <p class="text-gray-600 dark:text-gray-400">إحصائيات أداء الفنيين خلال الفترة المحددة</p>
        </div>
        <div class="p-6">
            @if(isset($technicianStats) && $technicianStats->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفني</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">عدد الإصلاحات</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">إجمالي الإيرادات</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">متوسط الوقت</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($technicianStats as $technician)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                                                    {{ substr($technician->first_name ?? 'ف', 0, 1) }}
                                                </div>
                                            </div>
                                            <div class="mr-4">
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ $technician->first_name }} {{ $technician->last_name }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $technician->specialization ?? 'عام' }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        {{ $technician->repairs_count ?? 0 }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                        {{ number_format($technician->repairs_sum_total_cost ?? 0, 2) }} ريال
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        {{ number_format(($technician->avg_completion_time ?? 0) / 24, 1) }} يوم
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $technician->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $technician->is_active ? 'نشط' : 'غير نشط' }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-user-cog text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد بيانات فنيين للفترة المحددة</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Device Statistics and Status Distribution -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Device Statistics -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">إحصائيات الأجهزة</h3>
                <p class="text-gray-600 dark:text-gray-400">أنواع الأجهزة الأكثر إصلاحاً</p>
            </div>
            <div class="p-6">
                @if(isset($deviceStats) && $deviceStats->count() > 0)
                    <div class="space-y-4">
                        @foreach($deviceStats as $device)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-mobile-alt text-blue-500 mr-3"></i>
                                    <span class="text-gray-900 dark:text-gray-100">{{ $device->device_type ?? 'غير محدد' }}</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-gray-600 dark:text-gray-400 mr-2">{{ $device->count }}</span>
                                    <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: {{ ($device->count / $deviceStats->max('count')) * 100 }}%"></div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-mobile text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400">لا توجد بيانات أجهزة</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Status Distribution -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">توزيع الحالات</h3>
                <p class="text-gray-600 dark:text-gray-400">توزيع طلبات الإصلاح حسب الحالة</p>
            </div>
            <div class="p-6">
                @if(isset($statusDistribution) && $statusDistribution->count() > 0)
                    <div class="space-y-4">
                        @foreach($statusDistribution as $status)
                            @php
                                $statusColors = [
                                    'pending' => 'text-yellow-500',
                                    'in_progress' => 'text-blue-500',
                                    'completed' => 'text-green-500',
                                    'cancelled' => 'text-red-500',
                                    'waiting_parts' => 'text-orange-500'
                                ];
                                $statusLabels = [
                                    'pending' => 'معلق',
                                    'in_progress' => 'قيد التنفيذ',
                                    'completed' => 'مكتمل',
                                    'cancelled' => 'ملغي',
                                    'waiting_parts' => 'انتظار قطع'
                                ];
                            @endphp
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-circle {{ $statusColors[$status->status] ?? 'text-gray-500' }} mr-3"></i>
                                    <span class="text-gray-900 dark:text-gray-100">{{ $statusLabels[$status->status] ?? $status->status }}</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-gray-600 dark:text-gray-400 mr-2">{{ $status->count }}</span>
                                    <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: {{ ($status->count / $statusDistribution->max('count')) * 100 }}%"></div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-chart-pie text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400">لا توجد بيانات حالات</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function exportReport() {
    // يمكن تطوير هذه الوظيفة لاحقاً
    alert('ميزة التصدير قيد التطوير');
}
</script>
@endpush
@endsection
