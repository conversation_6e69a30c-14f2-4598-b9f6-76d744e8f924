@extends('layouts.main')

@section('title', 'التقارير المحاسبية')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">التقارير المحاسبية</h1>
            <p class="text-gray-600 dark:text-gray-400">التقارير المالية والمحاسبية الأساسية</p>
        </div>
        
        <div class="text-sm text-gray-500 dark:text-gray-400">
            كما في: {{ $asOfDate }}
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <!-- Total Assets -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-coins text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الأصول</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($totalAssets, 0) }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">ريال سعودي</p>
                </div>
            </div>
        </div>

        <!-- Total Liabilities -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-credit-card text-red-600 dark:text-red-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الخصوم</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($totalLiabilities, 0) }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">ريال سعودي</p>
                </div>
            </div>
        </div>

        <!-- Total Equity -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-pie text-purple-600 dark:text-purple-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">حقوق الملكية</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($totalEquity, 0) }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">ريال سعودي</p>
                </div>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-arrow-up text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الإيرادات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($totalRevenue, 0) }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">ريال سعودي</p>
                </div>
            </div>
        </div>

        <!-- Total Expenses -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-arrow-down text-orange-600 dark:text-orange-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المصروفات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($totalExpenses, 0) }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">ريال سعودي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Trial Balance -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-balance-scale text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">ميزان المراجعة</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">جميع الحسابات وأرصدتها</p>
                    </div>
                </div>
            </div>
            <a href="{{ route('accounting.reports.trial-balance') }}" 
               class="inline-flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                عرض التقرير
                <i class="fas fa-arrow-left mr-2"></i>
            </a>
        </div>

        <!-- Balance Sheet -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-chart-bar text-green-600 dark:text-green-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الميزانية العمومية</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">الأصول والخصوم وحقوق الملكية</p>
                    </div>
                </div>
            </div>
            <a href="{{ route('accounting.reports.balance-sheet') }}" 
               class="inline-flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                عرض التقرير
                <i class="fas fa-arrow-left mr-2"></i>
            </a>
        </div>

        <!-- Income Statement -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-chart-line text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">قائمة الدخل</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">الإيرادات والمصروفات والأرباح</p>
                    </div>
                </div>
            </div>
            <a href="{{ route('accounting.reports.income-statement') }}" 
               class="inline-flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                عرض التقرير
                <i class="fas fa-arrow-left mr-2"></i>
            </a>
        </div>

        <!-- Cash Flow -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-exchange-alt text-indigo-600 dark:text-indigo-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">التدفقات النقدية</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">حركة النقدية والأنشطة</p>
                    </div>
                </div>
            </div>
            <a href="{{ route('accounting.reports.cash-flow') }}" 
               class="inline-flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                عرض التقرير
                <i class="fas fa-arrow-left mr-2"></i>
            </a>
        </div>

        <!-- Chart of Accounts -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-list-alt text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">دليل الحسابات</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">جميع الحسابات المحاسبية</p>
                    </div>
                </div>
            </div>
            <a href="{{ route('accounts.index') }}" 
               class="inline-flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                عرض الدليل
                <i class="fas fa-arrow-left mr-2"></i>
            </a>
        </div>

        <!-- Journal Entries -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-file-invoice text-red-600 dark:text-red-400"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">القيود المحاسبية</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">جميع القيود والمعاملات</p>
                    </div>
                </div>
            </div>
            <a href="{{ route('journal-entries.index') }}" 
               class="inline-flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                عرض القيود
                <i class="fas fa-arrow-left mr-2"></i>
            </a>
        </div>
    </div>

    <!-- Recent Journal Entries -->
    @if($recentEntries->count() > 0)
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">آخر القيود المحاسبية</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            رقم القيد
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            التاريخ
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الوصف
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المبلغ
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الحالة
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($recentEntries as $entry)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <a href="{{ route('journal-entries.show', $entry) }}" 
                                   class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                                    {{ $entry->entry_number }}
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ $entry->entry_date->format('Y-m-d') }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                {{ Str::limit($entry->description, 50) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                {{ number_format($entry->total_debit, 2) }} ريال
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $entry->getStatusColor() }}">
                                    {{ $entry->getStatusLabel() }}
                                </span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @endif
</div>
@endsection
