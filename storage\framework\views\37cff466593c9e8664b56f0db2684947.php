<?php $__env->startSection('title', 'إدارة الموردين'); ?>

<?php $__env->startSection('content'); ?>
<div class="content-wrapper animate-fade-in-up" x-data="suppliersManager()">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">إدارة الموردين</h1>
                    <p class="page-subtitle">إدارة بيانات الموردين والتعاملات التجارية بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="exportSuppliers()" class="btn btn-success hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        تصدير
                    </button>
                    <button @click="addSupplier()" class="btn btn-primary hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        إضافة مورد جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card hover-lift animate-scale-in animate-delay-100">
            <div class="stat-value" style="color: var(--accent-color);" x-text="stats.totalSuppliers">5</div>
            <div class="stat-label">إجمالي الموردين</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path>
                </svg>
                شراكات متنوعة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-200">
            <div class="stat-value" style="color: var(--success-color);" x-text="stats.activeSuppliers">5</div>
            <div class="stat-label">موردين نشطين</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                جميعهم نشطين
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-300">
            <div class="stat-value" style="color: var(--warning-color);" x-text="stats.totalOutstanding">₪15,750</div>
            <div class="stat-label">إجمالي المستحقات</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                تحتاج متابعة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-500">
            <div class="stat-value" style="color: var(--accent-color);" x-text="stats.totalPurchases">₪8,500</div>
            <div class="stat-label">مشتريات هذا الشهر</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                نشاط جيد
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card animate-fade-in-up animate-delay-300">
        <div class="card-header">
            <h3 class="card-title">فلترة وبحث الموردين</h3>
            <button @click="clearFilters()" class="btn btn-ghost btn-sm hover-scale">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                مسح الفلاتر
            </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="form-group">
                <label class="form-label">البحث</label>
                <div class="search-box">
                    <input type="text" x-model="filters.search" @input="filterSuppliers()"
                           placeholder="البحث في الموردين..."
                           class="search-input focus-glow">
                    <svg class="search-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">الحالة</label>
                <select x-model="filters.status" @change="filterSuppliers()" class="form-select focus-glow">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">المدينة</label>
                <select x-model="filters.city" @change="filterSuppliers()" class="form-select focus-glow">
                    <option value="">جميع المدن</option>
                    <option value="ramallah">رام الله</option>
                    <option value="nablus">نابلس</option>
                    <option value="hebron">الخليل</option>
                </select>
            </div>
            <div class="flex items-end">
                <button @click="applyFilters()" class="btn btn-secondary w-full hover-lift ripple">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"></path>
                    </svg>
                    تطبيق الفلاتر
                </button>
            </div>
        </div>
    </div>

    <!-- Suppliers Table -->
    <div class="table-container animate-fade-in-up animate-delay-500">
        <div class="card-header">
            <h3 class="card-title">قائمة الموردين</h3>
            <div class="action-group">
                <button @click="refreshSuppliers()" class="btn btn-ghost btn-sm hover-rotate">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    تحديث
                </button>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="table">
                <thead>
                    <tr>
                        <th>المورد</th>
                        <th>معلومات الاتصال</th>
                        <th>إجمالي المشتريات</th>
                        <th>المستحقات</th>
                        <th>التقييم</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="(supplier, index) in filteredSuppliers" :key="supplier.id">
                        <tr class="hover-lift" :class="'animate-fade-in-up animate-delay-' + (index * 100)">
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="w-12 h-12 rounded-full bg-accent-color/10 flex items-center justify-center">
                                        <span class="text-accent-color font-medium" x-text="supplier.name.split(' ').map(n => n[0]).join('').substring(0, 2)"></span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="supplier.name"></div>
                                        <div class="text-sm text-muted-light dark:text-muted-dark" x-text="'SUP' + supplier.id.toString().padStart(3, '0')"></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="text-sm text-primary-light dark:text-primary-dark" x-text="supplier.phone"></div>
                                <div class="text-sm text-muted-light dark:text-muted-dark" x-text="supplier.email"></div>
                            </td>
                            <td class="text-sm font-semibold text-success-color" x-text="'₪' + supplier.total_purchases.toLocaleString()"></td>
                            <td>
                                <span class="text-sm font-semibold" :class="supplier.outstanding_balance > 0 ? 'text-warning-color' : 'text-success-color'" x-text="'₪' + supplier.outstanding_balance.toLocaleString()"></span>
                            </td>
                            <td>
                                <div class="flex items-center gap-1">
                                    <template x-for="i in 5" :key="i">
                                        <svg class="w-4 h-4" :class="i <= supplier.rating ? 'text-yellow-400' : 'text-gray-300'" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    </template>
                                    <span class="text-xs text-muted-light dark:text-muted-dark mr-1" x-text="supplier.rating"></span>
                                </div>
                            </td>
                            <td>
                                <span class="badge" :class="getStatusBadgeClass(supplier.status)" x-text="getStatusText(supplier.status)"></span>
                            </td>
                            <td>
                                <div class="action-group">
                                    <button @click="viewSupplier(supplier.id)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        عرض
                                    </button>
                                    <button @click="editSupplier(supplier.id)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        تعديل
                                    </button>
                                    <button @click="deleteSupplier(supplier.id)" class="btn btn-ghost btn-sm hover-scale" style="color: var(--danger-color);">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        حذف
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        السابق
                    </button>
                    <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        التالي
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700 dark:text-gray-300">
                            عرض <span class="font-medium">1</span> إلى <span class="font-medium">5</span> من <span class="font-medium">5</span> نتيجة
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">السابق</span>
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                1
                            </button>
                            <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">التالي</span>
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function suppliersManager() {
    return {
        suppliers: [
            {
                id: 1,
                name: 'شركة التقنية المتقدمة',
                contact_person: 'أحمد محمد',
                phone: '02-123-4567',
                email: '<EMAIL>',
                address: 'رام الله، فلسطين',
                total_purchases: 25000,
                outstanding_balance: 5000,
                status: 'active',
                rating: 4.5,
                last_order: '2024-07-05'
            },
            {
                id: 2,
                name: 'مؤسسة الإلكترونيات الحديثة',
                contact_person: 'سارة أحمد',
                phone: '02-234-5678',
                email: '<EMAIL>',
                address: 'نابلس، فلسطين',
                total_purchases: 18000,
                outstanding_balance: 3000,
                status: 'active',
                rating: 4.2,
                last_order: '2024-07-08'
            },
            {
                id: 3,
                name: 'شركة قطع الغيار المحدودة',
                contact_person: 'محمد علي',
                phone: '02-345-6789',
                email: '<EMAIL>',
                address: 'الخليل، فلسطين',
                total_purchases: 32000,
                outstanding_balance: 7500,
                status: 'active',
                rating: 4.8,
                last_order: '2024-07-09'
            }
        ],
        filteredSuppliers: [],
        filters: {
            search: '',
            status: '',
            city: ''
        },
        stats: {
            totalSuppliers: 5,
            activeSuppliers: 5,
            totalOutstanding: '₪15,750',
            totalPurchases: '₪8,500'
        },

        init() {
            this.filteredSuppliers = [...this.suppliers];
        },

        filterSuppliers() {
            this.filteredSuppliers = this.suppliers.filter(supplier => {
                const matchesSearch = !this.filters.search ||
                    supplier.name.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    supplier.contact_person.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    supplier.email.toLowerCase().includes(this.filters.search.toLowerCase());

                const matchesStatus = !this.filters.status || supplier.status === this.filters.status;

                const matchesCity = !this.filters.city ||
                    supplier.address.toLowerCase().includes(this.filters.city.toLowerCase());

                return matchesSearch && matchesStatus && matchesCity;
            });
        },

        applyFilters() {
            this.filterSuppliers();
            if (window.showNotification) {
                window.showNotification('تم تطبيق الفلاتر', 'info');
            }
        },

        clearFilters() {
            this.filters = {
                search: '',
                status: '',
                city: ''
            };
            this.filteredSuppliers = [...this.suppliers];

            if (window.showNotification) {
                window.showNotification('تم مسح جميع الفلاتر', 'info');
            }
        },

        addSupplier() {
            if (window.showNotification) {
                window.showNotification('سيتم إضافة نافذة إنشاء مورد جديد قريباً', 'info');
            }
        },

        editSupplier(id) {
            if (window.showNotification) {
                window.showNotification(`سيتم فتح نافذة تعديل المورد رقم ${id}`, 'info');
            }
        },

        viewSupplier(id) {
            if (window.showNotification) {
                window.showNotification(`سيتم عرض تفاصيل المورد رقم ${id}`, 'info');
            }
        },

        deleteSupplier(id) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                this.suppliers = this.suppliers.filter(supplier => supplier.id !== id);
                this.filteredSuppliers = [...this.suppliers];

                if (window.showNotification) {
                    window.showNotification('تم حذف المورد بنجاح', 'success');
                }
            }
        },

        refreshSuppliers() {
            // إضافة تأثير التحديث
            const button = event.target;
            button.classList.add('animate-spin');

            // محاكاة تحديث البيانات
            setTimeout(() => {
                button.classList.remove('animate-spin');
                if (window.showNotification) {
                    window.showNotification('تم تحديث بيانات الموردين', 'info');
                }
            }, 1000);
        },

        getStatusText(status) {
            const statuses = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'suspended': 'معلق'
            };
            return statuses[status] || status;
        },

        getStatusBadgeClass(status) {
            const classes = {
                'active': 'badge-success',
                'inactive': 'badge-secondary',
                'suspended': 'badge-warning'
            };
            return classes[status] || 'badge-secondary';
        },

        exportSuppliers() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري التصدير...
            `;
            button.disabled = true;

            // محاكاة عملية التصدير
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                if (window.showNotification) {
                    window.showNotification('تم تصدير بيانات الموردين إلى Excel بنجاح!', 'success');
                }
            }, 2000);
        }
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\tareq\resources\views/suppliers/index.blade.php ENDPATH**/ ?>