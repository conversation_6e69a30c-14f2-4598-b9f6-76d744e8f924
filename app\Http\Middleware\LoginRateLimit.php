<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;

class LoginRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // تطبيق Rate Limiting فقط على طلبات تسجيل الدخول
        if ($request->isMethod('POST') && $request->routeIs('login')) {
            // التحقق من Rate Limiting قبل محاولة تسجيل الدخول
            $this->checkRateLimit($request);

            // تخزين الوقت الحالي في session للتحقق من نجاح تسجيل الدخول
            $request->session()->put('login_attempt_time', now()->timestamp);
        }

        return $next($request);
    }

    /**
     * التحقق من Rate Limiting
     */
    protected function checkRateLimit(Request $request)
    {
        $key = 'login_attempts:' . $request->ip();
        $maxAttempts = config('auth.login_rate_limit.max_attempts', 5);
        $decayMinutes = config('auth.login_rate_limit.decay_minutes', 15);
        
        $attempts = Cache::get($key, 0);
        
        if ($attempts >= $maxAttempts) {
            $remainingTime = Cache::get($key . ':lockout_until');
            
            if ($remainingTime && now()->lessThan($remainingTime)) {
                $minutes = now()->diffInMinutes($remainingTime);
                
                throw ValidationException::withMessages([
                    'email' => "تم تجاوز عدد المحاولات المسموح. يرجى المحاولة بعد {$minutes} دقيقة.",
                ]);
            } else {
                // انتهت فترة الحظر، إعادة تعيين العداد
                Cache::forget($key);
                Cache::forget($key . ':lockout_until');
            }
        }
    }

    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     */
    public static function recordFailedAttempt(Request $request)
    {
        $key = 'login_attempts:' . $request->ip();
        $maxAttempts = config('auth.login_rate_limit.max_attempts', 5);
        $decayMinutes = config('auth.login_rate_limit.decay_minutes', 15);
        
        $attempts = Cache::get($key, 0) + 1;
        
        if ($attempts >= $maxAttempts) {
            // تعيين وقت انتهاء الحظر
            Cache::put($key . ':lockout_until', now()->addMinutes($decayMinutes), now()->addMinutes($decayMinutes));
        }
        
        Cache::put($key, $attempts, now()->addMinutes($decayMinutes));
    }

    /**
     * مسح محاولات تسجيل الدخول الفاشلة
     */
    public static function clearFailedAttempts(Request $request)
    {
        $key = 'login_attempts:' . $request->ip();
        Cache::forget($key);
        Cache::forget($key . ':lockout_until');
    }
}
