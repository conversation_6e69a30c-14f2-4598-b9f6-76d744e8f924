@extends('layouts.main')

@section('title', 'الملف الشخصي')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">الملف الشخصي</h1>
            <p class="text-gray-600 dark:text-gray-400">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile Info -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <div class="text-center">
                    <div class="mx-auto h-32 w-32 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700">
                        <img src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name) }}&background=3B82F6&color=fff&size=128" 
                             alt="صورة المستخدم" class="h-full w-full object-cover">
                    </div>
                    <h3 class="mt-4 text-xl font-medium text-gray-900 dark:text-gray-100">{{ auth()->user()->name }}</h3>
                    <p class="text-gray-500 dark:text-gray-400">{{ auth()->user()->role->name ?? 'مستخدم' }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ auth()->user()->department ?? 'غير محدد' }}</p>
                    
                    <div class="mt-4 flex justify-center">
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            {{ auth()->user()->is_active ? 'نشط' : 'غير نشط' }}
                        </span>
                    </div>

                    <button class="mt-4 w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        تغيير الصورة الشخصية
                    </button>
                </div>

                <!-- Quick Stats -->
                <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6">
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400">رقم الموظف</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ auth()->user()->employee_id ?? 'غير محدد' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400">تاريخ التوظيف</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {{ auth()->user()->hire_date ? auth()->user()->hire_date->format('Y-m-d') : 'غير محدد' }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400">عدد مرات الدخول</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ auth()->user()->login_count ?? 0 }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400">آخر دخول</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {{ auth()->user()->last_login_at ? auth()->user()->last_login_at->diffForHumans() : 'لم يسجل دخول من قبل' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Details -->
        <div class="lg:col-span-2">
            <div class="space-y-6">
                <!-- Personal Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">المعلومات الشخصية</h3>
                        <button class="text-blue-600 hover:text-blue-700 dark:text-blue-400">تعديل</button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الكامل</label>
                            <input type="text" value="{{ auth()->user()->name }}" readonly 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 dark:text-gray-100">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                            <input type="email" value="{{ auth()->user()->email }}" readonly 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 dark:text-gray-100">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                            <input type="text" value="{{ auth()->user()->phone ?? 'غير محدد' }}" readonly 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 dark:text-gray-100">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">القسم</label>
                            <input type="text" value="{{ auth()->user()->department ?? 'غير محدد' }}" readonly 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 dark:text-gray-100">
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">إعدادات الأمان</h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">كلمة المرور</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">آخر تغيير منذ 30 يوماً</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-700 dark:text-blue-400">تغيير</button>
                        </div>

                        <div class="flex justify-between items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">المصادقة الثنائية</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">غير مفعلة</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-700 dark:text-blue-400">تفعيل</button>
                        </div>

                        <div class="flex justify-between items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">الجلسات النشطة</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">جلسة واحدة نشطة</p>
                            </div>
                            <button class="text-red-600 hover:text-red-700 dark:text-red-400">إنهاء الجلسات</button>
                        </div>
                    </div>
                </div>

                <!-- Preferences -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">التفضيلات</h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">اللغة</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">العربية</p>
                            </div>
                            <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-gray-100">
                                <option value="ar">العربية</option>
                                <option value="en">English</option>
                            </select>
                        </div>

                        <div class="flex justify-between items-center">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">المنطقة الزمنية</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Asia/Gaza</p>
                            </div>
                            <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-gray-100">
                                <option value="Asia/Gaza">Asia/Gaza</option>
                                <option value="Asia/Jerusalem">Asia/Jerusalem</option>
                            </select>
                        </div>

                        <div class="flex justify-between items-center">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">الإشعارات</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">تلقي إشعارات البريد الإلكتروني</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>

                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            حفظ التغييرات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
