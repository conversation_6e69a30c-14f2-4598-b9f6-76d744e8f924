@extends('layouts.main')

@section('title', 'الجدول الزمني للصيانة - ' . $repair->repair_number)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">الجدول الزمني للصيانة</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $repair->repair_number }} - {{ $repair->customer->full_name }}</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('repairs.show', $repair) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة للطلب
            </a>
            <a href="{{ route('repairs.print', $repair) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-print mr-2"></i>
                طباعة
            </a>
        </div>
    </div>

    <!-- Repair Summary -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">معلومات الجهاز</h3>
                <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {{ $repair->device_brand }} {{ $repair->device_model }}
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $repair->device_type }}</p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">الفني المسؤول</h3>
                <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {{ $repair->technician->full_name ?? 'غير محدد' }}
                </p>
                @if($repair->technician)
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $repair->technician->specialization ?? 'فني عام' }}</p>
                @endif
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">الحالة الحالية</h3>
                <span class="mt-1 inline-flex px-3 py-1 text-sm font-semibold rounded-full {{ $repair->status_color }}">
                    {{ $repair->status_label }}
                </span>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    منذ {{ $repair->updated_at->diffForHumans() }}
                </p>
            </div>
        </div>
    </div>

    <!-- Timeline -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تسلسل الأحداث</h3>
        </div>
        <div class="p-6">
            <div class="flow-root">
                <ul class="-mb-8">
                    <!-- Creation Event -->
                    <li>
                        <div class="relative pb-8">
                            <span class="absolute top-4 right-4 -mr-px h-full w-0.5 bg-gray-200 dark:bg-gray-600" aria-hidden="true"></span>
                            <div class="relative flex space-x-3 space-x-reverse">
                                <div>
                                    <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white dark:ring-gray-800">
                                        <i class="fas fa-plus text-white text-sm"></i>
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4 space-x-reverse">
                                    <div>
                                        <p class="text-sm text-gray-900 dark:text-gray-100">تم إنشاء طلب الصيانة</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $repair->problem_description }}</p>
                                    </div>
                                    <div class="text-left whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <time datetime="{{ $repair->created_at->toISOString() }}">
                                            {{ $repair->created_at->format('Y-m-d H:i') }}
                                        </time>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>

                    <!-- Status History -->
                    @foreach($repair->statusHistory as $history)
                    <li>
                        <div class="relative pb-8">
                            @if(!$loop->last)
                                <span class="absolute top-4 right-4 -mr-px h-full w-0.5 bg-gray-200 dark:bg-gray-600" aria-hidden="true"></span>
                            @endif
                            <div class="relative flex space-x-3 space-x-reverse">
                                <div>
                                    <span class="h-8 w-8 rounded-full {{ getStatusIconColor($history->status) }} flex items-center justify-center ring-8 ring-white dark:ring-gray-800">
                                        <i class="{{ getStatusIcon($history->status) }} text-white text-sm"></i>
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4 space-x-reverse">
                                    <div>
                                        <p class="text-sm text-gray-900 dark:text-gray-100">
                                            تم تغيير الحالة إلى: <span class="font-medium">{{ getStatusLabel($history->status) }}</span>
                                        </p>
                                        @if($history->notes)
                                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $history->notes }}</p>
                                        @endif
                                        @if($history->changedBy)
                                            <p class="text-xs text-gray-400 dark:text-gray-500">بواسطة: {{ $history->changedBy->name }}</p>
                                        @endif
                                    </div>
                                    <div class="text-left whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <time datetime="{{ $history->changed_at->toISOString() }}">
                                            {{ $history->changed_at->format('Y-m-d H:i') }}
                                        </time>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    @endforeach

                    <!-- Parts Added -->
                    @foreach($repair->parts as $part)
                    <li>
                        <div class="relative pb-8">
                            @if(!$loop->last || $repair->payments->count() > 0)
                                <span class="absolute top-4 right-4 -mr-px h-full w-0.5 bg-gray-200 dark:bg-gray-600" aria-hidden="true"></span>
                            @endif
                            <div class="relative flex space-x-3 space-x-reverse">
                                <div>
                                    <span class="h-8 w-8 rounded-full bg-purple-500 flex items-center justify-center ring-8 ring-white dark:ring-gray-800">
                                        <i class="fas fa-cog text-white text-sm"></i>
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4 space-x-reverse">
                                    <div>
                                        <p class="text-sm text-gray-900 dark:text-gray-100">تم إضافة قطعة غيار</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $part->name }} - الكمية: {{ $part->pivot->quantity }}
                                            @if($part->pivot->unit_price)
                                                - السعر: {{ number_format($part->pivot->unit_price, 2) }} ريال
                                            @endif
                                        </p>
                                    </div>
                                    <div class="text-left whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <time datetime="{{ $part->pivot->created_at }}">
                                            {{ \Carbon\Carbon::parse($part->pivot->created_at)->format('Y-m-d H:i') }}
                                        </time>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    @endforeach

                    <!-- Payments -->
                    @foreach($repair->payments as $payment)
                    <li>
                        <div class="relative pb-8">
                            @if(!$loop->last)
                                <span class="absolute top-4 right-4 -mr-px h-full w-0.5 bg-gray-200 dark:bg-gray-600" aria-hidden="true"></span>
                            @endif
                            <div class="relative flex space-x-3 space-x-reverse">
                                <div>
                                    <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white dark:ring-gray-800">
                                        <i class="fas fa-money-bill text-white text-sm"></i>
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4 space-x-reverse">
                                    <div>
                                        <p class="text-sm text-gray-900 dark:text-gray-100">تم استلام دفعة</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            المبلغ: {{ number_format($payment->amount, 2) }} ريال
                                            - طريقة الدفع: {{ $payment->payment_method }}
                                        </p>
                                    </div>
                                    <div class="text-left whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <time datetime="{{ $payment->created_at->toISOString() }}">
                                            {{ $payment->created_at->format('Y-m-d H:i') }}
                                        </time>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>

    <!-- Cost Summary -->
    @if($repair->total_cost > 0)
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">ملخص التكلفة</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p class="text-sm text-blue-600 dark:text-blue-400">تكلفة العمالة</p>
                <p class="text-xl font-bold text-blue-900 dark:text-blue-100">{{ number_format($repair->labor_cost, 2) }}</p>
            </div>
            <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <p class="text-sm text-purple-600 dark:text-purple-400">تكلفة القطع</p>
                <p class="text-xl font-bold text-purple-900 dark:text-purple-100">{{ number_format($repair->parts_cost, 2) }}</p>
            </div>
            <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <p class="text-sm text-yellow-600 dark:text-yellow-400">الخصم</p>
                <p class="text-xl font-bold text-yellow-900 dark:text-yellow-100">{{ number_format($repair->discount_amount, 2) }}</p>
            </div>
            <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <p class="text-sm text-green-600 dark:text-green-400">الإجمالي</p>
                <p class="text-xl font-bold text-green-900 dark:text-green-100">{{ number_format($repair->total_cost, 2) }}</p>
            </div>
        </div>
    </div>
    @endif
</div>

@php
function getStatusIcon($status) {
    return match($status) {
        'pending' => 'fas fa-clock',
        'diagnosed' => 'fas fa-search',
        'in_progress' => 'fas fa-tools',
        'waiting_parts' => 'fas fa-box',
        'completed' => 'fas fa-check',
        'delivered' => 'fas fa-handshake',
        'cancelled' => 'fas fa-times',
        'on_hold' => 'fas fa-pause',
        default => 'fas fa-circle'
    };
}

function getStatusIconColor($status) {
    return match($status) {
        'pending' => 'bg-yellow-500',
        'diagnosed' => 'bg-blue-500',
        'in_progress' => 'bg-orange-500',
        'waiting_parts' => 'bg-purple-500',
        'completed' => 'bg-green-500',
        'delivered' => 'bg-teal-500',
        'cancelled' => 'bg-red-500',
        'on_hold' => 'bg-gray-500',
        default => 'bg-gray-400'
    };
}

function getStatusLabel($status) {
    return match($status) {
        'pending' => 'في الانتظار',
        'diagnosed' => 'تم التشخيص',
        'in_progress' => 'قيد التنفيذ',
        'waiting_parts' => 'انتظار قطع غيار',
        'completed' => 'مكتمل',
        'delivered' => 'تم التسليم',
        'cancelled' => 'ملغي',
        'on_hold' => 'معلق',
        default => $status
    };
}
@endphp
@endsection
