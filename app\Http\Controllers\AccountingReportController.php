<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\AccountTransaction;
use App\Services\AccountingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AccountingReportController extends Controller
{
    protected $accountingService;

    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }

    /**
     * Display accounting reports dashboard
     */
    public function index()
    {
        $asOfDate = now()->toDateString();
        
        // Get summary data
        $totalAssets = Account::where('account_type', 'asset')
                             ->where('is_active', true)
                             ->sum('current_balance');
        
        $totalLiabilities = Account::where('account_type', 'liability')
                                  ->where('is_active', true)
                                  ->sum('current_balance');
        
        $totalEquity = Account::where('account_type', 'equity')
                             ->where('is_active', true)
                             ->sum('current_balance');
        
        $totalRevenue = Account::where('account_type', 'revenue')
                              ->where('is_active', true)
                              ->sum('current_balance');
        
        $totalExpenses = Account::where('account_type', 'expense')
                               ->where('is_active', true)
                               ->sum('current_balance');

        // Recent journal entries
        $recentEntries = JournalEntry::with(['creator', 'transactions.account'])
                                   ->where('status', 'posted')
                                   ->orderBy('entry_date', 'desc')
                                   ->limit(10)
                                   ->get();

        return view('accounting.reports.index', compact(
            'totalAssets', 'totalLiabilities', 'totalEquity', 
            'totalRevenue', 'totalExpenses', 'recentEntries', 'asOfDate'
        ));
    }

    /**
     * Display trial balance
     */
    public function trialBalance(Request $request)
    {
        $asOfDate = $request->get('as_of_date', now()->toDateString());
        
        $trialBalance = $this->accountingService->getTrialBalance($asOfDate);
        
        return view('accounting.reports.trial-balance', compact('trialBalance', 'asOfDate'));
    }

    /**
     * Display balance sheet
     */
    public function balanceSheet(Request $request)
    {
        $asOfDate = $request->get('as_of_date', now()->toDateString());
        
        // Assets
        $currentAssets = Account::where('account_type', 'asset')
                               ->where('account_category', 'current_asset')
                               ->where('is_active', true)
                               ->with('children')
                               ->whereNull('parent_id')
                               ->get();
        
        $fixedAssets = Account::where('account_type', 'asset')
                             ->where('account_category', 'fixed_asset')
                             ->where('is_active', true)
                             ->with('children')
                             ->whereNull('parent_id')
                             ->get();
        
        // Liabilities
        $currentLiabilities = Account::where('account_type', 'liability')
                                    ->where('account_category', 'current_liability')
                                    ->where('is_active', true)
                                    ->with('children')
                                    ->whereNull('parent_id')
                                    ->get();
        
        // Equity
        $equity = Account::where('account_type', 'equity')
                        ->where('is_active', true)
                        ->with('children')
                        ->whereNull('parent_id')
                        ->get();

        // Calculate totals
        $totalCurrentAssets = $this->calculateAccountGroupTotal($currentAssets, $asOfDate);
        $totalFixedAssets = $this->calculateAccountGroupTotal($fixedAssets, $asOfDate);
        $totalAssets = $totalCurrentAssets + $totalFixedAssets;
        
        $totalCurrentLiabilities = $this->calculateAccountGroupTotal($currentLiabilities, $asOfDate);
        $totalEquity = $this->calculateAccountGroupTotal($equity, $asOfDate);
        $totalLiabilitiesAndEquity = $totalCurrentLiabilities + $totalEquity;

        return view('accounting.reports.balance-sheet', compact(
            'currentAssets', 'fixedAssets', 'currentLiabilities', 'equity',
            'totalCurrentAssets', 'totalFixedAssets', 'totalAssets',
            'totalCurrentLiabilities', 'totalEquity', 'totalLiabilitiesAndEquity',
            'asOfDate'
        ));
    }

    /**
     * Display income statement
     */
    public function incomeStatement(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->toDateString());
        $endDate = $request->get('end_date', now()->toDateString());
        
        // Revenue accounts
        $revenueAccounts = Account::where('account_type', 'revenue')
                                 ->where('is_active', true)
                                 ->with('children')
                                 ->whereNull('parent_id')
                                 ->get();
        
        // Expense accounts
        $expenseAccounts = Account::where('account_type', 'expense')
                                 ->where('is_active', true)
                                 ->with('children')
                                 ->whereNull('parent_id')
                                 ->get();

        // Calculate totals for the period
        $totalRevenue = $this->calculateAccountGroupTotalForPeriod($revenueAccounts, $startDate, $endDate);
        $totalExpenses = $this->calculateAccountGroupTotalForPeriod($expenseAccounts, $startDate, $endDate);
        $netIncome = $totalRevenue - $totalExpenses;

        return view('accounting.reports.income-statement', compact(
            'revenueAccounts', 'expenseAccounts',
            'totalRevenue', 'totalExpenses', 'netIncome',
            'startDate', 'endDate'
        ));
    }

    /**
     * Display cash flow statement
     */
    public function cashFlow(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->toDateString());
        $endDate = $request->get('end_date', now()->toDateString());
        
        // Get cash accounts
        $cashAccounts = Account::where('account_category', 'cash')
                              ->orWhere('account_category', 'bank')
                              ->where('is_active', true)
                              ->get();

        // Calculate cash flows
        $operatingCashFlow = $this->calculateOperatingCashFlow($startDate, $endDate);
        $investingCashFlow = $this->calculateInvestingCashFlow($startDate, $endDate);
        $financingCashFlow = $this->calculateFinancingCashFlow($startDate, $endDate);
        
        $netCashFlow = $operatingCashFlow + $investingCashFlow + $financingCashFlow;
        
        // Beginning and ending cash balances
        $beginningCash = $this->calculateCashBalance($cashAccounts, $startDate, true);
        $endingCash = $this->calculateCashBalance($cashAccounts, $endDate, false);

        return view('accounting.reports.cash-flow', compact(
            'operatingCashFlow', 'investingCashFlow', 'financingCashFlow',
            'netCashFlow', 'beginningCash', 'endingCash',
            'startDate', 'endDate'
        ));
    }

    /**
     * Display account ledger
     */
    public function accountLedger(Request $request, Account $account)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->toDateString());
        $endDate = $request->get('end_date', now()->toDateString());
        
        // Get opening balance
        $openingBalance = $account->getBalance(null, $startDate);
        
        // Get transactions for the period
        $transactions = AccountTransaction::with(['journalEntry', 'account'])
                                         ->where('account_id', $account->id)
                                         ->whereHas('journalEntry', function ($query) use ($startDate, $endDate) {
                                             $query->where('status', 'posted')
                                                   ->whereBetween('entry_date', [$startDate, $endDate]);
                                         })
                                         ->orderBy('created_at')
                                         ->get();

        // Calculate running balance
        $runningBalance = $openingBalance;
        $transactionsWithBalance = $transactions->map(function ($transaction) use (&$runningBalance, $account) {
            if ($account->balance_type === 'debit') {
                $runningBalance += $transaction->debit_amount - $transaction->credit_amount;
            } else {
                $runningBalance += $transaction->credit_amount - $transaction->debit_amount;
            }
            
            $transaction->running_balance = $runningBalance;
            return $transaction;
        });

        $closingBalance = $runningBalance;

        return view('accounting.reports.account-ledger', compact(
            'account', 'transactionsWithBalance', 'openingBalance', 
            'closingBalance', 'startDate', 'endDate'
        ));
    }

    /**
     * Calculate total for account group
     */
    private function calculateAccountGroupTotal($accounts, $asOfDate)
    {
        $total = 0;
        
        foreach ($accounts as $account) {
            $total += $account->getBalance(null, $asOfDate);
            
            foreach ($account->children as $child) {
                $total += $child->getBalance(null, $asOfDate);
            }
        }
        
        return $total;
    }

    /**
     * Calculate total for account group for a period
     */
    public function calculateAccountGroupTotalForPeriod($accounts, $startDate, $endDate)
    {
        $total = 0;
        
        foreach ($accounts as $account) {
            // Get transactions for the period
            $periodBalance = AccountTransaction::where('account_id', $account->id)
                                              ->whereHas('journalEntry', function ($query) use ($startDate, $endDate) {
                                                  $query->where('status', 'posted')
                                                        ->whereBetween('entry_date', [$startDate, $endDate]);
                                              })
                                              ->sum(DB::raw('credit_amount - debit_amount'));
            
            if ($account->balance_type === 'debit') {
                $total -= $periodBalance;
            } else {
                $total += $periodBalance;
            }
            
            // Include children
            foreach ($account->children as $child) {
                $childPeriodBalance = AccountTransaction::where('account_id', $child->id)
                                                       ->whereHas('journalEntry', function ($query) use ($startDate, $endDate) {
                                                           $query->where('status', 'posted')
                                                                 ->whereBetween('entry_date', [$startDate, $endDate]);
                                                       })
                                                       ->sum(DB::raw('credit_amount - debit_amount'));
                
                if ($child->balance_type === 'debit') {
                    $total -= $childPeriodBalance;
                } else {
                    $total += $childPeriodBalance;
                }
            }
        }
        
        return abs($total);
    }

    /**
     * Calculate operating cash flow
     */
    private function calculateOperatingCashFlow($startDate, $endDate)
    {
        // This is a simplified calculation
        // In a real system, you would need more detailed cash flow analysis
        return 0;
    }

    /**
     * Calculate investing cash flow
     */
    private function calculateInvestingCashFlow($startDate, $endDate)
    {
        // This is a simplified calculation
        return 0;
    }

    /**
     * Calculate financing cash flow
     */
    private function calculateFinancingCashFlow($startDate, $endDate)
    {
        // This is a simplified calculation
        return 0;
    }

    /**
     * Calculate cash balance
     */
    private function calculateCashBalance($cashAccounts, $date, $isBefore = false)
    {
        $total = 0;
        
        foreach ($cashAccounts as $account) {
            if ($isBefore) {
                $balance = $account->getBalance(null, $date);
            } else {
                $balance = $account->getBalance(null, $date);
            }
            $total += $balance;
        }
        
        return $total;
    }
}
