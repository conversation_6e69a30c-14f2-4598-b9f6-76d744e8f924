<?php

namespace App\Http\Controllers;

use App\Models\Repair;
use App\Models\Customer;
use App\Models\Technician;
use App\Models\Part;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class RepairController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // TODO: Add permission middleware when role system is implemented
        // $this->middleware('permission:repairs.view')->only(['index', 'show']);
        // $this->middleware('permission:repairs.create')->only(['create', 'store']);
        // $this->middleware('permission:repairs.edit')->only(['edit', 'update']);
        // $this->middleware('permission:repairs.delete')->only(['destroy']);
    }

    public function index(Request $request)
    {
        $query = Repair::with(['customer', 'technician']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('repair_number', 'like', "%{$search}%")
                  ->orWhere('device_brand', 'like', "%{$search}%")
                  ->orWhere('device_model', 'like', "%{$search}%")
                  ->orWhere('device_serial', 'like', "%{$search}%")
                  ->orWhere('device_imei', 'like', "%{$search}%")
                  ->orWhere('problem_description', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('first_name', 'like', "%{$search}%")
                                   ->orWhere('last_name', 'like', "%{$search}%")
                                   ->orWhere('company_name', 'like', "%{$search}%")
                                   ->orWhere('phone', 'like', "%{$search}%")
                                   ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter by technician
        if ($request->filled('technician_id')) {
            $query->where('technician_id', $request->technician_id);
        }

        // Filter by device type
        if ($request->filled('device_type')) {
            $query->where('device_type', $request->device_type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Filter overdue repairs
        if ($request->boolean('overdue')) {
            $query->overdue();
        }

        $repairs = $query->latest()->paginate(15);

        // Get filter options
        $technicians = Technician::active()->get(['id', 'first_name', 'last_name']);
        $deviceTypes = Repair::distinct()->pluck('device_type')->filter();

        return view('repairs.index', compact('repairs', 'technicians', 'deviceTypes'));
    }

    public function create()
    {
        $customers = Customer::active()->get(['id', 'first_name', 'last_name', 'company_name', 'type']);
        $technicians = Technician::available()->get(['id', 'first_name', 'last_name']);
        
        $deviceTypes = [
            'Mobile Phone',
            'Smartphone',
            'Tablet',
            'Laptop',
            'Desktop Computer',
            'Gaming Console',
            'Smart Watch',
            'Headphones',
            'Speaker',
            'Camera',
            'Printer',
            'Other'
        ];

        return view('repairs.create', compact('customers', 'technicians', 'deviceTypes'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'technician_id' => 'nullable|exists:technicians,id',
            'device_type' => 'required|string|max:255',
            'device_brand' => 'required|string|max:255',
            'device_model' => 'required|string|max:255',
            'device_serial' => 'nullable|string|max:255',
            'device_imei' => 'nullable|string|max:255',
            'problem_description' => 'required|string',
            'repair_type' => 'required|in:warranty,paid,internal',
            'priority' => 'required|in:low,normal,high,urgent',
            'estimated_cost' => 'nullable|numeric|min:0',
            'estimated_completion' => 'nullable|date|after:today',
            'customer_notes' => 'nullable|string',
            'accessories' => 'nullable|array',
            'accessories.*' => 'string',
            'condition_on_arrival' => 'nullable|string',
            'photos_before' => 'nullable|array',
            'photos_before.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_warranty_repair' => 'boolean',
            'original_repair_id' => 'nullable|exists:repairs,id'
        ]);

        // Handle photo uploads
        if ($request->hasFile('photos_before')) {
            $photos = [];
            foreach ($request->file('photos_before') as $photo) {
                $photos[] = $photo->store('repairs/before', 'public');
            }
            $validated['photos_before'] = $photos;
        }

        // Set default values
        $validated['status'] = 'pending';
        $validated['payment_status'] = 'pending';

        // Auto-assign technician if not specified
        if (!$validated['technician_id']) {
            $availableTechnician = Technician::available()
                ->orderBy('id') // Simple round-robin assignment
                ->first();
            
            if ($availableTechnician) {
                $validated['technician_id'] = $availableTechnician->id;
                $availableTechnician->updateAvailability('busy');
            }
        }

        $repair = Repair::create($validated);

        return redirect()->route('repairs.show', $repair)
            ->with('success', 'تم إنشاء طلب الصيانة بنجاح.');
    }

    public function show(Repair $repair)
    {
        $repair->load(['customer', 'technician', 'parts', 'payments', 'statusHistory.changedBy']);

        return view('repairs.show', compact('repair'));
    }

    public function edit(Repair $repair)
    {
        if (!$repair->canBeEdited()) {
            return redirect()->route('repairs.show', $repair)
                ->with('error', 'لا يمكن تعديل هذا الطلب في الحالة الحالية.');
        }

        $customers = Customer::active()->get(['id', 'first_name', 'last_name', 'company_name', 'type']);
        $technicians = Technician::active()->get(['id', 'first_name', 'last_name']);
        
        $deviceTypes = [
            'Mobile Phone',
            'Smartphone',
            'Tablet',
            'Laptop',
            'Desktop Computer',
            'Gaming Console',
            'Smart Watch',
            'Headphones',
            'Speaker',
            'Camera',
            'Printer',
            'Other'
        ];

        return view('repairs.edit', compact('repair', 'customers', 'technicians', 'deviceTypes'));
    }

    public function update(Request $request, Repair $repair)
    {
        if (!$repair->canBeEdited()) {
            return redirect()->route('repairs.show', $repair)
                ->with('error', 'لا يمكن تعديل هذا الطلب في الحالة الحالية.');
        }

        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'technician_id' => 'nullable|exists:technicians,id',
            'device_type' => 'required|string|max:255',
            'device_brand' => 'required|string|max:255',
            'device_model' => 'required|string|max:255',
            'device_serial' => 'nullable|string|max:255',
            'device_imei' => 'nullable|string|max:255',
            'problem_description' => 'required|string',
            'diagnosis' => 'nullable|string',
            'repair_type' => 'required|in:warranty,paid,internal',
            'priority' => 'required|in:low,normal,high,urgent',
            'status' => 'required|in:pending,diagnosed,in_progress,waiting_parts,completed,delivered,cancelled,on_hold',
            'estimated_cost' => 'nullable|numeric|min:0',
            'labor_cost' => 'nullable|numeric|min:0',
            'estimated_completion' => 'nullable|date',
            'customer_notes' => 'nullable|string',
            'technician_notes' => 'nullable|string',
            'internal_notes' => 'nullable|string',
            'accessories' => 'nullable|array',
            'accessories.*' => 'string',
            'condition_on_arrival' => 'nullable|string',
            'test_results' => 'nullable|string',
            'repair_steps' => 'nullable|array',
            'repair_steps.*' => 'string',
            'quality_check' => 'nullable|string',
            'photos_before' => 'nullable|array',
            'photos_before.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'photos_after' => 'nullable|array',
            'photos_after.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'warranty_period' => 'nullable|integer|min:0',
            'is_warranty_repair' => 'boolean'
        ]);

        // Handle photo uploads
        if ($request->hasFile('photos_before')) {
            $photos = $repair->photos_before ?? [];
            foreach ($request->file('photos_before') as $photo) {
                $photos[] = $photo->store('repairs/before', 'public');
            }
            $validated['photos_before'] = $photos;
        }

        if ($request->hasFile('photos_after')) {
            $photos = $repair->photos_after ?? [];
            foreach ($request->file('photos_after') as $photo) {
                $photos[] = $photo->store('repairs/after', 'public');
            }
            $validated['photos_after'] = $photos;
        }

        // Update status if changed
        if ($repair->status !== $validated['status']) {
            $repair->updateStatus($validated['status'], $request->status_notes);
            unset($validated['status']); // Remove from validated array since it's handled by updateStatus
        }

        // Calculate warranty expiration
        if ($validated['warranty_period']) {
            $validated['warranty_expires_at'] = now()->addDays($validated['warranty_period']);
        }

        $repair->update($validated);
        $repair->calculateTotalCost();

        return redirect()->route('repairs.show', $repair)
            ->with('success', 'تم تحديث طلب الصيانة بنجاح.');
    }

    public function destroy(Repair $repair)
    {
        if (!$repair->canBeCancelled()) {
            return redirect()->route('repairs.index')
                ->with('error', 'لا يمكن حذف هذا الطلب في الحالة الحالية.');
        }

        // Delete photos
        if ($repair->photos_before) {
            foreach ($repair->photos_before as $photo) {
                Storage::disk('public')->delete($photo);
            }
        }
        if ($repair->photos_after) {
            foreach ($repair->photos_after as $photo) {
                Storage::disk('public')->delete($photo);
            }
        }

        $repair->delete();

        return redirect()->route('repairs.index')
            ->with('success', 'تم حذف طلب الصيانة بنجاح.');
    }

    public function updateStatus(Request $request, Repair $repair)
    {
        $request->validate([
            'status' => 'required|in:pending,diagnosed,in_progress,waiting_parts,completed,delivered,cancelled,on_hold',
            'notes' => 'nullable|string'
        ]);

        $repair->updateStatus($request->status, $request->notes);

        return redirect()->route('repairs.show', $repair)
            ->with('success', 'تم تحديث حالة الطلب بنجاح.');
    }

    public function addPart(Request $request, Repair $repair)
    {
        $request->validate([
            'part_id' => 'required|exists:parts,id',
            'quantity' => 'required|integer|min:1',
            'unit_price' => 'nullable|numeric|min:0'
        ]);

        $success = $repair->addPart(
            $request->part_id,
            $request->quantity,
            $request->unit_price
        );

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'تم إضافة القطعة بنجاح.',
                'total_cost' => $repair->fresh()->total_cost
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'فشل في إضافة القطعة.'
        ], 400);
    }

    public function removePart(Request $request, Repair $repair)
    {
        $request->validate([
            'part_id' => 'required|exists:parts,id'
        ]);

        $success = $repair->removePart($request->part_id);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'تم إزالة القطعة بنجاح.',
                'total_cost' => $repair->fresh()->total_cost
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'فشل في إزالة القطعة.'
        ], 400);
    }

    public function print(Repair $repair)
    {
        $repair->load(['customer', 'technician', 'parts']);
        
        return view('repairs.print', compact('repair'));
    }

    public function invoice(Repair $repair)
    {
        if ($repair->status !== 'completed') {
            return redirect()->route('repairs.show', $repair)
                ->with('error', 'لا يمكن إنشاء فاتورة لطلب غير مكتمل.');
        }

        $repair->load(['customer', 'technician', 'parts']);
        
        return view('repairs.invoice', compact('repair'));
    }

    public function warranty(Repair $repair)
    {
        if (!$repair->isWarrantyValid()) {
            return redirect()->route('repairs.show', $repair)
                ->with('error', 'انتهت صلاحية الضمان لهذا الطلب.');
        }

        return view('repairs.warranty', compact('repair'));
    }

    public function duplicate(Repair $repair)
    {
        $newRepair = $repair->replicate();
        $newRepair->repair_number = null; // Will be auto-generated
        $newRepair->status = 'pending';
        $newRepair->started_at = null;
        $newRepair->completed_at = null;
        $newRepair->delivered_at = null;
        $newRepair->payment_status = 'pending';
        $newRepair->save();

        return redirect()->route('repairs.edit', $newRepair)
            ->with('success', 'تم إنشاء نسخة من طلب الصيانة بنجاح.');
    }

    /**
     * Display repair workflow dashboard
     */
    public function workflow()
    {
        $statusCounts = [
            'pending' => Repair::where('status', 'pending')->count(),
            'diagnosed' => Repair::where('status', 'diagnosed')->count(),
            'in_progress' => Repair::where('status', 'in_progress')->count(),
            'waiting_parts' => Repair::where('status', 'waiting_parts')->count(),
            'completed' => Repair::where('status', 'completed')->count(),
            'delivered' => Repair::where('status', 'delivered')->count(),
            'on_hold' => Repair::where('status', 'on_hold')->count(),
            'cancelled' => Repair::where('status', 'cancelled')->count(),
        ];

        $recentRepairs = Repair::with(['customer', 'technician'])
            ->latest()
            ->limit(10)
            ->get();

        $urgentRepairs = Repair::where('priority', 'urgent')
            ->whereNotIn('status', ['completed', 'delivered', 'cancelled'])
            ->with(['customer', 'technician'])
            ->get();

        $overdueRepairs = Repair::where('estimated_completion', '<', now())
            ->whereNotIn('status', ['completed', 'delivered', 'cancelled'])
            ->with(['customer', 'technician'])
            ->get();

        return view('repairs.workflow', compact(
            'statusCounts', 'recentRepairs', 'urgentRepairs', 'overdueRepairs'
        ));
    }

    /**
     * Assign technician to repair
     */
    public function assignTechnician(Request $request, Repair $repair)
    {
        $request->validate([
            'technician_id' => 'required|exists:technicians,id',
            'notes' => 'nullable|string'
        ]);

        $oldTechnician = $repair->technician;
        $newTechnician = Technician::find($request->technician_id);

        // Update technician availability
        if ($oldTechnician) {
            $oldTechnician->updateAvailability('available');
        }

        $newTechnician->updateAvailability('busy');

        $repair->update([
            'technician_id' => $request->technician_id
        ]);

        // Log the assignment change
        $repair->statusHistory()->create([
            'status' => $repair->status,
            'notes' => $request->notes ?? "تم تعيين الفني: {$newTechnician->full_name}",
            'changed_by' => auth()->id(),
            'changed_at' => now()
        ]);

        return redirect()->route('repairs.show', $repair)
            ->with('success', 'تم تعيين الفني بنجاح.');
    }

    /**
     * Generate cost estimation
     */
    public function generateEstimate(Request $request, Repair $repair)
    {
        $request->validate([
            'labor_hours' => 'required|numeric|min:0',
            'hourly_rate' => 'required|numeric|min:0',
            'parts' => 'nullable|array',
            'parts.*.id' => 'required|exists:parts,id',
            'parts.*.quantity' => 'required|integer|min:1',
            'additional_costs' => 'nullable|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'notes' => 'nullable|string'
        ]);

        $laborCost = $request->labor_hours * $request->hourly_rate;
        $partsCost = 0;

        // Calculate parts cost
        if ($request->parts) {
            foreach ($request->parts as $partData) {
                $part = Part::find($partData['id']);
                $partsCost += $part->price * $partData['quantity'];
            }
        }

        $subtotal = $laborCost + $partsCost + ($request->additional_costs ?? 0);
        $discountAmount = $subtotal * (($request->discount_percentage ?? 0) / 100);
        $totalCost = $subtotal - $discountAmount;

        $repair->update([
            'labor_cost' => $laborCost,
            'parts_cost' => $partsCost,
            'estimated_cost' => $totalCost,
            'discount_amount' => $discountAmount,
            'technician_notes' => $request->notes
        ]);

        return redirect()->route('repairs.show', $repair)
            ->with('success', 'تم إنشاء تقدير التكلفة بنجاح.');
    }

    /**
     * Display repair timeline
     */
    public function timeline(Repair $repair)
    {
        $repair->load([
            'statusHistory.changedBy',
            'parts',
            'payments',
            'customer',
            'technician'
        ]);

        return view('repairs.timeline', compact('repair'));
    }

    /**
     * Bulk status update
     */
    public function bulkStatusUpdate(Request $request)
    {
        $request->validate([
            'repair_ids' => 'required|array',
            'repair_ids.*' => 'exists:repairs,id',
            'status' => 'required|in:pending,diagnosed,in_progress,waiting_parts,completed,delivered,cancelled,on_hold',
            'notes' => 'nullable|string'
        ]);

        $updatedCount = 0;
        foreach ($request->repair_ids as $repairId) {
            $repair = Repair::find($repairId);
            if ($repair && $repair->canUpdateStatus($request->status)) {
                $repair->updateStatus($request->status, $request->notes);
                $updatedCount++;
            }
        }

        return redirect()->back()
            ->with('success', "تم تحديث حالة {$updatedCount} طلب صيانة بنجاح.");
    }

    /**
     * Export repairs data
     */
    public function export(Request $request)
    {
        $query = Repair::with(['customer', 'technician']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $repairs = $query->get();

        return view('repairs.export', compact('repairs'));
    }
}
