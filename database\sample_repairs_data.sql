-- بيانات تجريبية للإصلاحات والفنيين لاختبار التقارير

-- إدراج فنيين تجريبيين
INSERT IGNORE INTO `technicians` (
    `id`, `employee_number`, `first_name`, `last_name`, `email`, `phone`, 
    `specialization`, `skill_level`, `hourly_rate`, `is_active`, 
    `created_at`, `updated_at`
) VALUES 
(1, 'TECH-001', 'محمد', 'أحمد', '<EMAIL>', '0599111111', 
    'mobile_repair', 'expert', 50.00, 1, NOW(), NOW()),
(2, 'TECH-002', 'علي', 'حسن', '<EMAIL>', '0599222222', 
    'computer_repair', 'advanced', 45.00, 1, NOW(), NOW()),
(3, 'TECH-003', 'سارة', 'محمد', '<EMAIL>', '0599333333', 
    'tablet_repair', 'intermediate', 40.00, 1, NOW(), NOW());

-- إدراج إصلاحات تجريبية
INSERT IGNORE INTO `repairs` (
    `id`, `repair_number`, `customer_id`, `technician_id`, `device_type`, 
    `device_brand`, `device_model`, `problem_description`, `diagnosis`, 
    `repair_type`, `priority`, `status`, `estimated_cost`, `actual_cost`, 
    `labor_cost`, `parts_cost`, `total_cost`, `started_at`, `completed_at`, 
    `payment_status`, `created_by`, `created_at`, `updated_at`
) VALUES 
(1, 'REP-001', 1, 1, 'smartphone', 'iPhone', '13 Pro', 'شاشة مكسورة', 
    'تحتاج استبدال شاشة كاملة', 'paid', 'normal', 'completed', 450.00, 450.00, 
    100.00, 350.00, 450.00, NOW() - INTERVAL 2 DAY, NOW() - INTERVAL 1 DAY, 
    'paid', 1, NOW() - INTERVAL 3 DAY, NOW()),

(2, 'REP-002', 2, 1, 'smartphone', 'Samsung', 'Galaxy S21', 'بطارية لا تشحن', 
    'بطارية تالفة تحتاج استبدال', 'paid', 'high', 'completed', 180.00, 180.00, 
    50.00, 130.00, 180.00, NOW() - INTERVAL 1 DAY, NOW(), 
    'paid', 1, NOW() - INTERVAL 2 DAY, NOW()),

(3, 'REP-003', 3, 2, 'laptop', 'HP', 'Pavilion', 'لا يعمل', 
    'مشكلة في اللوحة الأم', 'paid', 'urgent', 'in_progress', 800.00, 0.00, 
    200.00, 600.00, 800.00, NOW(), NULL, 
    'pending', 1, NOW() - INTERVAL 1 DAY, NOW()),

(4, 'REP-004', 1, 3, 'tablet', 'iPad', 'Air 4', 'شاشة لا تستجيب للمس', 
    'مشكلة في محول اللمس', 'paid', 'normal', 'completed', 320.00, 320.00, 
    80.00, 240.00, 320.00, NOW() - INTERVAL 3 DAY, NOW() - INTERVAL 2 DAY, 
    'paid', 1, NOW() - INTERVAL 4 DAY, NOW()),

(5, 'REP-005', 2, 1, 'smartphone', 'iPhone', '12', 'كاميرا لا تعمل', 
    'كاميرا خلفية تالفة', 'paid', 'low', 'waiting_parts', 250.00, 0.00, 
    60.00, 190.00, 250.00, NULL, NULL, 
    'pending', 1, NOW() - INTERVAL 1 DAY, NOW()),

(6, 'REP-006', 3, 2, 'laptop', 'Dell', 'Inspiron', 'بطيء جداً', 
    'يحتاج تنظيف وتحديث', 'paid', 'low', 'completed', 120.00, 120.00, 
    120.00, 0.00, 120.00, NOW() - INTERVAL 1 DAY, NOW(), 
    'paid', 1, NOW() - INTERVAL 2 DAY, NOW());

-- تحديث تسلسل الجداول
ALTER TABLE `technicians` AUTO_INCREMENT = 4;
ALTER TABLE `repairs` AUTO_INCREMENT = 7;
