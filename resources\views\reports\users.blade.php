@extends('layouts.main')

@section('title', 'تقارير المستخدمين والنشاط')

@section('content')
<div class="space-y-6" x-data="userReports()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تقارير المستخدمين والنشاط</h1>
            <p class="text-gray-600 dark:text-gray-400">تحليل نشاط المستخدمين وإحصائيات الاستخدام</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير التقرير
            </button>
            <button @click="printReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                طباعة
            </button>
        </div>
    </div>

    <!-- Report Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">فلاتر التقرير</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع التقرير</label>
                <select x-model="reportType" @change="generateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="activity">نشاط المستخدمين</option>
                    <option value="login">سجل تسجيل الدخول</option>
                    <option value="permissions">تقرير الصلاحيات</option>
                    <option value="performance">تقرير الأداء</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" x-model="dateFrom" @change="generateReport()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" x-model="dateTo" @change="generateReport()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المستخدم</label>
                <select x-model="selectedUser" @change="generateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع المستخدمين</option>
                    <template x-for="user in users" :key="user.id">
                        <option :value="user.id" x-text="user.name"></option>
                    </template>
                </select>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">المستخدمين النشطين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.activeUsers">12</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">تسجيلات دخول اليوم</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.todayLogins">28</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط وقت الجلسة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.avgSessionTime">4.2 ساعة</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي العمليات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.totalActions">1,247</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- User Activity Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">نشاط المستخدمين اليومي</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">رسم بياني لنشاط المستخدمين</p>
                </div>
            </div>
        </div>

        <!-- Top Active Users -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">أكثر المستخدمين نشاطاً</h3>
            <div class="space-y-3">
                <template x-for="user in topActiveUsers" :key="user.id">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img class="h-8 w-8 rounded-full mr-3" :src="user.avatar" :alt="user.name">
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="user.name"></p>
                                <p class="text-xs text-gray-500 dark:text-gray-400" x-text="user.role"></p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="user.actions + ' عملية'"></p>
                            <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                                <div class="bg-blue-600 h-2 rounded-full" :style="`width: ${user.percentage}%`"></div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- Activity Log Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden" x-show="reportType === 'activity'">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">سجل النشاط</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المستخدم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">النشاط</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ والوقت</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">عنوان IP</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="activity in activityLog" :key="activity.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-8 w-8 rounded-full mr-3" :src="activity.user_avatar" :alt="activity.user_name">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="activity.user_name"></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400" x-text="activity.user_role"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-gray-100" x-text="activity.action"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="activity.description"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="activity.timestamp"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="activity.ip_address"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': activity.status === 'success',
                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': activity.status === 'failed',
                                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': activity.status === 'warning'
                                      }"
                                      x-text="getStatusText(activity.status)">
                                </span>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Login History Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden" x-show="reportType === 'login'">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">سجل تسجيل الدخول</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المستخدم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">وقت الدخول</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">وقت الخروج</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">مدة الجلسة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">عنوان IP</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المتصفح</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="login in loginHistory" :key="login.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-8 w-8 rounded-full mr-3" :src="login.user_avatar" :alt="login.user_name">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="login.user_name"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="login.login_time"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="login.logout_time || 'نشط'"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="login.duration"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="login.ip_address"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" x-text="login.browser"></td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
function userReports() {
    return {
        reportType: 'activity',
        dateFrom: '2024-07-01',
        dateTo: '2024-07-09',
        selectedUser: '',
        users: [
            { id: 1, name: 'أحمد محمد علي' },
            { id: 2, name: 'سارة أحمد خالد' },
            { id: 3, name: 'محمد عبدالله حسن' },
            { id: 4, name: 'فاطمة حسين علي' }
        ],
        summary: {
            activeUsers: 12,
            todayLogins: 28,
            avgSessionTime: '4.2 ساعة',
            totalActions: '1,247'
        },
        topActiveUsers: [
            {
                id: 1,
                name: 'أحمد محمد علي',
                role: 'مدير النظام',
                actions: 156,
                percentage: 100,
                avatar: 'https://ui-avatars.com/api/?name=أحمد+محمد&background=3B82F6&color=fff&size=32'
            },
            {
                id: 2,
                name: 'سارة أحمد خالد',
                role: 'محاسب',
                actions: 124,
                percentage: 80,
                avatar: 'https://ui-avatars.com/api/?name=سارة+أحمد&background=10B981&color=fff&size=32'
            },
            {
                id: 3,
                name: 'محمد عبدالله حسن',
                role: 'فني',
                actions: 98,
                percentage: 63,
                avatar: 'https://ui-avatars.com/api/?name=محمد+عبدالله&background=F59E0B&color=fff&size=32'
            }
        ],
        activityLog: [
            {
                id: 1,
                user_name: 'أحمد محمد علي',
                user_role: 'مدير النظام',
                user_avatar: 'https://ui-avatars.com/api/?name=أحمد+محمد&background=3B82F6&color=fff&size=32',
                action: 'تسجيل دخول',
                description: 'دخول إلى النظام',
                timestamp: '2024-07-09 09:30:15',
                ip_address: '*************',
                status: 'success'
            },
            {
                id: 2,
                user_name: 'سارة أحمد خالد',
                user_role: 'محاسب',
                user_avatar: 'https://ui-avatars.com/api/?name=سارة+أحمد&background=10B981&color=fff&size=32',
                action: 'إنشاء فاتورة',
                description: 'إنشاء فاتورة مبيعات INV-001',
                timestamp: '2024-07-09 10:15:22',
                ip_address: '*************',
                status: 'success'
            }
        ],
        loginHistory: [
            {
                id: 1,
                user_name: 'أحمد محمد علي',
                user_avatar: 'https://ui-avatars.com/api/?name=أحمد+محمد&background=3B82F6&color=fff&size=32',
                login_time: '2024-07-09 09:30:15',
                logout_time: '2024-07-09 17:45:30',
                duration: '8 ساعات 15 دقيقة',
                ip_address: '*************',
                browser: 'Chrome 115.0'
            },
            {
                id: 2,
                user_name: 'سارة أحمد خالد',
                user_avatar: 'https://ui-avatars.com/api/?name=سارة+أحمد&background=10B981&color=fff&size=32',
                login_time: '2024-07-09 08:00:00',
                logout_time: null,
                duration: '9 ساعات 30 دقيقة',
                ip_address: '*************',
                browser: 'Firefox 115.0'
            }
        ],

        init() {
            this.generateReport();
        },

        generateReport() {
            // Generate report based on filters
            console.log('Generating report:', {
                type: this.reportType,
                dateFrom: this.dateFrom,
                dateTo: this.dateTo,
                user: this.selectedUser
            });
        },

        getStatusText(status) {
            const statuses = {
                'success': 'نجح',
                'failed': 'فشل',
                'warning': 'تحذير'
            };
            return statuses[status] || status;
        },

        exportReport() {
            alert('سيتم تصدير التقرير قريباً');
        },

        printReport() {
            window.print();
        }
    }
}
</script>
@endpush
@endsection
