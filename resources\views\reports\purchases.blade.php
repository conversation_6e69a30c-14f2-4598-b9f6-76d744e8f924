@extends('layouts.main')

@section('title', 'تقارير المشتريات')

@section('content')
<div class="space-y-6" x-data="purchaseReports()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تقارير المشتريات</h1>
            <p class="text-gray-600 dark:text-gray-400">تحليل وتقارير شاملة للمشتريات والموردين</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير التقرير
            </button>
            <button @click="printReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                طباعة
            </button>
        </div>
    </div>

    <!-- Report Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">فلاتر التقرير</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع التقرير</label>
                <select x-model="reportType" @change="generateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="summary">ملخص المشتريات</option>
                    <option value="detailed">تقرير مفصل</option>
                    <option value="supplier">تقرير الموردين</option>
                    <option value="category">تقرير حسب الفئة</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" x-model="dateFrom" @change="generateReport()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" x-model="dateTo" @change="generateReport()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المورد</label>
                <select x-model="selectedSupplier" @change="generateReport()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الموردين</option>
                    <template x-for="supplier in suppliers" :key="supplier.id">
                        <option :value="supplier.id" x-text="supplier.name"></option>
                    </template>
                </select>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المشتريات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.totalPurchases">₪45,250</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">عدد الفواتير</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.invoiceCount">28</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">عدد الموردين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.supplierCount">8</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط الفاتورة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.averageInvoice">₪1,616</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Monthly Purchases Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">المشتريات الشهرية</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">رسم بياني للمشتريات الشهرية</p>
                </div>
            </div>
        </div>

        <!-- Top Suppliers Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">أفضل الموردين</h3>
            <div class="space-y-3">
                <template x-for="supplier in topSuppliers" :key="supplier.id">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-3">
                                <span class="text-sm font-medium text-blue-600 dark:text-blue-400" x-text="supplier.rank"></span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="supplier.name"></p>
                                <p class="text-xs text-gray-500 dark:text-gray-400" x-text="supplier.orders + ' طلب'"></p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="'₪' + supplier.total"></p>
                            <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                                <div class="bg-blue-600 h-2 rounded-full" :style="`width: ${supplier.percentage}%`"></div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- Detailed Report Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden" x-show="reportType === 'detailed'">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">التقرير المفصل</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">رقم الفاتورة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المورد</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المبلغ</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="purchase in detailedData" :key="purchase.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100" x-text="purchase.invoice_number"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="purchase.supplier_name"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="purchase.date"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="'₪' + purchase.total"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': purchase.status === 'received',
                                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': purchase.status === 'pending',
                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': purchase.status === 'cancelled'
                                      }"
                                      x-text="getStatusText(purchase.status)">
                                </span>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
function purchaseReports() {
    return {
        reportType: 'summary',
        dateFrom: '2024-01-01',
        dateTo: '2024-12-31',
        selectedSupplier: '',
        suppliers: [
            { id: 1, name: 'شركة التقنية المتقدمة' },
            { id: 2, name: 'مؤسسة الإلكترونيات الحديثة' },
            { id: 3, name: 'شركة قطع الغيار المحدودة' }
        ],
        summary: {
            totalPurchases: '₪45,250',
            invoiceCount: 28,
            supplierCount: 8,
            averageInvoice: '₪1,616'
        },
        topSuppliers: [
            { id: 1, rank: 1, name: 'شركة التقنية المتقدمة', total: '18,500', orders: 12, percentage: 85 },
            { id: 2, rank: 2, name: 'مؤسسة الإلكترونيات الحديثة', total: '15,200', orders: 8, percentage: 70 },
            { id: 3, rank: 3, name: 'شركة قطع الغيار المحدودة', total: '11,550', orders: 8, percentage: 55 }
        ],
        detailedData: [
            {
                id: 1,
                invoice_number: 'PUR-001',
                supplier_name: 'شركة التقنية المتقدمة',
                date: '2024-07-09',
                total: '2,500',
                status: 'received'
            },
            {
                id: 2,
                invoice_number: 'PUR-002',
                supplier_name: 'مؤسسة الإلكترونيات الحديثة',
                date: '2024-07-08',
                total: '1,800',
                status: 'pending'
            }
        ],

        init() {
            this.generateReport();
        },

        generateReport() {
            // Generate report based on filters
            console.log('Generating report:', {
                type: this.reportType,
                dateFrom: this.dateFrom,
                dateTo: this.dateTo,
                supplier: this.selectedSupplier
            });
        },

        getStatusText(status) {
            const statuses = {
                'pending': 'معلقة',
                'received': 'مستلمة',
                'cancelled': 'ملغية'
            };
            return statuses[status] || status;
        },

        exportReport() {
            alert('سيتم تصدير التقرير قريباً');
        },

        printReport() {
            window.print();
        }
    }
}
</script>
@endpush
@endsection
