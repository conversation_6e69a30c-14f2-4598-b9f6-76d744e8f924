<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('page-title', 'لوحة التحكم') - {{ config('app.name', 'الطارق لإدارة مراكز الصيانة') }}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                            950: '#020617',
                        }
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/@alpinejs/persist@3.x.x/dist/cdn.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Advanced Interactions JS -->
    <script src="{{ asset('js/advanced-interactions.js') }}"></script>
    <script src="{{ asset('js/performance-manager.js') }}"></script>
    <script src="{{ asset('js/advanced-features.js') }}"></script>
    <script src="{{ asset('js/accessibility-manager.js') }}"></script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Modern Theme CSS -->
    <link rel="stylesheet" href="{{ asset('css/modern-theme.css') }}">
    <link rel="stylesheet" href="{{ asset('css/dashboard-enhancements.css') }}">
    <link rel="stylesheet" href="{{ asset('css/animations-interactions.css') }}">
    <link rel="stylesheet" href="{{ asset('css/performance-optimizations.css') }}">
    <link rel="stylesheet" href="{{ asset('css/accessibility-enhancements.css') }}">
    <link rel="stylesheet" href="{{ asset('css/visual-balance.css') }}">
    <!-- Layout Fixes - يجب أن يكون آخر ملف CSS لإصلاح التداخلات -->
    <link rel="stylesheet" href="{{ asset('css/layout-fixes.css') }}">
    
    <!-- Custom Styles -->
    <style>
        .btn-primary {
            @apply inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-lg font-medium text-sm text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200;
        }
        
        .btn-secondary {
            @apply inline-flex items-center px-4 py-2 bg-secondary-600 border border-transparent rounded-lg font-medium text-sm text-white hover:bg-secondary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary-500 transition-colors duration-200;
        }
        
        .btn-outline {
            @apply inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg font-medium text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200;
        }
        
        .card {
            @apply bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700;
        }
        
        .input-field {
            @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100;
        }
        
        /* ===== تصميم القائمة الجانبية العصري مع RTL ===== */

        /* خطوط عربية محسنة */
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap');

        /* متغيرات الألوان العصرية */
        :root {
            --sidebar-bg-light: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            --sidebar-bg-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --sidebar-border-light: rgba(148, 163, 184, 0.2);
            --sidebar-border-dark: rgba(71, 85, 105, 0.3);
            --link-hover-light: rgba(59, 130, 246, 0.08);
            --link-hover-dark: rgba(59, 130, 246, 0.15);
            --link-active-light: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            --link-active-dark: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            --text-primary-light: #1e293b;
            --text-primary-dark: #f1f5f9;
            --text-secondary-light: #64748b;
            --text-secondary-dark: #94a3b8;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
        }

        /* تحسين القائمة الجانبية الرئيسية - موضع يمين مع منع الحركة غير المرغوبة */
        .sidebar {
            background: var(--sidebar-bg-light);
            border-left: 1px solid var(--sidebar-border-light);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow:
                -4px 0 24px rgba(0, 0, 0, 0.04),
                -2px 0 8px rgba(0, 0, 0, 0.02);
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            left: auto !important;
            width: 18rem !important; /* 288px */
            z-index: 50 !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* منع الحركة في أجهزة سطح المكتب */
        @media (min-width: 1024px) {
            .sidebar {
                transition: none !important;
                transform: translateX(0) !important;
            }
        }

        /* السماح بالحركة فقط في الأجهزة المحمولة */
        @media (max-width: 1023px) {
            .sidebar {
                transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
        }

        .dark .sidebar {
            background: var(--sidebar-bg-dark);
            border-left: 1px solid var(--sidebar-border-dark);
            box-shadow:
                -4px 0 24px rgba(0, 0, 0, 0.3),
                -2px 0 8px rgba(0, 0, 0, 0.2);
        }

        /* تصميم الروابط العصري */
        .sidebar-link {
            @apply relative flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300;
            direction: rtl;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            color: var(--text-primary-light);
            margin: 0.25rem 0.75rem;
            border: 1px solid transparent;
            position: relative;
            overflow: hidden;
            display: block;
            width: calc(100% - 1.5rem);
            box-sizing: border-box;
        }

        .dark .sidebar-link {
            color: var(--text-primary-dark);
        }

        /* تأثير hover متقدم */
        .sidebar-link:hover {
            background: var(--link-hover-light);
            border-color: rgba(59, 130, 246, 0.2);
            transform: translateX(-3px) scale(1.02);
            box-shadow:
                0 4px 12px rgba(59, 130, 246, 0.15),
                0 2px 4px rgba(59, 130, 246, 0.1);
        }

        .dark .sidebar-link:hover {
            background: var(--link-hover-dark);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow:
                0 4px 12px rgba(59, 130, 246, 0.25),
                0 2px 4px rgba(59, 130, 246, 0.2);
        }

        /* تصميم العنصر النشط */
        .sidebar-link.active {
            background: var(--link-active-light);
            color: var(--accent-color);
            border-color: var(--accent-color);
            border-right: 4px solid var(--accent-color);
            font-weight: 600;
            box-shadow:
                0 4px 16px rgba(59, 130, 246, 0.2),
                0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .dark .sidebar-link.active {
            background: var(--link-active-dark);
            color: #60a5fa;
            border-color: #60a5fa;
            border-right: 4px solid #60a5fa;
            box-shadow:
                0 4px 16px rgba(96, 165, 250, 0.3),
                0 2px 8px rgba(96, 165, 250, 0.2);
        }

        /* تأثير الضغط */
        .sidebar-link:active {
            transform: translateX(-1px) scale(0.98);
            transition: transform 0.1s ease;
        }

        /* تنسيق النص */
        .sidebar-link .link-text {
            @apply text-right w-full;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 500;
            letter-spacing: 0.025em;
            display: block;
            width: 100%;
        }

        /* إصلاح التخطيط العمودي */
        .sidebar-link {
            display: flex !important;
            flex-direction: row !important;
            align-items: center !important;
            width: calc(100% - 1.5rem) !important;
            margin-left: 0.75rem !important;
            margin-right: 0.75rem !important;
            margin-top: 0.25rem !important;
            margin-bottom: 0.25rem !important;
            box-sizing: border-box !important;
        }

        /* ضمان عدم تداخل الروابط */
        .sidebar-group {
            display: flex !important;
            flex-direction: column !important;
            gap: 0 !important;
            width: 100% !important;
        }

        .sidebar-group .sidebar-link {
            flex-shrink: 0 !important;
            clear: both !important;
            display: block !important;
            width: calc(100% - 1.5rem) !important;
            margin: 0.25rem 0.75rem !important;
        }

        /* إصلاح شامل للتخطيط العمودي */
        .sidebar-content > * {
            width: 100% !important;
            display: block !important;
        }

        .sidebar-content .sidebar-group > .sidebar-link {
            display: flex !important;
            align-items: center !important;
            width: calc(100% - 1.5rem) !important;
            margin: 0.25rem 0.75rem !important;
            clear: both !important;
            float: none !important;
        }

        /* منع التداخل الأفقي */
        .sidebar-link {
            float: none !important;
            clear: both !important;
            display: flex !important;
            width: calc(100% - 1.5rem) !important;
        }

        /* تحسين المسافات والترتيب */
        .sidebar-group .section-title {
            margin-bottom: 0.75rem !important;
            display: block !important;
            width: calc(100% - 1.5rem) !important;
        }

        .section-divider {
            width: calc(100% - 2rem) !important;
            margin: 0.75rem 1rem !important;
            display: block !important;
            clear: both !important;
        }

        /* ضمان الترتيب العمودي للمحتوى */
        .sidebar-content {
            display: flex !important;
            flex-direction: column !important;
            align-items: stretch !important;
        }

        .sidebar-content > .sidebar-group,
        .sidebar-content > .section-divider {
            width: 100% !important;
            flex-shrink: 0 !important;
        }

        /* تصميم عناوين الأقسام العصري */
        .section-title {
            @apply relative px-4 py-3 text-xs font-bold uppercase tracking-wider;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            color: var(--text-secondary-light);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
            border-radius: 12px;
            margin: 1rem 0.75rem 0.5rem 0.75rem;
            border: 1px solid rgba(59, 130, 246, 0.1);
            position: relative;
            overflow: hidden;
        }

        .dark .section-title {
            color: var(--text-secondary-dark);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        /* تأثير glass morphism للعناوين */
        .section-title::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: -1;
        }

        /* خط فاصل للأقسام - مسافات محسنة */
        .section-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
            margin: 0.75rem 1rem;
        }

        .dark .section-divider {
            background: linear-gradient(90deg, transparent 0%, rgba(96, 165, 250, 0.4) 50%, transparent 100%);
        }

        /* تحسين مساحة القائمة الجانبية */
        .sidebar-content {
            padding: 1rem 0;
            height: 100%;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
            display: flex;
            flex-direction: column;
        }

        .sidebar-content::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar-content::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.3);
            border-radius: 3px;
        }

        .sidebar-content::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.5);
        }

        /* تحسين المجموعات - ترتيب عمودي ومسافات محسنة */
        .sidebar-group {
            display: flex;
            flex-direction: column;
            margin-bottom: 1rem;
            width: 100%;
        }

        .sidebar-group:last-child {
            margin-bottom: 0.5rem;
        }

        /* ضمان الترتيب العمودي للروابط */
        .sidebar-group > .sidebar-link {
            display: block;
            width: 100%;
            margin-bottom: 0.25rem;
        }

        .sidebar-group > .sidebar-link:last-child {
            margin-bottom: 0;
        }

        /* تأثيرات إضافية للتفاعل */
        .sidebar-link::before {
            content: '';
            position: absolute;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: right 0.5s ease;
        }

        .sidebar-link:hover::before {
            right: 100%;
        }

        /* تحسينات الاستجابة والأجهزة المحمولة - موضع يمين مع إزالة المسافات */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px !important;
                right: 0 !important;
                left: auto !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .main-content-lg {
                margin-right: 0 !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100vw !important;
                max-width: 320px !important;
                right: 0 !important;
                left: auto !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .main-content-lg {
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
            }

            .sidebar-link {
                margin: 0.25rem 0.5rem;
                padding: 0.875rem 1rem;
                font-size: 0.9rem;
            }

            .section-title {
                margin: 0.5rem 0.5rem 0.25rem 0.5rem;
                padding: 0.75rem 1rem;
                font-size: 0.7rem;
            }

            .section-divider {
                margin: 0.5rem 1rem !important;
            }

            .sidebar-group {
                margin-bottom: 0.75rem;
            }
        }

        /* تحسينات اللمس للأجهزة المحمولة */
        @media (hover: none) and (pointer: coarse) {
            .sidebar-link {
                padding: 1rem;
                min-height: 48px;
            }

            .sidebar-link:hover {
                transform: none;
            }

            .sidebar-link:active {
                transform: scale(0.95);
                background: var(--link-active-light);
            }

            .dark .sidebar-link:active {
                background: var(--link-active-dark);
            }
        }

        /* تحسينات إمكانية الوصول */
        .sidebar-link:focus {
            outline: 2px solid var(--accent-color);
            outline-offset: 2px;
        }

        .sidebar-link:focus-visible {
            outline: 2px solid var(--accent-color);
            outline-offset: 2px;
        }

        /* تحسين الحركة للمستخدمين الذين يفضلون تقليل الحركة */
        @media (prefers-reduced-motion: reduce) {
            .sidebar-link,
            .sidebar,
            .section-title {
                transition: none;
            }

            .sidebar-link:hover {
                transform: none;
            }

            .sidebar-link::before {
                display: none;
            }
        }

        /* تحسينات الطباعة */
        @media print {
            .sidebar {
                display: none;
            }
        }

        /* تحسين الألوان للوضع عالي التباين */
        @media (prefers-contrast: high) {
            .sidebar-link {
                border: 2px solid transparent;
            }

            .sidebar-link:hover {
                border-color: var(--accent-color);
            }

            .sidebar-link.active {
                border-color: var(--accent-color);
                border-width: 3px;
            }
        }

        /* ضمان الموضع الصحيح للقائمة الجانبية */
        .sidebar-container {
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            left: auto !important;
            width: 18rem !important;
            z-index: 50 !important;
        }

        /* إصلاح التحويل للأجهزة المحمولة - منع الحركة غير المرغوبة */
        @media (max-width: 1023px) {
            .sidebar-container.translate-x-full {
                transform: translateX(100%) !important;
            }

            .sidebar-container.translate-x-0 {
                transform: translateX(0) !important;
            }
        }

        /* في أجهزة سطح المكتب - القائمة ثابتة دائماً */
        @media (min-width: 1024px) {
            .sidebar-container {
                transform: translateX(0) !important;
                position: fixed !important;
                right: 0 !important;
            }

            .sidebar-container.translate-x-full,
            .sidebar-container.translate-x-0 {
                transform: translateX(0) !important;
            }
        }

        /* إصلاح المسافة الفارغة نهائياً */
        @media (min-width: 1024px) {
            .main-content-lg {
                margin-right: 18rem !important;    /* مساحة دقيقة للقائمة الجانبية */
                margin-left: 0 !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
                width: calc(100% - 18rem) !important;
                max-width: calc(100% - 18rem) !important;
            }

            /* إزالة أي مسافات إضافية من الحاوي الرئيسي */
            .flex.h-screen {
                gap: 0 !important;
                padding: 0 !important;
                margin: 0 !important;
                box-sizing: border-box !important;
            }

            /* ضمان عدم وجود مسافات في body */
            body {
                margin: 0 !important;
                padding: 0 !important;
            }
        }

        /* إزالة المسافات الزائدة بين القائمة والمحتوى - إصلاح نهائي */
        .layout-container {
            display: flex;
            height: 100vh;
            margin: 0 !important;
            padding: 0 !important;
            gap: 0 !important;
            overflow: hidden;
            box-sizing: border-box !important;
        }

        /* ضمان عدم وجود مسافات في الحاويات */
        .main-content-container {
            flex: 1;
            margin: 0 !important;
            padding: 0 !important;
            overflow-y: auto;
            box-sizing: border-box !important;
        }

        /* إزالة أي مسافات افتراضية من جميع العناصر */
        html, body {
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
            overflow-x: hidden !important;
        }

        .flex.h-screen {
            gap: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        }

        /* إصلاح شامل للمسافات */
        * {
            box-sizing: border-box !important;
        }

        /* إزالة أي borders أو margins من العناصر الرئيسية */
        .main-content-lg {
            border: none !important;
            outline: none !important;
        }

        .sidebar-container {
            border-right: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        * {
            transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* إصلاح شامل للمسافات والتخطيط */

        /* إزالة أي مسافات افتراضية من المتصفح */
        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0 !important;
            padding: 0 !important;
            height: 100%;
            overflow-x: hidden;
        }

        /* الحاوي الرئيسي للتخطيط */
        .app-layout {
            display: flex;
            height: 100vh;
            margin: 0;
            padding: 0;
            gap: 0;
            position: relative;
        }

        /* المحتوى الرئيسي */
        .main-content-area {
            flex: 1;
            margin: 0;
            padding: 0;
            overflow-y: auto;
            position: relative;
        }

        /* القائمة الجانبية */
        .sidebar-area {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            width: 18rem;
            z-index: 50;
            margin: 0;
            padding: 0;
        }

        /* إصلاح نهائي شامل للمسافات - فحص جميع المصادر المحتملة */

        /* إزالة أي مسافات من العناصر الأساسية */
        html {
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        }

        body {
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
            overflow-x: hidden !important;
        }

        /* الحاوي الرئيسي */
        .flex.h-screen {
            margin: 0 !important;
            padding: 0 !important;
            gap: 0 !important;
            box-sizing: border-box !important;
            position: relative !important;
        }

        /* المحتوى الرئيسي - إصلاح نهائي */
        .main-content-lg {
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            outline: none !important;
            box-sizing: border-box !important;
        }

        /* القائمة الجانبية - إصلاح نهائي */
        .sidebar-container {
            margin: 0 !important;
            padding: 0 !important;
            border-right: none !important;
            box-sizing: border-box !important;
        }

        /* إصلاح للأجهزة المختلفة */
        @media (min-width: 1024px) {
            .main-content-lg {
                margin-right: 18rem !important;
                margin-left: 0 !important;
                margin-top: 0 !important;
                margin-bottom: 0 !important;
                padding: 0 !important;
                width: calc(100% - 18rem) !important;
                max-width: calc(100% - 18rem) !important;
                border: none !important;
                box-sizing: border-box !important;
            }

            .sidebar-container {
                position: fixed !important;
                top: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                left: auto !important;
                width: 18rem !important;
                margin: 0 !important;
                padding: 0 !important;
                z-index: 50 !important;
            }
        }

        @media (max-width: 1023px) {
            .main-content-lg {
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
                border: none !important;
                box-sizing: border-box !important;
            }
        }

        /* إزالة أي تأثيرات جانبية من Tailwind */
        .container {
            margin: 0 !important;
            padding: 0 !important;
        }

        /* منع الحركة غير المرغوبة عند التحميل */
        .sidebar-container {
            opacity: 1 !important;
        }

        /* إخفاء القائمة أثناء التحميل في الأجهزة المحمولة فقط */
        @media (max-width: 1023px) {
            .sidebar-container:not(.translate-x-0) {
                transform: translateX(100%) !important;
            }
        }

        /* ضمان ظهور القائمة في أجهزة سطح المكتب */
        @media (min-width: 1024px) {
            .sidebar-container {
                transform: translateX(0) !important;
                opacity: 1 !important;
                visibility: visible !important;
            }
        }

        /* منع أي flash content */
        body {
            overflow-x: hidden !important;
        }

        /* تحسينات إضافية للتصميم */
        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .dark .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
        }

        /* تحسين الخطوط العربية */
        body {
            font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* تحسين الألوان للوضع المظلم */
        .dark {
            color-scheme: dark;
        }

        /* تحسين التمرير */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.5);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(156, 163, 175, 0.7);
        }

        .dark ::-webkit-scrollbar-thumb {
            background: rgba(75, 85, 99, 0.5);
        }

        .dark ::-webkit-scrollbar-thumb:hover {
            background: rgba(75, 85, 99, 0.7);
        }

        /* تحسينات الشريط الجانبي للـ RTL */
        .sidebar-container {
            transition: transform 0.3s ease-in-out;
            max-height: 100vh;
        }

        /* ضمان أن المحتوى داخل القائمة الجانبية */
        .sidebar-container nav {
            min-height: 0; /* للسماح بالتمرير */
        }

        /* Desktop Layout - RTL - إصلاح المسافات */
        @media (min-width: 1024px) {
            .sidebar-container {
                position: fixed !important;
                top: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                width: 18rem !important;
                transform: translateX(0) !important;
                flex-shrink: 0;
                z-index: 50;
            }

            .main-content-lg {
                margin-left: 0 !important;
                margin-right: 18rem !important; /* مساحة دقيقة للقائمة */
                padding-left: 0 !important;
                padding-right: 0 !important;
                flex: 1;
                width: calc(100% - 18rem) !important;
            }

            /* إزالة أي فجوات في الحاوي الرئيسي */
            .flex.h-screen {
                gap: 0 !important;
                padding: 0 !important;
                margin: 0 !important;
            }
        }

        /* Mobile Layout - RTL - إصلاح المسافات */
        @media (max-width: 1023px) {
            .sidebar-container {
                position: fixed !important;
                top: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                left: auto !important;
                width: 18rem !important;
                z-index: 50;
            }

            .main-content-lg {
                margin-right: 0 !important;
                margin-left: 0 !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
                width: 100% !important;
            }

            /* إزالة أي مسافات في الأجهزة المحمولة */
            .flex.h-screen {
                gap: 0 !important;
                padding: 0 !important;
                margin: 0 !important;
            }
        }

            /* تحريك القائمة من اليمين للداخل */
            .sidebar-container.translate-x-full {
                transform: translateX(100%) !important;
            }
            .sidebar-container.translate-x-0 {
                transform: translateX(0) !important;
            }
        }
    </style>
</head>
<body class="font-arabic antialiased bg-gray-50 dark:bg-gray-900"
      x-data="{
          darkMode: $persist(false),
          sidebarOpen: window.innerWidth >= 1024
      }"
      x-init="
          // تطبيق الوضع المظلم عند التحميل
          if (darkMode) {
              document.documentElement.classList.add('dark')
          }

          // مراقبة تغيير حجم الشاشة
          window.addEventListener('resize', () => {
              if (window.innerWidth >= 1024) {
                  sidebarOpen = true
              } else {
                  sidebarOpen = false
              }
          })

          // مراقبة تغيير الوضع المظلم
          $watch('darkMode', value => {
              if (value) {
                  document.documentElement.classList.add('dark')
              } else {
                  document.documentElement.classList.remove('dark')
              }
          })
      "
      :class="{ 'dark': darkMode }">

    <!-- Layout Container -->
    <div class="flex h-screen bg-gray-50 dark:bg-gray-900">
        <!-- Sidebar Overlay -->
        <div x-show="sidebarOpen"
             @click="sidebarOpen = false"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"></div>

        <!-- Main Content -->
        <div class="main-content-lg flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between h-16 px-6">
                    <div class="flex items-center">
                        <button @click="sidebarOpen = true" class="lg:hidden">
                            <svg class="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                        <div class="mr-4 lg:mr-0">
                            <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">@yield('title', 'لوحة التحكم')</h1>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4 space-x-reverse">
                        <!-- Notifications -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="p-2 text-gray-400 hover:text-gray-500 dark:text-gray-300 dark:hover:text-gray-200">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                                </svg>
                                <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
                            </button>
                        </div>

                        <!-- Dark Mode Toggle -->
                        <button @click="darkMode = !darkMode" class="p-2 text-gray-400 hover:text-gray-500 dark:text-gray-300 dark:hover:text-gray-200">
                            <svg x-show="!darkMode" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                            <svg x-show="darkMode" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                        </button>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name={{ auth()->user()->name }}&background=3B82F6&color=fff" alt="{{ auth()->user()->name }}">
                                <span class="mr-2 text-gray-700 dark:text-gray-300">{{ auth()->user()->name }}</span>
                                <svg class="mr-1 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>

                            <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="origin-top-right absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">الملف الشخصي</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">الإعدادات</a>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="block w-full text-right px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">تسجيل الخروج</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
                <div class="p-6">
                    <!-- Flash Messages -->
                    @if(session('success'))
                    <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" role="alert">
                        <span class="block sm:inline">{{ session('success') }}</span>
                    </div>
                    @endif

                    @if(session('error'))
                    <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg" role="alert">
                        <span class="block sm:inline">{{ session('error') }}</span>
                    </div>
                    @endif

                    @if(session('warning'))
                    <div class="mb-6 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg" role="alert">
                        <span class="block sm:inline">{{ session('warning') }}</span>
                    </div>
                    @endif

                    @yield('content')
                </div>
            </main>
        </div>

        <!-- Sidebar العصري -->
        <div class="sidebar sidebar-container fixed inset-y-0 right-0 z-50 w-72 transform transition-all duration-300 ease-in-out lg:relative lg:translate-x-0 flex flex-col"
             :class="{
                 'translate-x-0': sidebarOpen || window.innerWidth >= 1024,
                 'translate-x-full': !sidebarOpen && window.innerWidth < 1024
             }"
             style="right: 0; left: auto;"">

        <!-- Sidebar Header العصري -->
        <div class="flex items-center justify-between h-20 px-6 border-b border-gray-200/20 dark:border-gray-700/30 backdrop-blur-sm">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4">
                    <h1 class="text-xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 dark:from-blue-400 dark:to-blue-600 bg-clip-text text-transparent">الطارق</h1>
                    <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">إدارة مراكز الصيانة</p>
                </div>
            </div>
            <button @click="sidebarOpen = false" class="lg:hidden p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Navigation العصري -->
        <nav class="sidebar-content flex-1">
            <!-- لوحة التحكم الرئيسية -->
            <div class="sidebar-group">
                <a href="{{ route('dashboard') }}" class="sidebar-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    <span class="link-text">لوحة التحكم</span>
                </a>
            </div>

            <div class="section-divider"></div>

            <!-- المبيعات والعمليات -->
            <div class="sidebar-group">
                <h3 class="section-title">
                    <svg class="w-4 h-4 inline ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    المبيعات والعمليات
                </h3>

                @if(auth()->user()->hasPermission('sales.pos'))
                <a href="{{ route('pos.index') }}" class="sidebar-link {{ request()->routeIs('pos.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    <span class="link-text">نقاط البيع</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('sales.view'))
                <a href="{{ route('sales.index') }}" class="sidebar-link {{ request()->routeIs('sales.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                    <span class="link-text">المبيعات</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('purchases.view'))
                <a href="{{ route('purchases.index') }}" class="sidebar-link {{ request()->routeIs('purchases.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 3H3m4 10v6a1 1 0 001 1h9a1 1 0 001-1v-6M9 19v2m6-2v2"></path>
                    </svg>
                    <span class="link-text">المشتريات</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('payments.view'))
                <a href="{{ route('payments.index') }}" class="sidebar-link {{ request()->routeIs('payments.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <span class="link-text">المدفوعات</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('credit.view'))
                <a href="{{ route('credit.index') }}" class="sidebar-link {{ request()->routeIs('credit.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                    <span class="link-text">الائتمان والمستحقات</span>
                </a>
                @endif
            </div>

            <div class="section-divider"></div>

            <!-- المخزون والمنتجات -->
            <div class="sidebar-group">
                <h3 class="section-title">
                    <svg class="w-4 h-4 inline ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    المخزون والمنتجات
                </h3>

                @if(auth()->user()->hasPermission('products.view'))
                <a href="{{ route('products.index') }}" class="sidebar-link {{ request()->routeIs('products.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <span class="link-text">المنتجات</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('inventory.view'))
                <a href="{{ route('inventory.index') }}" class="sidebar-link {{ request()->routeIs('inventory.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                    </svg>
                    <span class="link-text">المخزون</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('parts.view'))
                <a href="{{ route('parts.index') }}" class="sidebar-link {{ request()->routeIs('parts.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span class="link-text">قطع الغيار</span>
                </a>
                @endif
            </div>

            <div class="section-divider"></div>

            <!-- العملاء والموردين -->
            <div class="sidebar-group">
                <h3 class="section-title">
                    <svg class="w-4 h-4 inline ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    العملاء والموردين
                </h3>

                @if(auth()->user()->hasPermission('customers.view'))
                <a href="{{ route('customers.index') }}" class="sidebar-link {{ request()->routeIs('customers.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <span class="link-text">العملاء</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('suppliers.view'))
                <a href="{{ route('suppliers.index') }}" class="sidebar-link {{ request()->routeIs('suppliers.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <span class="link-text">الموردين</span>
                </a>
                @endif
            </div>

            <div class="section-divider"></div>

            <!-- وحدات الصيانة -->
            <div class="sidebar-group">
                <h3 class="section-title">
                    <svg class="w-4 h-4 inline ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    وحدات الصيانة
                </h3>

                @if(auth()->user()->hasPermission('repairs.view'))
                <a href="{{ route('repairs.index') }}" class="sidebar-link {{ request()->routeIs('repairs.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span class="link-text">طلبات الصيانة</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('maintenance.view'))
                <a href="{{ route('maintenance.index') }}" class="sidebar-link {{ request()->routeIs('maintenance.*') && !request()->routeIs('maintenance.preventive') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V5a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
                    </svg>
                    <span class="link-text">إدارة الصيانة</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('maintenance.view'))
                <a href="{{ route('maintenance.preventive') }}" class="sidebar-link {{ request()->routeIs('maintenance.preventive') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="link-text">الصيانة الوقائية</span>
                </a>
                @endif

                <a href="{{ route('warranty.index') }}" class="sidebar-link {{ request()->routeIs('warranty.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <span class="link-text">الضمانات</span>
                </a>

                @if(auth()->user()->hasPermission('schedule.view'))
                <a href="{{ route('schedule.index') }}" class="sidebar-link {{ request()->routeIs('schedule.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span class="link-text">الجدولة</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('technicians.view'))
                <a href="{{ route('technicians.index') }}" class="sidebar-link {{ request()->routeIs('technicians.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span class="link-text">الفنيين</span>
                </a>
                @endif
            </div>

            <div class="section-divider"></div>



            <!-- التقارير الشاملة -->
            <div class="sidebar-group">
                <h3 class="section-title">
                    <svg class="w-4 h-4 inline ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    التقارير الشاملة
                </h3>

                <a href="{{ route('reports.sales') }}" class="sidebar-link {{ request()->routeIs('reports.sales') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                    <span class="link-text">تقارير المبيعات</span>
                </a>

                <a href="{{ route('reports.purchases') }}" class="sidebar-link {{ request()->routeIs('reports.purchases') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 3H3m4 10v6a1 1 0 001 1h9a1 1 0 001-1v-6M9 19v2m6-2v2"></path>
                    </svg>
                    <span class="link-text">تقارير المشتريات</span>
                </a>

                <a href="{{ route('reports.financial') }}" class="sidebar-link {{ request()->routeIs('reports.financial') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    <span class="link-text">التقارير المالية</span>
                </a>

                <a href="{{ route('reports.repairs') }}" class="sidebar-link {{ request()->routeIs('reports.repairs') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V5a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
                    </svg>
                    <span class="link-text">تقارير الصيانة</span>
                </a>

                <a href="{{ route('reports.inventory') }}" class="sidebar-link {{ request()->routeIs('reports.inventory') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                    </svg>
                    <span class="link-text">تقارير المخزون</span>
                </a>

                <a href="{{ route('reports.users') }}" class="sidebar-link {{ request()->routeIs('reports.users') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span class="link-text">تقارير المستخدمين</span>
                </a>

                <a href="{{ route('reports.advanced') }}" class="sidebar-link {{ request()->routeIs('reports.advanced') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span class="link-text">التقارير المتقدمة</span>
                </a>
            </div>

            <div class="section-divider"></div>

            <!-- إدارة المستخدمين -->
            <div class="sidebar-group">
                <h3 class="section-title">
                    <svg class="w-4 h-4 inline ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    إدارة المستخدمين
                </h3>

                @if(auth()->user()->hasPermission('users.view'))
                <a href="{{ route('users.index') }}" class="sidebar-link {{ request()->routeIs('users.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <span class="link-text">المستخدمين</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('roles.view'))
                <a href="{{ route('roles.index') }}" class="sidebar-link {{ request()->routeIs('roles.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <span class="link-text">الأدوار والصلاحيات</span>
                </a>
                @endif

                @if(auth()->user()->hasPermission('expenses.view'))
                <a href="{{ route('expenses.index') }}" class="sidebar-link {{ request()->routeIs('expenses.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <span class="link-text">المصروفات</span>
                </a>
                @endif

                <a href="{{ route('settings.index') }}" class="sidebar-link {{ request()->routeIs('settings.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                    </svg>
                    <span class="link-text">الإعدادات</span>
                </a>
            </div>

            <div class="section-divider"></div>

            <!-- الإدارة المتقدمة -->
            <div class="sidebar-group">
                <h3 class="section-title">
                    <svg class="w-4 h-4 inline ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    الإدارة المتقدمة
                </h3>

                @if(auth()->user()->email === '<EMAIL>' || auth()->user()->hasPermission('monitoring.view'))
                <a href="{{ route('monitoring.performance') }}" class="sidebar-link {{ request()->routeIs('monitoring.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span class="link-text">مراقبة الأداء</span>
                </a>
                @endif

                @if(auth()->user()->email === '<EMAIL>' || auth()->user()->hasPermission('security.view'))
                <a href="{{ route('security.backup') }}" class="sidebar-link {{ request()->routeIs('security.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <span class="link-text">الأمان والنسخ الاحتياطي</span>
                </a>
                @endif

                @if(auth()->user()->email === '<EMAIL>' || auth()->user()->hasPermission('barcode.view'))
                <a href="{{ route('barcode.generator') }}" class="sidebar-link {{ request()->routeIs('barcode.*') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                    </svg>
                    <span class="link-text">مولد الباركود</span>
                </a>
                @endif

                @if(auth()->user()->email === '<EMAIL>' || auth()->user()->hasPermission('mobile.view'))
                <a href="{{ route('mobile.app') }}" class="sidebar-link {{ request()->routeIs('mobile.*') ? 'active' : '' }}" target="_blank">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"></path>
                    </svg>
                    <span class="link-text">التطبيق المحمول</span>
                </a>
                @endif

                @if(auth()->user()->email === '<EMAIL>' || auth()->user()->hasPermission('notifications.advanced'))
                <a href="{{ route('notifications.advanced') }}" class="sidebar-link {{ request()->routeIs('notifications.advanced') ? 'active' : '' }}">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.868 19.718c.75.75 1.768.75 2.518 0L19.5 7.604c.75-.75.75-1.768 0-2.518s-1.768-.75-2.518 0L4.868 17.2c-.75.75-.75 1.768 0 2.518z"></path>
                    </svg>
                    <span class="link-text">الإشعارات المتقدمة</span>
                </a>
                @endif
            </div>
        </nav>
    </div>

    </div>

    @stack('scripts')
</body>
</html>
