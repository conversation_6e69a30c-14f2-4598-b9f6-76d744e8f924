@extends('layouts.main')

@section('title', 'طلبات صيانة العميل')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">طلبات صيانة العميل</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $customer->display_name }} - {{ $customer->customer_number }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('customers.show', $customer) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة لتفاصيل العميل
            </a>
            <a href="{{ route('repairs.create', ['customer_id' => $customer->id]) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                طلب صيانة جديد
            </a>
        </div>
    </div>

    <!-- Customer Summary -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $repairs->total() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $repairs->where('status', 'completed')->count() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">طلبات مكتملة</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ $repairs->whereIn('status', ['pending', 'in_progress'])->count() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">طلبات جارية</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ number_format($repairs->sum('total_cost'), 2) }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">إجمالي القيمة (ر.س)</div>
            </div>
        </div>
    </div>

    <!-- Repairs List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">طلبات الصيانة</h3>
        </div>

        @if($repairs->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            رقم الطلب
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الوصف
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الفني
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            التكلفة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            التاريخ
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($repairs as $repair)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                #{{ $repair->id }}
                            </div>
                            @if($repair->repair_number)
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $repair->repair_number }}
                                </div>
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                {{ $repair->title ?? 'طلب صيانة' }}
                            </div>
                            @if($repair->description)
                                <div class="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                                    {{ $repair->description }}
                                </div>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            @if($repair->technician)
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center ml-2">
                                        <span class="text-xs font-medium">{{ substr($repair->technician->name, 0, 2) }}</span>
                                    </div>
                                    {{ $repair->technician->name }}
                                </div>
                            @else
                                <span class="text-gray-500 dark:text-gray-400">غير محدد</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                @switch($repair->status)
                                    @case('pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 @break
                                    @case('in_progress') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 @break
                                    @case('waiting_parts') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 @break
                                    @case('completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @break
                                    @case('cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @break
                                    @default bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                @endswitch
                            ">
                                @switch($repair->status)
                                    @case('pending') معلق @break
                                    @case('in_progress') قيد التنفيذ @break
                                    @case('waiting_parts') انتظار قطع @break
                                    @case('completed') مكتمل @break
                                    @case('cancelled') ملغي @break
                                    @default {{ $repair->status }}
                                @endswitch
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <div class="font-medium">{{ number_format($repair->total_cost ?? 0, 2) }} ر.س</div>
                            @if($repair->payment_status)
                                <div class="text-xs {{ $repair->payment_status == 'paid' ? 'text-green-600' : 'text-red-600' }}">
                                    {{ $repair->payment_status == 'paid' ? 'مدفوع' : 'غير مدفوع' }}
                                </div>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <div>{{ $repair->created_at->format('Y-m-d') }}</div>
                            <div class="text-xs text-gray-500">{{ $repair->created_at->format('H:i') }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{{ route('repairs.show', $repair) }}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200">
                                    عرض
                                </a>
                                @if($repair->status != 'completed' && $repair->status != 'cancelled')
                                    <a href="{{ route('repairs.edit', $repair) }}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-200">
                                        تعديل
                                    </a>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            {{ $repairs->links() }}
        </div>
        @else
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد طلبات صيانة</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لم يقم هذا العميل بأي طلبات صيانة بعد.</p>
            <div class="mt-6">
                <a href="{{ route('repairs.create', ['customer_id' => $customer->id]) }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <svg class="-mr-1 ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة طلب صيانة
                </a>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
