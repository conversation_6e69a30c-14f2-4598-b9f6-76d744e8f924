@extends('layouts.main')

@section('title', 'تعديل الفني')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تعديل الفني</h1>
            <p class="text-gray-600 dark:text-gray-400">تعديل معلومات الفني {{ $technician->first_name }} {{ $technician->last_name }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('technicians.show', $technician) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                إلغاء
            </a>
            <button type="submit" form="technician-form" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                حفظ التغييرات
            </button>
        </div>
    </div>

    <form id="technician-form" action="{{ route('technicians.update', $technician) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Form -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Personal Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">المعلومات الشخصية</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأول</label>
                            <input type="text" name="first_name" id="first_name" required
                                   value="{{ old('first_name', $technician->first_name) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('first_name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأخير</label>
                            <input type="text" name="last_name" id="last_name" required
                                   value="{{ old('last_name', $technician->last_name) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('last_name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                            <input type="email" name="email" id="email" required
                                   value="{{ old('email', $technician->email) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('email')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                            <input type="tel" name="phone" id="phone"
                                   value="{{ old('phone', $technician->phone) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('phone')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="mobile" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الجوال</label>
                            <input type="tel" name="mobile" id="mobile"
                                   value="{{ old('mobile', $technician->mobile) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('mobile')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="employee_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الموظف</label>
                            <input type="text" name="employee_id" id="employee_id"
                                   value="{{ old('employee_id', $technician->employee_id) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('employee_id')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Work Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات العمل</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">القسم</label>
                            <select name="department" id="department" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="">اختر القسم</option>
                                @foreach($departments as $department)
                                    <option value="{{ $department }}" {{ $technician->department == $department ? 'selected' : '' }}>
                                        {{ $department }}
                                    </option>
                                @endforeach
                                <option value="صيانة الهواتف" {{ $technician->department == 'صيانة الهواتف' ? 'selected' : '' }}>صيانة الهواتف</option>
                                <option value="صيانة الحاسوب" {{ $technician->department == 'صيانة الحاسوب' ? 'selected' : '' }}>صيانة الحاسوب</option>
                                <option value="صيانة الأجهزة اللوحية" {{ $technician->department == 'صيانة الأجهزة اللوحية' ? 'selected' : '' }}>صيانة الأجهزة اللوحية</option>
                            </select>
                            @error('department')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="position" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المنصب</label>
                            <input type="text" name="position" id="position" required
                                   value="{{ old('position', $technician->position) }}" placeholder="مثل: فني أول، فني مساعد"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('position')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="hire_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ التوظيف</label>
                            <input type="date" name="hire_date" id="hire_date" required
                                   value="{{ old('hire_date', $technician->hire_date->format('Y-m-d')) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('hire_date')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="skill_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">مستوى المهارة</label>
                            <select name="skill_level" id="skill_level" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="">اختر المستوى</option>
                                <option value="beginner" {{ $technician->skill_level == 'beginner' ? 'selected' : '' }}>مبتدئ</option>
                                <option value="intermediate" {{ $technician->skill_level == 'intermediate' ? 'selected' : '' }}>متوسط</option>
                                <option value="advanced" {{ $technician->skill_level == 'advanced' ? 'selected' : '' }}>متقدم</option>
                                <option value="expert" {{ $technician->skill_level == 'expert' ? 'selected' : '' }}>خبير</option>
                            </select>
                            @error('skill_level')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="experience_years" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">سنوات الخبرة</label>
                            <input type="number" name="experience_years" id="experience_years" min="0"
                                   value="{{ old('experience_years', $technician->experience_years) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('experience_years')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="salary" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الراتب الشهري</label>
                            <input type="number" name="salary" id="salary" step="0.01" min="0"
                                   value="{{ old('salary', $technician->salary) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('salary')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Specializations -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">التخصصات</h3>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        @foreach($specializations as $specialization)
                        <label class="flex items-center">
                            <input type="checkbox" name="specializations[]" value="{{ $specialization }}"
                                   {{ in_array($specialization, old('specializations', $technician->specializations ?? [])) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">{{ $specialization }}</span>
                        </label>
                        @endforeach
                    </div>
                    @error('specializations')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Additional Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات إضافية</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العنوان</label>
                            <textarea name="address" id="address" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">{{ old('address', $technician->address) }}</textarea>
                            @error('address')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم جهة الاتصال الطارئ</label>
                                <input type="text" name="emergency_contact_name" id="emergency_contact_name"
                                       value="{{ old('emergency_contact_name', $technician->emergency_contact_name) }}"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                @error('emergency_contact_name')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم جهة الاتصال الطارئ</label>
                                <input type="tel" name="emergency_contact_phone" id="emergency_contact_phone"
                                       value="{{ old('emergency_contact_phone', $technician->emergency_contact_phone) }}"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                @error('emergency_contact_phone')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                        
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات</label>
                            <textarea name="notes" id="notes" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">{{ old('notes', $technician->notes) }}</textarea>
                            @error('notes')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="profile_image" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">صورة الملف الشخصي</label>
                            @if($technician->profile_image)
                                <div class="mb-2">
                                    <img src="{{ Storage::url($technician->profile_image) }}" alt="Current Image" class="w-20 h-20 rounded-full object-cover">
                                    <p class="text-sm text-gray-500 mt-1">الصورة الحالية</p>
                                </div>
                            @endif
                            <input type="file" name="profile_image" id="profile_image" accept="image/*"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            <p class="text-sm text-gray-500 mt-1">يمكنك اختيار صورة جديدة (JPEG, PNG, JPG, GIF) بحد أقصى 2MB</p>
                            @error('profile_image')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Current Status -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">الحالة الحالية</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">الحالة:</span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $technician->status_badge['class'] }}">
                                {{ $technician->status_badge['text'] }}
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">التوفر:</span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $technician->availability_badge['class'] }}">
                                {{ $technician->availability_badge['text'] }}
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">آخر تحديث:</span>
                            <span class="font-medium text-gray-900 dark:text-gray-100">{{ $technician->updated_at->format('Y-m-d H:i') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Status Controls -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">تحديث الحالة</h4>
                    <div class="space-y-3">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة الموظف</label>
                            <select name="status" id="status"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="active" {{ $technician->status == 'active' ? 'selected' : '' }}>نشط</option>
                                <option value="inactive" {{ $technician->status == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                <option value="suspended" {{ $technician->status == 'suspended' ? 'selected' : '' }}>معلق</option>
                            </select>
                            @error('status')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="availability" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة التوفر</label>
                            <select name="availability" id="availability"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="available" {{ $technician->availability == 'available' ? 'selected' : '' }}>متاح</option>
                                <option value="busy" {{ $technician->availability == 'busy' ? 'selected' : '' }}>مشغول</option>
                                <option value="unavailable" {{ $technician->availability == 'unavailable' ? 'selected' : '' }}>غير متاح</option>
                                <option value="on_break" {{ $technician->availability == 'on_break' ? 'selected' : '' }}>في استراحة</option>
                            </select>
                            @error('availability')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection
