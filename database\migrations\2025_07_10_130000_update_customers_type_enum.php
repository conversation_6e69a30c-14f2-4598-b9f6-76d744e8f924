<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update any existing 'company' values to 'business'
        DB::statement("UPDATE customers SET type = 'business' WHERE type = 'company'");

        // Then update the enum values for the type column
        DB::statement("ALTER TABLE customers MODIFY COLUMN type ENUM('individual', 'business') DEFAULT 'individual'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to the original enum values
        DB::statement("ALTER TABLE customers MODIFY COLUMN type ENUM('individual', 'company') DEFAULT 'individual'");
    }
};
