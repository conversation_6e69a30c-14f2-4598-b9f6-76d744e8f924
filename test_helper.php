<?php

/**
 * Interactive Test Helper
 * 
 * This script helps you test the system step by step
 */

echo "🧪 مساعد الاختبار التفاعلي\n";
echo "==========================\n\n";

// Test Configuration
$baseUrl = 'http://tareq.test';
$tests = [
    'basic' => [
        'name' => 'اختبارات أساسية',
        'routes' => [
            '/test' => 'اختبار النظام الأساسي',
            '/test-schedule' => 'اختبار route الجدولة',
            '/simple-schedule' => 'اختبار view الجدولة (فارغ)'
        ]
    ],
    'schedule' => [
        'name' => 'اختبار الجدولة الكاملة',
        'routes' => [
            '/technicians/schedule' => 'جدولة الفنيين (الهدف الرئيسي)'
        ]
    ],
    'technicians' => [
        'name' => 'إدارة الفنيين',
        'routes' => [
            '/technicians' => 'قائمة الفنيين',
            '/technicians/create' => 'إضافة فني جديد'
        ]
    ],
    'repairs' => [
        'name' => 'إدارة طلبات الصيانة',
        'routes' => [
            '/repairs' => 'قائمة طلبات الصيانة',
            '/repairs/create' => 'إضافة طلب جديد'
        ]
    ]
];

// Pre-flight checks
echo "🔍 فحص أولي للنظام...\n";

$criticalFiles = [
    'routes/web.php',
    'app/Http/Controllers/TechnicianController.php',
    'app/Http/Controllers/RepairController.php',
    'resources/views/technicians/schedule.blade.php'
];

$allFilesExist = true;
foreach ($criticalFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ " . basename($file) . "\n";
    } else {
        echo "   ❌ " . basename($file) . " مفقود\n";
        $allFilesExist = false;
    }
}

if (!$allFilesExist) {
    echo "\n❌ بعض الملفات الحرجة مفقودة. لا يمكن المتابعة.\n";
    exit(1);
}

// Check route order
echo "\n🛣️  فحص ترتيب Routes...\n";
$routesContent = file_get_contents('routes/web.php');
$schedulePos = strpos($routesContent, "Route::get('technicians/schedule'");
$resourcePos = strpos($routesContent, "Route::resource('technicians'");

if ($schedulePos !== false && $resourcePos !== false && $schedulePos < $resourcePos) {
    echo "   ✅ ترتيب Routes صحيح\n";
} else {
    echo "   ❌ ترتيب Routes خاطئ - قد يسبب 404\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🚀 بدء الاختبار التفاعلي\n";
echo str_repeat("=", 50) . "\n\n";

foreach ($tests as $category => $testGroup) {
    echo "📋 " . $testGroup['name'] . "\n";
    echo str_repeat("-", strlen($testGroup['name']) + 4) . "\n";
    
    foreach ($testGroup['routes'] as $route => $description) {
        $fullUrl = $baseUrl . $route;
        echo "\n🔗 $description\n";
        echo "   الرابط: $fullUrl\n";
        
        // Check if this is a critical route
        if ($route === '/technicians/schedule') {
            echo "   ⭐ هذا هو الاختبار الأهم!\n";
        }
        
        echo "   📝 اختبر الرابط في المتصفح\n";
        echo "   ✅ يجب أن يعمل بدون أخطاء\n";
        
        if ($category === 'basic') {
            echo "   📄 النتيجة المتوقعة: رسالة نجاح\n";
        } elseif ($route === '/technicians/schedule') {
            echo "   📄 النتيجة المتوقعة:\n";
            echo "      - عنوان 'جدولة الفنيين'\n";
            echo "      - بطاقات إحصائيات\n";
            echo "      - قائمة الفنيين\n";
            echo "      - قائمة الطلبات غير المُعينة\n";
        } else {
            echo "   📄 النتيجة المتوقعة: صفحة تحتوي على المحتوى المطلوب\n";
        }
        
        echo "\n   ⏳ اختبر الرابط الآن...\n";
        echo "   " . str_repeat(".", 30) . "\n";
    }
    
    echo "\n✅ انتهيت من اختبار " . $testGroup['name'] . "؟\n";
    echo "   اضغط Enter للمتابعة...\n\n";
}

echo str_repeat("=", 50) . "\n";
echo "📊 تقرير الاختبار النهائي\n";
echo str_repeat("=", 50) . "\n\n";

echo "📋 قائمة مراجعة الاختبار:\n\n";

$checkList = [
    'الاختبارات الأساسية' => [
        '/test - اختبار النظام الأساسي',
        '/test-schedule - اختبار route الجدولة',
        '/simple-schedule - اختبار view الجدولة'
    ],
    'الجدولة الكاملة' => [
        '/technicians/schedule - جدولة الفنيين ⭐'
    ],
    'إدارة الفنيين' => [
        '/technicians - قائمة الفنيين',
        '/technicians/create - إضافة فني جديد'
    ],
    'إدارة طلبات الصيانة' => [
        '/repairs - قائمة طلبات الصيانة',
        '/repairs/create - إضافة طلب جديد'
    ]
];

foreach ($checkList as $category => $items) {
    echo "🔸 $category:\n";
    foreach ($items as $item) {
        echo "   [ ] $item\n";
    }
    echo "\n";
}

echo "📈 معايير النجاح:\n";
echo "   ✅ جميع الصفحات تحمل بدون أخطاء\n";
echo "   ✅ المحتوى يظهر باللغة العربية\n";
echo "   ✅ التصميم متجاوب ويعمل على الجوال\n";
echo "   ✅ لا توجد أخطاء في console المتصفح\n";
echo "   ✅ النماذج تعمل وتحفظ البيانات\n\n";

echo "🐛 إذا واجهت مشاكل:\n";
echo "   1. امسح cache: php artisan route:clear\n";
echo "   2. أعد تشغيل الخادم: php artisan serve\n";
echo "   3. تحقق من logs: storage/logs/laravel.log\n";
echo "   4. تأكد من قاعدة البيانات: php artisan migrate:status\n\n";

echo "🎯 الهدف الرئيسي:\n";
echo "   تأكد من أن http://tareq.test/technicians/schedule يعمل بشكل مثالي!\n\n";

echo "📞 للدعم:\n";
echo "   إذا فشل أي اختبار، أرسل:\n";
echo "   - الرابط الذي فشل\n";
echo "   - رسالة الخطأ\n";
echo "   - screenshot إن أمكن\n\n";

echo "🏁 انتهى مساعد الاختبار\n";
echo "   ابدأ الاختبار الآن باتباع الروابط أعلاه!\n";
