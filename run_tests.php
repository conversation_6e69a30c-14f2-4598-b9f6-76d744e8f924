<?php

/**
 * Test Runner Script
 * 
 * This script runs basic integration tests to verify that the repair center
 * management system is working correctly.
 */

echo "🚀 بدء اختبار نظام إدارة مراكز الصيانة\n";
echo "=====================================\n\n";

// Test 1: Check if all required files exist
echo "📁 اختبار وجود الملفات المطلوبة...\n";

$requiredFiles = [
    'app/Http/Controllers/RepairController.php',
    'app/Http/Controllers/TechnicianController.php',
    'app/Models/Repair.php',
    'app/Models/Technician.php',
    'app/Models/Customer.php',
    'app/Models/RepairStatusHistory.php',
    'resources/views/repairs/index.blade.php',
    'resources/views/repairs/create.blade.php',
    'resources/views/repairs/show.blade.php',
    'resources/views/repairs/edit.blade.php',
    'resources/views/technicians/index.blade.php',
    'resources/views/technicians/create.blade.php',
    'resources/views/technicians/show.blade.php',
    'resources/views/technicians/edit.blade.php',
    'resources/views/technicians/schedule.blade.php',
    'routes/web.php',
];

$missingFiles = [];
foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        $missingFiles[] = $file;
    }
}

if (empty($missingFiles)) {
    echo "✅ جميع الملفات المطلوبة موجودة\n\n";
} else {
    echo "❌ الملفات التالية مفقودة:\n";
    foreach ($missingFiles as $file) {
        echo "   - $file\n";
    }
    echo "\n";
}

// Test 2: Check Routes
echo "🛣️  اختبار Routes...\n";

$routesContent = file_get_contents('routes/web.php');

$requiredRoutes = [
    'repairs',
    'technicians',
    'RepairController',
    'TechnicianController',
    'update-status',
    'assign-repair',
    'auto-assign',
    'schedule'
];

$missingRoutes = [];
foreach ($requiredRoutes as $route) {
    if (strpos($routesContent, $route) === false) {
        $missingRoutes[] = $route;
    }
}

if (empty($missingRoutes)) {
    echo "✅ جميع Routes مُعرفة بشكل صحيح\n\n";
} else {
    echo "❌ Routes التالية مفقودة أو غير مُعرفة:\n";
    foreach ($missingRoutes as $route) {
        echo "   - $route\n";
    }
    echo "\n";
}

// Test 3: Check Controllers
echo "🎮 اختبار Controllers...\n";

// Check RepairController
$repairControllerContent = file_get_contents('app/Http/Controllers/RepairController.php');
$repairMethods = ['index', 'create', 'store', 'show', 'edit', 'update', 'destroy', 'updateStatus'];

$missingRepairMethods = [];
foreach ($repairMethods as $method) {
    if (strpos($repairControllerContent, "function $method") === false) {
        $missingRepairMethods[] = $method;
    }
}

if (empty($missingRepairMethods)) {
    echo "✅ RepairController يحتوي على جميع الوظائف المطلوبة\n";
} else {
    echo "❌ RepairController يفتقد للوظائف التالية:\n";
    foreach ($missingRepairMethods as $method) {
        echo "   - $method\n";
    }
}

// Check TechnicianController
$technicianControllerContent = file_get_contents('app/Http/Controllers/TechnicianController.php');
$technicianMethods = ['index', 'create', 'store', 'show', 'edit', 'update', 'destroy', 'schedule', 'updateAvailability', 'assignRepair', 'autoAssignRepairs'];

$missingTechnicianMethods = [];
foreach ($technicianMethods as $method) {
    if (strpos($technicianControllerContent, "function $method") === false) {
        $missingTechnicianMethods[] = $method;
    }
}

if (empty($missingTechnicianMethods)) {
    echo "✅ TechnicianController يحتوي على جميع الوظائف المطلوبة\n\n";
} else {
    echo "❌ TechnicianController يفتقد للوظائف التالية:\n";
    foreach ($missingTechnicianMethods as $method) {
        echo "   - $method\n";
    }
    echo "\n";
}

// Test 4: Check Models
echo "📊 اختبار Models...\n";

$models = [
    'Repair' => 'app/Models/Repair.php',
    'Technician' => 'app/Models/Technician.php',
    'Customer' => 'app/Models/Customer.php',
    'RepairStatusHistory' => 'app/Models/RepairStatusHistory.php'
];

foreach ($models as $modelName => $modelPath) {
    if (file_exists($modelPath)) {
        $modelContent = file_get_contents($modelPath);
        if (strpos($modelContent, "class $modelName") !== false) {
            echo "✅ $modelName Model موجود ومُعرف بشكل صحيح\n";
        } else {
            echo "❌ $modelName Model موجود لكن غير مُعرف بشكل صحيح\n";
        }
    } else {
        echo "❌ $modelName Model غير موجود\n";
    }
}
echo "\n";

// Test 5: Check Views
echo "👁️  اختبار Views...\n";

$viewCategories = [
    'repairs' => ['index', 'create', 'show', 'edit'],
    'technicians' => ['index', 'create', 'show', 'edit', 'schedule']
];

foreach ($viewCategories as $category => $views) {
    echo "📁 $category views:\n";
    foreach ($views as $view) {
        $viewPath = "resources/views/$category/$view.blade.php";
        if (file_exists($viewPath)) {
            echo "   ✅ $view.blade.php\n";
        } else {
            echo "   ❌ $view.blade.php مفقود\n";
        }
    }
    echo "\n";
}

// Test 6: Check Database Structure
echo "🗄️  اختبار هيكل قاعدة البيانات...\n";

$migrationFiles = glob('database/migrations/*.php');
$requiredTables = ['repairs', 'technicians', 'customers', 'repair_status_histories'];

foreach ($requiredTables as $table) {
    $found = false;
    foreach ($migrationFiles as $migration) {
        if (strpos($migration, $table) !== false) {
            $found = true;
            break;
        }
    }
    
    if ($found) {
        echo "✅ جدول $table له migration\n";
    } else {
        echo "❌ جدول $table ليس له migration\n";
    }
}
echo "\n";

// Summary
echo "📋 ملخص الاختبار\n";
echo "================\n";

$totalIssues = count($missingFiles) + count($missingRoutes) + count($missingRepairMethods) + count($missingTechnicianMethods);

if ($totalIssues === 0) {
    echo "🎉 تهانينا! جميع الاختبارات نجحت\n";
    echo "✅ النظام جاهز للاستخدام\n";
    echo "✅ جميع Controllers مطورة بالكامل\n";
    echo "✅ جميع Views موجودة\n";
    echo "✅ جميع Routes مُعرفة\n";
    echo "✅ جميع Models موجودة\n\n";
    
    echo "🚀 الخطوات التالية:\n";
    echo "1. تشغيل الـ migrations: php artisan migrate\n";
    echo "2. تشغيل الـ seeders: php artisan db:seed\n";
    echo "3. تشغيل الخادم: php artisan serve\n";
    echo "4. زيارة الموقع: http://localhost:8000\n";
} else {
    echo "⚠️  تم العثور على $totalIssues مشكلة\n";
    echo "❌ يرجى إصلاح المشاكل المذكورة أعلاه\n";
}

echo "\n🏁 انتهى الاختبار\n";
