<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Account extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_code',
        'account_name',
        'account_name_en',
        'parent_id',
        'account_type',
        'account_category',
        'is_active',
        'is_system',
        'balance_type',
        'opening_balance',
        'current_balance',
        'description',
        'settings',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'settings' => 'array',
    ];

    /**
     * Get the parent account
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }

    /**
     * Get the child accounts
     */
    public function children(): HasMany
    {
        return $this->hasMany(Account::class, 'parent_id');
    }

    /**
     * Get all descendants recursively
     */
    public function descendants(): Has<PERSON>any
    {
        return $this->children()->with('descendants');
    }

    /**
     * Get account transactions
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(AccountTransaction::class);
    }

    /**
     * Scope for active accounts
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for accounts by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('account_type', $type);
    }

    /**
     * Scope for main accounts (no parent)
     */
    public function scopeMainAccounts($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Get account balance
     */
    public function getBalance($startDate = null, $endDate = null)
    {
        $query = $this->transactions();
        
        if ($startDate) {
            $query->whereHas('journalEntry', function ($q) use ($startDate) {
                $q->where('entry_date', '>=', $startDate);
            });
        }
        
        if ($endDate) {
            $query->whereHas('journalEntry', function ($q) use ($endDate) {
                $q->where('entry_date', '<=', $endDate);
            });
        }

        $transactions = $query->get();
        $debitTotal = $transactions->sum('debit_amount');
        $creditTotal = $transactions->sum('credit_amount');

        if ($this->balance_type === 'debit') {
            return $this->opening_balance + $debitTotal - $creditTotal;
        } else {
            return $this->opening_balance + $creditTotal - $debitTotal;
        }
    }

    /**
     * Update current balance
     */
    public function updateBalance()
    {
        $this->current_balance = $this->getBalance();
        $this->save();
    }

    /**
     * Get account hierarchy path
     */
    public function getHierarchyPath()
    {
        $path = [$this->account_name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($path, $parent->account_name);
            $parent = $parent->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * Check if account can be deleted
     */
    public function canBeDeleted()
    {
        return !$this->is_system && 
               $this->transactions()->count() === 0 && 
               $this->children()->count() === 0;
    }

    /**
     * Get account type label
     */
    public function getAccountTypeLabel()
    {
        $types = [
            'asset' => 'أصول',
            'liability' => 'خصوم',
            'equity' => 'حقوق الملكية',
            'revenue' => 'إيرادات',
            'expense' => 'مصروفات',
        ];

        return $types[$this->account_type] ?? $this->account_type;
    }

    /**
     * Get balance type label
     */
    public function getBalanceTypeLabel()
    {
        return $this->balance_type === 'debit' ? 'مدين' : 'دائن';
    }

    /**
     * Format account code for display
     */
    public function getFormattedCodeAttribute()
    {
        return $this->account_code;
    }

    /**
     * Get full account name with code
     */
    public function getFullNameAttribute()
    {
        return $this->account_code . ' - ' . $this->account_name;
    }

    /**
     * Static method to get account types
     */
    public static function getAccountTypes()
    {
        return [
            'asset' => 'أصول',
            'liability' => 'خصوم',
            'equity' => 'حقوق الملكية',
            'revenue' => 'إيرادات',
            'expense' => 'مصروفات',
        ];
    }

    /**
     * Static method to get balance types
     */
    public static function getBalanceTypes()
    {
        return [
            'debit' => 'مدين',
            'credit' => 'دائن',
        ];
    }
}
