@extends('layouts.main')

@section('title', 'جدولة الفنيين')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">جدولة الفنيين</h1>
            <p class="text-gray-600 dark:text-gray-400">إدارة جداول العمل وتوزيع المهام على الفنيين</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button onclick="autoAssignRepairs()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                توزيع تلقائي
            </button>
            <a href="{{ route('technicians.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">فنيين متاحين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $availableTechnicians }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                    <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">طلبات غير مُعينة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $unassignedRepairs }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">طلبات نشطة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $activeRepairs }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط العبء</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ number_format($averageWorkload, 1) }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Technicians List -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الفنيين وأعباء العمل</h3>
                
                <div class="space-y-4">
                    @foreach($technicians as $technician)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div class="flex justify-between items-start">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                @if($technician->profile_image)
                                    <img src="{{ Storage::url($technician->profile_image) }}" alt="{{ $technician->first_name }}" 
                                         class="w-10 h-10 rounded-full object-cover">
                                @else
                                    <div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </div>
                                @endif
                                
                                <div>
                                    <h4 class="font-medium text-gray-900 dark:text-gray-100">
                                        {{ $technician->first_name }} {{ $technician->last_name }}
                                    </h4>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $technician->department }}</p>
                                </div>
                            </div>
                            
                            <div class="text-right">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $technician->availability_badge['class'] }}">
                                    {{ $technician->availability_badge['text'] }}
                                </span>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                    {{ $technician->current_repairs_count }} طلب نشط
                                </p>
                            </div>
                        </div>
                        
                        <!-- Workload Bar -->
                        <div class="mt-3">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>عبء العمل</span>
                                <span>{{ $technician->current_repairs_count }}/{{ $technician->max_concurrent_repairs ?? 10 }}</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                @php
                                    $maxRepairs = $technician->max_concurrent_repairs ?? 10;
                                    $percentage = min(($technician->current_repairs_count / $maxRepairs) * 100, 100);
                                    $colorClass = $percentage > 80 ? 'bg-red-600' : ($percentage > 60 ? 'bg-yellow-600' : 'bg-green-600');
                                @endphp
                                <div class="{{ $colorClass }} h-2 rounded-full transition-all duration-300" style="width: {{ $percentage }}%"></div>
                            </div>
                        </div>
                        
                        <!-- Specializations -->
                        @if($technician->specializations && count($technician->specializations) > 0)
                        <div class="mt-3">
                            <div class="flex flex-wrap gap-1">
                                @foreach($technician->specializations as $specialization)
                                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                        {{ $specialization }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                        @endif
                        
                        <!-- Actions -->
                        <div class="mt-3 flex space-x-2 space-x-reverse">
                            <a href="{{ route('technicians.show', $technician) }}" 
                               class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm font-medium">
                                عرض التفاصيل
                            </a>
                            <a href="{{ route('technicians.workload', $technician) }}" 
                               class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200 text-sm font-medium">
                                عرض المهام
                            </a>
                            @if($technician->availability == 'available')
                            <button onclick="showAssignModal({{ $technician->id }})" 
                                    class="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-200 text-sm font-medium">
                                تعيين مهمة
                            </button>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Unassigned Repairs -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">طلبات غير مُعينة</h3>
                
                @if($unassignedRepairsList->count() > 0)
                <div class="space-y-3">
                    @foreach($unassignedRepairsList as $repair)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                        <div class="flex justify-between items-start">
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-gray-100 text-sm">
                                    {{ $repair->repair_number }}
                                </h4>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $repair->device_brand }} {{ $repair->device_model }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $repair->created_at->diffForHumans() }}
                                </p>
                            </div>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $repair->priority_badge['class'] }}">
                                {{ $repair->priority_badge['text'] }}
                            </span>
                        </div>
                        
                        <div class="mt-2">
                            <button onclick="showAssignRepairModal({{ $repair->id }})" 
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1 px-2 rounded text-xs">
                                تعيين فني
                            </button>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">جميع الطلبات مُعينة</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Assign Repair Modal -->
<div id="assignRepairModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">تعيين طلب صيانة</h3>
            
            <form id="assignRepairForm" method="POST">
                @csrf
                <div class="mb-4">
                    <label for="technician_select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اختر الفني</label>
                    <select id="technician_select" name="technician_id" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="">اختر الفني</option>
                        @foreach($availableTechniciansList as $tech)
                            <option value="{{ $tech->id }}">
                                {{ $tech->first_name }} {{ $tech->last_name }} 
                                ({{ $tech->current_repairs_count }} طلب نشط)
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
                </div>
                
                <div class="flex space-x-2 space-x-reverse">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg">
                        تعيين
                    </button>
                    <button type="button" onclick="closeAssignModal()" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let currentRepairId = null;

function showAssignRepairModal(repairId) {
    currentRepairId = repairId;
    document.getElementById('assignRepairForm').action = `/repairs/${repairId}/assign-technician`;
    document.getElementById('assignRepairModal').classList.remove('hidden');
}

function showAssignModal(technicianId) {
    // This would show a modal to assign a repair to a specific technician
    // For now, redirect to technician details
    window.location.href = `/technicians/${technicianId}`;
}

function closeAssignModal() {
    document.getElementById('assignRepairModal').classList.add('hidden');
    currentRepairId = null;
}

function autoAssignRepairs() {
    if (confirm('هل تريد توزيع جميع الطلبات غير المُعينة تلقائياً؟')) {
        fetch('/technicians/auto-assign', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                repair_ids: @json($unassignedRepairsList->pluck('id'))
            })
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء التوزيع التلقائي');
        });
    }
}

// Close modal when clicking outside
document.getElementById('assignRepairModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAssignModal();
    }
});
</script>
@endsection
