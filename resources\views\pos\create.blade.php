@extends('layouts.main')

@section('title', 'مبيعة جديدة - POS')

@section('content')
<div class="space-y-6" x-data="posCreateSale()">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">مبيعة جديدة</h1>
            <p class="text-gray-600 dark:text-gray-400">
                الموقع: {{ $currentLocation->name }}
            </p>
        </div>
        
        <div class="flex gap-2">
            <a href="{{ route('pos.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
        </div>
    </div>

    <form action="{{ route('pos.store') }}" method="POST" @submit="submitForm">
        @csrf
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Products Selection -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                    <!-- Search Header -->
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    البحث عن منتج
                                </label>
                                <div class="relative">
                                    <input type="text" 
                                           x-model="searchQuery"
                                           @input="searchProducts"
                                           placeholder="ابحث بالاسم أو الكود أو امسح الباركود..."
                                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="w-full sm:w-48">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    التصنيف
                                </label>
                                <select x-model="selectedCategory" @change="filterProducts" 
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                    <option value="">جميع التصنيفات</option>
                                    <option value="parts">قطع غيار</option>
                                    <option value="accessories">إكسسوارات</option>
                                    <option value="cables">كابلات</option>
                                    <option value="cases">جرابات</option>
                                    <option value="chargers">شواحن</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div class="p-6">
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                            <template x-for="product in filteredProducts" :key="product.id">
                                <div @click="addToCart(product)"
                                     class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-600">
                                    <div class="text-center">
                                        <div class="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-lg mx-auto mb-3 flex items-center justify-center">
                                            <i class="fas fa-box text-gray-400 text-xl"></i>
                                        </div>
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1" x-text="product.name"></h3>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mb-2" x-text="product.code"></p>
                                        <div class="flex justify-between items-center">
                                            <span class="text-sm font-bold text-blue-600 dark:text-blue-400" x-text="formatCurrency(product.price)"></span>
                                            <span class="text-xs px-2 py-1 rounded-full"
                                                  :class="product.stock > 10 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                                          product.stock > 0 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'"
                                                  x-text="product.stock"></span>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                        
                        <!-- No products found -->
                        <div x-show="filteredProducts.length === 0" class="text-center py-12">
                            <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500 dark:text-gray-400">لا توجد منتجات تطابق البحث</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cart & Sale Details -->
            <div class="space-y-6">
                <!-- Customer Selection -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات العميل</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                العميل (اختياري)
                            </label>
                            <select name="customer_id" x-model="selectedCustomer" @change="updateCustomerInfo"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="">عميل مجهول</option>
                                @foreach($customers as $customer)
                                    <option value="{{ $customer->id }}">{{ $customer->first_name }} {{ $customer->last_name }} - {{ $customer->phone }}</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div x-show="selectedCustomer" class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                            <div class="text-sm text-blue-800 dark:text-blue-200">
                                <div x-text="customerInfo.name"></div>
                                <div x-text="customerInfo.phone"></div>
                                <div x-text="customerInfo.email"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sale Type -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">نوع المبيعة</h3>
                    
                    <select name="sale_type" x-model="saleType" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="parts_only">قطع غيار فقط</option>
                        <option value="accessories">إكسسوارات</option>
                        <option value="repair_service">خدمة صيانة</option>
                        <option value="mixed">مختلط</option>
                    </select>
                </div>

                <!-- Cart -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">سلة المشتريات</h3>
                            <span class="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-2 py-1 rounded-full text-sm" 
                                  x-text="cart.length + ' عنصر'"></span>
                        </div>
                    </div>

                    <div class="p-6">
                        <div class="space-y-3 max-h-64 overflow-y-auto">
                            <template x-for="(item, index) in cart" :key="index">
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="item.name"></h4>
                                            <p class="text-xs text-gray-500 dark:text-gray-400" x-text="formatCurrency(item.price)"></p>
                                        </div>
                                        <button @click="removeFromCart(index)" type="button"
                                                class="text-red-600 hover:text-red-700 ml-2">
                                            <i class="fas fa-trash text-sm"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="flex items-center justify-between mt-2">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <button @click="decrementQuantity(index)" type="button"
                                                    class="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded text-xs hover:bg-gray-300 dark:hover:bg-gray-500">
                                                -
                                            </button>
                                            <span class="text-sm font-medium w-8 text-center" x-text="item.quantity"></span>
                                            <button @click="incrementQuantity(index)" type="button"
                                                    class="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded text-xs hover:bg-gray-300 dark:hover:bg-gray-500">
                                                +
                                            </button>
                                        </div>
                                        <span class="text-sm font-bold text-gray-900 dark:text-gray-100" 
                                              x-text="formatCurrency(item.price * item.quantity)"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                        
                        <!-- Empty cart message -->
                        <div x-show="cart.length === 0" class="text-center py-8">
                            <i class="fas fa-shopping-cart text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500 dark:text-gray-400">السلة فارغة</p>
                            <p class="text-sm text-gray-400 dark:text-gray-500">اختر المنتجات لإضافتها</p>
                        </div>
                    </div>
                </div>

                <!-- Discount & Promotion -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الخصم والعروض</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                كود الخصم
                            </label>
                            <div class="flex gap-2">
                                <input type="text" name="coupon_code" x-model="couponCode"
                                       placeholder="أدخل كود الخصم"
                                       class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <button type="button" @click="validateCoupon"
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                                    تحقق
                                </button>
                            </div>
                        </div>
                        
                        <div x-show="appliedPromotion" class="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                            <div class="text-sm text-green-800 dark:text-green-200">
                                <div class="font-medium" x-text="appliedPromotion?.name"></div>
                                <div x-text="'خصم: ' + formatCurrency(appliedPromotion?.discount_amount)"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Totals -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الإجمالي</h3>
                    
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                            <span x-text="formatCurrency(subtotal)"></span>
                        </div>
                        
                        <div x-show="discountAmount > 0" class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">الخصم:</span>
                            <span class="text-red-600" x-text="'-' + formatCurrency(discountAmount)"></span>
                        </div>
                        
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">الضريبة (15%):</span>
                            <span x-text="formatCurrency(taxAmount)"></span>
                        </div>
                        
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-2">
                            <div class="flex justify-between text-lg font-bold">
                                <span>المجموع الكلي:</span>
                                <span x-text="formatCurrency(totalAmount)"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        ملاحظات
                    </label>
                    <textarea name="notes" x-model="notes" rows="3"
                              placeholder="ملاحظات إضافية..."
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
                </div>

                <!-- Submit Button -->
                <button type="submit" :disabled="cart.length === 0"
                        class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-3 rounded-lg font-medium text-lg">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    إنشاء المبيعة
                </button>
            </div>
        </div>

        <!-- Hidden inputs for cart items -->
        <template x-for="(item, index) in cart" :key="index">
            <div>
                <input type="hidden" :name="'items[' + index + '][type]'" :value="item.type">
                <input type="hidden" :name="'items[' + index + '][name]'" :value="item.name">
                <input type="hidden" :name="'items[' + index + '][code]'" :value="item.code">
                <input type="hidden" :name="'items[' + index + '][description]'" :value="item.description">
                <input type="hidden" :name="'items[' + index + '][quantity]'" :value="item.quantity">
                <input type="hidden" :name="'items[' + index + '][unit_price]'" :value="item.price">
                <input type="hidden" :name="'items[' + index + '][original_price]'" :value="item.original_price">
                <input type="hidden" :name="'items[' + index + '][part_id]'" :value="item.part_id">
            </div>
        </template>
    </form>
</div>

@push('scripts')
<script>
// البيانات من الخادم
const serverParts = @json($parts ?? collect());
const serverCustomers = @json($customers ?? collect());

function posCreateSale() {
    return {
        // Data
        searchQuery: '',
        selectedCategory: '',
        selectedCustomer: '',
        saleType: 'parts_only',
        couponCode: '',
        notes: '',
        
        // Products and cart
        products: serverParts || [],
        filteredProducts: [],
        cart: [],

        // Customer info
        customers: serverCustomers || [],
        customerInfo: {},
        
        // Promotion
        appliedPromotion: null,
        
        // Computed properties
        get subtotal() {
            return this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        },
        
        get discountAmount() {
            return this.appliedPromotion?.discount_amount || 0;
        },
        
        get taxAmount() {
            return (this.subtotal - this.discountAmount) * 0.15;
        },
        
        get totalAmount() {
            return this.subtotal - this.discountAmount + this.taxAmount;
        },

        // Methods
        init() {
            this.filteredProducts = this.products;
        },

        searchProducts() {
            this.filterProducts();
        },

        filterProducts() {
            let filtered = this.products;

            if (this.searchQuery) {
                const query = this.searchQuery.toLowerCase();
                filtered = filtered.filter(product => 
                    product.name.toLowerCase().includes(query) ||
                    product.code.toLowerCase().includes(query) ||
                    (product.description && product.description.toLowerCase().includes(query))
                );
            }

            if (this.selectedCategory) {
                filtered = filtered.filter(product => 
                    product.category === this.selectedCategory
                );
            }

            this.filteredProducts = filtered;
        },

        addToCart(product) {
            if (product.stock <= 0) {
                alert('هذا المنتج غير متوفر في المخزون');
                return;
            }

            const existingIndex = this.cart.findIndex(item => item.id === product.id);
            
            if (existingIndex !== -1) {
                if (this.cart[existingIndex].quantity < product.stock) {
                    this.cart[existingIndex].quantity++;
                } else {
                    alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                }
            } else {
                this.cart.push({
                    ...product,
                    quantity: 1
                });
            }
        },

        removeFromCart(index) {
            this.cart.splice(index, 1);
        },

        incrementQuantity(index) {
            const item = this.cart[index];
            const product = this.products.find(p => p.id === item.id);
            
            if (item.quantity < product.stock) {
                this.cart[index].quantity++;
            } else {
                alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
            }
        },

        decrementQuantity(index) {
            if (this.cart[index].quantity > 1) {
                this.cart[index].quantity--;
            }
        },

        updateCustomerInfo() {
            if (this.selectedCustomer) {
                const customer = this.customers.find(c => c.id == this.selectedCustomer);
                if (customer) {
                    this.customerInfo = {
                        name: customer.first_name + ' ' + customer.last_name,
                        phone: customer.phone,
                        email: customer.email
                    };
                }
            } else {
                this.customerInfo = {};
            }
        },

        async validateCoupon() {
            if (!this.couponCode) return;

            try {
                const response = await fetch('{{ route("pos.validate-promotion") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        code: this.couponCode,
                        sale_amount: this.subtotal,
                        customer_id: this.selectedCustomer
                    })
                });

                const data = await response.json();

                if (data.valid) {
                    this.appliedPromotion = data.promotion;
                    alert('تم تطبيق كود الخصم بنجاح');
                } else {
                    this.appliedPromotion = null;
                    alert(data.message);
                }
            } catch (error) {
                console.error('Error validating coupon:', error);
                alert('حدث خطأ أثناء التحقق من كود الخصم');
            }
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR',
                minimumFractionDigits: 2
            }).format(amount);
        },

        submitForm(event) {
            if (this.cart.length === 0) {
                event.preventDefault();
                alert('يجب إضافة منتجات للسلة أولاً');
                return;
            }
        }
    }
}
</script>
@endpush
@endsection
