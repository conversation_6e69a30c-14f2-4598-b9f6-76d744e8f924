<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Purchase extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'location_id',
        'supplier_id',
        'purchase_number',
        'purchase_date',
        'due_date',
        'status',
        'payment_status',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'paid_amount',
        'notes',
        'payment_method'
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'due_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2'
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع الموقع
     */
    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * العلاقة مع المورد
     */
    public function supplier()
    {
        return $this->belongsTo(Contact::class, 'supplier_id');
    }

    /**
     * العلاقة مع عناصر المشترى
     */
    public function items()
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments()
    {
        return $this->hasMany(Payment::class, 'reference_id')->where('reference_type', self::class);
    }

    /**
     * الحصول على المبلغ المتبقي
     */
    public function getRemainingAmountAttribute()
    {
        return $this->total_amount - $this->paid_amount;
    }

    /**
     * التحقق من اكتمال الدفع
     */
    public function getIsFullyPaidAttribute()
    {
        return $this->paid_amount >= $this->total_amount;
    }

    /**
     * التحقق من تأخر الدفع
     */
    public function getIsOverdueAttribute()
    {
        return $this->due_date && $this->due_date->isPast() && !$this->is_fully_paid;
    }

    /**
     * scope للمشتريات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * scope للمشتريات المدفوعة
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    /**
     * scope للمشتريات المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->where('payment_status', '!=', 'paid');
    }

    /**
     * إنشاء رقم مشترى تلقائي
     */
    public static function generatePurchaseNumber($companyId)
    {
        $prefix = 'PUR';
        $lastPurchase = self::where('company_id', $companyId)
            ->orderBy('id', 'desc')
            ->first();

        $number = $lastPurchase ? (int) substr($lastPurchase->purchase_number, strlen($prefix)) + 1 : 1;
        
        return $prefix . str_pad($number, 6, '0', STR_PAD_LEFT);
    }
}
