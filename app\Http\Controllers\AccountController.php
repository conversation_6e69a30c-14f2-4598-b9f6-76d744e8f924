<?php

namespace App\Http\Controllers;

use App\Models\Account;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AccountController extends Controller
{
    /**
     * Display chart of accounts
     */
    public function index(Request $request)
    {
        $query = Account::with('parent', 'children');

        // Filter by type
        if ($request->filled('type')) {
            $query->where('account_type', $request->type);
        }

        // Filter by active status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('account_name', 'like', "%{$search}%")
                  ->orWhere('account_code', 'like', "%{$search}%")
                  ->orWhere('account_name_en', 'like', "%{$search}%");
            });
        }

        // Get main accounts (no parent) with their children
        if (!$request->filled('search')) {
            $accounts = $query->whereNull('parent_id')
                            ->orderBy('account_code')
                            ->get();
        } else {
            $accounts = $query->orderBy('account_code')->get();
        }

        $accountTypes = Account::getAccountTypes();

        return view('accounting.accounts.index', compact('accounts', 'accountTypes'));
    }

    /**
     * Show account details
     */
    public function show(Account $account)
    {
        $account->load('parent', 'children', 'transactions.journalEntry');
        
        // Get account balance
        $currentBalance = $account->getBalance();
        
        // Get recent transactions
        $recentTransactions = $account->transactions()
            ->with('journalEntry')
            ->whereHas('journalEntry', function ($query) {
                $query->where('status', 'posted');
            })
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return view('accounting.accounts.show', compact('account', 'currentBalance', 'recentTransactions'));
    }

    /**
     * Show create form
     */
    public function create(Request $request)
    {
        $parentId = $request->get('parent_id');
        $parentAccount = $parentId ? Account::find($parentId) : null;
        
        $accountTypes = Account::getAccountTypes();
        $balanceTypes = Account::getBalanceTypes();
        $parentAccounts = Account::whereNull('parent_id')->orderBy('account_code')->get();

        return view('accounting.accounts.create', compact(
            'accountTypes', 
            'balanceTypes', 
            'parentAccounts', 
            'parentAccount'
        ));
    }

    /**
     * Store new account
     */
    public function store(Request $request)
    {
        $request->validate([
            'account_code' => 'required|string|max:10|unique:accounts',
            'account_name' => 'required|string|max:255',
            'account_name_en' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:accounts,id',
            'account_type' => 'required|in:asset,liability,equity,revenue,expense',
            'account_category' => 'nullable|string|max:50',
            'balance_type' => 'required|in:debit,credit',
            'opening_balance' => 'nullable|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        $account = Account::create([
            'account_code' => $request->account_code,
            'account_name' => $request->account_name,
            'account_name_en' => $request->account_name_en,
            'parent_id' => $request->parent_id,
            'account_type' => $request->account_type,
            'account_category' => $request->account_category,
            'balance_type' => $request->balance_type,
            'opening_balance' => $request->opening_balance ?? 0,
            'current_balance' => $request->opening_balance ?? 0,
            'description' => $request->description,
            'is_active' => true,
        ]);

        return redirect()->route('accounts.index')
                        ->with('success', 'تم إنشاء الحساب بنجاح');
    }

    /**
     * Show edit form
     */
    public function edit(Account $account)
    {
        if ($account->is_system) {
            return redirect()->route('accounts.index')
                           ->with('error', 'لا يمكن تعديل حسابات النظام');
        }

        $accountTypes = Account::getAccountTypes();
        $balanceTypes = Account::getBalanceTypes();
        $parentAccounts = Account::whereNull('parent_id')
                                ->where('id', '!=', $account->id)
                                ->orderBy('account_code')
                                ->get();

        return view('accounting.accounts.edit', compact(
            'account',
            'accountTypes', 
            'balanceTypes', 
            'parentAccounts'
        ));
    }

    /**
     * Update account
     */
    public function update(Request $request, Account $account)
    {
        if ($account->is_system) {
            return redirect()->route('accounts.index')
                           ->with('error', 'لا يمكن تعديل حسابات النظام');
        }

        $request->validate([
            'account_code' => 'required|string|max:10|unique:accounts,account_code,' . $account->id,
            'account_name' => 'required|string|max:255',
            'account_name_en' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:accounts,id',
            'account_type' => 'required|in:asset,liability,equity,revenue,expense',
            'account_category' => 'nullable|string|max:50',
            'balance_type' => 'required|in:debit,credit',
            'description' => 'nullable|string',
        ]);

        // Prevent circular reference
        if ($request->parent_id && $this->wouldCreateCircularReference($account, $request->parent_id)) {
            return back()->withErrors(['parent_id' => 'لا يمكن جعل الحساب فرعاً لنفسه أو لأحد فروعه']);
        }

        $account->update([
            'account_code' => $request->account_code,
            'account_name' => $request->account_name,
            'account_name_en' => $request->account_name_en,
            'parent_id' => $request->parent_id,
            'account_type' => $request->account_type,
            'account_category' => $request->account_category,
            'balance_type' => $request->balance_type,
            'description' => $request->description,
        ]);

        return redirect()->route('accounts.index')
                        ->with('success', 'تم تحديث الحساب بنجاح');
    }

    /**
     * Toggle account status
     */
    public function toggleStatus(Account $account)
    {
        if ($account->is_system) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تعطيل حسابات النظام'
            ]);
        }

        $account->update(['is_active' => !$account->is_active]);

        return response()->json([
            'success' => true,
            'message' => $account->is_active ? 'تم تفعيل الحساب' : 'تم تعطيل الحساب',
            'is_active' => $account->is_active
        ]);
    }

    /**
     * Delete account
     */
    public function destroy(Account $account)
    {
        if (!$account->canBeDeleted()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف هذا الحساب لوجود معاملات أو حسابات فرعية'
            ]);
        }

        $account->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف الحساب بنجاح'
        ]);
    }

    /**
     * Get account hierarchy as JSON
     */
    public function hierarchy()
    {
        $accounts = Account::with('children')
                          ->whereNull('parent_id')
                          ->orderBy('account_code')
                          ->get();

        return response()->json($this->buildHierarchy($accounts));
    }

    /**
     * Get account balance
     */
    public function balance(Account $account, Request $request)
    {
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        
        $balance = $account->getBalance($startDate, $endDate);
        
        return response()->json([
            'account_id' => $account->id,
            'account_name' => $account->account_name,
            'balance' => $balance,
            'balance_type' => $account->balance_type,
            'formatted_balance' => number_format($balance, 2) . ' ريال'
        ]);
    }

    /**
     * Build hierarchy array for JSON response
     */
    private function buildHierarchy($accounts)
    {
        $hierarchy = [];
        
        foreach ($accounts as $account) {
            $node = [
                'id' => $account->id,
                'code' => $account->account_code,
                'name' => $account->account_name,
                'type' => $account->account_type,
                'balance' => $account->current_balance,
                'is_active' => $account->is_active,
                'children' => []
            ];
            
            if ($account->children->count() > 0) {
                $node['children'] = $this->buildHierarchy($account->children);
            }
            
            $hierarchy[] = $node;
        }
        
        return $hierarchy;
    }

    /**
     * Check if parent assignment would create circular reference
     */
    private function wouldCreateCircularReference(Account $account, $parentId)
    {
        $parent = Account::find($parentId);
        
        while ($parent) {
            if ($parent->id === $account->id) {
                return true;
            }
            $parent = $parent->parent;
        }
        
        return false;
    }
}
