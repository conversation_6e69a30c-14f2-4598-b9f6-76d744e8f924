<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Company;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\Contact;

class BasicDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $company = Company::first();
        
        if (!$company) {
            return;
        }

        // إنشاء التصنيفات الأساسية
        $this->createCategories($company->id);
        
        // إنشاء العلامات التجارية
        $this->createBrands($company->id);
        
        // إنشاء الوحدات
        $this->createUnits($company->id);
        
        // إنشاء عملاء وموردين تجريبيين
        $this->createContacts($company->id);
    }

    private function createCategories($companyId)
    {
        $categories = [
            [
                'name' => 'أجهزة الحاسوب',
                'name_en' => 'Computers',
                'description' => 'أجهزة الحاسوب المكتبية والمحمولة',
                'sort_order' => 1,
                'children' => [
                    ['name' => 'أجهزة مكتبية', 'name_en' => 'Desktop Computers'],
                    ['name' => 'أجهزة محمولة', 'name_en' => 'Laptops'],
                    ['name' => 'أجهزة لوحية', 'name_en' => 'Tablets']
                ]
            ],
            [
                'name' => 'الهواتف الذكية',
                'name_en' => 'Smartphones',
                'description' => 'الهواتف الذكية والملحقات',
                'sort_order' => 2,
                'children' => [
                    ['name' => 'هواتف أندرويد', 'name_en' => 'Android Phones'],
                    ['name' => 'هواتف آيفون', 'name_en' => 'iPhones'],
                    ['name' => 'هواتف أخرى', 'name_en' => 'Other Phones']
                ]
            ],
            [
                'name' => 'قطع الغيار',
                'name_en' => 'Spare Parts',
                'description' => 'قطع غيار للأجهزة المختلفة',
                'sort_order' => 3,
                'children' => [
                    ['name' => 'شاشات', 'name_en' => 'Screens'],
                    ['name' => 'بطاريات', 'name_en' => 'Batteries'],
                    ['name' => 'لوحات مفاتيح', 'name_en' => 'Keyboards'],
                    ['name' => 'ذاكرة تخزين', 'name_en' => 'Storage'],
                    ['name' => 'معالجات', 'name_en' => 'Processors']
                ]
            ],
            [
                'name' => 'الإكسسوارات',
                'name_en' => 'Accessories',
                'description' => 'إكسسوارات وملحقات متنوعة',
                'sort_order' => 4,
                'children' => [
                    ['name' => 'كابلات', 'name_en' => 'Cables'],
                    ['name' => 'شواحن', 'name_en' => 'Chargers'],
                    ['name' => 'جرابات', 'name_en' => 'Cases'],
                    ['name' => 'سماعات', 'name_en' => 'Headphones'],
                    ['name' => 'حوامل', 'name_en' => 'Stands']
                ]
            ],
            [
                'name' => 'خدمات الصيانة',
                'name_en' => 'Maintenance Services',
                'description' => 'خدمات الصيانة والإصلاح',
                'sort_order' => 5,
                'children' => [
                    ['name' => 'صيانة الحاسوب', 'name_en' => 'Computer Repair'],
                    ['name' => 'صيانة الهواتف', 'name_en' => 'Phone Repair'],
                    ['name' => 'تنظيف الأجهزة', 'name_en' => 'Device Cleaning'],
                    ['name' => 'تثبيت البرامج', 'name_en' => 'Software Installation']
                ]
            ]
        ];

        foreach ($categories as $categoryData) {
            $children = $categoryData['children'] ?? [];
            unset($categoryData['children']);
            
            $categoryData['company_id'] = $companyId;
            $category = Category::create($categoryData);
            
            foreach ($children as $childData) {
                $childData['company_id'] = $companyId;
                $childData['parent_id'] = $category->id;
                $childData['sort_order'] = 1;
                Category::create($childData);
            }
        }
    }

    private function createBrands($companyId)
    {
        $brands = [
            ['name' => 'Apple', 'name_en' => 'Apple', 'description' => 'منتجات آبل'],
            ['name' => 'Samsung', 'name_en' => 'Samsung', 'description' => 'منتجات سامسونج'],
            ['name' => 'Huawei', 'name_en' => 'Huawei', 'description' => 'منتجات هواوي'],
            ['name' => 'Xiaomi', 'name_en' => 'Xiaomi', 'description' => 'منتجات شاومي'],
            ['name' => 'Dell', 'name_en' => 'Dell', 'description' => 'أجهزة ديل'],
            ['name' => 'HP', 'name_en' => 'HP', 'description' => 'أجهزة إتش بي'],
            ['name' => 'Lenovo', 'name_en' => 'Lenovo', 'description' => 'أجهزة لينوفو'],
            ['name' => 'Asus', 'name_en' => 'Asus', 'description' => 'أجهزة أسوس'],
            ['name' => 'Acer', 'name_en' => 'Acer', 'description' => 'أجهزة أيسر'],
            ['name' => 'MSI', 'name_en' => 'MSI', 'description' => 'أجهزة إم إس آي'],
            ['name' => 'Oppo', 'name_en' => 'Oppo', 'description' => 'هواتف أوبو'],
            ['name' => 'Vivo', 'name_en' => 'Vivo', 'description' => 'هواتف فيفو'],
            ['name' => 'OnePlus', 'name_en' => 'OnePlus', 'description' => 'هواتف ون بلس'],
            ['name' => 'Google', 'name_en' => 'Google', 'description' => 'منتجات جوجل'],
            ['name' => 'Microsoft', 'name_en' => 'Microsoft', 'description' => 'منتجات مايكروسوفت']
        ];

        foreach ($brands as $brandData) {
            $brandData['company_id'] = $companyId;
            Brand::create($brandData);
        }
    }

    private function createUnits($companyId)
    {
        $units = [
            ['name' => 'قطعة', 'name_en' => 'Piece', 'short_name' => 'قطعة', 'short_name_en' => 'pcs'],
            ['name' => 'مجموعة', 'name_en' => 'Set', 'short_name' => 'مجموعة', 'short_name_en' => 'set'],
            ['name' => 'صندوق', 'name_en' => 'Box', 'short_name' => 'صندوق', 'short_name_en' => 'box'],
            ['name' => 'كيلوجرام', 'name_en' => 'Kilogram', 'short_name' => 'كجم', 'short_name_en' => 'kg'],
            ['name' => 'جرام', 'name_en' => 'Gram', 'short_name' => 'جم', 'short_name_en' => 'g'],
            ['name' => 'متر', 'name_en' => 'Meter', 'short_name' => 'م', 'short_name_en' => 'm'],
            ['name' => 'سنتيمتر', 'name_en' => 'Centimeter', 'short_name' => 'سم', 'short_name_en' => 'cm'],
            ['name' => 'لتر', 'name_en' => 'Liter', 'short_name' => 'لتر', 'short_name_en' => 'L'],
            ['name' => 'خدمة', 'name_en' => 'Service', 'short_name' => 'خدمة', 'short_name_en' => 'srv'],
            ['name' => 'ساعة', 'name_en' => 'Hour', 'short_name' => 'ساعة', 'short_name_en' => 'hr']
        ];

        foreach ($units as $unitData) {
            $unitData['company_id'] = $companyId;
            Unit::create($unitData);
        }
    }

    private function createContacts($companyId)
    {
        $contacts = [
            // عملاء
            [
                'type' => 'customer',
                'name' => 'محمد أحمد علي',
                'email' => '<EMAIL>',
                'phone' => '0599123456',
                'mobile' => '0599123456',
                'address' => 'رام الله، فلسطين',
                'city' => 'رام الله',
                'country' => 'فلسطين',
                'credit_limit' => 5000.00,
                'payment_terms' => 30,
                'discount_percentage' => 5.0
            ],
            [
                'type' => 'customer',
                'name' => 'فاطمة حسن محمود',
                'email' => '<EMAIL>',
                'phone' => '0599234567',
                'mobile' => '0599234567',
                'address' => 'نابلس، فلسطين',
                'city' => 'نابلس',
                'country' => 'فلسطين',
                'credit_limit' => 3000.00,
                'payment_terms' => 15
            ],
            [
                'type' => 'customer',
                'name' => 'أحمد محمود خالد',
                'email' => '<EMAIL>',
                'phone' => '0599345678',
                'mobile' => '0599345678',
                'address' => 'الخليل، فلسطين',
                'city' => 'الخليل',
                'country' => 'فلسطين',
                'credit_limit' => 2000.00,
                'payment_terms' => 7
            ],
            
            // موردين
            [
                'type' => 'supplier',
                'name' => 'شركة التقنية المتقدمة',
                'name_en' => 'Advanced Technology Company',
                'email' => '<EMAIL>',
                'phone' => '*********',
                'mobile' => '0599876543',
                'address' => 'رام الله، فلسطين',
                'city' => 'رام الله',
                'country' => 'فلسطين',
                'payment_terms' => 30,
                'contact_person' => 'سامر أحمد',
                'contact_person_phone' => '0599876543'
            ],
            [
                'type' => 'supplier',
                'name' => 'مؤسسة الإلكترونيات الحديثة',
                'name_en' => 'Modern Electronics Est.',
                'email' => '<EMAIL>',
                'phone' => '*********',
                'mobile' => '0599765432',
                'address' => 'نابلس، فلسطين',
                'city' => 'نابلس',
                'country' => 'فلسطين',
                'payment_terms' => 45,
                'contact_person' => 'نادر محمد',
                'contact_person_phone' => '0599765432'
            ],
            
            // عميل ومورد
            [
                'type' => 'both',
                'name' => 'مركز الكمبيوتر الذهبي',
                'name_en' => 'Golden Computer Center',
                'email' => '<EMAIL>',
                'phone' => '*********',
                'mobile' => '0599654321',
                'address' => 'غزة، فلسطين',
                'city' => 'غزة',
                'country' => 'فلسطين',
                'credit_limit' => 10000.00,
                'payment_terms' => 30,
                'contact_person' => 'خالد عبدالله',
                'contact_person_phone' => '0599654321'
            ]
        ];

        foreach ($contacts as $contactData) {
            $contactData['company_id'] = $companyId;
            $contactData['contact_number'] = Contact::generateContactNumber($contactData['type'], $companyId);
            Contact::create($contactData);
        }
    }
}
