@extends('layouts.main')

@section('title', 'تفاصيل طلب الصيانة')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تفاصيل طلب الصيانة</h1>
            <p class="text-gray-600 dark:text-gray-400">متابعة حالة وتفاصيل طلب الصيانة</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('repairs.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة للقائمة
            </a>
            <a href="{{ route('repairs.print', $repair) }}" target="_blank" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                طباعة
            </a>
            @if($repair->canBeEdited())
                <a href="{{ route('repairs.edit', $repair) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    تعديل
                </a>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Repair Info Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h3 class="text-xl font-medium text-gray-900 dark:text-gray-100">{{ $repair->repair_number }}</h3>
                        <p class="text-gray-500 dark:text-gray-400">تاريخ الإنشاء: {{ $repair->created_at->format('Y-m-d H:i') }}</p>
                    </div>
                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full {{ $repair->status_badge['class'] }}">
                        {{ $repair->status_badge['text'] }}
                    </span>
                    </span>
                </div>

                <!-- Customer Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">معلومات العميل</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">الاسم:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ $repair->customer ? $repair->customer->first_name . ' ' . $repair->customer->last_name : 'غير محدد' }}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">الهاتف:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->customer->phone ?? '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">البريد:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->customer->email ?? '-' }}</span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">معلومات الجهاز</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">النوع:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->device_type }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">الماركة:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->device_brand }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">الموديل:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->device_model }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">الرقم التسلسلي:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->device_serial ?? '-' }}</span>
                            </div>
                            @if($repair->device_imei)
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">IMEI:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->device_imei }}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Problem Description -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">وصف المشكلة</h4>
                <p class="text-gray-700 dark:text-gray-300">{{ $repair->problem_description }}</p>

                <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">الأولوية:</span>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2 {{ $repair->priority_badge['class'] }}">
                            {{ $repair->priority_badge['text'] }}
                        </span>
                    </div>
                    <div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">نوع الإصلاح:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">
                            @switch($repair->repair_type)
                                @case('warranty') ضمان @break
                                @case('paid') مدفوع @break
                                @case('internal') داخلي @break
                                @default {{ $repair->repair_type }}
                            @endswitch
                        </span>
                    </div>
                    <div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">الفني المسؤول:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">
                            {{ $repair->technician ? $repair->technician->first_name . ' ' . $repair->technician->last_name : 'غير محدد' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Cost and Timeline -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">التكلفة والجدولة</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">التكلفة المقدرة:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">
                            {{ $repair->estimated_cost ? number_format($repair->estimated_cost, 2) . ' ₪' : 'غير محدد' }}
                        </span>
                    </div>
                    <div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">التكلفة الفعلية:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">
                            {{ $repair->total_cost ? number_format($repair->total_cost, 2) . ' ₪' : 'غير محدد' }}
                        </span>
                    </div>
                    <div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">التسليم المتوقع:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">
                            {{ $repair->estimated_completion ? $repair->estimated_completion->format('Y-m-d') : 'غير محدد' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Diagnosis -->
            @if($repair->diagnosis)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">التشخيص</h4>
                <p class="text-gray-700 dark:text-gray-300">{{ $repair->diagnosis }}</p>
            </div>
            @endif

            <!-- Parts Used -->
            @if($repair->parts->count() > 0)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">قطع الغيار المستخدمة</h4>
                <div class="space-y-3">
                    @foreach($repair->parts as $part)
                    <div class="flex justify-between items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900 dark:text-gray-100">{{ $part->name }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">الكمية: {{ $part->pivot->quantity }}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-gray-900 dark:text-gray-100">{{ number_format($part->pivot->total_price, 2) }} ₪</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ number_format($part->pivot->unit_price, 2) }} ₪ للوحدة</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Notes -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">الملاحظات</h4>
                <div class="space-y-4">
                    @if($repair->customer_notes)
                    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                        <h5 class="font-medium text-blue-900 dark:text-blue-100 mb-2">ملاحظات العميل:</h5>
                        <p class="text-blue-800 dark:text-blue-200">{{ $repair->customer_notes }}</p>
                    </div>
                    @endif

                    @if($repair->technician_notes)
                    <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                        <h5 class="font-medium text-green-900 dark:text-green-100 mb-2">ملاحظات الفني:</h5>
                        <p class="text-green-800 dark:text-green-200">{{ $repair->technician_notes }}</p>
                    </div>
                    @endif

                    @if($repair->internal_notes)
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h5 class="font-medium text-gray-900 dark:text-gray-100 mb-2">ملاحظات داخلية:</h5>
                        <p class="text-gray-700 dark:text-gray-300">{{ $repair->internal_notes }}</p>
                    </div>
                    @endif

                    @if(!$repair->customer_notes && !$repair->technician_notes && !$repair->internal_notes)
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                        </svg>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">لا توجد ملاحظات</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Status History -->
            @if($repair->statusHistory->count() > 0)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">سجل تحديثات الحالة</h4>
                <div class="space-y-4">
                    @foreach($repair->statusHistory as $history)
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900 dark:text-gray-100">
                                تم تغيير الحالة من
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $history->old_status_badge['class'] }}">
                                    {{ $history->old_status_badge['text'] }}
                                </span>
                                إلى
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $history->new_status_badge['class'] }}">
                                    {{ $history->new_status_badge['text'] }}
                                </span>
                            </p>
                            @if($history->notes)
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $history->notes }}</p>
                            @endif
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $history->changed_at->format('Y-m-d H:i') }} - {{ $history->changedBy->name ?? 'النظام' }}
                            </p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Quick Actions -->
            @if($repair->canBeEdited())
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات سريعة</h4>
                <div class="space-y-3">
                    <form action="{{ route('repairs.update-status', $repair) }}" method="POST" class="space-y-3">
                        @csrf
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تحديث الحالة</label>
                            <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="pending" {{ $repair->status == 'pending' ? 'selected' : '' }}>قيد الانتظار</option>
                                <option value="diagnosed" {{ $repair->status == 'diagnosed' ? 'selected' : '' }}>تم التشخيص</option>
                                <option value="in_progress" {{ $repair->status == 'in_progress' ? 'selected' : '' }}>قيد الإصلاح</option>
                                <option value="waiting_parts" {{ $repair->status == 'waiting_parts' ? 'selected' : '' }}>انتظار قطع غيار</option>
                                <option value="completed" {{ $repair->status == 'completed' ? 'selected' : '' }}>مكتملة</option>
                                <option value="delivered" {{ $repair->status == 'delivered' ? 'selected' : '' }}>تم التسليم</option>
                                <option value="cancelled" {{ $repair->status == 'cancelled' ? 'selected' : '' }}>ملغية</option>
                                <option value="on_hold" {{ $repair->status == 'on_hold' ? 'selected' : '' }}>معلقة</option>
                            </select>
                        </div>
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظة</label>
                            <textarea name="notes" id="notes" rows="3" placeholder="ملاحظة حول التحديث..."
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
                        </div>
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg">
                            تحديث الحالة
                        </button>
                    </form>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection