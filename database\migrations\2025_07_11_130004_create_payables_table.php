<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payables', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('supplier_id')->comment('معرف المورد');
            $table->unsignedBigInteger('purchase_id')->nullable()->comment('معرف المشتريات');
            $table->string('invoice_number', 50)->comment('رقم فاتورة المورد');
            $table->string('our_reference', 50)->nullable()->comment('مرجعنا');
            $table->date('invoice_date')->comment('تاريخ الفاتورة');
            $table->date('due_date')->comment('تاريخ الاستحقاق');
            $table->decimal('original_amount', 15, 2)->comment('المبلغ الأصلي');
            $table->decimal('paid_amount', 15, 2)->default(0)->comment('المبلغ المدفوع');
            $table->decimal('remaining_amount', 15, 2)->comment('المبلغ المتبقي');
            $table->enum('status', ['pending', 'partial', 'paid', 'overdue', 'cancelled'])->default('pending')->comment('الحالة');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->json('payment_history')->nullable()->comment('تاريخ المدفوعات');
            $table->timestamps();

            // Indexes
            $table->index(['supplier_id', 'status']);
            $table->index(['due_date', 'status']);
            $table->index(['invoice_number']);
            $table->index(['purchase_id']);
            
            // Foreign Keys
            $table->foreign('supplier_id')->references('id')->on('suppliers')->onDelete('cascade');
            // Note: purchase_id will be added when purchase system is implemented
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payables');
    }
};
