<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Company;
use App\Models\Location;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\Inventory;

class InventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $company = Company::first();
        $location = Location::first();
        
        if (!$company || !$location) {
            return;
        }

        // إنشاء منتجات تجريبية
        $this->createSampleProducts($company->id);
        
        // إنشاء مخزون تجريبي
        $this->createSampleInventory($location->id);
    }

    private function createSampleProducts($companyId)
    {
        // الحصول على التصنيفات والعلامات التجارية والوحدات
        $categories = Category::where('company_id', $companyId)->get();
        $brands = Brand::where('company_id', $companyId)->get();
        $units = Unit::where('company_id', $companyId)->get();

        if ($categories->isEmpty() || $brands->isEmpty() || $units->isEmpty()) {
            return;
        }

        $products = [
            // أجهزة الحاسوب
            [
                'name' => 'لابتوب Dell Inspiron 15',
                'name_en' => 'Dell Inspiron 15 Laptop',
                'description' => 'لابتوب Dell Inspiron 15 بمعالج Intel Core i5',
                'sku' => 'LAP-DELL-001',
                'barcode' => '1234567890123',
                'category_id' => $categories->where('name', 'أجهزة محمولة')->first()?->id ?? $categories->first()->id,
                'brand_id' => $brands->where('name', 'Dell')->first()?->id ?? $brands->first()->id,
                'unit_id' => $units->where('short_name', 'قطعة')->first()?->id ?? $units->first()->id,
                'purchase_price' => 2500.00,
                'selling_price' => 3200.00,
                'profit_margin' => 28.00,
                'min_stock_level' => 5,
                'max_stock_level' => 20,
                'reorder_level' => 8,
                'is_active' => true
            ],
            [
                'name' => 'جهاز كمبيوتر مكتبي HP',
                'name_en' => 'HP Desktop Computer',
                'description' => 'جهاز كمبيوتر مكتبي HP بمعالج Intel Core i7',
                'sku' => 'DES-HP-001',
                'barcode' => '1234567890124',
                'category_id' => $categories->where('name', 'أجهزة مكتبية')->first()?->id ?? $categories->first()->id,
                'brand_id' => $brands->where('name', 'HP')->first()?->id ?? $brands->first()->id,
                'unit_id' => $units->where('short_name', 'قطعة')->first()?->id ?? $units->first()->id,
                'purchase_price' => 1800.00,
                'selling_price' => 2300.00,
                'profit_margin' => 27.78,
                'min_stock_level' => 3,
                'max_stock_level' => 15,
                'reorder_level' => 5,
                'is_active' => true
            ],
            
            // الهواتف الذكية
            [
                'name' => 'هاتف Samsung Galaxy S23',
                'name_en' => 'Samsung Galaxy S23',
                'description' => 'هاتف Samsung Galaxy S23 بذاكرة 256GB',
                'sku' => 'PHN-SAM-001',
                'barcode' => '1234567890125',
                'category_id' => $categories->where('name', 'هواتف أندرويد')->first()?->id ?? $categories->first()->id,
                'brand_id' => $brands->where('name', 'Samsung')->first()?->id ?? $brands->first()->id,
                'unit_id' => $units->where('short_name', 'قطعة')->first()?->id ?? $units->first()->id,
                'purchase_price' => 3500.00,
                'selling_price' => 4200.00,
                'profit_margin' => 20.00,
                'min_stock_level' => 10,
                'max_stock_level' => 50,
                'reorder_level' => 15,
                'is_active' => true
            ],
            [
                'name' => 'هاتف iPhone 14',
                'name_en' => 'iPhone 14',
                'description' => 'هاتف iPhone 14 بذاكرة 128GB',
                'sku' => 'PHN-APL-001',
                'barcode' => '1234567890126',
                'category_id' => $categories->where('name', 'هواتف آيفون')->first()?->id ?? $categories->first()->id,
                'brand_id' => $brands->where('name', 'Apple')->first()?->id ?? $brands->first()->id,
                'unit_id' => $units->where('short_name', 'قطعة')->first()?->id ?? $units->first()->id,
                'purchase_price' => 4000.00,
                'selling_price' => 4800.00,
                'profit_margin' => 20.00,
                'min_stock_level' => 8,
                'max_stock_level' => 30,
                'reorder_level' => 12,
                'is_active' => true
            ],
            
            // قطع الغيار
            [
                'name' => 'شاشة لابتوب 15.6 بوصة',
                'name_en' => '15.6 inch Laptop Screen',
                'description' => 'شاشة لابتوب LED 15.6 بوصة دقة Full HD',
                'sku' => 'SCR-LAP-001',
                'barcode' => '1234567890127',
                'category_id' => $categories->where('name', 'شاشات')->first()?->id ?? $categories->first()->id,
                'brand_id' => $brands->first()->id,
                'unit_id' => $units->where('short_name', 'قطعة')->first()?->id ?? $units->first()->id,
                'purchase_price' => 180.00,
                'selling_price' => 250.00,
                'profit_margin' => 38.89,
                'min_stock_level' => 15,
                'max_stock_level' => 50,
                'reorder_level' => 20,
                'is_active' => true
            ],
            [
                'name' => 'بطارية لابتوب Dell',
                'name_en' => 'Dell Laptop Battery',
                'description' => 'بطارية لابتوب Dell أصلية 6 خلايا',
                'sku' => 'BAT-DELL-001',
                'barcode' => '1234567890128',
                'category_id' => $categories->where('name', 'بطاريات')->first()?->id ?? $categories->first()->id,
                'brand_id' => $brands->where('name', 'Dell')->first()?->id ?? $brands->first()->id,
                'unit_id' => $units->where('short_name', 'قطعة')->first()?->id ?? $units->first()->id,
                'purchase_price' => 120.00,
                'selling_price' => 180.00,
                'profit_margin' => 50.00,
                'min_stock_level' => 20,
                'max_stock_level' => 60,
                'reorder_level' => 25,
                'is_active' => true
            ],
            
            // الإكسسوارات
            [
                'name' => 'كابل USB-C',
                'name_en' => 'USB-C Cable',
                'description' => 'كابل USB-C طول 1 متر',
                'sku' => 'CAB-USBC-001',
                'barcode' => '1234567890129',
                'category_id' => $categories->where('name', 'كابلات')->first()?->id ?? $categories->first()->id,
                'brand_id' => $brands->first()->id,
                'unit_id' => $units->where('short_name', 'قطعة')->first()?->id ?? $units->first()->id,
                'purchase_price' => 15.00,
                'selling_price' => 25.00,
                'profit_margin' => 66.67,
                'min_stock_level' => 50,
                'max_stock_level' => 200,
                'reorder_level' => 75,
                'is_active' => true
            ],
            [
                'name' => 'شاحن لابتوب عام',
                'name_en' => 'Universal Laptop Charger',
                'description' => 'شاحن لابتوب عام 90W مع محولات متعددة',
                'sku' => 'CHR-LAP-001',
                'barcode' => '1234567890130',
                'category_id' => $categories->where('name', 'شواحن')->first()?->id ?? $categories->first()->id,
                'brand_id' => $brands->first()->id,
                'unit_id' => $units->where('short_name', 'قطعة')->first()?->id ?? $units->first()->id,
                'purchase_price' => 45.00,
                'selling_price' => 70.00,
                'profit_margin' => 55.56,
                'min_stock_level' => 25,
                'max_stock_level' => 80,
                'reorder_level' => 35,
                'is_active' => true
            ]
        ];

        foreach ($products as $productData) {
            $productData['company_id'] = $companyId;
            Product::create($productData);
        }
    }

    private function createSampleInventory($locationId)
    {
        $products = Product::all();
        
        foreach ($products as $product) {
            // إنشاء مخزون عشوائي لكل منتج
            $quantity = rand($product->min_stock_level - 5, $product->max_stock_level + 10);
            $quantity = max(0, $quantity); // التأكد من أن الكمية لا تقل عن صفر
            
            Inventory::create([
                'product_id' => $product->id,
                'location_id' => $locationId,
                'quantity' => $quantity,
                'cost_price' => $product->purchase_price,
                'selling_price' => $product->selling_price,
                'expiry_date' => null, // معظم المنتجات الإلكترونية لا تنتهي صلاحيتها
                'batch_number' => 'BATCH-' . date('Ymd') . '-' . str_pad($product->id, 3, '0', STR_PAD_LEFT),
                'notes' => 'مخزون ابتدائي'
            ]);
        }
    }
}
