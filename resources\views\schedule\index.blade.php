@extends('layouts.main')

@section('title', 'جدولة المواعيد والفنيين')

@section('content')
<div class="space-y-6" x-data="scheduleManager()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">جدولة المواعيد والفنيين</h1>
            <p class="text-gray-600 dark:text-gray-400">إدارة مواعيد الصيانة وتوزيع المهام على الفنيين</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="viewType = 'calendar'" :class="viewType === 'calendar' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'" class="px-4 py-2 rounded-lg">
                عرض التقويم
            </button>
            <button @click="viewType = 'list'" :class="viewType === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'" class="px-4 py-2 rounded-lg">
                عرض القائمة
            </button>
            <button @click="addAppointment()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                موعد جديد
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مواعيد اليوم</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.todayAppointments">12</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">الفنيين المتاحين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.availableTechnicians">5</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مواعيد معلقة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.pendingAppointments">8</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط وقت الإنجاز</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.avgCompletionTime">2.5 ساعة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التاريخ</label>
                <input type="date" x-model="filters.date" @change="filterAppointments()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفني</label>
                <select x-model="filters.technician" @change="filterAppointments()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الفنيين</option>
                    <template x-for="tech in technicians" :key="tech.id">
                        <option :value="tech.id" x-text="tech.name"></option>
                    </template>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                <select x-model="filters.status" @change="filterAppointments()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="scheduled">مجدولة</option>
                    <option value="in_progress">قيد التنفيذ</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع المهمة</label>
                <select x-model="filters.taskType" @change="filterAppointments()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع المهام</option>
                    <option value="repair">إصلاح</option>
                    <option value="diagnosis">تشخيص</option>
                    <option value="maintenance">صيانة وقائية</option>
                    <option value="delivery">تسليم</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الأولوية</label>
                <select x-model="filters.priority" @change="filterAppointments()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الأولويات</option>
                    <option value="low">منخفضة</option>
                    <option value="medium">متوسطة</option>
                    <option value="high">عالية</option>
                    <option value="urgent">عاجلة</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Calendar View -->
    <div x-show="viewType === 'calendar'" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">التقويم الأسبوعي</h3>
                <div class="flex space-x-2 space-x-reverse">
                    <button @click="previousWeek()" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <span class="text-lg font-medium text-gray-900 dark:text-gray-100" x-text="currentWeekText"></span>
                    <button @click="nextWeek()" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Calendar Grid -->
        <div class="overflow-x-auto">
            <div class="min-w-full">
                <!-- Time Header -->
                <div class="grid grid-cols-8 border-b border-gray-200 dark:border-gray-700">
                    <div class="p-4 text-sm font-medium text-gray-500 dark:text-gray-400">الوقت</div>
                    <template x-for="day in weekDays" :key="day.date">
                        <div class="p-4 text-center">
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="day.name"></div>
                            <div class="text-xs text-gray-500 dark:text-gray-400" x-text="day.date"></div>
                        </div>
                    </template>
                </div>
                
                <!-- Time Slots -->
                <template x-for="hour in workingHours" :key="hour">
                    <div class="grid grid-cols-8 border-b border-gray-100 dark:border-gray-700">
                        <div class="p-2 text-sm text-gray-500 dark:text-gray-400 border-l border-gray-200 dark:border-gray-700" x-text="hour + ':00'"></div>
                        <template x-for="day in weekDays" :key="day.date + hour">
                            <div class="p-1 border-l border-gray-100 dark:border-gray-700 min-h-[60px] relative">
                                <template x-for="appointment in getAppointmentsForSlot(day.date, hour)" :key="appointment.id">
                                    <div @click="viewAppointment(appointment.id)" 
                                         class="absolute inset-1 rounded text-xs p-1 cursor-pointer"
                                         :class="{
                                             'bg-blue-100 text-blue-800 border border-blue-200': appointment.priority === 'low',
                                             'bg-yellow-100 text-yellow-800 border border-yellow-200': appointment.priority === 'medium',
                                             'bg-orange-100 text-orange-800 border border-orange-200': appointment.priority === 'high',
                                             'bg-red-100 text-red-800 border border-red-200': appointment.priority === 'urgent'
                                         }">
                                        <div class="font-medium truncate" x-text="appointment.customer_name"></div>
                                        <div class="truncate" x-text="appointment.technician_name"></div>
                                        <div class="truncate" x-text="appointment.task_type"></div>
                                    </div>
                                </template>
                            </div>
                        </template>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- List View -->
    <div x-show="viewType === 'list'" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">قائمة المواعيد</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ والوقت</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفني</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">نوع المهمة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الأولوية</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="appointment in filteredAppointments" :key="appointment.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="appointment.date"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="appointment.start_time + ' - ' + appointment.end_time"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-gray-100" x-text="appointment.customer_name"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="appointment.repair_ticket"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="appointment.technician_name"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="getTaskTypeText(appointment.task_type)"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': appointment.priority === 'low',
                                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': appointment.priority === 'medium',
                                          'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200': appointment.priority === 'high',
                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': appointment.priority === 'urgent'
                                      }"
                                      x-text="getPriorityText(appointment.priority)">
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': appointment.status === 'scheduled',
                                          'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200': appointment.status === 'in_progress',
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': appointment.status === 'completed',
                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': appointment.status === 'cancelled'
                                      }"
                                      x-text="getStatusText(appointment.status)">
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button @click="viewAppointment(appointment.id)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400">عرض</button>
                                    <button @click="editAppointment(appointment.id)" class="text-green-600 hover:text-green-900 dark:text-green-400">تعديل</button>
                                    <button @click="rescheduleAppointment(appointment.id)" class="text-purple-600 hover:text-purple-900 dark:text-purple-400">إعادة جدولة</button>
                                    <button @click="cancelAppointment(appointment.id)" class="text-red-600 hover:text-red-900 dark:text-red-400">إلغاء</button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
function scheduleManager() {
    return {
        viewType: 'calendar',
        currentWeek: new Date(),
        appointments: [
            {
                id: 1,
                date: '2024-07-09',
                start_time: '09:00',
                end_time: '10:00',
                customer_name: 'أحمد محمد علي',
                technician_name: 'محمد الفني',
                technician_id: 1,
                repair_ticket: 'REP-001',
                task_type: 'repair',
                priority: 'high',
                status: 'scheduled',
                notes: 'إصلاح شاشة مكسورة'
            },
            {
                id: 2,
                date: '2024-07-09',
                start_time: '10:30',
                end_time: '11:30',
                customer_name: 'سارة أحمد خالد',
                technician_name: 'علي الفني',
                technician_id: 2,
                repair_ticket: 'REP-002',
                task_type: 'diagnosis',
                priority: 'medium',
                status: 'in_progress',
                notes: 'تشخيص مشكلة البطارية'
            }
        ],
        technicians: [
            { id: 1, name: 'محمد الفني', available: true },
            { id: 2, name: 'علي الفني', available: true },
            { id: 3, name: 'أحمد الفني', available: false }
        ],
        filteredAppointments: [],
        filters: {
            date: new Date().toISOString().split('T')[0],
            technician: '',
            status: '',
            taskType: '',
            priority: ''
        },
        stats: {
            todayAppointments: 12,
            availableTechnicians: 5,
            pendingAppointments: 8,
            avgCompletionTime: '2.5 ساعة'
        },
        workingHours: [8, 9, 10, 11, 12, 13, 14, 15, 16, 17],

        init() {
            this.filteredAppointments = [...this.appointments];
        },

        get currentWeekText() {
            const start = new Date(this.currentWeek);
            start.setDate(start.getDate() - start.getDay());
            const end = new Date(start);
            end.setDate(start.getDate() + 6);
            return `${start.toLocaleDateString('ar-EG')} - ${end.toLocaleDateString('ar-EG')}`;
        },

        get weekDays() {
            const days = [];
            const start = new Date(this.currentWeek);
            start.setDate(start.getDate() - start.getDay());
            
            for (let i = 0; i < 7; i++) {
                const day = new Date(start);
                day.setDate(start.getDate() + i);
                days.push({
                    name: day.toLocaleDateString('ar-EG', { weekday: 'short' }),
                    date: day.toISOString().split('T')[0]
                });
            }
            return days;
        },

        filterAppointments() {
            this.filteredAppointments = this.appointments.filter(appointment => {
                const matchesDate = !this.filters.date || appointment.date === this.filters.date;
                const matchesTechnician = !this.filters.technician || appointment.technician_id === parseInt(this.filters.technician);
                const matchesStatus = !this.filters.status || appointment.status === this.filters.status;
                const matchesTaskType = !this.filters.taskType || appointment.task_type === this.filters.taskType;
                const matchesPriority = !this.filters.priority || appointment.priority === this.filters.priority;

                return matchesDate && matchesTechnician && matchesStatus && matchesTaskType && matchesPriority;
            });
        },

        getAppointmentsForSlot(date, hour) {
            return this.filteredAppointments.filter(appointment => {
                if (appointment.date !== date) return false;
                const startHour = parseInt(appointment.start_time.split(':')[0]);
                return startHour === hour;
            });
        },

        previousWeek() {
            this.currentWeek.setDate(this.currentWeek.getDate() - 7);
            this.currentWeek = new Date(this.currentWeek);
        },

        nextWeek() {
            this.currentWeek.setDate(this.currentWeek.getDate() + 7);
            this.currentWeek = new Date(this.currentWeek);
        },

        getTaskTypeText(type) {
            const types = {
                'repair': 'إصلاح',
                'diagnosis': 'تشخيص',
                'maintenance': 'صيانة وقائية',
                'delivery': 'تسليم'
            };
            return types[type] || type;
        },

        getPriorityText(priority) {
            const priorities = {
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية',
                'urgent': 'عاجلة'
            };
            return priorities[priority] || priority;
        },

        getStatusText(status) {
            const statuses = {
                'scheduled': 'مجدولة',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتملة',
                'cancelled': 'ملغية'
            };
            return statuses[status] || status;
        },

        addAppointment() {
            alert('سيتم إضافة نافذة إنشاء موعد جديد');
        },

        viewAppointment(id) {
            alert(`عرض تفاصيل الموعد ${id}`);
        },

        editAppointment(id) {
            alert(`تعديل الموعد ${id}`);
        },

        rescheduleAppointment(id) {
            alert(`إعادة جدولة الموعد ${id}`);
        },

        cancelAppointment(id) {
            if (confirm('هل أنت متأكد من إلغاء هذا الموعد؟')) {
                const appointment = this.appointments.find(a => a.id === id);
                if (appointment) {
                    appointment.status = 'cancelled';
                    this.filterAppointments();
                }
            }
        }
    }
}
</script>
@endpush
@endsection
