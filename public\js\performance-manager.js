/**
 * مدير الأداء وتحسين سرعة التحميل
 * Performance Manager & Loading Speed Optimization
 */

class PerformanceManager {
    constructor() {
        this.observers = new Map();
        this.lazyElements = new Set();
        this.performanceMetrics = {};
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupImageOptimization();
        this.setupVirtualScrolling();
        this.setupPerformanceMonitoring();
        this.setupMemoryOptimization();
        this.setupCacheManagement();
    }

    // تحسين التحميل التدريجي
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const lazyObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        this.loadElement(element);
                        lazyObserver.unobserve(element);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.1
            });

            // مراقبة العناصر التي تحتاج تحميل تدريجي فقط إذا كانت موجودة
            const lazyElements = document.querySelectorAll('.lazy-load:not(.loaded)');
            if (lazyElements.length > 0) {
                lazyElements.forEach(el => {
                    lazyObserver.observe(el);
                    this.lazyElements.add(el);
                });
            }

            this.observers.set('lazy', lazyObserver);
        }
    }

    // تحميل العنصر
    loadElement(element) {
        // إضافة كلاس التحميل
        element.classList.add('loading');
        
        // محاكاة تحميل المحتوى
        requestAnimationFrame(() => {
            element.classList.remove('lazy-load');
            element.classList.add('loaded');
            element.classList.remove('loading');
        });
    }

    // تحسين الصور
    setupImageOptimization() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.optimizeImage(img);
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });

            this.observers.set('image', imageObserver);
        }
    }

    // تحسين الصورة
    optimizeImage(img) {
        const placeholder = img.previousElementSibling;
        
        img.onload = () => {
            img.classList.add('loaded');
            if (placeholder && placeholder.classList.contains('image-placeholder')) {
                placeholder.remove();
            }
        };

        img.onerror = () => {
            img.src = '/images/placeholder.svg';
        };

        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        }
    }

    // التمرير الافتراضي للجداول الكبيرة
    setupVirtualScrolling() {
        // تطبيق التمرير الافتراضي فقط على الجداول الكبيرة (أكثر من 100 عنصر)
        document.querySelectorAll('.virtual-scroll').forEach(container => {
            const itemCount = container.children.length;
            if (itemCount > 100) {
                this.initVirtualScroll(container);
            }
        });
    }

    initVirtualScroll(container) {
        const items = Array.from(container.children);
        const itemHeight = 50; // ارتفاع العنصر الواحد
        const containerHeight = container.clientHeight;
        const visibleItems = Math.ceil(containerHeight / itemHeight) + 2;
        
        let scrollTop = 0;
        let startIndex = 0;

        const updateVisibleItems = () => {
            const newStartIndex = Math.floor(scrollTop / itemHeight);
            
            if (newStartIndex !== startIndex) {
                startIndex = newStartIndex;
                const endIndex = Math.min(startIndex + visibleItems, items.length);
                
                // إخفاء جميع العناصر
                items.forEach(item => item.style.display = 'none');
                
                // إظهار العناصر المرئية فقط
                for (let i = startIndex; i < endIndex; i++) {
                    if (items[i]) {
                        items[i].style.display = 'block';
                        items[i].style.transform = `translateY(${i * itemHeight}px)`;
                    }
                }
            }
        };

        container.addEventListener('scroll', () => {
            scrollTop = container.scrollTop;
            requestAnimationFrame(updateVisibleItems);
        });

        // التحديث الأولي
        updateVisibleItems();
    }

    // مراقبة الأداء
    setupPerformanceMonitoring() {
        if ('PerformanceObserver' in window) {
            // مراقبة أوقات التحميل
            const perfObserver = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    this.recordMetric(entry.name, entry.duration);
                });
            });

            perfObserver.observe({ entryTypes: ['measure', 'navigation'] });
            this.observers.set('performance', perfObserver);
        }

        // قياس أوقات التفاعل
        this.measureInteractionTimes();
    }

    // قياس أوقات التفاعل
    measureInteractionTimes() {
        document.addEventListener('click', (e) => {
            const startTime = performance.now();
            
            requestAnimationFrame(() => {
                const endTime = performance.now();
                this.recordMetric('click-response', endTime - startTime);
            });
        });
    }

    // تسجيل المقاييس
    recordMetric(name, value) {
        if (!this.performanceMetrics[name]) {
            this.performanceMetrics[name] = [];
        }
        
        this.performanceMetrics[name].push(value);
        
        // الاحتفاظ بآخر 100 قياس فقط
        if (this.performanceMetrics[name].length > 100) {
            this.performanceMetrics[name].shift();
        }
    }

    // تحسين الذاكرة
    setupMemoryOptimization() {
        // تنظيف الذاكرة كل 5 دقائق
        setInterval(() => {
            this.cleanupMemory();
        }, 5 * 60 * 1000);

        // تنظيف عند إخفاء الصفحة
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.cleanupMemory();
            }
        });
    }

    // تنظيف الذاكرة
    cleanupMemory() {
        // إزالة العناصر غير المستخدمة
        this.lazyElements.forEach(element => {
            if (!document.contains(element)) {
                this.lazyElements.delete(element);
            }
        });

        // تنظيف المقاييس القديمة
        Object.keys(this.performanceMetrics).forEach(key => {
            if (this.performanceMetrics[key].length > 50) {
                this.performanceMetrics[key] = this.performanceMetrics[key].slice(-50);
            }
        });

        // إجبار جمع القمامة إذا كان متاحاً
        if (window.gc) {
            window.gc();
        }
    }

    // إدارة التخزين المؤقت
    setupCacheManagement() {
        if ('caches' in window) {
            this.manageCaches();
        }
    }

    // إدارة التخزين المؤقت
    async manageCaches() {
        try {
            const cacheNames = await caches.keys();
            const oldCaches = cacheNames.filter(name => 
                name.includes('old') || name.includes('v1')
            );

            // حذف التخزين المؤقت القديم
            await Promise.all(
                oldCaches.map(cacheName => caches.delete(cacheName))
            );
        } catch (error) {
            console.warn('Cache management failed:', error);
        }
    }

    // تحسين الخطوط
    preloadFonts() {
        const fonts = [
            '/fonts/cairo.woff2',
            '/fonts/tajawal.woff2'
        ];

        fonts.forEach(font => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = font;
            link.as = 'font';
            link.type = 'font/woff2';
            link.crossOrigin = 'anonymous';
            document.head.appendChild(link);
        });
    }

    // تحسين الموارد الحرجة
    preloadCriticalResources() {
        const criticalResources = [
            '/css/modern-theme.css',
            '/css/performance-optimizations.css',
            '/js/advanced-interactions.js'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            
            if (resource.endsWith('.css')) {
                link.as = 'style';
            } else if (resource.endsWith('.js')) {
                link.as = 'script';
            }
            
            document.head.appendChild(link);
        });
    }

    // تحسين التمرير
    optimizeScrolling() {
        let ticking = false;

        const updateScrollPosition = () => {
            // تحديث موقع التمرير
            const scrollTop = window.pageYOffset;
            document.documentElement.style.setProperty('--scroll-y', scrollTop + 'px');
            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        }, { passive: true });
    }

    // تحسين الأحداث
    optimizeEvents() {
        // استخدام التفويض للأحداث
        document.addEventListener('click', this.handleClick.bind(this), { passive: true });
        document.addEventListener('input', this.handleInput.bind(this), { passive: true });
        document.addEventListener('change', this.handleChange.bind(this), { passive: true });
    }

    handleClick(e) {
        const target = e.target.closest('[data-action]');
        if (target) {
            const action = target.dataset.action;
            this.executeAction(action, target);
        }
    }

    handleInput(e) {
        if (e.target.matches('[data-debounce]')) {
            this.debounceInput(e.target);
        }
    }

    handleChange(e) {
        if (e.target.matches('select[data-filter]')) {
            this.handleFilterChange(e.target);
        }
    }

    // تنفيذ الإجراءات
    executeAction(action, element) {
        const actions = {
            'toggle-theme': () => this.toggleTheme(),
            'refresh-data': () => this.refreshData(element),
            'export-data': () => this.exportData(element),
            'load-more': () => this.loadMore(element)
        };

        if (actions[action]) {
            actions[action]();
        }
    }

    // تبديل المظهر
    toggleTheme() {
        const html = document.documentElement;
        const currentTheme = html.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        html.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
    }

    // تحديث البيانات
    refreshData(element) {
        element.classList.add('loading');
        
        // محاكاة تحديث البيانات
        setTimeout(() => {
            element.classList.remove('loading');
            if (window.showNotification) {
                window.showNotification('تم تحديث البيانات بنجاح', 'success');
            }
        }, 1000);
    }

    // تصدير البيانات
    exportData(element) {
        element.classList.add('loading');
        
        // محاكاة تصدير البيانات
        setTimeout(() => {
            element.classList.remove('loading');
            if (window.showNotification) {
                window.showNotification('تم تصدير البيانات بنجاح', 'success');
            }
        }, 2000);
    }

    // تحميل المزيد
    loadMore(element) {
        element.classList.add('loading');
        
        // محاكاة تحميل المزيد من البيانات
        setTimeout(() => {
            element.classList.remove('loading');
            // إضافة عناصر جديدة هنا
        }, 1500);
    }

    // تأخير الإدخال
    debounceInput(input) {
        clearTimeout(input.debounceTimer);
        input.debounceTimer = setTimeout(() => {
            this.processInput(input);
        }, 300);
    }

    // معالجة الإدخال
    processInput(input) {
        const value = input.value.trim();
        if (value.length >= 2) {
            // تنفيذ البحث أو الفلترة
            this.performSearch(value);
        }
    }

    // تنفيذ البحث
    performSearch(query) {
        // محاكاة البحث
        console.log('Searching for:', query);
    }

    // معالجة تغيير الفلتر
    handleFilterChange(select) {
        const value = select.value;
        const filterType = select.dataset.filter;
        
        // تطبيق الفلتر
        this.applyFilter(filterType, value);
    }

    // تطبيق الفلتر
    applyFilter(type, value) {
        // محاكاة تطبيق الفلتر
        console.log('Applying filter:', type, value);
    }

    // الحصول على مقاييس الأداء
    getPerformanceMetrics() {
        return this.performanceMetrics;
    }

    // تنظيف الموارد
    cleanup() {
        // إيقاف جميع المراقبين
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        
        // تنظيف المتغيرات
        this.observers.clear();
        this.lazyElements.clear();
        this.performanceMetrics = {};
    }
}

// تهيئة مدير الأداء عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.performanceManager = new PerformanceManager();
    
    // تحميل الخطوط والموارد الحرجة
    window.performanceManager.preloadFonts();
    window.performanceManager.preloadCriticalResources();
    window.performanceManager.optimizeScrolling();
    window.performanceManager.optimizeEvents();
});

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    if (window.performanceManager) {
        window.performanceManager.cleanup();
    }
});

// تصدير للاستخدام العام
window.PerformanceManager = PerformanceManager;
