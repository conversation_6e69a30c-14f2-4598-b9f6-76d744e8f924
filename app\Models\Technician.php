<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Technician extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'employee_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'mobile',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'date_of_birth',
        'hire_date',
        'department',
        'position',
        'salary',
        'hourly_rate',
        'specializations',
        'certifications',
        'experience_years',
        'skill_level',
        'status',
        'availability',
        'working_hours',
        'emergency_contact_name',
        'emergency_contact_phone',
        'notes',
        'profile_image',
        'is_active'
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'hire_date' => 'date',
        'salary' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'specializations' => 'array',
        'certifications' => 'array',
        'working_hours' => 'array',
        'is_active' => 'boolean'
    ];

    protected $dates = [
        'date_of_birth',
        'hire_date',
        'deleted_at'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function repairs()
    {
        return $this->hasMany(Repair::class);
    }

    public function schedules()
    {
        return $this->hasMany(Schedule::class);
    }

    public function maintenances()
    {
        return $this->hasMany(Maintenance::class);
    }

    public function workOrders()
    {
        return $this->hasMany(WorkOrder::class);
    }

    public function timeEntries()
    {
        return $this->hasMany(TimeEntry::class);
    }

    public function performanceReviews()
    {
        return $this->hasMany(PerformanceReview::class);
    }

    public function expenses()
    {
        return $this->morphMany(Expense::class, 'expensable');
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'related');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'technician_categories');
    }

    public function sales()
    {
        return $this->hasMany(Sale::class, 'technician_id');
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getAvatarAttribute()
    {
        if ($this->profile_image) {
            return asset('storage/' . $this->profile_image);
        }
        
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->full_name) . '&background=3B82F6&color=fff&size=100';
    }

    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'active' => ['class' => 'badge-success', 'text' => 'نشط'],
            'inactive' => ['class' => 'badge-secondary', 'text' => 'غير نشط'],
            'on_leave' => ['class' => 'badge-warning', 'text' => 'في إجازة'],
            'terminated' => ['class' => 'badge-danger', 'text' => 'منتهي الخدمة']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getAvailabilityBadgeAttribute()
    {
        $availabilities = [
            'available' => ['class' => 'badge-success', 'text' => 'متاح'],
            'busy' => ['class' => 'badge-warning', 'text' => 'مشغول'],
            'unavailable' => ['class' => 'badge-danger', 'text' => 'غير متاح'],
            'on_break' => ['class' => 'badge-info', 'text' => 'في استراحة']
        ];

        return $availabilities[$this->availability] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    public function scopeAvailable($query)
    {
        return $query->where('availability', 'available');
    }

    public function scopeBySpecialization($query, $specialization)
    {
        return $query->whereJsonContains('specializations', $specialization);
    }

    public function scopeBySkillLevel($query, $level)
    {
        return $query->where('skill_level', $level);
    }

    // Methods
    public function isAvailable()
    {
        return $this->is_active && $this->status === 'active' && $this->availability === 'available';
    }

    public function hasSpecialization($specialization)
    {
        return in_array($specialization, $this->specializations ?? []);
    }

    public function hasCertification($certification)
    {
        return in_array($certification, $this->certifications ?? []);
    }

    public function getWorkingHoursForDay($day)
    {
        $workingHours = $this->working_hours ?? [];
        return $workingHours[$day] ?? null;
    }

    public function isWorkingToday()
    {
        $today = strtolower(now()->format('l'));
        $todayHours = $this->getWorkingHoursForDay($today);
        
        return $todayHours && $todayHours['start'] && $todayHours['end'];
    }

    public function getCurrentWorkload()
    {
        return $this->repairs()
            ->whereIn('status', ['pending', 'in_progress'])
            ->count();
    }

    public function getAverageRating()
    {
        return $this->performanceReviews()
            ->where('created_at', '>=', now()->subMonths(6))
            ->avg('rating') ?? 0;
    }

    public function getTotalRepairsThisMonth()
    {
        return $this->repairs()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
    }

    public function getCompletedRepairsThisMonth()
    {
        return $this->repairs()
            ->where('status', 'completed')
            ->whereMonth('updated_at', now()->month)
            ->whereYear('updated_at', now()->year)
            ->count();
    }

    public function getAverageRepairTime()
    {
        $completedRepairs = $this->repairs()
            ->where('status', 'completed')
            ->whereNotNull('completed_at')
            ->get();

        if ($completedRepairs->isEmpty()) {
            return 0;
        }

        $totalHours = $completedRepairs->sum(function ($repair) {
            return $repair->created_at->diffInHours($repair->completed_at);
        });

        return round($totalHours / $completedRepairs->count(), 2);
    }

    public function updateAvailability($availability)
    {
        $this->update(['availability' => $availability]);
    }

    public function assignToRepair($repairId)
    {
        $repair = Repair::find($repairId);
        if ($repair && $this->isAvailable()) {
            $repair->update(['technician_id' => $this->id]);
            $this->updateAvailability('busy');
            return true;
        }
        return false;
    }

    public function completeRepair($repairId)
    {
        $repair = $this->repairs()->find($repairId);
        if ($repair) {
            $repair->update([
                'status' => 'completed',
                'completed_at' => now()
            ]);
            
            // Check if technician has other pending repairs
            $pendingRepairs = $this->repairs()
                ->whereIn('status', ['pending', 'in_progress'])
                ->count();
                
            if ($pendingRepairs === 0) {
                $this->updateAvailability('available');
            }
            
            return true;
        }
        return false;
    }

    public function getWorkloadPercentage()
    {
        $currentRepairs = $this->repairs()
            ->whereIn('status', ['pending', 'in_progress', 'diagnosed'])
            ->count();

        // Assume maximum capacity is 10 repairs per technician
        $maxCapacity = 10;

        return min(($currentRepairs / $maxCapacity) * 100, 100);
    }

    public function getEstimatedCompletionTime()
    {
        $pendingRepairs = $this->repairs()
            ->whereIn('status', ['pending', 'in_progress', 'diagnosed'])
            ->get();

        if ($pendingRepairs->isEmpty()) {
            return 'متاح';
        }

        // Calculate average completion time based on repair complexity
        $totalHours = 0;
        foreach ($pendingRepairs as $repair) {
            // Estimate hours based on device type and priority
            $baseHours = match($repair->device_type) {
                'Mobile Phone', 'Smartphone' => 2,
                'Tablet' => 3,
                'Laptop' => 4,
                'Desktop Computer' => 5,
                default => 3
            };

            $priorityMultiplier = match($repair->priority) {
                'urgent' => 0.5,
                'high' => 0.7,
                'normal' => 1.0,
                'low' => 1.5,
                default => 1.0
            };

            $totalHours += $baseHours * $priorityMultiplier;
        }

        if ($totalHours <= 8) {
            return 'اليوم';
        } elseif ($totalHours <= 24) {
            return 'غداً';
        } elseif ($totalHours <= 72) {
            return '3 أيام';
        } else {
            return 'أسبوع';
        }
    }
}
