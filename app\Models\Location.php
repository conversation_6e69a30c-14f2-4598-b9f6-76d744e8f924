<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Location extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'name',
        'name_en',
        'type', // store, warehouse, service_center
        'code',
        'email',
        'phone',
        'address',
        'city',
        'country',
        'postal_code',
        'manager_id',
        'is_active',
        'is_default',
        'invoice_settings',
        'pos_settings',
        'inventory_settings',
        // POS specific fields
        'description',
        'state',
        'manager_name',
        'is_main_branch',
        'business_hours',
        'services_offered',
        'receipt_header',
        'receipt_footer',
        'tax_number',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'is_main_branch' => 'boolean',
        'invoice_settings' => 'array',
        'pos_settings' => 'array',
        'inventory_settings' => 'array',
        'business_hours' => 'array',
        'services_offered' => 'array',
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع المدير
     */
    public function manager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * العلاقة مع المستخدمين
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_locations');
    }

    /**
     * العلاقة مع المخزون
     */
    public function inventories()
    {
        return $this->hasMany(Inventory::class);
    }

    /**
     * العلاقة مع المبيعات
     */
    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * العلاقة مع المشتريات
     */
    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    /**
     * العلاقة مع المصروفات
     */
    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * الحصول على إعدادات الفاتورة
     */
    public function getInvoiceSetting($key, $default = null)
    {
        return data_get($this->invoice_settings, $key, $default);
    }

    /**
     * تحديث إعدادات الفاتورة
     */
    public function updateInvoiceSetting($key, $value)
    {
        $settings = $this->invoice_settings ?? [];
        data_set($settings, $key, $value);
        $this->update(['invoice_settings' => $settings]);
    }

    /**
     * الحصول على إعدادات نقاط البيع
     */
    public function getPosSetting($key, $default = null)
    {
        return data_get($this->pos_settings, $key, $default);
    }

    /**
     * تحديث إعدادات نقاط البيع
     */
    public function updatePosSetting($key, $value)
    {
        $settings = $this->pos_settings ?? [];
        data_set($settings, $key, $value);
        $this->update(['pos_settings' => $settings]);
    }

    /**
     * الحصول على نوع الموقع مترجم
     */
    public function getTypeNameAttribute()
    {
        $types = [
            'store' => 'متجر',
            'warehouse' => 'مستودع',
            'service_center' => 'مركز خدمة'
        ];

        return $types[$this->type] ?? $this->type;
    }

    /**
     * scope للمواقع النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * scope للموقع الافتراضي
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    // POS-specific relationships (sales relationship already exists above)

    // POS-specific methods
    public function getTodaysSales()
    {
        return $this->sales()->whereDate('sale_date', today())->get();
    }

    public function getTodaysRevenue()
    {
        return $this->sales()
                   ->whereDate('sale_date', today())
                   ->where('status', 'completed')
                   ->sum('total_amount');
    }

    public function canProcessSales()
    {
        return $this->is_active && ($this->pos_settings['allow_sales'] ?? true);
    }

    public function getReceiptHeader()
    {
        return $this->receipt_header ?? $this->name;
    }

    public function getReceiptFooter()
    {
        return $this->receipt_footer ?? 'شكراً لزيارتكم - Thank you for your visit';
    }

    public function getTaxRate()
    {
        return $this->pos_settings['tax_rate'] ?? 15; // Default 15% VAT for Saudi Arabia
    }

    public function getDefaultPaymentMethods()
    {
        return $this->pos_settings['payment_methods'] ?? ['cash', 'card', 'bank_transfer'];
    }

    public function supportsPaymentMethod($method)
    {
        $supportedMethods = $this->getDefaultPaymentMethods();
        return in_array($method, $supportedMethods);
    }

    /**
     * Get the main branch location
     */
    public static function getMainBranch()
    {
        return static::where('is_main_branch', true)->first() ??
               static::where('is_default', true)->first() ??
               static::active()->first();
    }

    /**
     * Get all active locations
     */
    public static function getActiveLocations()
    {
        return static::where('is_active', true)->get();
    }

    /**
     * Create default location if none exists
     */
    public static function createDefaultLocation()
    {
        if (static::count() === 0) {
            return static::create([
                'name' => 'الفرع الرئيسي',
                'code' => 'MAIN',
                'type' => 'store',
                'city' => 'الرياض',
                'country' => 'المملكة العربية السعودية',
                'is_active' => true,
                'is_default' => true,
                'is_main_branch' => true,
                'pos_settings' => [
                    'allow_sales' => true,
                    'tax_rate' => 15,
                    'payment_methods' => ['cash', 'card', 'bank_transfer'],
                    'auto_print_receipt' => true,
                    'require_customer_info' => false,
                ],
                'receipt_header' => 'مركز الصيانة المتقدم',
                'receipt_footer' => 'شكراً لثقتكم - نتطلع لخدمتكم مرة أخرى',
            ]);
        }

        return static::first();
    }
}
