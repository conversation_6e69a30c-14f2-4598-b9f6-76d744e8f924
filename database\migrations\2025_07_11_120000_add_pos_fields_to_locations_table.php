<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('locations', function (Blueprint $table) {
            $table->boolean('is_main_branch')->default(false)->after('is_default');
            $table->text('description')->nullable()->after('name_en');
            $table->string('state')->nullable()->after('city');
            $table->string('manager_name')->nullable()->after('manager_id');
            $table->json('business_hours')->nullable()->after('inventory_settings');
            $table->json('services_offered')->nullable()->after('business_hours');
            $table->text('receipt_header')->nullable()->after('services_offered');
            $table->text('receipt_footer')->nullable()->after('receipt_header');
            $table->string('tax_number')->nullable()->after('receipt_footer');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('locations', function (Blueprint $table) {
            $table->dropColumn([
                'is_main_branch',
                'description',
                'state',
                'manager_name',
                'business_hours',
                'services_offered',
                'receipt_header',
                'receipt_footer',
                'tax_number'
            ]);
        });
    }
};
