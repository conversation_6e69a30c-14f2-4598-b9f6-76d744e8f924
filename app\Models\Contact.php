<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Contact extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'type', // customer, supplier, both
        'name',
        'name_en',
        'email',
        'phone',
        'mobile',
        'address',
        'city',
        'country',
        'postal_code',
        'tax_number',
        'credit_limit',
        'payment_terms', // days
        'discount_percentage',
        'is_active',
        'notes',
        'contact_person',
        'contact_person_phone',
        'bank_details'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'credit_limit' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'payment_terms' => 'integer',
        'bank_details' => 'array'
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع المبيعات (كعميل)
     */
    public function sales()
    {
        return $this->hasMany(Sale::class, 'customer_id');
    }

    /**
     * العلاقة مع المشتريات (كمورد)
     */
    public function purchases()
    {
        return $this->hasMany(Purchase::class, 'supplier_id');
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * العلاقة مع طلبات الصيانة
     */
    public function maintenanceOrders()
    {
        return $this->hasMany(MaintenanceOrder::class, 'customer_id');
    }

    /**
     * حساب الرصيد الإجمالي
     */
    public function getBalanceAttribute()
    {
        $salesTotal = $this->sales()->sum('total_amount');
        $purchasesTotal = $this->purchases()->sum('total_amount');
        $paymentsReceived = $this->payments()->where('type', 'received')->sum('amount');
        $paymentsPaid = $this->payments()->where('type', 'paid')->sum('amount');

        if ($this->type === 'customer' || $this->type === 'both') {
            return $salesTotal - $paymentsReceived;
        } elseif ($this->type === 'supplier') {
            return $purchasesTotal - $paymentsPaid;
        }

        return ($salesTotal - $paymentsReceived) - ($purchasesTotal - $paymentsPaid);
    }

    /**
     * حساب المبلغ المستحق
     */
    public function getOverdueAmountAttribute()
    {
        $overdueAmount = 0;

        if ($this->type === 'customer' || $this->type === 'both') {
            $overdueSales = $this->sales()
                ->where('due_date', '<', now())
                ->where('payment_status', '!=', 'paid')
                ->get();

            foreach ($overdueSales as $sale) {
                $overdueAmount += $sale->total_amount - $sale->paid_amount;
            }
        }

        if ($this->type === 'supplier' || $this->type === 'both') {
            $overduePurchases = $this->purchases()
                ->where('due_date', '<', now())
                ->where('payment_status', '!=', 'paid')
                ->get();

            foreach ($overduePurchases as $purchase) {
                $overdueAmount += $purchase->total_amount - $purchase->paid_amount;
            }
        }

        return $overdueAmount;
    }

    /**
     * التحقق من وجود مبالغ مستحقة قريباً
     */
    public function getUpcomingDuesAttribute()
    {
        $upcomingDues = collect();

        if ($this->type === 'customer' || $this->type === 'both') {
            $upcomingSales = $this->sales()
                ->where('due_date', '>=', now())
                ->where('due_date', '<=', now()->addWeek())
                ->where('payment_status', '!=', 'paid')
                ->get();

            $upcomingDues = $upcomingDues->merge($upcomingSales);
        }

        if ($this->type === 'supplier' || $this->type === 'both') {
            $upcomingPurchases = $this->purchases()
                ->where('due_date', '>=', now())
                ->where('due_date', '<=', now()->addWeek())
                ->where('payment_status', '!=', 'paid')
                ->get();

            $upcomingDues = $upcomingDues->merge($upcomingPurchases);
        }

        return $upcomingDues;
    }

    /**
     * الحصول على نوع جهة الاتصال مترجم
     */
    public function getTypeNameAttribute()
    {
        $types = [
            'customer' => 'عميل',
            'supplier' => 'مورد',
            'both' => 'عميل ومورد'
        ];

        return $types[$this->type] ?? $this->type;
    }

    /**
     * scope للعملاء فقط
     */
    public function scopeCustomers($query)
    {
        return $query->whereIn('type', ['customer', 'both']);
    }

    /**
     * scope للموردين فقط
     */
    public function scopeSuppliers($query)
    {
        return $query->whereIn('type', ['supplier', 'both']);
    }

    /**
     * scope للجهات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * scope للجهات التي لديها مستحقات
     */
    public function scopeWithOverdue($query)
    {
        return $query->whereHas('sales', function ($q) {
            $q->where('due_date', '<', now())
              ->where('payment_status', '!=', 'paid');
        })->orWhereHas('purchases', function ($q) {
            $q->where('due_date', '<', now())
              ->where('payment_status', '!=', 'paid');
        });
    }

    /**
     * إنشاء رقم عميل/مورد تلقائي
     */
    public static function generateContactNumber($type, $companyId)
    {
        $prefix = match($type) {
            'customer' => 'CUS',
            'supplier' => 'SUP',
            'both' => 'CON',
            default => 'CON'
        };

        $lastContact = self::where('company_id', $companyId)
            ->where('contact_number', 'like', $prefix . '%')
            ->orderBy('contact_number', 'desc')
            ->first();

        $number = 1;
        if ($lastContact) {
            $lastNumber = (int) substr($lastContact->contact_number, strlen($prefix));
            $number = $lastNumber + 1;
        }

        return $prefix . str_pad($number, 6, '0', STR_PAD_LEFT);
    }
}
