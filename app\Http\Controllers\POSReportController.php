<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\SalePayment;
use App\Models\Location;
use App\Models\Part;
use App\Services\InventoryIntegrationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class POSReportController extends Controller
{
    protected $inventoryService;

    public function __construct(InventoryIntegrationService $inventoryService)
    {
        $this->inventoryService = $inventoryService;
    }

    /**
     * POS Dashboard with key metrics
     */
    public function dashboard(Request $request)
    {
        $locationId = $request->get('location_id');
        $period = $request->get('period', 'today'); // today, week, month, year
        
        $dateRange = $this->getDateRange($period);
        
        $metrics = [
            'sales_overview' => $this->getSalesOverview($dateRange, $locationId),
            'payment_methods' => $this->getPaymentMethodsBreakdown($dateRange, $locationId),
            'top_products' => $this->getTopSellingProducts($dateRange, $locationId),
            'hourly_sales' => $this->getHourlySales($dateRange, $locationId),
            'inventory_alerts' => $this->inventoryService->getLowStockAlerts($locationId),
            'recent_sales' => $this->getRecentSales($locationId),
        ];

        if ($request->expectsJson()) {
            return response()->json($metrics);
        }

        $locations = Location::active()->get();
        return view('pos.reports.dashboard', compact('metrics', 'locations', 'locationId', 'period'));
    }

    /**
     * Sales performance report
     */
    public function salesPerformance(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_id' => 'nullable|exists:locations,id',
            'group_by' => 'nullable|in:day,week,month',
        ]);

        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());
        $locationId = $request->get('location_id');
        $groupBy = $request->get('group_by', 'day');

        $salesData = $this->getSalesPerformanceData($startDate, $endDate, $locationId, $groupBy);
        
        if ($request->expectsJson()) {
            return response()->json($salesData);
        }

        $locations = Location::active()->get();
        return view('pos.reports.sales-performance', compact('salesData', 'locations', 'startDate', 'endDate', 'locationId', 'groupBy'));
    }

    /**
     * Product performance report
     */
    public function productPerformance(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_id' => 'nullable|exists:locations,id',
            'category' => 'nullable|string',
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());
        $locationId = $request->get('location_id');
        $category = $request->get('category');
        $limit = $request->get('limit', 20);

        $productData = $this->getProductPerformanceData($startDate, $endDate, $locationId, $category, $limit);
        
        if ($request->expectsJson()) {
            return response()->json($productData);
        }

        $locations = Location::active()->get();
        $categories = Part::distinct()->pluck('category')->filter();
        
        return view('pos.reports.product-performance', compact(
            'productData', 'locations', 'categories', 'startDate', 'endDate', 
            'locationId', 'category', 'limit'
        ));
    }

    /**
     * Payment methods analysis
     */
    public function paymentAnalysis(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_id' => 'nullable|exists:locations,id',
        ]);

        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());
        $locationId = $request->get('location_id');

        $paymentData = $this->getPaymentAnalysisData($startDate, $endDate, $locationId);
        
        if ($request->expectsJson()) {
            return response()->json($paymentData);
        }

        $locations = Location::active()->get();
        return view('pos.reports.payment-analysis', compact('paymentData', 'locations', 'startDate', 'endDate', 'locationId'));
    }

    /**
     * Inventory movement report
     */
    public function inventoryMovement(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_id' => 'nullable|exists:locations,id',
            'part_id' => 'nullable|exists:parts,id',
        ]);

        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());
        $locationId = $request->get('location_id');
        $partId = $request->get('part_id');

        $movementData = $this->getInventoryMovementData($startDate, $endDate, $locationId, $partId);
        
        if ($request->expectsJson()) {
            return response()->json($movementData);
        }

        $locations = Location::active()->get();
        $parts = Part::active()->orderBy('name')->get();
        
        return view('pos.reports.inventory-movement', compact(
            'movementData', 'locations', 'parts', 'startDate', 'endDate', 
            'locationId', 'partId'
        ));
    }

    /**
     * Export sales data
     */
    public function exportSales(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'location_id' => 'nullable|exists:locations,id',
            'format' => 'required|in:csv,excel,pdf',
        ]);

        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $locationId = $request->get('location_id');
        $format = $request->get('format');

        $sales = $this->getSalesForExport($startDate, $endDate, $locationId);

        switch ($format) {
            case 'csv':
                return $this->exportToCsv($sales, 'sales');
            case 'excel':
                return $this->exportToExcel($sales, 'sales');
            case 'pdf':
                return $this->exportToPdf($sales, 'sales');
            default:
                return response()->json(['error' => 'تنسيق غير مدعوم'], 400);
        }
    }

    // Private helper methods

    private function getDateRange($period)
    {
        switch ($period) {
            case 'today':
                return [now()->startOfDay(), now()->endOfDay()];
            case 'week':
                return [now()->startOfWeek(), now()->endOfWeek()];
            case 'month':
                return [now()->startOfMonth(), now()->endOfMonth()];
            case 'year':
                return [now()->startOfYear(), now()->endOfYear()];
            default:
                return [now()->startOfDay(), now()->endOfDay()];
        }
    }

    private function getSalesOverview($dateRange, $locationId = null)
    {
        $query = Sale::whereBetween('sale_date', $dateRange);
        
        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        $sales = $query->get();
        $completedSales = $sales->where('status', 'completed');

        return [
            'total_sales' => $sales->count(),
            'completed_sales' => $completedSales->count(),
            'pending_sales' => $sales->where('status', 'pending')->count(),
            'cancelled_sales' => $sales->where('status', 'cancelled')->count(),
            'total_revenue' => $completedSales->sum('total_amount'),
            'total_tax' => $completedSales->sum('tax_amount'),
            'total_discount' => $completedSales->sum('discount_amount'),
            'average_sale_value' => $completedSales->count() > 0 ? $completedSales->avg('total_amount') : 0,
            'items_sold' => $completedSales->sum(function ($sale) {
                return $sale->items->sum('quantity');
            }),
        ];
    }

    private function getPaymentMethodsBreakdown($dateRange, $locationId = null)
    {
        $query = SalePayment::join('sales', 'sale_payments.sale_id', '=', 'sales.id')
                           ->whereBetween('sales.sale_date', $dateRange)
                           ->where('sale_payments.status', 'completed');

        if ($locationId) {
            $query->where('sales.location_id', $locationId);
        }

        return $query->select('payment_method', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
                    ->groupBy('payment_method')
                    ->get()
                    ->map(function ($item) {
                        return [
                            'method' => $item->payment_method,
                            'method_label' => $this->getPaymentMethodLabel($item->payment_method),
                            'count' => $item->count,
                            'total' => $item->total,
                        ];
                    });
    }

    private function getTopSellingProducts($dateRange, $locationId = null, $limit = 10)
    {
        $query = SaleItem::join('sales', 'sale_items.sale_id', '=', 'sales.id')
                        ->join('parts', 'sale_items.part_id', '=', 'parts.id')
                        ->whereBetween('sales.sale_date', $dateRange)
                        ->where('sales.status', 'completed')
                        ->where('sale_items.item_type', 'part');

        if ($locationId) {
            $query->where('sales.location_id', $locationId);
        }

        return $query->select(
                    'parts.name',
                    'parts.part_number',
                    DB::raw('SUM(sale_items.quantity) as total_sold'),
                    DB::raw('SUM(sale_items.line_total) as total_revenue')
                )
                ->groupBy('parts.id', 'parts.name', 'parts.part_number')
                ->orderBy('total_sold', 'desc')
                ->limit($limit)
                ->get();
    }

    private function getHourlySales($dateRange, $locationId = null)
    {
        $query = Sale::whereBetween('sale_date', $dateRange)
                    ->where('status', 'completed');

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        return $query->select(
                    DB::raw('HOUR(sale_date) as hour'),
                    DB::raw('COUNT(*) as sales_count'),
                    DB::raw('SUM(total_amount) as total_revenue')
                )
                ->groupBy(DB::raw('HOUR(sale_date)'))
                ->orderBy('hour')
                ->get()
                ->map(function ($item) {
                    return [
                        'hour' => $item->hour . ':00',
                        'sales_count' => $item->sales_count,
                        'total_revenue' => $item->total_revenue,
                    ];
                });
    }

    private function getRecentSales($locationId = null, $limit = 10)
    {
        $query = Sale::with(['customer', 'user']);

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        return $query->latest('sale_date')->limit($limit)->get();
    }

    private function getPaymentMethodLabel($method)
    {
        $labels = [
            'cash' => 'نقدي',
            'card' => 'بطاقة ائتمان',
            'bank_transfer' => 'تحويل بنكي',
            'check' => 'شيك',
            'digital_wallet' => 'محفظة رقمية',
            'installment' => 'تقسيط',
        ];

        return $labels[$method] ?? $method;
    }

    // Additional helper methods would be implemented here for:
    // - getSalesPerformanceData()
    // - getProductPerformanceData()
    // - getPaymentAnalysisData()
    // - getInventoryMovementData()
    // - getSalesForExport()
    // - exportToCsv(), exportToExcel(), exportToPdf()
}
