<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('locations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique(); // Short code for location
            $table->text('description')->nullable();
            
            // Address information
            $table->string('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->default('Saudi Arabia');
            
            // Contact information
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('manager_name')->nullable();
            
            // Business information
            $table->boolean('is_active')->default(true);
            $table->boolean('is_main_branch')->default(false);
            $table->json('business_hours')->nullable(); // Store opening hours
            $table->json('services_offered')->nullable(); // What services this location offers
            
            // POS configuration
            $table->json('pos_settings')->nullable(); // Location-specific POS settings
            $table->string('receipt_header')->nullable();
            $table->string('receipt_footer')->nullable();
            $table->string('tax_number')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index('code');
            $table->index(['is_active', 'city']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('locations');
    }
};
