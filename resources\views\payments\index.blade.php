@extends('layouts.main')

@section('title', 'إدارة المدفوعات')

@section('content')
<div class="space-y-6" x-data="paymentsManager()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إدارة المدفوعات</h1>
            <p class="text-gray-600 dark:text-gray-400">تتبع المدفوعات والمستحقات</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="exportPayments()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير
            </button>
            <button @click="addPayment()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                تسجيل دفعة
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المدفوعات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.totalPayments">₪12,450</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مستحقات معلقة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.pendingPayments">₪3,200</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متأخرات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.overduePayments">₪850</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">دفعات اليوم</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.todayPayments">₪1,250</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <input type="text" x-model="filters.search" @input="filterPayments()" placeholder="رقم الفاتورة أو العميل..."
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                <select x-model="filters.status" @change="filterPayments()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="paid">مدفوع</option>
                    <option value="partial">جزئي</option>
                    <option value="pending">معلق</option>
                    <option value="overdue">متأخر</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة الدفع</label>
                <select x-model="filters.paymentMethod" @change="filterPayments()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الطرق</option>
                    <option value="cash">نقدي</option>
                    <option value="card">بطاقة</option>
                    <option value="credit">آجل</option>
                    <option value="bank_transfer">تحويل بنكي</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" x-model="filters.dateFrom" @change="filterPayments()"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" x-model="filters.dateTo" @change="filterPayments()"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">سجل المدفوعات</h3>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">رقم الفاتورة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">إجمالي الفاتورة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المدفوع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المتبقي</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">طريقة الدفع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="payment in filteredPayments" :key="payment.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="payment.invoice_number"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="payment.date"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-gray-100" x-text="payment.customer_name"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="payment.customer_phone"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                ₪<span x-text="payment.total_amount.toFixed(2)"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">
                                ₪<span x-text="payment.paid_amount.toFixed(2)"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"
                                :class="payment.remaining_amount > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'">
                                ₪<span x-text="payment.remaining_amount.toFixed(2)"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': payment.payment_method === 'cash',
                                          'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': payment.payment_method === 'card',
                                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': payment.payment_method === 'credit',
                                          'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200': payment.payment_method === 'bank_transfer'
                                      }"
                                      x-text="getPaymentMethodText(payment.payment_method)">
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': payment.status === 'paid',
                                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': payment.status === 'partial',
                                          'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200': payment.status === 'pending',
                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': payment.status === 'overdue'
                                      }"
                                      x-text="getStatusText(payment.status)">
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button @click="viewPayment(payment.id)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400">عرض</button>
                                    <button @click="addPartialPayment(payment.id)" x-show="payment.remaining_amount > 0" class="text-green-600 hover:text-green-900 dark:text-green-400">دفع</button>
                                    <button @click="sendReminder(payment.id)" x-show="payment.status === 'overdue'" class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400">تذكير</button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Payment Modal -->
    <div x-show="showPaymentModal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" @click="showPaymentModal = false">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">تسجيل دفعة جديدة</h3>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الفاتورة</label>
                            <input type="text" x-model="newPayment.invoice_number"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ</label>
                            <input type="number" x-model="newPayment.amount" step="0.01"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">طريقة الدفع</label>
                            <select x-model="newPayment.method"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات</label>
                            <textarea x-model="newPayment.notes" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="savePayment()"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 sm:ml-3 sm:w-auto sm:text-sm">
                        حفظ
                    </button>
                    <button @click="showPaymentModal = false"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-100 dark:border-gray-500">
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function paymentsManager() {
    return {
        payments: [
            {
                id: 1,
                invoice_number: 'INV-001',
                customer_name: 'أحمد محمد علي',
                customer_phone: '0599123456',
                date: '2024-07-09',
                total_amount: 1250.00,
                paid_amount: 1250.00,
                remaining_amount: 0.00,
                payment_method: 'cash',
                status: 'paid'
            },
            {
                id: 2,
                invoice_number: 'INV-002',
                customer_name: 'سارة أحمد خالد',
                customer_phone: '0598765432',
                date: '2024-07-08',
                total_amount: 850.00,
                paid_amount: 500.00,
                remaining_amount: 350.00,
                payment_method: 'card',
                status: 'partial'
            },
            {
                id: 3,
                invoice_number: 'INV-003',
                customer_name: 'محمد عبدالله حسن',
                customer_phone: '0597654321',
                date: '2024-07-05',
                total_amount: 2100.00,
                paid_amount: 0.00,
                remaining_amount: 2100.00,
                payment_method: 'credit',
                status: 'overdue'
            }
        ],
        filteredPayments: [],
        filters: {
            search: '',
            status: '',
            paymentMethod: '',
            dateFrom: '',
            dateTo: ''
        },
        stats: {
            totalPayments: '₪12,450',
            pendingPayments: '₪3,200',
            overduePayments: '₪850',
            todayPayments: '₪1,250'
        },
        showPaymentModal: false,
        newPayment: {
            invoice_number: '',
            amount: '',
            method: 'cash',
            notes: ''
        },

        init() {
            this.filteredPayments = [...this.payments];
        },

        filterPayments() {
            this.filteredPayments = this.payments.filter(payment => {
                const matchesSearch = !this.filters.search ||
                    payment.invoice_number.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    payment.customer_name.toLowerCase().includes(this.filters.search.toLowerCase());

                const matchesStatus = !this.filters.status || payment.status === this.filters.status;
                const matchesPayment = !this.filters.paymentMethod || payment.payment_method === this.filters.paymentMethod;

                const matchesDateFrom = !this.filters.dateFrom || payment.date >= this.filters.dateFrom;
                const matchesDateTo = !this.filters.dateTo || payment.date <= this.filters.dateTo;

                return matchesSearch && matchesStatus && matchesPayment && matchesDateFrom && matchesDateTo;
            });
        },

        getPaymentMethodText(method) {
            const methods = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'credit': 'آجل',
                'bank_transfer': 'تحويل بنكي'
            };
            return methods[method] || method;
        },

        getStatusText(status) {
            const statuses = {
                'paid': 'مدفوع',
                'partial': 'جزئي',
                'pending': 'معلق',
                'overdue': 'متأخر'
            };
            return statuses[status] || status;
        },

        addPayment() {
            this.newPayment = {
                invoice_number: '',
                amount: '',
                method: 'cash',
                notes: ''
            };
            this.showPaymentModal = true;
        },

        savePayment() {
            // Save payment logic
            alert('تم تسجيل الدفعة بنجاح');
            this.showPaymentModal = false;
        },

        viewPayment(id) {
            // View payment details
            window.location.href = `/payments/${id}`;
        },

        addPartialPayment(id) {
            const payment = this.payments.find(p => p.id === id);
            if (payment) {
                this.newPayment = {
                    invoice_number: payment.invoice_number,
                    amount: payment.remaining_amount,
                    method: 'cash',
                    notes: ''
                };
                this.showPaymentModal = true;
            }
        },

        sendReminder(id) {
            // Send payment reminder
            alert('تم إرسال تذكير الدفع');
        },

        exportPayments() {
            // Export payments to Excel
            alert('سيتم تصدير البيانات قريباً');
        }
    }
}
</script>
@endpush
@endsection