@extends('layouts.main')

@section('title', 'الصيانة الوقائية')

@section('content')
<div class="space-y-6" x-data="preventiveMaintenanceManager()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">الصيانة الوقائية</h1>
            <p class="text-gray-600 dark:text-gray-400">جدولة وإدارة الصيانة الوقائية للأجهزة</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="exportSchedule()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير الجدولة
            </button>
            <button @click="createSchedule()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                جدولة جديدة
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مجدولة هذا الشهر</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.scheduledThisMonth">45</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متأخرة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.overdue">8</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مكتملة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.completed">127</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">معدل الإنجاز</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.completionRate">94%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات سريعة</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button @click="scheduleByDevice()" class="p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">جدولة حسب الجهاز</p>
                </div>
            </button>
            
            <button @click="scheduleByCustomer()" class="p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">جدولة حسب العميل</p>
                </div>
            </button>
            
            <button @click="bulkSchedule()" class="p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">جدولة مجمعة</p>
                </div>
            </button>
            
            <button @click="generateReport()" class="p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors">
                <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">تقرير الأداء</p>
                </div>
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <input type="text" x-model="filters.search" @input="filterSchedules()" placeholder="العميل أو الجهاز..." 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الصيانة</label>
                <select x-model="filters.type" @change="filterSchedules()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الأنواع</option>
                    <option value="cleaning">تنظيف</option>
                    <option value="inspection">فحص</option>
                    <option value="software_update">تحديث البرمجيات</option>
                    <option value="hardware_check">فحص العتاد</option>
                    <option value="performance_test">اختبار الأداء</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                <select x-model="filters.status" @change="filterSchedules()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="scheduled">مجدولة</option>
                    <option value="in_progress">قيد التنفيذ</option>
                    <option value="completed">مكتملة</option>
                    <option value="overdue">متأخرة</option>
                    <option value="cancelled">ملغية</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفني</label>
                <select x-model="filters.technician" @change="filterSchedules()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الفنيين</option>
                    <template x-for="tech in technicians" :key="tech.id">
                        <option :value="tech.id" x-text="tech.name"></option>
                    </template>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التاريخ</label>
                <input type="date" x-model="filters.date" @change="filterSchedules()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
        </div>
    </div>

    <!-- Schedules Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">جدولة الصيانة الوقائية</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الجهاز</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">نوع الصيانة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ المجدول</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفني</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التكرار</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="schedule in filteredSchedules" :key="schedule.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-gray-100" x-text="schedule.customer_name"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="schedule.customer_phone"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-gray-100" x-text="schedule.device_brand + ' ' + schedule.device_model"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="schedule.device_serial"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="getMaintenanceTypeText(schedule.maintenance_type)"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-gray-100" x-text="schedule.scheduled_date"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="schedule.scheduled_time"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="schedule.technician_name"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="getRecurrenceText(schedule.recurrence)"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': schedule.status === 'scheduled',
                                          'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200': schedule.status === 'in_progress',
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': schedule.status === 'completed',
                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': schedule.status === 'overdue',
                                          'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200': schedule.status === 'cancelled'
                                      }"
                                      x-text="getStatusText(schedule.status)">
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button @click="viewSchedule(schedule.id)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400">عرض</button>
                                    <button @click="editSchedule(schedule.id)" class="text-green-600 hover:text-green-900 dark:text-green-400">تعديل</button>
                                    <button @click="completeSchedule(schedule.id)" class="text-purple-600 hover:text-purple-900 dark:text-purple-400">إكمال</button>
                                    <button @click="reschedule(schedule.id)" class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400">إعادة جدولة</button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
function preventiveMaintenanceManager() {
    return {
        schedules: [
            {
                id: 1,
                customer_name: 'أحمد محمد علي',
                customer_phone: '0599123456',
                device_brand: 'Samsung',
                device_model: 'Galaxy S21',
                device_serial: '123456789',
                maintenance_type: 'cleaning',
                scheduled_date: '2024-07-15',
                scheduled_time: '10:00',
                technician_name: 'محمد الفني',
                technician_id: 1,
                recurrence: 'monthly',
                status: 'scheduled',
                notes: 'تنظيف شامل للجهاز'
            },
            {
                id: 2,
                customer_name: 'سارة أحمد خالد',
                customer_phone: '0598765432',
                device_brand: 'iPhone',
                device_model: '13 Pro',
                device_serial: '987654321',
                maintenance_type: 'software_update',
                scheduled_date: '2024-07-10',
                scheduled_time: '14:00',
                technician_name: 'علي الفني',
                technician_id: 2,
                recurrence: 'quarterly',
                status: 'overdue',
                notes: 'تحديث نظام التشغيل'
            },
            {
                id: 3,
                customer_name: 'محمد عبدالله حسن',
                customer_phone: '0597654321',
                device_brand: 'HP',
                device_model: 'Pavilion 15',
                device_serial: '456789123',
                maintenance_type: 'hardware_check',
                scheduled_date: '2024-07-12',
                scheduled_time: '09:00',
                technician_name: 'أحمد الفني',
                technician_id: 3,
                recurrence: 'yearly',
                status: 'completed',
                notes: 'فحص شامل للعتاد'
            }
        ],
        technicians: [
            { id: 1, name: 'محمد الفني' },
            { id: 2, name: 'علي الفني' },
            { id: 3, name: 'أحمد الفني' }
        ],
        filteredSchedules: [],
        filters: {
            search: '',
            type: '',
            status: '',
            technician: '',
            date: ''
        },
        stats: {
            scheduledThisMonth: 45,
            overdue: 8,
            completed: 127,
            completionRate: '94%'
        },

        init() {
            this.filteredSchedules = [...this.schedules];
        },

        filterSchedules() {
            this.filteredSchedules = this.schedules.filter(schedule => {
                const matchesSearch = !this.filters.search || 
                    schedule.customer_name.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    schedule.device_brand.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    schedule.device_model.toLowerCase().includes(this.filters.search.toLowerCase());
                
                const matchesType = !this.filters.type || schedule.maintenance_type === this.filters.type;
                const matchesStatus = !this.filters.status || schedule.status === this.filters.status;
                const matchesTechnician = !this.filters.technician || schedule.technician_id === parseInt(this.filters.technician);
                const matchesDate = !this.filters.date || schedule.scheduled_date === this.filters.date;

                return matchesSearch && matchesType && matchesStatus && matchesTechnician && matchesDate;
            });
        },

        getMaintenanceTypeText(type) {
            const types = {
                'cleaning': 'تنظيف',
                'inspection': 'فحص',
                'software_update': 'تحديث البرمجيات',
                'hardware_check': 'فحص العتاد',
                'performance_test': 'اختبار الأداء'
            };
            return types[type] || type;
        },

        getRecurrenceText(recurrence) {
            const recurrences = {
                'weekly': 'أسبوعياً',
                'monthly': 'شهرياً',
                'quarterly': 'ربع سنوي',
                'yearly': 'سنوياً',
                'once': 'مرة واحدة'
            };
            return recurrences[recurrence] || recurrence;
        },

        getStatusText(status) {
            const statuses = {
                'scheduled': 'مجدولة',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتملة',
                'overdue': 'متأخرة',
                'cancelled': 'ملغية'
            };
            return statuses[status] || status;
        },

        createSchedule() {
            alert('سيتم إضافة نافذة إنشاء جدولة جديدة');
        },

        scheduleByDevice() {
            alert('جدولة صيانة وقائية حسب نوع الجهاز');
        },

        scheduleByCustomer() {
            alert('جدولة صيانة وقائية حسب العميل');
        },

        bulkSchedule() {
            alert('جدولة مجمعة للصيانة الوقائية');
        },

        generateReport() {
            alert('إنشاء تقرير أداء الصيانة الوقائية');
        },

        viewSchedule(id) {
            alert(`عرض تفاصيل الجدولة ${id}`);
        },

        editSchedule(id) {
            alert(`تعديل الجدولة ${id}`);
        },

        completeSchedule(id) {
            const schedule = this.schedules.find(s => s.id === id);
            if (schedule) {
                schedule.status = 'completed';
                this.filterSchedules();
                alert('تم إكمال الصيانة الوقائية');
            }
        },

        reschedule(id) {
            alert(`إعادة جدولة الصيانة ${id}`);
        },

        exportSchedule() {
            alert('سيتم تصدير جدولة الصيانة الوقائية');
        }
    }
}
</script>
@endpush
@endsection
