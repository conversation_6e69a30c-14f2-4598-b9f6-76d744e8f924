@extends('layouts.main')

@section('title', 'تعديل العميل')

@section('content')
@php
    $isEdit = isset($customer) && $customer->exists;
    $customer = $customer ?? new \App\Models\Customer();
@endphp

<div class="container mx-auto px-4 py-6">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {{ $isEdit ? 'تعديل العميل' : 'إضافة عميل جديد' }}
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
                @if($isEdit)
                    {{ $customer->display_name ?? ($customer->first_name . ' ' . $customer->last_name) }} - {{ $customer->customer_number }}
                @else
                    إضافة عميل جديد إلى النظام
                @endif
            </p>
        </div>

        <!-- Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <form action="{{ $isEdit ? route('customers.update', $customer) : route('customers.store') }}" method="POST" class="space-y-6">
                @csrf
                @if($isEdit)
                    @method('PUT')
                @endif

                <!-- Customer Type -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">نوع العميل</label>
                    <div class="grid grid-cols-2 gap-4">
                        <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                            <input type="radio" name="type" value="individual" {{ old('type', $customer->type ?? 'individual') == 'individual' ? 'checked' : '' }} class="text-blue-600 focus:ring-blue-500">
                            <span class="mr-2 text-sm text-gray-900 dark:text-gray-100">عميل فردي</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                            <input type="radio" name="type" value="business" {{ old('type', $customer->type ?? 'individual') == 'business' ? 'checked' : '' }} class="text-blue-600 focus:ring-blue-500">
                            <span class="mr-2 text-sm text-gray-900 dark:text-gray-100">عميل تجاري</span>
                        </label>
                    </div>
                </div>

                <!-- Basic Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأول *</label>
                        <input type="text" name="first_name" id="first_name" value="{{ old('first_name', $customer->first_name) }}" required 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        @error('first_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأخير *</label>
                        <input type="text" name="last_name" id="last_name" value="{{ old('last_name', $customer->last_name) }}" required 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        @error('last_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Company Name (for business) -->
                <div id="company-field" style="{{ old('type', $customer->type) == 'business' ? '' : 'display: none;' }}">
                    <label for="company_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم الشركة</label>
                    <input type="text" name="company_name" id="company_name" value="{{ old('company_name', $customer->company_name) }}" 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    @error('company_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Contact Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="mobile" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الجوال *</label>
                        <input type="text" name="mobile" id="mobile" value="{{ old('mobile', $customer->mobile) }}" required 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        @error('mobile')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                        <input type="email" name="email" id="email" value="{{ old('email', $customer->email) }}" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Additional Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المدينة</label>
                        <input type="text" name="city" id="city" value="{{ old('city', $customer->city) }}" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        @error('city')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                        <input type="text" name="phone" id="phone" value="{{ old('phone', $customer->phone) }}" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        @error('phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Settings -->
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $customer->is_active) ? 'checked' : '' }} 
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <label for="is_active" class="mr-2 text-sm text-gray-700 dark:text-gray-300">عميل نشط</label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" name="is_vip" id="is_vip" value="1" {{ old('is_vip', $customer->is_vip) ? 'checked' : '' }} 
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <label for="is_vip" class="mr-2 text-sm text-gray-700 dark:text-gray-300">عميل VIP</label>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex justify-end space-x-2 space-x-reverse pt-6">
                    <a href="{{ route('customers.show', $customer) }}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg">
                        إلغاء
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Toggle company field based on customer type
document.querySelectorAll('input[name="type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const companyField = document.getElementById('company-field');
        if (this.value === 'business') {
            companyField.style.display = 'block';
        } else {
            companyField.style.display = 'none';
        }
    });
});
</script>
@endsection
