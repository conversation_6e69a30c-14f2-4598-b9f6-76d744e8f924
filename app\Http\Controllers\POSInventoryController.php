<?php

namespace App\Http\Controllers;

use App\Services\InventoryIntegrationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class POSInventoryController extends Controller
{
    protected $inventoryService;

    public function __construct(InventoryIntegrationService $inventoryService)
    {
        $this->inventoryService = $inventoryService;
    }

    /**
     * Check stock availability for sale items
     */
    public function checkStock(Request $request)
    {
        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.type' => 'required|string',
            'items.*.part_id' => 'nullable|exists:parts,id',
            'items.*.quantity' => 'required|integer|min:1',
            'location_id' => 'nullable|exists:locations,id',
        ]);

        try {
            $items = $request->get('items');
            $locationId = $request->get('location_id');
            
            $result = $this->inventoryService->checkStockAvailability($items, $locationId);
            
            return response()->json([
                'success' => true,
                'stock_check' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to check stock availability', [
                'items' => $request->get('items'),
                'location_id' => $request->get('location_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في التحقق من توفر المخزون',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get low stock alerts
     */
    public function lowStockAlerts(Request $request)
    {
        $request->validate([
            'location_id' => 'nullable|exists:locations,id',
        ]);

        try {
            $locationId = $request->get('location_id');
            $alerts = $this->inventoryService->getLowStockAlerts($locationId);
            
            return response()->json([
                'success' => true,
                'alerts' => $alerts,
                'count' => $alerts->count()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get low stock alerts', [
                'location_id' => $request->get('location_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في الحصول على تنبيهات المخزون المنخفض',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get stock movement summary
     */
    public function movementSummary(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_id' => 'nullable|exists:locations,id',
        ]);

        try {
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            $locationId = $request->get('location_id');
            
            $summary = $this->inventoryService->getStockMovementSummary($startDate, $endDate, $locationId);
            
            return response()->json([
                'success' => true,
                'summary' => $summary
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get stock movement summary', [
                'start_date' => $request->get('start_date'),
                'end_date' => $request->get('end_date'),
                'location_id' => $request->get('location_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في الحصول على ملخص حركة المخزون',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available stock for a part
     */
    public function getAvailableStock(Request $request)
    {
        $request->validate([
            'part_id' => 'required|exists:parts,id',
            'location_id' => 'nullable|exists:locations,id',
        ]);

        try {
            $part = \App\Models\Part::findOrFail($request->get('part_id'));
            $locationId = $request->get('location_id');
            
            $availableStock = $this->inventoryService->getAvailableStock($part, $locationId);
            
            return response()->json([
                'success' => true,
                'part' => [
                    'id' => $part->id,
                    'name' => $part->name,
                    'part_number' => $part->part_number,
                    'total_stock' => $part->stock_quantity,
                    'reserved_stock' => $part->reserved_quantity ?? 0,
                    'available_stock' => $availableStock,
                    'min_stock_level' => $part->min_stock_level,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get available stock', [
                'part_id' => $request->get('part_id'),
                'location_id' => $request->get('location_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في الحصول على المخزون المتاح',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
