<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AccountTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'journal_entry_id',
        'account_id',
        'debit_amount',
        'credit_amount',
        'description',
        'reference_number',
        'metadata',
    ];

    protected $casts = [
        'debit_amount' => 'decimal:2',
        'credit_amount' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * Get the journal entry
     */
    public function journalEntry(): BelongsTo
    {
        return $this->belongsTo(JournalEntry::class);
    }

    /**
     * Get the account
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * Get the net amount (debit - credit)
     */
    public function getNetAmountAttribute()
    {
        return $this->debit_amount - $this->credit_amount;
    }

    /**
     * Get the transaction amount (non-zero amount)
     */
    public function getAmountAttribute()
    {
        return $this->debit_amount > 0 ? $this->debit_amount : $this->credit_amount;
    }

    /**
     * Get the transaction type
     */
    public function getTypeAttribute()
    {
        return $this->debit_amount > 0 ? 'debit' : 'credit';
    }

    /**
     * Get type label
     */
    public function getTypeLabel()
    {
        return $this->type === 'debit' ? 'مدين' : 'دائن';
    }

    /**
     * Scope for debit transactions
     */
    public function scopeDebit($query)
    {
        return $query->where('debit_amount', '>', 0);
    }

    /**
     * Scope for credit transactions
     */
    public function scopeCredit($query)
    {
        return $query->where('credit_amount', '>', 0);
    }

    /**
     * Scope for transactions by account
     */
    public function scopeByAccount($query, $accountId)
    {
        return $query->where('account_id', $accountId);
    }

    /**
     * Scope for transactions by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereHas('journalEntry', function ($q) use ($startDate, $endDate) {
            $q->whereBetween('entry_date', [$startDate, $endDate]);
        });
    }

    /**
     * Scope for posted transactions only
     */
    public function scopePosted($query)
    {
        return $query->whereHas('journalEntry', function ($q) {
            $q->where('status', 'posted');
        });
    }
}
