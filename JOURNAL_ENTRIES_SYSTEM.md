# دليل نظام القيود المحاسبية

## 📋 نظرة عامة

تم تطوير نظام قيود محاسبية متكامل يدعم إدارة جميع المعاملات المالية مع التحقق التلقائي من التوازن المحاسبي.

### ✅ **الميزات المتاحة:**
- **إنشاء وتعديل القيود** مع واجهة تفاعلية
- **التحقق التلقائي من التوازن** المحاسبي
- **إدارة حالات القيود** (مسودة، مرحل، معكوس)
- **ترحيل القيود** مع تحديث الأرصدة
- **عكس القيود** مع تسجيل الأسباب
- **واجهات عربية حديثة** متجاوبة

---

## 🚀 الصفحات المتاحة

### **القيود المحاسبية الرئيسية:**
- **عرض القيود**: `http://tareq.test/journal-entries`
- **إنشاء قيد جديد**: `http://tareq.test/journal-entries/create`
- **تفاصيل قيد**: `http://tareq.test/journal-entries/{id}`
- **تعديل قيد**: `http://tareq.test/journal-entries/{id}/edit`

---

## 📊 هيكل نظام القيود

### **1. حالات القيود:**

#### **مسودة (Draft):**
- ✅ قابلة للتعديل والحذف
- ✅ لا تؤثر على أرصدة الحسابات
- ✅ يمكن ترحيلها بعد التحقق من التوازن

#### **مرحل (Posted):**
- ✅ مؤثرة على أرصدة الحسابات
- ❌ غير قابلة للتعديل
- ✅ قابلة للعكس مع تسجيل الأسباب

#### **معكوس (Reversed):**
- ✅ ملغاة مع إنشاء قيد عكسي
- ❌ غير قابلة للتعديل
- ✅ محفوظة للمراجعة والتدقيق

### **2. مكونات القيد:**

#### **المعلومات الأساسية:**
- رقم القيد (تلقائي)
- تاريخ القيد
- وصف القيد
- نوع المرجع (اختياري)
- معرف المرجع (اختياري)

#### **تفاصيل القيد:**
- الحساب المحاسبي
- وصف العملية
- المبلغ المدين
- المبلغ الدائن

---

## 🔄 سير العمل

### **1. إنشاء قيد جديد:**
1. اذهب إلى `http://tareq.test/journal-entries/create`
2. أدخل تاريخ ووصف القيد
3. أضف سطور القيد (حساب + مبلغ)
4. تأكد من التوازن (مدين = دائن)
5. احفظ القيد كمسودة

### **2. ترحيل القيد:**
1. اذهب إلى تفاصيل القيد
2. اضغط "ترحيل"
3. تأكيد العملية
4. تحديث أرصدة الحسابات تلقائياً

### **3. عكس القيد:**
1. اذهب إلى تفاصيل القيد المرحل
2. اضغط "عكس القيد"
3. أدخل سبب العكس
4. إنشاء قيد عكسي تلقائياً

---

## 📝 قواعد النظام

### **التوازن المحاسبي:**
✅ **إجمالي المدين = إجمالي الدائن**  
✅ **منع الحفظ** للقيود غير المتوازنة  
✅ **تحذيرات بصرية** للقيود غير المتوازنة  

### **قيود الإدخال:**
✅ **سطرين على الأقل** لكل قيد  
✅ **مبلغ واحد فقط** (مدين أو دائن) لكل سطر  
✅ **اختيار حساب إجباري** لكل سطر  

### **الأمان والحماية:**
✅ **حماية القيود المرحلة** من التعديل  
✅ **تسجيل المستخدمين** لجميع العمليات  
✅ **تسجيل الأوقات** للمراجعة  

---

## 🎨 الواجهات

### **صفحة القيود الرئيسية:**
✅ **جدول تفاعلي** مع الفلترة والبحث  
✅ **عرض الحالات** بألوان مميزة  
✅ **أزرار الإجراءات** حسب الصلاحيات  
✅ **ترقيم الصفحات** للأداء المحسن  

### **إنشاء/تعديل القيد:**
✅ **جدول ديناميكي** لإضافة/حذف السطور  
✅ **قوائم منسدلة** لاختيار الحسابات  
✅ **حساب الإجماليات** تلقائياً  
✅ **التحقق من التوازن** في الوقت الفعلي  

### **تفاصيل القيد:**
✅ **عرض شامل** لجميع المعلومات  
✅ **روابط للحسابات** المرتبطة  
✅ **معلومات الترحيل والعكس**  
✅ **إمكانية الطباعة** مع تنسيق مناسب  

---

## 🔗 التكامل مع النظام

### **مع دليل الحسابات:**
✅ **تحديث الأرصدة** تلقائياً عند الترحيل  
✅ **روابط مباشرة** للحسابات  
✅ **عرض معلومات الحساب** في القيود  

### **مع التقارير المالية:**
✅ **ميزان المراجعة** يعتمد على القيود  
✅ **دفتر الأستاذ** يعرض تفاصيل القيود  
✅ **التقارير المالية** تستخدم بيانات القيود  

### **مع نقطة البيع:**
✅ **قيود تلقائية** للمبيعات  
✅ **قيود تحصيل** المدفوعات  
✅ **قيود المخزون** والتكلفة  

---

## 📊 أمثلة على القيود

### **1. قيد مبيعة نقدية:**
```
من حـ/ الصندوق                    1,150
    إلى حـ/ مبيعات قطع الغيار           1,000
    إلى حـ/ ضريبة القيمة المضافة        150
```

### **2. قيد مبيعة آجلة:**
```
من حـ/ ذمم العملاء                2,300
    إلى حـ/ مبيعات قطع الغيار           2,000
    إلى حـ/ ضريبة القيمة المضافة        300
```

### **3. قيد تحصيل ذمة:**
```
من حـ/ الصندوق                    1,000
    إلى حـ/ ذمم العملاء                 1,000
```

### **4. قيد شراء مخزون:**
```
من حـ/ مخزون قطع الغيار            5,000
    إلى حـ/ ذمم الموردين                5,000
```

### **5. قيد مصروف:**
```
من حـ/ الإيجار                     3,000
    إلى حـ/ الصندوق                     3,000
```

---

## 🛠️ البيانات التجريبية

### **تشغيل البيانات التجريبية:**
```bash
php artisan db:seed --class=JournalEntriesSeeder
```

### **ما يتم إنشاؤه:**
✅ **6 قيود محاسبية** متنوعة  
✅ **قيود مبيعات** (نقدية وآجلة)  
✅ **قيود تحصيل** وتسديد  
✅ **قيود مشتريات** ومصروفات  
✅ **قيود تحويلات** بين الحسابات  

---

## 🎯 الميزات المتقدمة

### **التحقق التلقائي:**
✅ **فحص التوازن** في الوقت الفعلي  
✅ **منع الإدخال المزدوج** (مدين ودائن)  
✅ **التحقق من البيانات** قبل الحفظ  

### **إدارة الحالات:**
✅ **تتبع دورة حياة القيد** كاملة  
✅ **تسجيل المستخدمين** والأوقات  
✅ **حفظ أسباب العكس** للمراجعة  

### **الأداء والكفاءة:**
✅ **استعلامات محسنة** مع العلاقات  
✅ **فهارس قاعدة البيانات** للبحث السريع  
✅ **تخزين مؤقت** للبيانات المحسوبة  

---

## 🎉 النتيجة النهائية

**نظام قيود محاسبية احترافي ومتكامل** يشمل:

- ✅ **إدارة كاملة للقيود** مع جميع الحالات
- ✅ **واجهات تفاعلية** سهلة الاستخدام
- ✅ **تحقق تلقائي من التوازن** المحاسبي
- ✅ **تكامل كامل** مع دليل الحسابات
- ✅ **حماية وأمان** للبيانات المحاسبية
- ✅ **تصميم عربي حديث** متجاوب

**النظام جاهز للاستخدام الإنتاجي!** 🚀
