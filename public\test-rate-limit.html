<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Rate Limiting</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            padding: 2rem;
            direction: rtl;
        }
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            margin: 0.5rem;
            font-size: 1rem;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .test-results {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔒 اختبار نظام Rate Limiting</h1>
        
        <div class="test-section info">
            <h3>📋 الإصلاحات المطبقة:</h3>
            <ul>
                <li><span class="status-indicator status-success"></span><strong>إزالة client-side rate limiting:</strong> تم نقل كامل المنطق إلى server-side</li>
                <li><span class="status-indicator status-success"></span><strong>تسجيل middleware:</strong> تم إضافة LoginRateLimit إلى Kernel</li>
                <li><span class="status-indicator status-success"></span><strong>تطبيق middleware:</strong> تم ربطه بـ route تسجيل الدخول</li>
                <li><span class="status-indicator status-success"></span><strong>إصلاح clearFailedAttempts:</strong> يتم استدعاؤها عند النجاح</li>
                <li><span class="status-indicator status-success"></span><strong>إضافة logging:</strong> لتتبع العمليات</li>
            </ul>
        </div>

        <div class="test-section warning">
            <h3>⚠️ خطوات الاختبار المطلوبة:</h3>
            <ol>
                <li><strong>اختبار تسجيل دخول ناجح:</strong> استخدم بيانات صحيحة وتأكد من عدم الحظر</li>
                <li><strong>اختبار محاولات فاشلة:</strong> استخدم بيانات خاطئة 5 مرات</li>
                <li><strong>اختبار الحظر:</strong> تأكد من الحظر بعد 5 محاولات فاشلة</li>
                <li><strong>اختبار مسح العداد:</strong> سجل دخول ناجح بعد محاولات فاشلة</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🧪 أدوات الاختبار:</h3>
            <button class="btn" onclick="testSuccessfulLogin()">اختبار تسجيل دخول ناجح</button>
            <button class="btn btn-danger" onclick="testFailedLogins()">اختبار محاولات فاشلة (5x)</button>
            <button class="btn btn-success" onclick="checkCacheStatus()">فحص حالة Cache</button>
            <button class="btn" onclick="clearCache()">مسح Cache</button>
            
            <div id="testResults" class="test-results" style="display: none;"></div>
        </div>

        <div class="test-section success">
            <h3>✅ الحسابات التجريبية للاختبار:</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <div>
                    <strong>✅ صحيح - مدير النظام:</strong><br>
                    Email: <EMAIL><br>
                    Password: password123
                </div>
                <div>
                    <strong>❌ خاطئ - للاختبار:</strong><br>
                    Email: <EMAIL><br>
                    Password: wrongpassword
                </div>
            </div>
        </div>

        <div class="test-section error">
            <h3>🚨 نقاط مهمة للاختبار:</h3>
            <ul>
                <li><strong>تسجيل دخول ناجح:</strong> يجب ألا يؤثر على العداد</li>
                <li><strong>5 محاولات فاشلة:</strong> يجب أن تؤدي للحظر</li>
                <li><strong>تسجيل دخول ناجح بعد فشل:</strong> يجب أن يمسح العداد</li>
                <li><strong>انتهاء مدة الحظر:</strong> 15 دقيقة</li>
                <li><strong>IP-based:</strong> كل IP له عداد منفصل</li>
            </ul>
        </div>

        <div class="test-section info">
            <h3>📊 معلومات النظام:</h3>
            <p><strong>IP الحالي:</strong> <span id="currentIP">جاري التحميل...</span></p>
            <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
            <p><strong>الوقت الحالي:</strong> <span id="currentTime"></span></p>
        </div>

        <div class="test-section">
            <h3>📝 سجل الاختبارات:</h3>
            <div id="testLog" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 1rem; border-radius: 6px;">
                <p><em>سيتم عرض نتائج الاختبارات هنا...</em></p>
            </div>
        </div>
    </div>

    <script>
        // تحديث معلومات النظام
        document.getElementById('userAgent').textContent = navigator.userAgent;
        
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // الحصول على IP
        fetch('https://api.ipify.org?format=json')
            .then(response => response.json())
            .then(data => {
                document.getElementById('currentIP').textContent = data.ip;
            })
            .catch(() => {
                document.getElementById('currentIP').textContent = 'غير متاح';
            });

        function logTest(message, type = 'info') {
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            
            const entry = document.createElement('div');
            entry.style.color = colors[type] || colors.info;
            entry.style.marginBottom = '0.5rem';
            entry.innerHTML = `[${timestamp}] ${message}`;
            
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function testSuccessfulLogin() {
            logTest('🧪 لاختبار تسجيل الدخول الناجح، افتح صفحة تسجيل الدخول واستخدم البيانات الصحيحة', 'info');
            openLoginPage();
        }

        async function testFailedLogins() {
            logTest('🧪 بدء اختبار المحاولات الفاشلة (5 مرات)...', 'warning');
            
            for (let i = 1; i <= 5; i++) {
                try {
                    const response = await fetch('/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRF-TOKEN': await getCSRFToken()
                        },
                        body: 'email=<EMAIL>&password=wrongpassword'
                    });

                    logTest(`محاولة ${i}/5: ${response.status}`, 'warning');
                    
                    if (response.status === 422) {
                        const data = await response.json();
                        if (data.message && data.message.includes('تجاوز')) {
                            logTest('🚫 تم تفعيل الحظر!', 'error');
                            break;
                        }
                    }
                } catch (error) {
                    logTest(`❌ خطأ في المحاولة ${i}: ${error.message}`, 'error');
                }
                
                // انتظار قصير بين المحاولات
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        async function getCSRFToken() {
            try {
                const response = await fetch('/login');
                const html = await response.text();
                const match = html.match(/name="_token" value="([^"]+)"/);
                return match ? match[1] : '';
            } catch {
                return '';
            }
        }

        function checkCacheStatus() {
            logTest('📊 فحص حالة Cache...', 'info');
            logTest('ℹ️ لفحص Cache الفعلي، تحقق من Laravel logs', 'info');
            logTest('ℹ️ أو استخدم: php artisan cache:clear', 'info');
        }

        function clearCache() {
            logTest('🧹 تنظيف Cache...', 'info');
            logTest('ℹ️ لمسح Cache الفعلي، استخدم: php artisan cache:clear', 'warning');
        }

        // إضافة معلومات إضافية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            logTest('🚀 تم تحميل صفحة اختبار Rate Limiting', 'success');
            logTest('📍 تأكد من تشغيل Laravel server', 'info');
            logTest('📍 تأكد من تفعيل Cache driver', 'info');
        });
    </script>
</body>
</html>
