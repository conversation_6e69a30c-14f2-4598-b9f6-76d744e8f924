<?php
// Debug script to identify where $customer variable is being used

echo "=== Customer Variable Debug Report ===\n\n";

// Check if we're in the right context
echo "Current URL: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'CLI') . "\n";
echo "Current Route: " . (function_exists('request') && request()->route() ? request()->route()->getName() : 'Unknown') . "\n\n";

// Check what variables are available
echo "Available Variables in View Context:\n";
if (isset($__data)) {
    foreach ($__data as $key => $value) {
        echo "- \${$key}: " . gettype($value) . "\n";
    }
} else {
    echo "No \$__data available\n";
}

echo "\nDirect Variable Check:\n";
echo "- \$customer exists: " . (isset($customer) ? 'YES' : 'NO') . "\n";
echo "- \$customer value: " . (isset($customer) ? var_export($customer, true) : 'UNDEFINED') . "\n";

echo "\nView Stack Trace:\n";
$trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
foreach ($trace as $index => $frame) {
    if (isset($frame['file']) && strpos($frame['file'], 'views') !== false) {
        echo "#{$index} {$frame['file']}:{$frame['line']}\n";
    }
}

echo "\n=== End Debug Report ===\n";
?>
