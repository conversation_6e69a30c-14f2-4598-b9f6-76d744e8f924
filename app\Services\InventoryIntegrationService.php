<?php

namespace App\Services;

use App\Models\Part;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Location;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InventoryIntegrationService
{
    /**
     * Check stock availability for sale items
     */
    public function checkStockAvailability(array $items, $locationId = null)
    {
        $stockIssues = [];
        
        foreach ($items as $item) {
            if ($item['type'] === 'part' && isset($item['part_id'])) {
                $part = Part::find($item['part_id']);
                
                if (!$part) {
                    $stockIssues[] = [
                        'item' => $item,
                        'issue' => 'part_not_found',
                        'message' => "القطعة غير موجودة: {$item['name']}"
                    ];
                    continue;
                }

                $availableStock = $this->getAvailableStock($part, $locationId);
                $requestedQuantity = $item['quantity'];

                if ($availableStock < $requestedQuantity) {
                    $stockIssues[] = [
                        'item' => $item,
                        'part' => $part,
                        'issue' => 'insufficient_stock',
                        'available' => $availableStock,
                        'requested' => $requestedQuantity,
                        'message' => "مخزون غير كافي: {$part->name} (متوفر: {$availableStock}, مطلوب: {$requestedQuantity})"
                    ];
                }

                if ($availableStock <= $part->min_stock_level) {
                    $stockIssues[] = [
                        'item' => $item,
                        'part' => $part,
                        'issue' => 'low_stock_warning',
                        'available' => $availableStock,
                        'min_level' => $part->min_stock_level,
                        'message' => "تحذير: مخزون منخفض لـ {$part->name}"
                    ];
                }
            }
        }

        return [
            'has_issues' => !empty($stockIssues),
            'issues' => $stockIssues,
            'can_proceed' => !collect($stockIssues)->contains('issue', 'insufficient_stock')
        ];
    }

    /**
     * Reserve stock for pending sale
     */
    public function reserveStock(Sale $sale)
    {
        DB::beginTransaction();
        try {
            $reservations = [];

            foreach ($sale->items as $item) {
                if ($item->affects_inventory && $item->part_id) {
                    $part = $item->part;
                    
                    if ($part) {
                        // Create reservation record
                        $reservation = [
                            'part_id' => $part->id,
                            'sale_id' => $sale->id,
                            'quantity' => $item->quantity,
                            'reserved_at' => now(),
                            'expires_at' => now()->addHours(24), // Reservation expires in 24 hours
                        ];

                        // Update part's reserved quantity
                        $part->increment('reserved_quantity', $item->quantity);
                        
                        $reservations[] = $reservation;

                        Log::info("Stock reserved", [
                            'part_id' => $part->id,
                            'part_name' => $part->name,
                            'quantity' => $item->quantity,
                            'sale_id' => $sale->id
                        ]);
                    }
                }
            }

            // Store reservations in sale metadata
            $sale->update([
                'metadata' => array_merge($sale->metadata ?? [], [
                    'stock_reservations' => $reservations
                ])
            ]);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error("Failed to reserve stock", [
                'sale_id' => $sale->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Release stock reservations
     */
    public function releaseReservations(Sale $sale)
    {
        DB::beginTransaction();
        try {
            $reservations = $sale->metadata['stock_reservations'] ?? [];

            foreach ($reservations as $reservation) {
                $part = Part::find($reservation['part_id']);
                if ($part) {
                    $part->decrement('reserved_quantity', $reservation['quantity']);
                    
                    Log::info("Stock reservation released", [
                        'part_id' => $part->id,
                        'part_name' => $part->name,
                        'quantity' => $reservation['quantity'],
                        'sale_id' => $sale->id
                    ]);
                }
            }

            // Clear reservations from metadata
            $metadata = $sale->metadata ?? [];
            unset($metadata['stock_reservations']);
            $sale->update(['metadata' => $metadata]);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error("Failed to release stock reservations", [
                'sale_id' => $sale->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Process inventory movements for completed sale
     */
    public function processInventoryMovements(Sale $sale)
    {
        DB::beginTransaction();
        try {
            foreach ($sale->items as $item) {
                if ($item->affects_inventory && $item->part_id) {
                    $part = $item->part;
                    
                    if ($part) {
                        // Reduce actual stock
                        $part->decrement('stock_quantity', $item->quantity);
                        
                        // Reduce reserved quantity if it was reserved
                        if ($part->reserved_quantity >= $item->quantity) {
                            $part->decrement('reserved_quantity', $item->quantity);
                        }

                        // Create inventory movement record
                        $this->createInventoryMovement([
                            'part_id' => $part->id,
                            'type' => 'sale',
                            'quantity' => -$item->quantity,
                            'reference_type' => 'sale',
                            'reference_id' => $sale->id,
                            'location_id' => $sale->location_id,
                            'unit_cost' => $part->cost_price,
                            'total_cost' => $part->cost_price * $item->quantity,
                            'notes' => "Sale: {$sale->sale_number}",
                            'processed_by' => $sale->user_id,
                        ]);

                        // Update item with inventory movement details
                        $item->update([
                            'inventory_movements' => [
                                'part_id' => $part->id,
                                'quantity_reduced' => $item->quantity,
                                'previous_stock' => $part->stock_quantity + $item->quantity,
                                'new_stock' => $part->stock_quantity,
                                'movement_date' => now(),
                                'sale_id' => $sale->id,
                            ]
                        ]);

                        Log::info("Inventory movement processed", [
                            'part_id' => $part->id,
                            'part_name' => $part->name,
                            'quantity' => $item->quantity,
                            'new_stock' => $part->stock_quantity,
                            'sale_id' => $sale->id
                        ]);
                    }
                }
            }

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error("Failed to process inventory movements", [
                'sale_id' => $sale->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Reverse inventory movements for cancelled/refunded sale
     */
    public function reverseInventoryMovements(Sale $sale)
    {
        DB::beginTransaction();
        try {
            foreach ($sale->items as $item) {
                if ($item->affects_inventory && $item->part_id) {
                    $part = $item->part;
                    
                    if ($part) {
                        // Restore stock
                        $part->increment('stock_quantity', $item->quantity);

                        // Create reverse inventory movement record
                        $this->createInventoryMovement([
                            'part_id' => $part->id,
                            'type' => 'return',
                            'quantity' => $item->quantity,
                            'reference_type' => 'sale_return',
                            'reference_id' => $sale->id,
                            'location_id' => $sale->location_id,
                            'unit_cost' => $part->cost_price,
                            'total_cost' => $part->cost_price * $item->quantity,
                            'notes' => "Sale return: {$sale->sale_number}",
                            'processed_by' => auth()->id(),
                        ]);

                        Log::info("Inventory movement reversed", [
                            'part_id' => $part->id,
                            'part_name' => $part->name,
                            'quantity' => $item->quantity,
                            'new_stock' => $part->stock_quantity,
                            'sale_id' => $sale->id
                        ]);
                    }
                }
            }

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error("Failed to reverse inventory movements", [
                'sale_id' => $sale->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get available stock for a part at specific location
     */
    public function getAvailableStock(Part $part, $locationId = null)
    {
        // For now, return the global stock
        // In future, this could be enhanced for location-specific inventory
        return max(0, $part->stock_quantity - $part->reserved_quantity);
    }

    /**
     * Get low stock alerts
     */
    public function getLowStockAlerts($locationId = null)
    {
        $query = Part::where('stock_quantity', '<=', DB::raw('min_stock_level'))
                    ->where('is_active', true);

        // Add location filter if needed
        if ($locationId) {
            // Future enhancement for location-specific inventory
        }

        return $query->get()->map(function ($part) {
            return [
                'part' => $part,
                'current_stock' => $part->stock_quantity,
                'min_level' => $part->min_stock_level,
                'shortage' => $part->min_stock_level - $part->stock_quantity,
                'severity' => $this->getStockSeverity($part),
            ];
        });
    }

    /**
     * Get stock movement summary
     */
    public function getStockMovementSummary($startDate = null, $endDate = null, $locationId = null)
    {
        $startDate = $startDate ?? now()->startOfMonth();
        $endDate = $endDate ?? now()->endOfMonth();

        $sales = Sale::whereBetween('sale_date', [$startDate, $endDate])
                    ->where('status', 'completed');

        if ($locationId) {
            $sales->where('location_id', $locationId);
        }

        $totalSales = $sales->count();
        $totalRevenue = $sales->sum('total_amount');
        $totalItemsSold = $sales->with('items')->get()->sum(function ($sale) {
            return $sale->items->sum('quantity');
        });

        return [
            'period' => [
                'start' => $startDate,
                'end' => $endDate,
            ],
            'sales' => [
                'total_sales' => $totalSales,
                'total_revenue' => $totalRevenue,
                'total_items_sold' => $totalItemsSold,
                'average_sale_value' => $totalSales > 0 ? $totalRevenue / $totalSales : 0,
            ],
            'top_selling_parts' => $this->getTopSellingParts($startDate, $endDate, $locationId),
            'low_stock_alerts' => $this->getLowStockAlerts($locationId),
        ];
    }

    /**
     * Get top selling parts
     */
    private function getTopSellingParts($startDate, $endDate, $locationId = null, $limit = 10)
    {
        $query = SaleItem::join('sales', 'sale_items.sale_id', '=', 'sales.id')
                         ->join('parts', 'sale_items.part_id', '=', 'parts.id')
                         ->whereBetween('sales.sale_date', [$startDate, $endDate])
                         ->where('sales.status', 'completed')
                         ->where('sale_items.item_type', 'part');

        if ($locationId) {
            $query->where('sales.location_id', $locationId);
        }

        return $query->select(
                    'parts.id',
                    'parts.name',
                    'parts.part_number',
                    DB::raw('SUM(sale_items.quantity) as total_sold'),
                    DB::raw('SUM(sale_items.line_total) as total_revenue'),
                    DB::raw('COUNT(DISTINCT sales.id) as sales_count')
                )
                ->groupBy('parts.id', 'parts.name', 'parts.part_number')
                ->orderBy('total_sold', 'desc')
                ->limit($limit)
                ->get();
    }

    /**
     * Create inventory movement record
     */
    private function createInventoryMovement(array $data)
    {
        // Check if InventoryMovement model exists
        if (class_exists('App\Models\InventoryMovement')) {
            return \App\Models\InventoryMovement::create($data);
        }

        // Log the movement if model doesn't exist
        Log::info("Inventory movement (logged only)", $data);
        return null;
    }

    /**
     * Get stock severity level
     */
    private function getStockSeverity(Part $part)
    {
        $ratio = $part->stock_quantity / max(1, $part->min_stock_level);
        
        if ($ratio <= 0) {
            return 'critical'; // Out of stock
        } elseif ($ratio <= 0.5) {
            return 'high'; // Very low stock
        } elseif ($ratio <= 1) {
            return 'medium'; // Below minimum
        } else {
            return 'low'; // Above minimum but still low
        }
    }

    /**
     * Cleanup expired reservations
     */
    public function cleanupExpiredReservations()
    {
        $expiredSales = Sale::whereJsonContains('metadata->stock_reservations', function ($query) {
                return $query->where('expires_at', '<', now());
            })
            ->where('status', 'pending')
            ->get();

        foreach ($expiredSales as $sale) {
            $this->releaseReservations($sale);
            Log::info("Expired reservations cleaned up for sale: {$sale->sale_number}");
        }

        return $expiredSales->count();
    }
}
