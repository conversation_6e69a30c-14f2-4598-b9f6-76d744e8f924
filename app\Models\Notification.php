<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Notification extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'action_url',
        'action_text',
        'priority',
        'category',
        'channel',
        'status',
        'read_at',
        'sent_at',
        'scheduled_at',
        'expires_at',
        'related_type',
        'related_id',
        'template_id',
        'metadata',
        'delivery_status',
        'delivery_attempts',
        'last_attempt_at',
        'error_message',
        'created_by'
    ];

    protected $casts = [
        'data' => 'array',
        'metadata' => 'array',
        'read_at' => 'datetime',
        'sent_at' => 'datetime',
        'scheduled_at' => 'datetime',
        'expires_at' => 'datetime',
        'last_attempt_at' => 'datetime',
        'delivery_attempts' => 'integer'
    ];

    protected $dates = [
        'read_at',
        'sent_at',
        'scheduled_at',
        'expires_at',
        'last_attempt_at',
        'deleted_at'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function related()
    {
        return $this->morphTo();
    }

    public function template()
    {
        return $this->belongsTo(NotificationTemplate::class, 'template_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'pending' => ['class' => 'badge-warning', 'text' => 'في الانتظار'],
            'sent' => ['class' => 'badge-success', 'text' => 'مرسل'],
            'delivered' => ['class' => 'badge-info', 'text' => 'تم التسليم'],
            'read' => ['class' => 'badge-primary', 'text' => 'مقروء'],
            'failed' => ['class' => 'badge-danger', 'text' => 'فشل'],
            'expired' => ['class' => 'badge-secondary', 'text' => 'منتهي'],
            'cancelled' => ['class' => 'badge-dark', 'text' => 'ملغي']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getPriorityBadgeAttribute()
    {
        $priorities = [
            'low' => ['class' => 'badge-success', 'text' => 'منخفضة'],
            'normal' => ['class' => 'badge-info', 'text' => 'عادية'],
            'high' => ['class' => 'badge-warning', 'text' => 'عالية'],
            'urgent' => ['class' => 'badge-danger', 'text' => 'عاجلة']
        ];

        return $priorities[$this->priority] ?? ['class' => 'badge-info', 'text' => 'عادية'];
    }

    public function getChannelBadgeAttribute()
    {
        $channels = [
            'database' => ['class' => 'badge-primary', 'text' => 'النظام'],
            'email' => ['class' => 'badge-info', 'text' => 'بريد إلكتروني'],
            'sms' => ['class' => 'badge-success', 'text' => 'رسالة نصية'],
            'push' => ['class' => 'badge-warning', 'text' => 'إشعار فوري'],
            'slack' => ['class' => 'badge-secondary', 'text' => 'سلاك'],
            'webhook' => ['class' => 'badge-dark', 'text' => 'ويب هوك']
        ];

        return $channels[$this->channel] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getIsReadAttribute()
    {
        return !is_null($this->read_at);
    }

    public function getIsExpiredAttribute()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function getIsScheduledAttribute()
    {
        return $this->scheduled_at && $this->scheduled_at->isFuture();
    }

    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    // Scopes
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByChannel($query, $channel)
    {
        return $query->where('channel', $channel);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeScheduled($query)
    {
        return $query->where('scheduled_at', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    public function scopeActive($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    // Methods
    public function markAsRead()
    {
        if (!$this->is_read) {
            $this->update([
                'read_at' => now(),
                'status' => 'read'
            ]);
        }
    }

    public function markAsUnread()
    {
        $this->update([
            'read_at' => null,
            'status' => $this->sent_at ? 'sent' : 'pending'
        ]);
    }

    public function send()
    {
        if ($this->status !== 'pending') {
            return false;
        }

        try {
            $success = $this->deliverNotification();
            
            if ($success) {
                $this->update([
                    'status' => 'sent',
                    'sent_at' => now(),
                    'delivery_status' => 'delivered'
                ]);
            } else {
                $this->incrementDeliveryAttempts();
            }
            
            return $success;
        } catch (\Exception $e) {
            $this->handleDeliveryError($e->getMessage());
            return false;
        }
    }

    private function deliverNotification()
    {
        switch ($this->channel) {
            case 'email':
                return $this->sendEmail();
            case 'sms':
                return $this->sendSMS();
            case 'push':
                return $this->sendPushNotification();
            case 'slack':
                return $this->sendSlackMessage();
            case 'webhook':
                return $this->sendWebhook();
            case 'database':
            default:
                return true; // Database notifications are already stored
        }
    }

    private function sendEmail()
    {
        // Implementation for email sending
        // This would integrate with your email service
        return true;
    }

    private function sendSMS()
    {
        // Implementation for SMS sending
        // This would integrate with your SMS service
        return true;
    }

    private function sendPushNotification()
    {
        // Implementation for push notifications
        // This would integrate with your push notification service
        return true;
    }

    private function sendSlackMessage()
    {
        // Implementation for Slack messages
        // This would integrate with Slack API
        return true;
    }

    private function sendWebhook()
    {
        // Implementation for webhook delivery
        // This would make HTTP requests to external endpoints
        return true;
    }

    private function incrementDeliveryAttempts()
    {
        $this->increment('delivery_attempts');
        $this->update([
            'last_attempt_at' => now(),
            'status' => $this->delivery_attempts >= 3 ? 'failed' : 'pending'
        ]);
    }

    private function handleDeliveryError($errorMessage)
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'last_attempt_at' => now()
        ]);
        
        $this->increment('delivery_attempts');
    }

    public function retry()
    {
        if ($this->status === 'failed' && $this->delivery_attempts < 5) {
            $this->update([
                'status' => 'pending',
                'error_message' => null
            ]);
            
            return $this->send();
        }
        
        return false;
    }

    public function cancel()
    {
        if (in_array($this->status, ['pending', 'scheduled'])) {
            $this->update(['status' => 'cancelled']);
            return true;
        }
        
        return false;
    }

    public function schedule($dateTime)
    {
        $this->update([
            'scheduled_at' => $dateTime,
            'status' => 'pending'
        ]);
    }

    public function setExpiration($dateTime)
    {
        $this->update(['expires_at' => $dateTime]);
    }

    public static function createForUser($userId, $data)
    {
        return static::create(array_merge($data, [
            'user_id' => $userId,
            'status' => 'pending',
            'created_by' => auth()->id()
        ]));
    }

    public static function createForUsers($userIds, $data)
    {
        $notifications = [];
        
        foreach ($userIds as $userId) {
            $notifications[] = static::createForUser($userId, $data);
        }
        
        return $notifications;
    }

    public static function broadcast($data, $criteria = [])
    {
        $query = User::query();
        
        // Apply criteria filters
        if (isset($criteria['role'])) {
            $query->where('role', $criteria['role']);
        }
        
        if (isset($criteria['department'])) {
            $query->where('department', $criteria['department']);
        }
        
        $users = $query->get();
        
        return static::createForUsers($users->pluck('id')->toArray(), $data);
    }

    public static function sendScheduledNotifications()
    {
        $notifications = static::where('status', 'pending')
                               ->where('scheduled_at', '<=', now())
                               ->get();
        
        $sent = 0;
        
        foreach ($notifications as $notification) {
            if ($notification->send()) {
                $sent++;
            }
        }
        
        return $sent;
    }

    public static function cleanupExpiredNotifications()
    {
        $expired = static::where('expires_at', '<', now())
                         ->where('status', '!=', 'expired')
                         ->get();
        
        foreach ($expired as $notification) {
            $notification->update(['status' => 'expired']);
        }
        
        return $expired->count();
    }

    public static function getUnreadCountForUser($userId)
    {
        return static::where('user_id', $userId)
                    ->unread()
                    ->active()
                    ->count();
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($notification) {
            if (!$notification->priority) {
                $notification->priority = 'normal';
            }
            
            if (!$notification->channel) {
                $notification->channel = 'database';
            }
            
            if (!$notification->status) {
                $notification->status = 'pending';
            }
            
            $notification->created_by = auth()->id();
        });
    }
}
