-- بيانات تجريبية بسيطة لاختبار نظام نقطة البيع

-- إدراج مستخدم افتراضي
INSERT IGNORE INTO `users` (`id`, `name`, `email`, `password`, `created_at`, `updated_at`) 
VALUES (1, 'مدير النظام', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW());

-- إدراج عميل واحد للاختبار
INSERT IGNORE INTO `customers` (
    `id`, `customer_number`, `type`, `first_name`, `last_name`, 
    `email`, `phone`, `is_active`, `created_by`, `created_at`, `updated_at`
) VALUES (
    1, 'CUST-001', 'individual', 'أحمد', 'محمد', 
    '<EMAIL>', '0599123456', 1, 1, NOW(), NOW()
);

-- إدراج قطعة واحدة للاختبار
INSERT IGNORE INTO `parts` (
    `id`, `part_number`, `name`, `description`, `category`, `brand`, 
    `price`, `cost_price`, `stock_quantity`, `unit`, `is_active`, `created_at`, `updated_at`
) VALUES (
    1, 'TEST-001', 'قطعة تجريبية', 'قطعة للاختبار', 'test', 'Test Brand', 
    100.00, 50.00, 10, 'piece', 1, NOW(), NOW()
);

-- تحديث تسلسل الجداول
ALTER TABLE `users` AUTO_INCREMENT = 2;
ALTER TABLE `customers` AUTO_INCREMENT = 2;
ALTER TABLE `parts` AUTO_INCREMENT = 2;
