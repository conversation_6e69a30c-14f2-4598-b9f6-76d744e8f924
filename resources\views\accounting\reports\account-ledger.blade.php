@extends('layouts.main')

@section('title', 'دفتر الأستاذ - ' . $account->account_name)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">دفتر الأستاذ</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $account->account_code }} - {{ $account->account_name }}</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <button onclick="printReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-print mr-2"></i>
                طباعة
            </button>
            
            <button onclick="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير
            </button>
            
            <a href="{{ route('accounts.show', $account) }}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-eye mr-2"></i>
                تفاصيل الحساب
            </a>
            
            <a href="{{ route('accounting-reports.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" name="start_date" value="{{ $startDate }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" name="end_date" value="{{ $endDate }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع القيد</label>
                <select name="entry_type" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الأنواع</option>
                    <option value="sale" {{ request('entry_type') == 'sale' ? 'selected' : '' }}>مبيعات</option>
                    <option value="purchase" {{ request('entry_type') == 'purchase' ? 'selected' : '' }}>مشتريات</option>
                    <option value="payment" {{ request('entry_type') == 'payment' ? 'selected' : '' }}>مدفوعات</option>
                    <option value="expense" {{ request('entry_type') == 'expense' ? 'selected' : '' }}>مصروفات</option>
                    <option value="transfer" {{ request('entry_type') == 'transfer' ? 'selected' : '' }}>تحويلات</option>
                    <option value="manual" {{ request('entry_type') == 'manual' ? 'selected' : '' }}>قيود يدوية</option>
                </select>
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                <i class="fas fa-search mr-2"></i>
                تحديث
            </button>
        </form>
    </div>

    <!-- Account Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calculator text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">الرصيد الافتتاحي</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($openingBalance, 2) }}
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-plus text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المدين</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($totalDebits, 2) }}
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-minus text-red-600 dark:text-red-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الدائن</p>
                    <p class="text-xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($totalCredits, 2) }}
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-balance-scale text-purple-600 dark:text-purple-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">الرصيد الختامي</p>
                    <p class="text-xl font-bold {{ $closingBalance >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        {{ number_format($closingBalance, 2) }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Ledger Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden" id="ledger-report">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="text-center">
                <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100">دفتر الأستاذ</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $account->account_code }} - {{ $account->account_name }}</p>
                <p class="text-gray-500 dark:text-gray-500 text-sm">من {{ $startDate }} إلى {{ $endDate }}</p>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            @if($transactions->count() > 0)
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                التاريخ
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                رقم القيد
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                الوصف
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                المرجع
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                مدين
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                دائن
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                الرصيد
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <!-- Opening Balance Row -->
                        @if($openingBalance != 0)
                            <tr class="bg-blue-50 dark:bg-blue-900/20">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    {{ $startDate }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    -
                                </td>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900 dark:text-gray-100">
                                    الرصيد الافتتاحي
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    -
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    @if($account->balance_type === 'debit' && $openingBalance > 0)
                                        {{ number_format($openingBalance, 2) }}
                                    @else
                                        -
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    @if($account->balance_type === 'credit' && $openingBalance > 0)
                                        {{ number_format($openingBalance, 2) }}
                                    @else
                                        -
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                    {{ number_format($openingBalance, 2) }}
                                </td>
                            </tr>
                        @endif

                        @php $runningBalance = $openingBalance; @endphp
                        @foreach($transactions as $transaction)
                            @php
                                if ($account->balance_type === 'debit') {
                                    $runningBalance += $transaction->debit_amount - $transaction->credit_amount;
                                } else {
                                    $runningBalance += $transaction->credit_amount - $transaction->debit_amount;
                                }
                            @endphp
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    {{ $transaction->journalEntry->entry_date->format('Y-m-d') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <a href="{{ route('journal-entries.show', $transaction->journalEntry) }}" 
                                       class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                                        {{ $transaction->journalEntry->entry_number }}
                                    </a>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                    {{ $transaction->description ?: $transaction->journalEntry->description }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    @if($transaction->journalEntry->reference_type)
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                            {{ $transaction->journalEntry->reference_type }}
                                        </span>
                                    @else
                                        -
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    @if($transaction->debit_amount > 0)
                                        {{ number_format($transaction->debit_amount, 2) }}
                                    @else
                                        -
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    @if($transaction->credit_amount > 0)
                                        {{ number_format($transaction->credit_amount, 2) }}
                                    @else
                                        -
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium {{ $runningBalance >= 0 ? 'text-gray-900 dark:text-gray-100' : 'text-red-600 dark:text-red-400' }}">
                                    {{ number_format($runningBalance, 2) }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot class="bg-gray-50 dark:bg-gray-700">
                        <tr class="font-bold">
                            <td colspan="4" class="px-6 py-4 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                                الإجمالي
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-gray-100">
                                {{ number_format($totalDebits, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-gray-100">
                                {{ number_format($totalCredits, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold {{ $closingBalance >= 0 ? 'text-gray-900 dark:text-gray-100' : 'text-red-600 dark:text-red-400' }}">
                                {{ number_format($closingBalance, 2) }}
                            </td>
                        </tr>
                    </tfoot>
                </table>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-book text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد معاملات في هذه الفترة</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Pagination -->
    @if($transactions->hasPages())
        <div class="flex justify-center">
            {{ $transactions->appends(request()->query())->links() }}
        </div>
    @endif
</div>

@push('scripts')
<script>
function printReport() {
    window.print();
}

function exportReport() {
    alert('ميزة التصدير قيد التطوير');
}

// Print styles
const printStyles = `
    @media print {
        body * {
            visibility: hidden;
        }
        #ledger-report, #ledger-report * {
            visibility: visible;
        }
        #ledger-report {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none !important;
        }
    }
`;

// Add print styles to head
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
@endpush
@endsection
