/* ===================================
   التفاعلات المتقدمة والرسوم المتحركة
   Advanced Interactions & Animations JS
   =================================== */

// ===================================
// مدير الرسوم المتحركة
// ===================================
class AnimationManager {
    constructor() {
        this.observers = [];
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupClickEffects();
    }

    // مراقب التقاطع للرسوم المتحركة عند الظهور
    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateElement(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // مراقبة العناصر القابلة للرسوم المتحركة
        document.querySelectorAll('[data-animate]').forEach(el => {
            observer.observe(el);
        });

        this.observers.push(observer);
    }

    // إعداد رسوم التمرير
    setupScrollAnimations() {
        let ticking = false;

        const updateScrollAnimations = () => {
            const scrollY = window.scrollY;
            const windowHeight = window.innerHeight;

            // تأثير المنظر (Parallax)
            document.querySelectorAll('[data-parallax]').forEach(el => {
                const speed = parseFloat(el.dataset.parallax) || 0.5;
                const yPos = -(scrollY * speed);
                el.style.transform = `translateY(${yPos}px)`;
            });

            // تأثير التلاشي عند التمرير
            document.querySelectorAll('[data-fade-scroll]').forEach(el => {
                const rect = el.getBoundingClientRect();
                const opacity = Math.max(0, Math.min(1, 1 - (Math.abs(rect.top - windowHeight / 2) / windowHeight)));
                el.style.opacity = opacity;
            });

            ticking = false;
        };

        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollAnimations);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestScrollUpdate, { passive: true });
    }

    // إعداد تأثيرات التمرير
    setupHoverEffects() {
        // تأثير الموجة للأزرار
        document.querySelectorAll('.ripple').forEach(button => {
            button.addEventListener('click', this.createRipple.bind(this));
        });

        // تأثير التوهج
        document.querySelectorAll('.hover-glow').forEach(el => {
            el.addEventListener('mouseenter', () => {
                el.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4)';
            });
            el.addEventListener('mouseleave', () => {
                el.style.boxShadow = '';
            });
        });

        // تأثير الرفع
        document.querySelectorAll('.hover-lift').forEach(el => {
            el.addEventListener('mouseenter', () => {
                el.style.transform = 'translateY(-4px) scale(1.02)';
            });
            el.addEventListener('mouseleave', () => {
                el.style.transform = '';
            });
        });
    }

    // إعداد تأثيرات النقر
    setupClickEffects() {
        // تأثير الضغط
        document.querySelectorAll('.active-scale').forEach(el => {
            el.addEventListener('mousedown', () => {
                el.style.transform = 'scale(0.95)';
            });
            el.addEventListener('mouseup', () => {
                el.style.transform = '';
            });
            el.addEventListener('mouseleave', () => {
                el.style.transform = '';
            });
        });
    }

    // إنشاء تأثير الموجة
    createRipple(event) {
        const button = event.currentTarget;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        `;

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // تحريك عنصر
    animateElement(element) {
        const animationType = element.dataset.animate;
        const delay = element.dataset.delay || 0;

        setTimeout(() => {
            element.classList.add(`animate-${animationType}`);
        }, delay);
    }

    // تنظيف المراقبين
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers = [];
    }
}

// ===================================
// مدير الإشعارات
// ===================================
class NotificationManager {
    constructor() {
        this.container = this.createContainer();
        this.notifications = [];
    }

    createContainer() {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            pointer-events: none;
        `;
        document.body.appendChild(container);
        return container;
    }

    show(message, type = 'info', duration = 3000) {
        const notification = this.createNotification(message, type);
        this.container.appendChild(notification);
        this.notifications.push(notification);

        // إضافة تأثير الدخول
        setTimeout(() => {
            notification.classList.add('animate-slide-in-down');
        }, 10);

        // إزالة الإشعار بعد المدة المحددة
        setTimeout(() => {
            this.remove(notification);
        }, duration);

        return notification;
    }

    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type}`;
        notification.style.cssText = `
            min-width: 300px;
            margin-bottom: 10px;
            pointer-events: auto;
            cursor: pointer;
        `;
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button class="ml-2 text-current opacity-70 hover:opacity-100">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;

        // إضافة مستمع للإغلاق
        notification.querySelector('button').addEventListener('click', () => {
            this.remove(notification);
        });

        return notification;
    }

    remove(notification) {
        notification.classList.remove('animate-slide-in-down');
        notification.classList.add('animate-slide-out-up');

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }

    clear() {
        this.notifications.forEach(notification => this.remove(notification));
    }
}

// ===================================
// مدير التحميل
// ===================================
class LoadingManager {
    constructor() {
        this.overlay = this.createOverlay();
        this.isLoading = false;
    }

    createOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        `;

        overlay.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center gap-3">
                <div class="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                <span class="text-gray-700 dark:text-gray-300">جاري التحميل...</span>
            </div>
        `;

        document.body.appendChild(overlay);
        return overlay;
    }

    show(message = 'جاري التحميل...') {
        if (this.isLoading) return;

        this.isLoading = true;
        this.overlay.querySelector('span').textContent = message;
        this.overlay.style.opacity = '1';
        this.overlay.style.visibility = 'visible';
    }

    hide() {
        if (!this.isLoading) return;

        this.isLoading = false;
        this.overlay.style.opacity = '0';
        this.overlay.style.visibility = 'hidden';
    }
}

// ===================================
// مدير النماذج المنبثقة
// ===================================
class ModalManager {
    constructor() {
        this.modals = new Map();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // إغلاق النماذج بالضغط على Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAll();
            }
        });

        // إغلاق النماذج بالنقر خارجها
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.close(e.target.dataset.modalId);
            }
        });
    }

    create(id, content, options = {}) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.dataset.modalId = id;
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        `;

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        modalContent.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: ${options.maxWidth || '500px'};
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        `;

        modalContent.innerHTML = content;
        modal.appendChild(modalContent);
        document.body.appendChild(modal);

        this.modals.set(id, modal);
        return modal;
    }

    show(id) {
        const modal = this.modals.get(id);
        if (!modal) return;

        modal.style.opacity = '1';
        modal.style.visibility = 'visible';
        modal.querySelector('.modal-content').style.transform = 'scale(1)';
    }

    close(id) {
        const modal = this.modals.get(id);
        if (!modal) return;

        modal.style.opacity = '0';
        modal.style.visibility = 'hidden';
        modal.querySelector('.modal-content').style.transform = 'scale(0.9)';
    }

    closeAll() {
        this.modals.forEach((modal, id) => {
            this.close(id);
        });
    }

    destroy(id) {
        const modal = this.modals.get(id);
        if (modal && modal.parentNode) {
            modal.parentNode.removeChild(modal);
            this.modals.delete(id);
        }
    }
}

// ===================================
// تهيئة المدراء العامين
// ===================================
let animationManager;
let notificationManager;
let loadingManager;
let modalManager;

document.addEventListener('DOMContentLoaded', () => {
    animationManager = new AnimationManager();
    notificationManager = new NotificationManager();
    loadingManager = new LoadingManager();
    modalManager = new ModalManager();

    // إضافة CSS للرسوم المتحركة
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});

// تصدير المدراء للاستخدام العام
window.AnimationManager = AnimationManager;
window.NotificationManager = NotificationManager;
window.LoadingManager = LoadingManager;
window.ModalManager = ModalManager;

// متغيرات عامة للوصول السريع
window.showNotification = (message, type, duration) => {
    return notificationManager?.show(message, type, duration);
};

window.showLoading = (message) => {
    loadingManager?.show(message);
};

window.hideLoading = () => {
    loadingManager?.hide();
};

window.showModal = (id) => {
    modalManager?.show(id);
};

window.closeModal = (id) => {
    modalManager?.close(id);
};
