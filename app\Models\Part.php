<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Part extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'part_number',
        'name',
        'description',
        'category_id',
        'supplier_id',
        'brand',
        'model',
        'compatibility',
        'part_type',
        'condition',
        'location',
        'shelf_location',
        'barcode',
        'sku',
        'cost_price',
        'selling_price',
        'markup_percentage',
        'stock_quantity',
        'minimum_stock',
        'maximum_stock',
        'reorder_point',
        'reorder_quantity',
        'unit_of_measure',
        'weight',
        'dimensions',
        'warranty_period',
        'warranty_type',
        'supplier_part_number',
        'manufacturer_part_number',
        'notes',
        'images',
        'specifications',
        'installation_instructions',
        'is_active',
        'is_serialized',
        'is_returnable',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'markup_percentage' => 'decimal:2',
        'stock_quantity' => 'integer',
        'minimum_stock' => 'integer',
        'maximum_stock' => 'integer',
        'reorder_point' => 'integer',
        'reorder_quantity' => 'integer',
        'weight' => 'decimal:3',
        'warranty_period' => 'integer',
        'compatibility' => 'array',
        'dimensions' => 'array',
        'images' => 'array',
        'specifications' => 'array',
        'installation_instructions' => 'array',
        'is_active' => 'boolean',
        'is_serialized' => 'boolean',
        'is_returnable' => 'boolean'
    ];

    protected $dates = [
        'deleted_at'
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function repairs()
    {
        return $this->belongsToMany(Repair::class, 'repair_parts')
            ->withPivot('quantity', 'unit_price', 'total_price')
            ->withTimestamps();
    }

    public function purchases()
    {
        return $this->belongsToMany(Purchase::class, 'purchase_parts')
            ->withPivot('quantity', 'unit_price', 'total_price')
            ->withTimestamps();
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function inventoryMovements()
    {
        return $this->hasMany(InventoryMovement::class);
    }

    public function serialNumbers()
    {
        return $this->hasMany(PartSerialNumber::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function warranties()
    {
        return $this->morphMany(Warranty::class, 'warrantable');
    }

    public function expenses()
    {
        return $this->morphMany(Expense::class, 'expensable');
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'related');
    }

    // Accessors
    public function getStockStatusAttribute()
    {
        if ($this->stock_quantity <= 0) {
            return ['class' => 'badge-danger', 'text' => 'نفد المخزون'];
        } elseif ($this->stock_quantity <= $this->reorder_point) {
            return ['class' => 'badge-warning', 'text' => 'مخزون منخفض'];
        } elseif ($this->stock_quantity <= $this->minimum_stock) {
            return ['class' => 'badge-info', 'text' => 'مخزون قليل'];
        } else {
            return ['class' => 'badge-success', 'text' => 'متوفر'];
        }
    }

    public function getStockStatusBadgeAttribute()
    {
        if ($this->stock_quantity <= 0) {
            return ['class' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200', 'text' => 'نفد المخزون'];
        } elseif ($this->stock_quantity <= $this->reorder_point) {
            return ['class' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200', 'text' => 'مخزون منخفض'];
        } elseif ($this->stock_quantity <= $this->minimum_stock) {
            return ['class' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200', 'text' => 'مخزون قليل'];
        } else {
            return ['class' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200', 'text' => 'متوفر'];
        }
    }

    public function getConditionBadgeAttribute()
    {
        $conditions = [
            'new' => ['class' => 'badge-success', 'text' => 'جديد'],
            'refurbished' => ['class' => 'badge-info', 'text' => 'مجدد'],
            'used' => ['class' => 'badge-warning', 'text' => 'مستعمل'],
            'damaged' => ['class' => 'badge-danger', 'text' => 'تالف']
        ];

        return $conditions[$this->condition] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getProfitMarginAttribute()
    {
        if ($this->cost_price > 0) {
            return (($this->selling_price - $this->cost_price) / $this->cost_price) * 100;
        }
        return 0;
    }

    public function getTotalValueAttribute()
    {
        return $this->stock_quantity * $this->cost_price;
    }

    public function getMainImageAttribute()
    {
        $images = $this->images ?? [];
        return !empty($images) ? asset('storage/' . $images[0]) : asset('images/no-image.png');
    }

    public function getFormattedDimensionsAttribute()
    {
        $dimensions = $this->dimensions ?? [];
        if (empty($dimensions)) {
            return 'غير محدد';
        }
        
        return sprintf('%s × %s × %s %s', 
            $dimensions['length'] ?? '0',
            $dimensions['width'] ?? '0', 
            $dimensions['height'] ?? '0',
            $dimensions['unit'] ?? 'سم'
        );
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeLowStock($query)
    {
        return $query->whereColumn('stock_quantity', '<=', 'reorder_point');
    }

    public function scopeOutOfStock($query)
    {
        return $query->where('stock_quantity', '<=', 0);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    public function scopeByBrand($query, $brand)
    {
        return $query->where('brand', $brand);
    }

    public function scopeCompatibleWith($query, $device)
    {
        return $query->whereJsonContains('compatibility', $device);
    }

    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('part_number', 'like', "%{$term}%")
              ->orWhere('description', 'like', "%{$term}%")
              ->orWhere('barcode', 'like', "%{$term}%")
              ->orWhere('sku', 'like', "%{$term}%");
        });
    }

    // Methods
    public function generatePartNumber()
    {
        $prefix = 'PRT';
        $category = $this->category ? strtoupper(substr($this->category->name, 0, 3)) : 'GEN';
        $year = now()->year;
        
        $lastPart = static::where('part_number', 'like', $prefix . $category . $year . '%')
                          ->orderBy('id', 'desc')
                          ->first();
        
        $sequence = $lastPart ? (int)substr($lastPart->part_number, -4) + 1 : 1;
        
        return $prefix . $category . $year . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function updateStock($quantity, $type = 'adjustment', $reason = null)
    {
        $oldQuantity = $this->stock_quantity;
        $newQuantity = $oldQuantity + $quantity;
        
        $this->update(['stock_quantity' => max(0, $newQuantity)]);
        
        // Record stock movement
        $this->stockMovements()->create([
            'type' => $type,
            'quantity' => $quantity,
            'old_quantity' => $oldQuantity,
            'new_quantity' => $this->stock_quantity,
            'reason' => $reason,
            'user_id' => auth()->id(),
            'created_at' => now()
        ]);
        
        return $this->stock_quantity;
    }

    public function addStock($quantity, $reason = null)
    {
        return $this->updateStock($quantity, 'in', $reason);
    }

    public function removeStock($quantity, $reason = null)
    {
        return $this->updateStock(-$quantity, 'out', $reason);
    }

    public function isLowStock()
    {
        return $this->stock_quantity <= $this->reorder_point;
    }

    public function isOutOfStock()
    {
        return $this->stock_quantity <= 0;
    }

    public function needsReorder()
    {
        return $this->stock_quantity <= $this->reorder_point && $this->is_active;
    }

    public function calculateSellingPrice($markupPercentage = null)
    {
        $markup = $markupPercentage ?? $this->markup_percentage ?? 0;
        return $this->cost_price * (1 + ($markup / 100));
    }

    public function updatePricing($costPrice, $markupPercentage = null)
    {
        $markup = $markupPercentage ?? $this->markup_percentage;
        $sellingPrice = $costPrice * (1 + ($markup / 100));
        
        $this->update([
            'cost_price' => $costPrice,
            'markup_percentage' => $markup,
            'selling_price' => $sellingPrice
        ]);
        
        return $sellingPrice;
    }

    public function isCompatibleWith($deviceType, $deviceBrand = null, $deviceModel = null)
    {
        $compatibility = $this->compatibility ?? [];
        
        if (empty($compatibility)) {
            return true; // Universal part
        }
        
        foreach ($compatibility as $compat) {
            if ($compat['type'] === $deviceType) {
                if (!$deviceBrand || $compat['brand'] === $deviceBrand) {
                    if (!$deviceModel || in_array($deviceModel, $compat['models'] ?? [])) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }

    public function addCompatibility($deviceType, $deviceBrand, $deviceModels = [])
    {
        $compatibility = $this->compatibility ?? [];
        
        $compatibility[] = [
            'type' => $deviceType,
            'brand' => $deviceBrand,
            'models' => is_array($deviceModels) ? $deviceModels : [$deviceModels]
        ];
        
        $this->update(['compatibility' => $compatibility]);
    }

    public function addImage($imagePath)
    {
        $images = $this->images ?? [];
        $images[] = $imagePath;
        
        $this->update(['images' => $images]);
    }

    public function removeImage($imagePath)
    {
        $images = $this->images ?? [];
        $images = array_filter($images, function($image) use ($imagePath) {
            return $image !== $imagePath;
        });
        
        $this->update(['images' => array_values($images)]);
    }

    public function getUsageHistory($days = 30)
    {
        return $this->repairs()
            ->where('created_at', '>=', now()->subDays($days))
            ->withPivot('quantity')
            ->get()
            ->sum('pivot.quantity');
    }

    public function getAverageUsage($days = 30)
    {
        $usage = $this->getUsageHistory($days);
        return $usage / $days;
    }

    public function predictReorderDate()
    {
        $averageUsage = $this->getAverageUsage();
        
        if ($averageUsage <= 0) {
            return null;
        }
        
        $daysUntilReorder = ($this->stock_quantity - $this->reorder_point) / $averageUsage;
        
        return $daysUntilReorder > 0 ? now()->addDays($daysUntilReorder) : now();
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($part) {
            if (!$part->part_number) {
                $part->part_number = $part->generatePartNumber();
            }
            $part->created_by = auth()->id();
        });
        
        static::updating(function ($part) {
            $part->updated_by = auth()->id();
        });
    }
}
