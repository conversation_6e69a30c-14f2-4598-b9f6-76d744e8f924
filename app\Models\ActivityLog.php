<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ActivityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'description',
        'model_type',
        'model_id',
        'ip_address',
        'user_agent',
        'properties'
    ];

    protected $casts = [
        'properties' => 'array'
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع النموذج المرتبط
     */
    public function model()
    {
        return $this->morphTo();
    }

    /**
     * scope للأنشطة الحديثة
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * scope للأنشطة حسب المستخدم
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * scope للأنشطة حسب النوع
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * الحصول على وصف النشاط مترجم
     */
    public function getActionNameAttribute()
    {
        $actions = [
            'login' => 'تسجيل دخول',
            'logout' => 'تسجيل خروج',
            'created' => 'إنشاء',
            'updated' => 'تحديث',
            'deleted' => 'حذف',
            'viewed' => 'عرض',
            'location.created' => 'إنشاء موقع',
            'location.updated' => 'تحديث موقع',
            'location.deleted' => 'حذف موقع',
            'location.invoice_settings_updated' => 'تحديث إعدادات الفاتورة',
            'location.pos_settings_updated' => 'تحديث إعدادات نقاط البيع',
            'product.created' => 'إنشاء منتج',
            'product.updated' => 'تحديث منتج',
            'product.deleted' => 'حذف منتج',
            'customer.created' => 'إنشاء عميل',
            'customer.updated' => 'تحديث عميل',
            'customer.deleted' => 'حذف عميل',
            'supplier.created' => 'إنشاء مورد',
            'supplier.updated' => 'تحديث مورد',
            'supplier.deleted' => 'حذف مورد',
            'sale.created' => 'إنشاء مبيعة',
            'sale.updated' => 'تحديث مبيعة',
            'sale.deleted' => 'حذف مبيعة',
            'purchase.created' => 'إنشاء مشترى',
            'purchase.updated' => 'تحديث مشترى',
            'purchase.deleted' => 'حذف مشترى',
            'maintenance.created' => 'إنشاء طلب صيانة',
            'maintenance.updated' => 'تحديث طلب صيانة',
            'maintenance.deleted' => 'حذف طلب صيانة',
            'maintenance.assigned' => 'تعيين فني للصيانة',
            'inventory.adjusted' => 'تعديل المخزون',
            'inventory.transferred' => 'نقل المخزون',
            'expense.created' => 'إنشاء مصروف',
            'expense.updated' => 'تحديث مصروف',
            'expense.deleted' => 'حذف مصروف',
            'user.created' => 'إنشاء مستخدم',
            'user.updated' => 'تحديث مستخدم',
            'user.deleted' => 'حذف مستخدم',
            'role.created' => 'إنشاء دور',
            'role.updated' => 'تحديث دور',
            'role.deleted' => 'حذف دور',
            'settings.updated' => 'تحديث الإعدادات',
            'backup.created' => 'إنشاء نسخة احتياطية',
            'backup.restored' => 'استعادة نسخة احتياطية'
        ];

        return $actions[$this->action] ?? $this->action;
    }

    /**
     * تسجيل نشاط جديد
     */
    public static function log($action, $description = null, $model = null, $properties = [])
    {
        return self::create([
            'user_id' => auth()->id(),
            'action' => $action,
            'description' => $description,
            'model_type' => $model ? get_class($model) : null,
            'model_id' => $model ? $model->id : null,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'properties' => $properties
        ]);
    }

    /**
     * الحصول على أحدث الأنشطة
     */
    public static function getRecentActivities($limit = 10)
    {
        return self::with('user')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * الحصول على أنشطة المستخدم
     */
    public static function getUserActivities($userId, $limit = 20)
    {
        return self::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * الحصول على إحصائيات الأنشطة
     */
    public static function getActivityStats($days = 30)
    {
        $startDate = now()->subDays($days);
        
        return [
            'total_activities' => self::where('created_at', '>=', $startDate)->count(),
            'unique_users' => self::where('created_at', '>=', $startDate)->distinct('user_id')->count(),
            'most_active_user' => self::where('created_at', '>=', $startDate)
                ->selectRaw('user_id, COUNT(*) as activity_count')
                ->groupBy('user_id')
                ->orderBy('activity_count', 'desc')
                ->with('user')
                ->first(),
            'top_actions' => self::where('created_at', '>=', $startDate)
                ->selectRaw('action, COUNT(*) as count')
                ->groupBy('action')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get()
        ];
    }

    /**
     * تنظيف الأنشطة القديمة
     */
    public static function cleanup($keepDays = 90)
    {
        $cutoffDate = now()->subDays($keepDays);
        return self::where('created_at', '<', $cutoffDate)->delete();
    }
}
