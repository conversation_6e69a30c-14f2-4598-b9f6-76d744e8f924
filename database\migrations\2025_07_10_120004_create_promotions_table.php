<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('promotions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique()->nullable(); // Coupon code
            $table->text('description')->nullable();
            
            // Promotion type and value
            $table->enum('type', ['percentage', 'fixed_amount', 'buy_x_get_y', 'free_shipping']);
            $table->decimal('value', 8, 2)->nullable(); // Percentage or fixed amount
            $table->decimal('max_discount', 8, 2)->nullable(); // Maximum discount for percentage
            $table->decimal('min_purchase', 8, 2)->nullable(); // Minimum purchase amount
            
            // Buy X Get Y promotion
            $table->integer('buy_quantity')->nullable();
            $table->integer('get_quantity')->nullable();
            $table->json('applicable_items')->nullable(); // Which items this applies to
            
            // Validity and usage
            $table->boolean('is_active')->default(true);
            $table->date('start_date');
            $table->date('end_date');
            $table->integer('usage_limit')->nullable(); // Total usage limit
            $table->integer('usage_limit_per_customer')->nullable();
            $table->integer('used_count')->default(0);
            
            // Conditions
            $table->json('conditions')->nullable(); // Additional conditions
            $table->json('applicable_locations')->nullable(); // Which locations
            $table->json('applicable_customer_types')->nullable(); // Customer segments
            
            // Created by
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            
            $table->timestamps();
            
            // Indexes
            $table->index(['is_active', 'start_date', 'end_date']);
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotions');
    }
};
