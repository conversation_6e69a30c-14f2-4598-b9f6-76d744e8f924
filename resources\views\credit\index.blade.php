@extends('layouts.main')

@section('title', 'إدارة الائتمان والمستحقات')

@section('content')
<div class="space-y-6" x-data="creditManager()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إدارة الائتمان والمستحقات</h1>
            <p class="text-gray-600 dark:text-gray-400">متابعة حدود الائتمان والمستحقات المالية</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="sendReminders()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                إرسال تذكيرات
            </button>
            <button @click="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير التقرير
            </button>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المستحقات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.totalOutstanding">₪25,750</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متأخرات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.overdue">₪8,500</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">عملاء بمستحقات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.customersWithDebt">12</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط فترة التحصيل</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.avgCollectionDays">18 يوم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <input type="text" x-model="filters.search" @input="filterData()" placeholder="اسم العميل..." 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة المستحقات</label>
                <select x-model="filters.status" @change="filterData()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="current">جارية</option>
                    <option value="overdue">متأخرة</option>
                    <option value="critical">حرجة</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ من</label>
                <input type="number" x-model="filters.amountFrom" @input="filterData()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المبلغ إلى</label>
                <input type="number" x-model="filters.amountTo" @input="filterData()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
        </div>
    </div>

    <!-- Credit Limits Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">حدود الائتمان والمستحقات</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">حد الائتمان</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المستحق</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المتاح</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">أقدم مستحق</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="customer in filteredCustomers" :key="customer.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" :src="`https://ui-avatars.com/api/?name=${encodeURIComponent(customer.name)}&background=3B82F6&color=fff&size=40`" :alt="customer.name">
                                    </div>
                                    <div class="mr-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="customer.name"></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400" x-text="customer.phone"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                ₪<span x-text="customer.credit_limit.toLocaleString()"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"
                                :class="customer.outstanding > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'">
                                ₪<span x-text="customer.outstanding.toLocaleString()"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                ₪<span x-text="(customer.credit_limit - customer.outstanding).toLocaleString()"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="customer.oldest_debt_days + ' يوم'"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': customer.status === 'current',
                                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': customer.status === 'overdue',
                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': customer.status === 'critical'
                                      }"
                                      x-text="getStatusText(customer.status)">
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button @click="viewCustomer(customer.id)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400">عرض</button>
                                    <button @click="adjustCredit(customer.id)" class="text-green-600 hover:text-green-900 dark:text-green-400">تعديل الحد</button>
                                    <button @click="sendReminder(customer.id)" x-show="customer.outstanding > 0" class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400">تذكير</button>
                                    <button @click="blockCredit(customer.id)" x-show="customer.status === 'critical'" class="text-red-600 hover:text-red-900 dark:text-red-400">حظر</button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Outstanding Invoices -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الفواتير المستحقة</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">رقم الفاتورة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تاريخ الفاتورة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تاريخ الاستحقاق</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المبلغ</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">أيام التأخير</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="invoice in outstandingInvoices" :key="invoice.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100" x-text="invoice.number"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="invoice.customer_name"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="invoice.invoice_date"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="invoice.due_date"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">₪<span x-text="invoice.amount.toLocaleString()"></span></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm"
                                :class="invoice.days_overdue > 30 ? 'text-red-600 dark:text-red-400' : invoice.days_overdue > 0 ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400'"
                                x-text="invoice.days_overdue > 0 ? invoice.days_overdue + ' يوم' : 'في الموعد'"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button @click="viewInvoice(invoice.id)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400">عرض</button>
                                    <button @click="recordPayment(invoice.id)" class="text-green-600 hover:text-green-900 dark:text-green-400">تسجيل دفعة</button>
                                    <button @click="sendInvoiceReminder(invoice.id)" class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400">تذكير</button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
function creditManager() {
    return {
        customers: [
            {
                id: 1,
                name: 'أحمد محمد علي',
                phone: '0599123456',
                credit_limit: 10000,
                outstanding: 2500,
                oldest_debt_days: 15,
                status: 'current'
            },
            {
                id: 2,
                name: 'سارة أحمد خالد',
                phone: '0598765432',
                credit_limit: 5000,
                outstanding: 3200,
                oldest_debt_days: 45,
                status: 'overdue'
            },
            {
                id: 3,
                name: 'محمد عبدالله حسن',
                phone: '0597654321',
                credit_limit: 15000,
                outstanding: 8500,
                oldest_debt_days: 75,
                status: 'critical'
            }
        ],
        outstandingInvoices: [
            {
                id: 1,
                number: 'INV-001',
                customer_name: 'أحمد محمد علي',
                invoice_date: '2024-06-15',
                due_date: '2024-07-15',
                amount: 2500,
                days_overdue: 0
            },
            {
                id: 2,
                number: 'INV-002',
                customer_name: 'سارة أحمد خالد',
                invoice_date: '2024-05-20',
                due_date: '2024-06-20',
                amount: 3200,
                days_overdue: 19
            }
        ],
        filteredCustomers: [],
        filters: {
            search: '',
            status: '',
            amountFrom: '',
            amountTo: ''
        },
        summary: {
            totalOutstanding: '₪25,750',
            overdue: '₪8,500',
            customersWithDebt: 12,
            avgCollectionDays: '18 يوم'
        },

        init() {
            this.filteredCustomers = [...this.customers];
        },

        filterData() {
            this.filteredCustomers = this.customers.filter(customer => {
                const matchesSearch = !this.filters.search || 
                    customer.name.toLowerCase().includes(this.filters.search.toLowerCase());
                
                const matchesStatus = !this.filters.status || customer.status === this.filters.status;
                
                const matchesAmountFrom = !this.filters.amountFrom || customer.outstanding >= this.filters.amountFrom;
                const matchesAmountTo = !this.filters.amountTo || customer.outstanding <= this.filters.amountTo;

                return matchesSearch && matchesStatus && matchesAmountFrom && matchesAmountTo;
            });
        },

        getStatusText(status) {
            const statuses = {
                'current': 'جارية',
                'overdue': 'متأخرة',
                'critical': 'حرجة'
            };
            return statuses[status] || status;
        },

        viewCustomer(id) {
            window.location.href = `/customers/${id}`;
        },

        adjustCredit(id) {
            const newLimit = prompt('أدخل حد الائتمان الجديد:');
            if (newLimit && !isNaN(newLimit)) {
                const customer = this.customers.find(c => c.id === id);
                if (customer) {
                    customer.credit_limit = parseFloat(newLimit);
                    alert('تم تحديث حد الائتمان بنجاح');
                }
            }
        },

        sendReminder(id) {
            alert(`تم إرسال تذكير للعميل رقم ${id}`);
        },

        blockCredit(id) {
            if (confirm('هل أنت متأكد من حظر الائتمان لهذا العميل؟')) {
                alert(`تم حظر الائتمان للعميل رقم ${id}`);
            }
        },

        sendReminders() {
            alert('تم إرسال تذكيرات لجميع العملاء المتأخرين');
        },

        exportReport() {
            alert('سيتم تصدير تقرير الائتمان والمستحقات');
        },

        viewInvoice(id) {
            window.location.href = `/sales/${id}`;
        },

        recordPayment(id) {
            alert(`تسجيل دفعة للفاتورة ${id}`);
        },

        sendInvoiceReminder(id) {
            alert(`تم إرسال تذكير للفاتورة ${id}`);
        }
    }
}
</script>
@endpush
@endsection
