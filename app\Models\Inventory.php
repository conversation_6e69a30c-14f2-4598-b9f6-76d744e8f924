<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Inventory extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'location_id',
        'quantity',
        'cost_price',
        'selling_price',
        'expiry_date',
        'batch_number',
        'notes'
    ];

    protected $casts = [
        'quantity' => 'integer',
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'expiry_date' => 'date'
    ];

    /**
     * العلاقة مع المنتج
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * العلاقة مع الموقع
     */
    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * التحقق من انتهاء الصلاحية
     */
    public function getIsExpiredAttribute()
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * التحقق من قرب انتهاء الصلاحية
     */
    public function getIsNearExpiryAttribute()
    {
        if (!$this->expiry_date) {
            return false;
        }
        
        return $this->expiry_date->diffInDays(now()) <= 30;
    }

    /**
     * scope للمنتجات منخفضة المخزون
     */
    public function scopeLowStock($query)
    {
        return $query->whereRaw('quantity <= (SELECT min_stock_level FROM products WHERE products.id = inventories.product_id)');
    }

    /**
     * scope للمنتجات نافدة المخزون
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('quantity', '<=', 0);
    }

    /**
     * scope للمنتجات منتهية الصلاحية
     */
    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    /**
     * scope للمنتجات قريبة انتهاء الصلاحية
     */
    public function scopeNearExpiry($query, $days = 30)
    {
        return $query->where('expiry_date', '<=', now()->addDays($days))
                    ->where('expiry_date', '>', now());
    }

    /**
     * الحصول على إجمالي قيمة المخزون
     */
    public function getTotalValueAttribute()
    {
        return $this->quantity * $this->cost_price;
    }

    /**
     * تحديث المخزون
     */
    public function adjustQuantity($newQuantity, $reason = null)
    {
        $oldQuantity = $this->quantity;
        $this->update(['quantity' => $newQuantity]);
        
        // تسجيل حركة المخزون
        InventoryMovement::create([
            'inventory_id' => $this->id,
            'type' => $newQuantity > $oldQuantity ? 'in' : 'out',
            'quantity' => abs($newQuantity - $oldQuantity),
            'reason' => $reason ?? 'تعديل يدوي',
            'user_id' => auth()->id()
        ]);
        
        return $this;
    }

    /**
     * إضافة كمية للمخزون
     */
    public function addStock($quantity, $reason = 'إضافة مخزون')
    {
        return $this->adjustQuantity($this->quantity + $quantity, $reason);
    }

    /**
     * خصم كمية من المخزون
     */
    public function removeStock($quantity, $reason = 'خصم مخزون')
    {
        $newQuantity = max(0, $this->quantity - $quantity);
        return $this->adjustQuantity($newQuantity, $reason);
    }

    /**
     * نقل المخزون إلى موقع آخر
     */
    public function transferTo(Location $targetLocation, $quantity, $reason = 'نقل مخزون')
    {
        if ($quantity > $this->quantity) {
            throw new \Exception('الكمية المطلوب نقلها أكبر من المتاح في المخزون');
        }

        // خصم من الموقع الحالي
        $this->removeStock($quantity, $reason);

        // إضافة للموقع الهدف
        $targetInventory = self::firstOrCreate([
            'product_id' => $this->product_id,
            'location_id' => $targetLocation->id
        ], [
            'quantity' => 0,
            'cost_price' => $this->cost_price,
            'selling_price' => $this->selling_price
        ]);

        $targetInventory->addStock($quantity, $reason);

        return $targetInventory;
    }

    /**
     * الحصول على إجمالي المخزون لمنتج معين
     */
    public static function getTotalStock($productId)
    {
        return self::where('product_id', $productId)->sum('quantity');
    }

    /**
     * الحصول على المخزون في موقع معين
     */
    public static function getStockInLocation($productId, $locationId)
    {
        return self::where('product_id', $productId)
                  ->where('location_id', $locationId)
                  ->value('quantity') ?? 0;
    }

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    public static function getLowStockProducts($locationId = null)
    {
        $query = self::with(['product', 'location'])
                    ->lowStock();

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        return $query->get();
    }

    /**
     * الحصول على المنتجات منتهية الصلاحية
     */
    public static function getExpiredProducts($locationId = null)
    {
        $query = self::with(['product', 'location'])
                    ->expired()
                    ->where('quantity', '>', 0);

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        return $query->get();
    }
}
