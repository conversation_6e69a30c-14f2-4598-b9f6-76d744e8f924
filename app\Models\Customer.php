<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'customer_number',
        'type',
        'first_name',
        'last_name',
        'company_name',
        'email',
        'phone',
        'mobile',
        'whatsapp',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'date_of_birth',
        'gender',
        'national_id',
        'tax_number',
        'credit_limit',
        'payment_terms',
        'discount_percentage',
        'preferred_contact_method',
        'language_preference',
        'notes',
        'tags',
        'source',
        'referral_source',
        'marketing_consent',
        'sms_consent',
        'email_consent',
        'is_vip',
        'is_active',
        'last_contact_date',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'credit_limit' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'tags' => 'array',
        'marketing_consent' => 'boolean',
        'sms_consent' => 'boolean',
        'email_consent' => 'boolean',
        'is_vip' => 'boolean',
        'is_active' => 'boolean',
        'last_contact_date' => 'datetime'
    ];

    protected $dates = [
        'date_of_birth',
        'last_contact_date',
        'deleted_at'
    ];

    // Relationships
    public function repairs()
    {
        return $this->hasMany(Repair::class);
    }

    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function communications()
    {
        return $this->hasMany(CustomerCommunication::class);
    }

    public function devices()
    {
        return $this->hasMany(CustomerDevice::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function warranties()
    {
        return $this->hasMany(Warranty::class);
    }

    public function expenses()
    {
        return $this->morphMany(Expense::class, 'expensable');
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'related');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'customer_categories');
    }

    // Accessors
    public function getFullNameAttribute()
    {
        if ($this->type === 'company') {
            return $this->company_name;
        }
        
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function getDisplayNameAttribute()
    {
        return $this->full_name;
    }

    public function getAvatarAttribute()
    {
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->full_name) . '&background=10B981&color=fff&size=100';
    }

    public function getTypeBadgeAttribute()
    {
        $types = [
            'individual' => ['class' => 'badge-info', 'text' => 'فرد'],
            'company' => ['class' => 'badge-primary', 'text' => 'شركة']
        ];

        return $types[$this->type] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getStatusBadgeAttribute()
    {
        if ($this->is_vip) {
            return ['class' => 'badge-warning', 'text' => 'VIP'];
        }
        
        if ($this->is_active) {
            return ['class' => 'badge-success', 'text' => 'نشط'];
        }
        
        return ['class' => 'badge-secondary', 'text' => 'غير نشط'];
    }

    public function getAgeAttribute()
    {
        if (!$this->date_of_birth) {
            return null;
        }
        
        return $this->date_of_birth->age;
    }

    public function getTotalSpentAttribute()
    {
        return $this->repairs()->sum('total_cost') + $this->sales()->sum('total_amount');
    }

    public function getOutstandingBalanceAttribute()
    {
        $totalOwed = $this->repairs()
            ->whereIn('payment_status', ['pending', 'partial'])
            ->sum('total_cost');
            
        $totalPaid = $this->payments()
            ->where('status', 'completed')
            ->sum('amount');
            
        return max(0, $totalOwed - $totalPaid);
    }

    public function getLifetimeValueAttribute()
    {
        return $this->total_spent;
    }

    public function getLastRepairDateAttribute()
    {
        $lastRepair = $this->repairs()->latest()->first();
        return $lastRepair ? $lastRepair->created_at : null;
    }

    public function getRepairCountAttribute()
    {
        return $this->repairs()->count();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeVip($query)
    {
        return $query->where('is_vip', true);
    }

    public function scopeIndividuals($query)
    {
        return $query->where('type', 'individual');
    }

    public function scopeCompanies($query)
    {
        return $query->where('type', 'company');
    }

    public function scopeWithOutstandingBalance($query)
    {
        return $query->whereHas('repairs', function ($q) {
            $q->whereIn('payment_status', ['pending', 'partial']);
        });
    }

    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('first_name', 'like', "%{$term}%")
              ->orWhere('last_name', 'like', "%{$term}%")
              ->orWhere('company_name', 'like', "%{$term}%")
              ->orWhere('email', 'like', "%{$term}%")
              ->orWhere('phone', 'like', "%{$term}%")
              ->orWhere('mobile', 'like', "%{$term}%")
              ->orWhere('customer_number', 'like', "%{$term}%");
        });
    }

    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }

    public function scopeBySource($query, $source)
    {
        return $query->where('source', $source);
    }

    public function scopeRecentlyActive($query, $days = 30)
    {
        return $query->where('last_contact_date', '>=', now()->subDays($days));
    }

    // Methods
    public function generateCustomerNumber()
    {
        $prefix = $this->type === 'company' ? 'COM' : 'CUS';
        $year = now()->year;
        
        $lastCustomer = static::where('type', $this->type)
                             ->whereYear('created_at', $year)
                             ->orderBy('id', 'desc')
                             ->first();
        
        $sequence = $lastCustomer ? (int)substr($lastCustomer->customer_number, -5) + 1 : 1;
        
        return $prefix . $year . str_pad($sequence, 5, '0', STR_PAD_LEFT);
    }

    public function updateLastContact()
    {
        $this->update(['last_contact_date' => now()]);
    }

    public function addCommunication($type, $subject, $content, $method = 'email')
    {
        return $this->communications()->create([
            'type' => $type,
            'subject' => $subject,
            'content' => $content,
            'method' => $method,
            'sent_at' => now(),
            'sent_by' => auth()->id()
        ]);
    }

    public function sendSMS($message)
    {
        if (!$this->sms_consent || !$this->mobile) {
            return false;
        }
        
        // SMS sending logic would go here
        
        $this->addCommunication('sms', 'SMS Message', $message, 'sms');
        $this->updateLastContact();
        
        return true;
    }

    public function sendEmail($subject, $content)
    {
        if (!$this->email_consent || !$this->email) {
            return false;
        }
        
        // Email sending logic would go here
        
        $this->addCommunication('email', $subject, $content, 'email');
        $this->updateLastContact();
        
        return true;
    }

    public function addDevice($deviceType, $brand, $model, $serial = null, $imei = null)
    {
        return $this->devices()->create([
            'device_type' => $deviceType,
            'brand' => $brand,
            'model' => $model,
            'serial_number' => $serial,
            'imei' => $imei,
            'added_at' => now()
        ]);
    }

    public function hasDevice($deviceType, $brand = null, $model = null)
    {
        $query = $this->devices()->where('device_type', $deviceType);
        
        if ($brand) {
            $query->where('brand', $brand);
        }
        
        if ($model) {
            $query->where('model', $model);
        }
        
        return $query->exists();
    }

    public function getDeviceHistory($deviceType = null)
    {
        $query = $this->repairs();
        
        if ($deviceType) {
            $query->where('device_type', $deviceType);
        }
        
        return $query->orderBy('created_at', 'desc')->get();
    }

    public function calculateLoyaltyScore()
    {
        $repairCount = $this->repair_count;
        $totalSpent = $this->total_spent;
        $daysSinceFirstRepair = $this->repairs()->oldest()->first()?->created_at?->diffInDays(now()) ?? 0;
        
        $score = 0;
        
        // Points for repair frequency
        $score += min($repairCount * 10, 50);
        
        // Points for total spending
        $score += min($totalSpent / 100, 30);
        
        // Points for loyalty duration
        if ($daysSinceFirstRepair > 365) {
            $score += 20;
        } elseif ($daysSinceFirstRepair > 180) {
            $score += 10;
        }
        
        return min($score, 100);
    }

    public function isEligibleForDiscount()
    {
        return $this->is_vip || $this->calculateLoyaltyScore() >= 70;
    }

    public function getRecommendedDiscount()
    {
        if ($this->discount_percentage > 0) {
            return $this->discount_percentage;
        }
        
        $loyaltyScore = $this->calculateLoyaltyScore();
        
        if ($loyaltyScore >= 90) {
            return 15;
        } elseif ($loyaltyScore >= 70) {
            return 10;
        } elseif ($loyaltyScore >= 50) {
            return 5;
        }
        
        return 0;
    }

    public function addTag($tag)
    {
        $tags = $this->tags ?? [];
        
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->update(['tags' => $tags]);
        }
    }

    public function removeTag($tag)
    {
        $tags = $this->tags ?? [];
        $tags = array_filter($tags, function($t) use ($tag) {
            return $t !== $tag;
        });
        
        $this->update(['tags' => array_values($tags)]);
    }

    public function hasTag($tag)
    {
        return in_array($tag, $this->tags ?? []);
    }

    public function markAsVip($reason = null)
    {
        $this->update(['is_vip' => true]);
        
        $this->addCommunication(
            'system',
            'VIP Status Granted',
            "Customer has been marked as VIP. Reason: {$reason}",
            'system'
        );
    }

    public function removeVipStatus($reason = null)
    {
        $this->update(['is_vip' => false]);
        
        $this->addCommunication(
            'system',
            'VIP Status Removed',
            "VIP status has been removed. Reason: {$reason}",
            'system'
        );
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($customer) {
            if (!$customer->customer_number) {
                $customer->customer_number = $customer->generateCustomerNumber();
            }
            $customer->created_by = auth()->id();
        });
        
        static::updating(function ($customer) {
            $customer->updated_by = auth()->id();
        });
    }
}
