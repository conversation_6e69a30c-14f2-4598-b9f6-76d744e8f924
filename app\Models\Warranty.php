<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Warranty extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'warranty_number',
        'repair_id',
        'customer_id',
        'device_type',
        'device_brand',
        'device_model',
        'device_serial',
        'warranty_type',
        'warranty_period',
        'start_date',
        'end_date',
        'coverage_details',
        'terms_conditions',
        'status',
        'claim_count',
        'last_claim_date',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'last_claim_date' => 'date',
        'warranty_period' => 'integer',
        'claim_count' => 'integer',
        'coverage_details' => 'array',
        'terms_conditions' => 'array'
    ];

    protected $dates = [
        'start_date',
        'end_date',
        'last_claim_date',
        'deleted_at'
    ];

    // Relationships
    public function repair()
    {
        return $this->belongsTo(Repair::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function claims()
    {
        return $this->hasMany(WarrantyClaim::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function warrantable()
    {
        return $this->morphTo();
    }

    public function expenses()
    {
        return $this->morphMany(Expense::class, 'expensable');
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'related');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'active' => ['class' => 'badge-success', 'text' => 'ساري'],
            'expired' => ['class' => 'badge-danger', 'text' => 'منتهي'],
            'voided' => ['class' => 'badge-secondary', 'text' => 'ملغي'],
            'claimed' => ['class' => 'badge-warning', 'text' => 'مستخدم']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getDaysRemainingAttribute()
    {
        if ($this->end_date && $this->end_date->isFuture()) {
            return $this->end_date->diffInDays(now());
        }
        return 0;
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active' && $this->end_date && $this->end_date->isFuture();
    }

    public function getIsExpiredAttribute()
    {
        return $this->end_date && $this->end_date->isPast();
    }

    public function getIsExpiringAttribute()
    {
        return $this->end_date && $this->end_date->diffInDays(now()) <= 30 && $this->end_date->isFuture();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('end_date', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('end_date', '<', now());
    }

    public function scopeExpiring($query, $days = 30)
    {
        return $query->where('status', 'active')
                    ->whereBetween('end_date', [now(), now()->addDays($days)]);
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeByDeviceType($query, $deviceType)
    {
        return $query->where('device_type', $deviceType);
    }

    // Methods
    public function generateWarrantyNumber()
    {
        $prefix = 'WAR';
        $year = now()->year;
        $month = now()->format('m');
        
        $lastWarranty = static::whereYear('created_at', $year)
                             ->whereMonth('created_at', now()->month)
                             ->orderBy('id', 'desc')
                             ->first();
        
        $sequence = $lastWarranty ? (int)substr($lastWarranty->warranty_number, -4) + 1 : 1;
        
        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    public function isValid()
    {
        return $this->status === 'active' && 
               $this->end_date && 
               $this->end_date->isFuture();
    }

    public function canBeClaimed()
    {
        return $this->isValid() && 
               $this->status !== 'voided';
    }

    public function createClaim($description, $claimType = 'repair')
    {
        if (!$this->canBeClaimed()) {
            return false;
        }

        $claim = $this->claims()->create([
            'claim_number' => $this->generateClaimNumber(),
            'claim_type' => $claimType,
            'description' => $description,
            'status' => 'pending',
            'claim_date' => now(),
            'created_by' => auth()->id()
        ]);

        $this->increment('claim_count');
        $this->update(['last_claim_date' => now()]);

        return $claim;
    }

    private function generateClaimNumber()
    {
        $prefix = 'CLM';
        $year = now()->year;
        
        $lastClaim = WarrantyClaim::whereYear('created_at', $year)
                                 ->orderBy('id', 'desc')
                                 ->first();
        
        $sequence = $lastClaim ? (int)substr($lastClaim->claim_number, -5) + 1 : 1;
        
        return $prefix . $year . str_pad($sequence, 5, '0', STR_PAD_LEFT);
    }

    public function extend($additionalDays, $reason = null)
    {
        $newEndDate = $this->end_date->addDays($additionalDays);
        
        $this->update([
            'end_date' => $newEndDate,
            'notes' => $this->notes . "\n\nExtended by {$additionalDays} days. Reason: {$reason}"
        ]);

        return $newEndDate;
    }

    public function void($reason)
    {
        $this->update([
            'status' => 'voided',
            'notes' => $this->notes . "\n\nVoided: {$reason}"
        ]);
    }

    public function activate()
    {
        if ($this->end_date && $this->end_date->isFuture()) {
            $this->update(['status' => 'active']);
            return true;
        }
        return false;
    }

    public function getUsageStatistics()
    {
        return [
            'total_claims' => $this->claim_count,
            'pending_claims' => $this->claims()->where('status', 'pending')->count(),
            'approved_claims' => $this->claims()->where('status', 'approved')->count(),
            'rejected_claims' => $this->claims()->where('status', 'rejected')->count(),
            'days_remaining' => $this->days_remaining,
            'usage_percentage' => $this->warranty_period > 0 ? 
                (($this->warranty_period - $this->days_remaining) / $this->warranty_period) * 100 : 0
        ];
    }

    public function checkExpiration()
    {
        if ($this->is_expired && $this->status === 'active') {
            $this->update(['status' => 'expired']);
            return true;
        }
        return false;
    }

    public static function checkAllExpirations()
    {
        $expiredWarranties = static::where('status', 'active')
                                  ->where('end_date', '<', now())
                                  ->get();

        foreach ($expiredWarranties as $warranty) {
            $warranty->update(['status' => 'expired']);
        }

        return $expiredWarranties->count();
    }

    public static function getExpiringWarranties($days = 30)
    {
        return static::expiring($days)->with(['customer', 'repair'])->get();
    }

    public function sendExpirationNotification()
    {
        // Implementation for sending expiration notification
        // This would integrate with your notification system
        
        if ($this->customer && $this->customer->email) {
            // Send email notification
        }
        
        if ($this->customer && $this->customer->mobile) {
            // Send SMS notification
        }
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($warranty) {
            if (!$warranty->warranty_number) {
                $warranty->warranty_number = $warranty->generateWarrantyNumber();
            }
            
            if (!$warranty->end_date && $warranty->start_date && $warranty->warranty_period) {
                $warranty->end_date = $warranty->start_date->addDays($warranty->warranty_period);
            }
            
            $warranty->created_by = auth()->id();
        });
        
        static::updating(function ($warranty) {
            $warranty->updated_by = auth()->id();
        });
    }
}
