@extends('layouts.main')

@section('title', 'تفاصيل الحساب - ' . $account->account_name)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تفاصيل الحساب</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $account->account_code }} - {{ $account->account_name }}</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            @if(!$account->is_system)
                <a href="{{ route('accounts.edit', $account) }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-edit mr-2"></i>
                    تعديل
                </a>
            @endif
            
            <a href="{{ route('accounts.create', ['parent_id' => $account->id]) }}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-plus mr-2"></i>
                إضافة حساب فرعي
            </a>
            
            <a href="{{ route('accounting.reports.account-ledger', $account) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-book mr-2"></i>
                دفتر الأستاذ
            </a>
            
            <a href="{{ route('accounts.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- Account Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Basic Information -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">معلومات الحساب</h3>
                </div>
                
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الحساب</label>
                            <div class="text-lg font-mono bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $account->account_code }}
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم الحساب</label>
                            <div class="text-lg font-medium text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $account->account_name }}
                            </div>
                        </div>
                        
                        @if($account->account_name_en)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الاسم الإنجليزي</label>
                            <div class="text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $account->account_name_en }}
                            </div>
                        </div>
                        @endif
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">نوع الحساب</label>
                            <div class="bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ $account->getAccountTypeLabel() }}
                                </span>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">طبيعة الرصيد</label>
                            <div class="bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                    {{ $account->getBalanceTypeLabel() }}
                                </span>
                            </div>
                        </div>
                        
                        @if($account->account_category)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">فئة الحساب</label>
                            <div class="text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                {{ $account->account_category }}
                            </div>
                        </div>
                        @endif
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الحالة</label>
                            <div class="bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                                @if($account->is_active)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                        نشط
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                        غير نشط
                                    </span>
                                @endif
                                
                                @if($account->is_system)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 mr-2">
                                        حساب نظام
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    @if($account->description)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الوصف</label>
                        <div class="text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                            {{ $account->description }}
                        </div>
                    </div>
                    @endif
                    
                    <!-- Hierarchy Path -->
                    @if($account->parent)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المسار الهرمي</label>
                        <div class="text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg">
                            {{ $account->getHierarchyPath() }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Balance Information -->
        <div class="space-y-6">
            <!-- Current Balance -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">معلومات الرصيد</h3>
                </div>
                
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الرصيد الافتتاحي</label>
                        <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                            {{ number_format($account->opening_balance, 2) }} ريال
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الرصيد الحالي</label>
                        <div class="text-3xl font-bold {{ $currentBalance >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                            {{ number_format($currentBalance, 2) }} ريال
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            كما في {{ now()->format('Y-m-d') }}
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">عدد المعاملات</label>
                        <div class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            {{ $account->transactions()->count() }} معاملة
                        </div>
                    </div>
                </div>
            </div>

            <!-- Parent Account -->
            @if($account->parent)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الحساب الأب</h3>
                </div>
                
                <div class="p-6">
                    <a href="{{ route('accounts.show', $account->parent) }}" 
                       class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                        <div class="flex-1">
                            <div class="font-medium text-gray-900 dark:text-gray-100">
                                {{ $account->parent->account_name }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $account->parent->account_code }}
                            </div>
                        </div>
                        <i class="fas fa-arrow-left text-gray-400"></i>
                    </a>
                </div>
            </div>
            @endif

            <!-- Child Accounts -->
            @if($account->children->count() > 0)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الحسابات الفرعية</h3>
                </div>
                
                <div class="p-6 space-y-2">
                    @foreach($account->children as $child)
                        <a href="{{ route('accounts.show', $child) }}" 
                           class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                            <div>
                                <div class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ $child->account_name }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $child->account_code }}
                                </div>
                            </div>
                            <div class="text-left">
                                <div class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ number_format($child->current_balance, 2) }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">ريال</div>
                            </div>
                        </a>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Recent Transactions -->
    @if($recentTransactions->count() > 0)
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">آخر المعاملات</h3>
                <a href="{{ route('accounting.reports.account-ledger', $account) }}" 
                   class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                    عرض جميع المعاملات
                </a>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            التاريخ
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            رقم القيد
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الوصف
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المدين
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الدائن
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($recentTransactions as $transaction)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ $transaction->journalEntry->entry_date->format('Y-m-d') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <a href="{{ route('journal-entries.show', $transaction->journalEntry) }}" 
                                   class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                                    {{ $transaction->journalEntry->entry_number }}
                                </a>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                {{ Str::limit($transaction->description ?: $transaction->journalEntry->description, 50) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                @if($transaction->debit_amount > 0)
                                    {{ number_format($transaction->debit_amount, 2) }}
                                @else
                                    -
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                @if($transaction->credit_amount > 0)
                                    {{ number_format($transaction->credit_amount, 2) }}
                                @else
                                    -
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @endif
</div>
@endsection
