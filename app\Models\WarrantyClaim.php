<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WarrantyClaim extends Model
{
    use HasFactory;

    protected $fillable = [
        'claim_number',
        'warranty_id',
        'claim_type',
        'description',
        'status',
        'claim_date',
        'resolution_date',
        'resolution_description',
        'cost',
        'approved_by',
        'created_by'
    ];

    protected $casts = [
        'claim_date' => 'date',
        'resolution_date' => 'date',
        'cost' => 'decimal:2'
    ];

    // Relationships
    public function warranty()
    {
        return $this->belongsTo(Warranty::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'pending' => ['class' => 'badge-warning', 'text' => 'في الانتظار'],
            'approved' => ['class' => 'badge-success', 'text' => 'معتمد'],
            'rejected' => ['class' => 'badge-danger', 'text' => 'مرفوض'],
            'resolved' => ['class' => 'badge-info', 'text' => 'محلول']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getClaimTypeBadgeAttribute()
    {
        $types = [
            'repair' => ['class' => 'badge-primary', 'text' => 'إصلاح'],
            'replacement' => ['class' => 'badge-warning', 'text' => 'استبدال'],
            'refund' => ['class' => 'badge-info', 'text' => 'استرداد']
        ];

        return $types[$this->claim_type] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    // Methods
    public function approve($approverId = null)
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approverId ?? auth()->id()
        ]);
    }

    public function reject($reason = null)
    {
        $this->update([
            'status' => 'rejected',
            'resolution_description' => $reason
        ]);
    }

    public function resolve($description = null, $cost = null)
    {
        $this->update([
            'status' => 'resolved',
            'resolution_date' => now(),
            'resolution_description' => $description,
            'cost' => $cost
        ]);
    }
}
