@extends('layouts.main')

@section('title', 'حركات المخزون - ' . $part->name)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">حركات المخزون</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $part->name }} - {{ $part->part_number ?? $part->sku }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('parts.show', $part) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة للقطعة
            </a>
            <button onclick="exportMovements()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                تصدير
            </button>
        </div>
    </div>

    <!-- Part Summary -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="flex items-center">
                @if($part->images && count($part->images) > 0)
                    <img src="{{ Storage::url($part->images[0]) }}" alt="{{ $part->name }}" class="w-16 h-16 rounded-lg object-cover ml-4">
                @else
                    <div class="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center ml-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"></path>
                        </svg>
                    </div>
                @endif
                <div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{ $part->name }}</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $part->category->name ?? 'غير محدد' }}</p>
                </div>
            </div>

            <div class="text-center">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">المخزون الحالي</p>
                <div class="flex items-center justify-center mt-1">
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $part->stock_quantity }}</p>
                    <span class="mr-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $part->stock_status_badge['class'] }}">
                        {{ $part->stock_status_badge['text'] }}
                    </span>
                </div>
            </div>

            <div class="text-center">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي الحركات</p>
                <p class="text-2xl font-bold text-blue-600 dark:text-blue-400 mt-1">{{ $movements->total() }}</p>
            </div>

            <div class="text-center">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">آخر حركة</p>
                <p class="text-sm text-gray-900 dark:text-gray-100 mt-1">
                    {{ $movements->count() > 0 ? $movements->first()->created_at->diffForHumans() : 'لا توجد حركات' }}
                </p>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <form method="GET" action="{{ route('parts.stock-movements', $part) }}">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الحركة</label>
                    <select name="type" id="type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="">جميع الأنواع</option>
                        <option value="stock_in" {{ request('type') == 'stock_in' ? 'selected' : '' }}>إدخال</option>
                        <option value="stock_out" {{ request('type') == 'stock_out' ? 'selected' : '' }}>إخراج</option>
                        <option value="adjustment_in" {{ request('type') == 'adjustment_in' ? 'selected' : '' }}>تعديل زيادة</option>
                        <option value="adjustment_out" {{ request('type') == 'adjustment_out' ? 'selected' : '' }}>تعديل نقص</option>
                        <option value="initial_stock" {{ request('type') == 'initial_stock' ? 'selected' : '' }}>مخزون أولي</option>
                    </select>
                </div>
                
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                    <input type="date" name="date_from" id="date_from" value="{{ request('date_from') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>
                
                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                    <input type="date" name="date_to" id="date_to" value="{{ request('date_to') }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        تصفية
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Movements Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                حركات المخزون ({{ $movements->total() }} حركة)
            </h3>
        </div>

        @if($movements->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            التاريخ والوقت
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            نوع الحركة
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الكمية
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المرجع
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المستخدم
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            ملاحظات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($movements as $movement)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <div>
                                <div class="font-medium">{{ $movement->created_at->format('Y-m-d') }}</div>
                                <div class="text-gray-500 dark:text-gray-400">{{ $movement->created_at->format('H:i:s') }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="p-2 rounded-lg ml-3 
                                    @if(str_contains($movement->type, 'in')) bg-green-100 dark:bg-green-900
                                    @else bg-red-100 dark:bg-red-900 @endif">
                                    <svg class="w-4 h-4 
                                        @if(str_contains($movement->type, 'in')) text-green-600 dark:text-green-400
                                        @else text-red-600 dark:text-red-400 @endif" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        @if(str_contains($movement->type, 'in'))
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        @else
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6"></path>
                                        @endif
                                    </svg>
                                </div>
                                <div>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @if(str_contains($movement->type, 'in')) bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                        @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @endif">
                                        @switch($movement->type)
                                            @case('stock_in') إدخال @break
                                            @case('stock_out') إخراج @break
                                            @case('adjustment_in') تعديل زيادة @break
                                            @case('adjustment_out') تعديل نقص @break
                                            @case('initial_stock') مخزون أولي @break
                                            @default {{ $movement->type }}
                                        @endswitch
                                    </span>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-lg font-bold 
                                @if(str_contains($movement->type, 'in')) text-green-600 dark:text-green-400
                                @else text-red-600 dark:text-red-400 @endif">
                                @if(str_contains($movement->type, 'in'))+@else-@endif{{ $movement->quantity }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            @if($movement->reference_type && $movement->reference_id)
                                <div>
                                    <div class="font-medium">{{ $movement->reference_type }}</div>
                                    <div class="text-gray-500 dark:text-gray-400">#{{ $movement->reference_id }}</div>
                                </div>
                            @else
                                <span class="text-gray-500 dark:text-gray-400">-</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {{ $movement->createdBy->name ?? 'النظام' }}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                            {{ $movement->notes ?? '-' }}
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            {{ $movements->appends(request()->query())->links() }}
        </div>
        @else
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد حركات مخزون</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لم يتم تسجيل أي حركات مخزون لهذه القطعة بعد.</p>
        </div>
        @endif
    </div>

    <!-- Summary Statistics -->
    @if($movements->count() > 0)
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إحصائيات الحركات</h3>
        
        @php
            $totalIn = $movements->where('type', 'like', '%in%')->sum('quantity');
            $totalOut = $movements->where('type', 'like', '%out%')->sum('quantity');
            $netMovement = $totalIn - $totalOut;
        @endphp
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                <p class="text-sm font-medium text-green-600 dark:text-green-400">إجمالي الإدخال</p>
                <p class="text-2xl font-bold text-green-600 dark:text-green-400">+{{ number_format($totalIn) }}</p>
            </div>
            
            <div class="text-center p-4 bg-red-50 dark:bg-red-900 rounded-lg">
                <p class="text-sm font-medium text-red-600 dark:text-red-400">إجمالي الإخراج</p>
                <p class="text-2xl font-bold text-red-600 dark:text-red-400">-{{ number_format($totalOut) }}</p>
            </div>
            
            <div class="text-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                <p class="text-sm font-medium text-blue-600 dark:text-blue-400">صافي الحركة</p>
                <p class="text-2xl font-bold {{ $netMovement >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                    {{ $netMovement >= 0 ? '+' : '' }}{{ number_format($netMovement) }}
                </p>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط الحركة</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {{ $movements->count() > 0 ? number_format(($totalIn + $totalOut) / $movements->count(), 1) : '0' }}
                </p>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
function exportMovements() {
    // Implementation for exporting movements
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');
    window.open(`{{ route('parts.stock-movements', $part) }}?${params.toString()}`, '_blank');
}
</script>
@endsection
