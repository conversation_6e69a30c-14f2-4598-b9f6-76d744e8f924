<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Schedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'technician_id',
        'repair_id',
        'scheduled_date',
        'scheduled_time',
        'estimated_duration',
        'actual_start_time',
        'actual_end_time',
        'status',
        'notes',
        'priority'
    ];

    protected $casts = [
        'scheduled_date' => 'date',
        'scheduled_time' => 'datetime',
        'actual_start_time' => 'datetime',
        'actual_end_time' => 'datetime',
        'estimated_duration' => 'integer' // in minutes
    ];

    // Relationships
    public function technician()
    {
        return $this->belongsTo(Technician::class);
    }

    public function repair()
    {
        return $this->belongsTo(Repair::class);
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'scheduled' => ['class' => 'badge-info', 'text' => 'مجدول'],
            'in_progress' => ['class' => 'badge-warning', 'text' => 'قيد التنفيذ'],
            'completed' => ['class' => 'badge-success', 'text' => 'مكتمل'],
            'cancelled' => ['class' => 'badge-danger', 'text' => 'ملغي'],
            'rescheduled' => ['class' => 'badge-secondary', 'text' => 'معاد جدولته']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getPriorityBadgeAttribute()
    {
        $priorities = [
            'low' => ['class' => 'badge-success', 'text' => 'منخفضة'],
            'normal' => ['class' => 'badge-info', 'text' => 'عادية'],
            'high' => ['class' => 'badge-warning', 'text' => 'عالية'],
            'urgent' => ['class' => 'badge-danger', 'text' => 'عاجلة']
        ];

        return $priorities[$this->priority] ?? ['class' => 'badge-info', 'text' => 'عادية'];
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->whereDate('scheduled_date', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('scheduled_date', '>=', today());
    }

    public function scopeOverdue($query)
    {
        return $query->where('scheduled_date', '<', today())
                    ->where('status', '!=', 'completed');
    }
}
