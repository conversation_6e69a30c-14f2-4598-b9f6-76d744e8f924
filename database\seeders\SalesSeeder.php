<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Company;
use App\Models\Location;
use App\Models\Contact;
use App\Models\Product;
use App\Models\User;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Purchase;
use App\Models\PurchaseItem;
use App\Models\Inventory;

class SalesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $company = Company::first();
        $location = Location::first();
        
        if (!$company || !$location) {
            return;
        }

        // إنشاء مبيعات تجريبية
        $this->createSampleSales($company->id, $location->id);
        
        // إنشاء مشتريات تجريبية
        $this->createSamplePurchases($company->id, $location->id);
    }

    private function createSampleSales($companyId, $locationId)
    {
        $customers = Contact::where('company_id', $companyId)->customers()->get();
        $products = Product::where('company_id', $companyId)->get();
        $salesperson = User::where('company_id', $companyId)->first();

        if ($customers->isEmpty() || $products->isEmpty() || !$salesperson) {
            return;
        }

        // إنشاء 10 مبيعات تجريبية
        for ($i = 1; $i <= 10; $i++) {
            $customer = $customers->random();
            $saleDate = now()->subDays(rand(0, 30));
            
            $sale = Sale::create([
                'company_id' => $companyId,
                'location_id' => $locationId,
                'customer_id' => $customer->id,
                'salesperson_id' => $salesperson->id,
                'sale_number' => Sale::generateSaleNumber($companyId),
                'sale_date' => $saleDate,
                'due_date' => $saleDate->copy()->addDays(30),
                'status' => 'completed',
                'payment_status' => rand(0, 1) ? 'paid' : 'partial',
                'subtotal' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'total_amount' => 0,
                'paid_amount' => 0,
                'payment_method' => ['cash', 'bank_transfer', 'credit_card'][rand(0, 2)]
            ]);

            // إضافة عناصر للمبيعة
            $numItems = rand(1, 4);
            $subtotal = 0;

            for ($j = 0; $j < $numItems; $j++) {
                $product = $products->random();
                $quantity = rand(1, 3);
                $unitPrice = $product->selling_price;
                $itemTotal = $quantity * $unitPrice;
                
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'discount_amount' => 0,
                    'tax_amount' => $itemTotal * 0.16, // ضريبة 16%
                    'total_amount' => $itemTotal
                ]);

                $subtotal += $itemTotal;

                // تحديث المخزون
                $inventory = Inventory::where('product_id', $product->id)
                                    ->where('location_id', $locationId)
                                    ->first();
                
                if ($inventory && $inventory->quantity >= $quantity) {
                    $inventory->removeStock($quantity, "مبيعة رقم {$sale->sale_number}");
                }
            }

            // تحديث إجماليات المبيعة
            $taxAmount = $subtotal * 0.16;
            $totalAmount = $subtotal + $taxAmount;
            $paidAmount = $sale->payment_status === 'paid' ? $totalAmount : $totalAmount * 0.6;

            $sale->update([
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'paid_amount' => $paidAmount
            ]);
        }
    }

    private function createSamplePurchases($companyId, $locationId)
    {
        $suppliers = Contact::where('company_id', $companyId)->suppliers()->get();
        $products = Product::where('company_id', $companyId)->get();

        if ($suppliers->isEmpty() || $products->isEmpty()) {
            return;
        }

        // إنشاء 5 مشتريات تجريبية
        for ($i = 1; $i <= 5; $i++) {
            $supplier = $suppliers->random();
            $purchaseDate = now()->subDays(rand(5, 60));
            
            $purchase = Purchase::create([
                'company_id' => $companyId,
                'location_id' => $locationId,
                'supplier_id' => $supplier->id,
                'purchase_number' => Purchase::generatePurchaseNumber($companyId),
                'purchase_date' => $purchaseDate,
                'due_date' => $purchaseDate->copy()->addDays(30),
                'status' => 'completed',
                'payment_status' => rand(0, 1) ? 'paid' : 'partial',
                'subtotal' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'total_amount' => 0,
                'paid_amount' => 0,
                'payment_method' => ['cash', 'bank_transfer', 'check'][rand(0, 2)]
            ]);

            // إضافة عناصر للمشترى
            $numItems = rand(2, 5);
            $subtotal = 0;

            for ($j = 0; $j < $numItems; $j++) {
                $product = $products->random();
                $quantity = rand(5, 20);
                $unitPrice = $product->purchase_price;
                $itemTotal = $quantity * $unitPrice;
                
                PurchaseItem::create([
                    'purchase_id' => $purchase->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'discount_amount' => 0,
                    'tax_amount' => $itemTotal * 0.16,
                    'total_amount' => $itemTotal
                ]);

                $subtotal += $itemTotal;

                // تحديث المخزون
                $inventory = Inventory::where('product_id', $product->id)
                                    ->where('location_id', $locationId)
                                    ->first();
                
                if ($inventory) {
                    $inventory->addStock($quantity, "مشترى رقم {$purchase->purchase_number}");
                }
            }

            // تحديث إجماليات المشترى
            $taxAmount = $subtotal * 0.16;
            $totalAmount = $subtotal + $taxAmount;
            $paidAmount = $purchase->payment_status === 'paid' ? $totalAmount : $totalAmount * 0.7;

            $purchase->update([
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'paid_amount' => $paidAmount
            ]);
        }
    }
}
