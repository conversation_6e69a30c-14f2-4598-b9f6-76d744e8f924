@extends('layouts.main')

@section('title', 'سير عمل الصيانة')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">سير عمل الصيانة</h1>
            <p class="text-gray-600 dark:text-gray-400">متابعة حالة جميع طلبات الصيانة</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('repairs.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-plus mr-2"></i>
                طلب صيانة جديد
            </a>
            <a href="{{ route('repairs.export') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير البيانات
            </a>
        </div>
    </div>

    <!-- Status Overview Cards -->
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">في الانتظار</p>
                    <p class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">{{ $statusCounts['pending'] }}</p>
                </div>
                <div class="text-yellow-600">
                    <i class="fas fa-clock text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-blue-800 dark:text-blue-200">تم التشخيص</p>
                    <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">{{ $statusCounts['diagnosed'] }}</p>
                </div>
                <div class="text-blue-600">
                    <i class="fas fa-search text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-orange-800 dark:text-orange-200">قيد التنفيذ</p>
                    <p class="text-2xl font-bold text-orange-900 dark:text-orange-100">{{ $statusCounts['in_progress'] }}</p>
                </div>
                <div class="text-orange-600">
                    <i class="fas fa-tools text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-purple-800 dark:text-purple-200">انتظار قطع</p>
                    <p class="text-2xl font-bold text-purple-900 dark:text-purple-100">{{ $statusCounts['waiting_parts'] }}</p>
                </div>
                <div class="text-purple-600">
                    <i class="fas fa-box text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-green-800 dark:text-green-200">مكتمل</p>
                    <p class="text-2xl font-bold text-green-900 dark:text-green-100">{{ $statusCounts['completed'] }}</p>
                </div>
                <div class="text-green-600">
                    <i class="fas fa-check text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-800 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-teal-800 dark:text-teal-200">تم التسليم</p>
                    <p class="text-2xl font-bold text-teal-900 dark:text-teal-100">{{ $statusCounts['delivered'] }}</p>
                </div>
                <div class="text-teal-600">
                    <i class="fas fa-handshake text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-800 dark:text-gray-200">معلق</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $statusCounts['on_hold'] }}</p>
                </div>
                <div class="text-gray-600">
                    <i class="fas fa-pause text-xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-red-800 dark:text-red-200">ملغي</p>
                    <p class="text-2xl font-bold text-red-900 dark:text-red-100">{{ $statusCounts['cancelled'] }}</p>
                </div>
                <div class="text-red-600">
                    <i class="fas fa-times text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Urgent Repairs Alert -->
    @if($urgentRepairs->count() > 0)
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
        <div class="flex items-center mb-4">
            <i class="fas fa-exclamation-triangle text-red-600 text-xl mr-3"></i>
            <h3 class="text-lg font-medium text-red-800 dark:text-red-200">طلبات صيانة عاجلة ({{ $urgentRepairs->count() }})</h3>
        </div>
        <div class="space-y-3">
            @foreach($urgentRepairs as $repair)
                <div class="flex items-center justify-between bg-white dark:bg-gray-800 p-3 rounded-lg">
                    <div>
                        <a href="{{ route('repairs.show', $repair) }}" class="font-medium text-gray-900 dark:text-gray-100 hover:text-blue-600">
                            {{ $repair->repair_number }}
                        </a>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            {{ $repair->customer->full_name }} - {{ $repair->device_brand }} {{ $repair->device_model }}
                        </p>
                    </div>
                    <div class="text-left">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                            {{ $repair->status_label }}
                        </span>
                        @if($repair->technician)
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ $repair->technician->full_name }}</p>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Overdue Repairs Alert -->
    @if($overdueRepairs->count() > 0)
    <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-6">
        <div class="flex items-center mb-4">
            <i class="fas fa-clock text-orange-600 text-xl mr-3"></i>
            <h3 class="text-lg font-medium text-orange-800 dark:text-orange-200">طلبات متأخرة ({{ $overdueRepairs->count() }})</h3>
        </div>
        <div class="space-y-3">
            @foreach($overdueRepairs as $repair)
                <div class="flex items-center justify-between bg-white dark:bg-gray-800 p-3 rounded-lg">
                    <div>
                        <a href="{{ route('repairs.show', $repair) }}" class="font-medium text-gray-900 dark:text-gray-100 hover:text-blue-600">
                            {{ $repair->repair_number }}
                        </a>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            {{ $repair->customer->full_name }} - متأخر {{ $repair->estimated_completion->diffForHumans() }}
                        </p>
                    </div>
                    <div class="text-left">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                            {{ $repair->status_label }}
                        </span>
                        @if($repair->technician)
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ $repair->technician->full_name }}</p>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Recent Repairs -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">أحدث طلبات الصيانة</h3>
                <a href="{{ route('repairs.index') }}" class="text-blue-600 hover:text-blue-700 text-sm">
                    عرض الكل
                </a>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">رقم الطلب</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الجهاز</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفني</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الأولوية</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($recentRepairs as $repair)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <a href="{{ route('repairs.show', $repair) }}" class="text-blue-600 hover:text-blue-700 font-medium">
                                    {{ $repair->repair_number }}
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ $repair->customer->full_name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ $repair->device_brand }} {{ $repair->device_model }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ $repair->technician->full_name ?? 'غير محدد' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $repair->status_color }}">
                                    {{ $repair->status_label }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $repair->priority_color }}">
                                    {{ $repair->priority_label }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $repair->created_at->diffForHumans() }}
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-tools text-4xl mb-4"></i>
                                    <p>لا توجد طلبات صيانة</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection
