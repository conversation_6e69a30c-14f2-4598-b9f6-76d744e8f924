@extends('layouts.main')

@section('title', 'إدارة الضمانات')

@section('content')
<div class="content-wrapper animate-fade-in-up" x-data="warrantyManager()">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">إدارة الضمانات</h1>
                    <p class="page-subtitle">متابعة ضمانات الأجهزة والإصلاحات بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="exportWarranties()" class="btn btn-success hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        تصدير
                    </button>
                    <button @click="checkWarranty()" class="btn btn-primary hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        فحص ضمان
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card hover-lift animate-scale-in animate-delay-100">
            <div class="stat-value" style="color: var(--success-color);" x-text="stats.activeWarranties">89</div>
            <div class="stat-label">ضمانات نشطة</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                حماية فعالة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-200">
            <div class="stat-value" style="color: var(--warning-color);" x-text="stats.expiringSoon">12</div>
            <div class="stat-label">تنتهي قريباً</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                تحتاج متابعة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-300">
            <div class="stat-value" style="color: var(--danger-color);" x-text="stats.expiredWarranties">156</div>
            <div class="stat-label">منتهية الصلاحية</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                انتهت الحماية
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-500">
            <div class="stat-value" style="color: var(--accent-color);" x-text="stats.monthlyClaims">23</div>
            <div class="stat-label">مطالبات هذا الشهر</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                خدمة نشطة
            </div>
        </div>
    </div>

    <!-- Warranty Check -->
    <div class="card animate-fade-in-up animate-delay-200">
        <div class="card-header">
            <h3 class="card-title">فحص الضمان السريع</h3>
            <div class="action-group">
                <button @click="clearWarrantyCheck()" class="btn btn-ghost btn-sm hover-scale">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    مسح
                </button>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="form-group">
                <label class="form-label">رقم الجهاز / IMEI</label>
                <input type="text" x-model="warrantyCheck.deviceId" placeholder="أدخل رقم الجهاز أو IMEI"
                       class="form-input focus-glow">
            </div>
            <div class="form-group">
                <label class="form-label">رقم الطلب</label>
                <input type="text" x-model="warrantyCheck.repairId" placeholder="رقم طلب الصيانة"
                       class="form-input focus-glow">
            </div>
            <div class="form-group">
                <label class="form-label">رقم هاتف العميل</label>
                <input type="tel" x-model="warrantyCheck.customerPhone" placeholder="رقم الهاتف"
                       class="form-input focus-glow">
            </div>
            <div class="flex items-end">
                <button @click="performWarrantyCheck()" class="btn btn-primary w-full hover-lift ripple">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    فحص الضمان
                </button>
            </div>
        </div>

        <!-- Warranty Check Result -->
        <div x-show="warrantyResult" x-transition class="mt-6 p-4 rounded-lg border-2 animate-fade-in-up"
             :class="warrantyResult?.valid ? 'border-success-color bg-success-color/10' : 'border-danger-color bg-danger-color/10'">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 rounded-full flex items-center justify-center"
                     :class="warrantyResult?.valid ? 'bg-success-color text-white' : 'bg-danger-color text-white'">
                    <svg x-show="warrantyResult?.valid" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <svg x-show="!warrantyResult?.valid" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <h4 class="font-medium text-lg"
                    :class="warrantyResult?.valid ? 'text-success-color' : 'text-danger-color'"
                    x-text="warrantyResult?.message"></h4>
            </div>
            <div x-show="warrantyResult?.details" class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white/50 dark:bg-gray-800/50 p-3 rounded-lg">
                    <p class="text-sm text-muted-light dark:text-muted-dark">نوع الضمان</p>
                    <p class="font-medium text-primary-light dark:text-primary-dark" x-text="warrantyResult?.details?.type"></p>
                </div>
                <div class="bg-white/50 dark:bg-gray-800/50 p-3 rounded-lg">
                    <p class="text-sm text-muted-light dark:text-muted-dark">تاريخ الانتهاء</p>
                    <p class="font-medium text-primary-light dark:text-primary-dark" x-text="warrantyResult?.details?.expiryDate"></p>
                </div>
                <div class="bg-white/50 dark:bg-gray-800/50 p-3 rounded-lg">
                    <p class="text-sm text-muted-light dark:text-muted-dark">الأيام المتبقية</p>
                    <p class="font-medium text-primary-light dark:text-primary-dark" x-text="warrantyResult?.details?.daysRemaining"></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <input type="text" x-model="filters.search" @input="filterWarranties()" placeholder="رقم الجهاز أو العميل..." 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الضمان</label>
                <select x-model="filters.type" @change="filterWarranties()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الأنواع</option>
                    <option value="manufacturer">ضمان الشركة</option>
                    <option value="repair">ضمان الإصلاح</option>
                    <option value="extended">ضمان ممدد</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                <select x-model="filters.status" @change="filterWarranties()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="expiring_soon">ينتهي قريباً</option>
                    <option value="expired">منتهي</option>
                    <option value="claimed">تم استخدامه</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" x-model="filters.dateFrom" @change="filterWarranties()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" x-model="filters.dateTo" @change="filterWarranties()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
        </div>
    </div>

    <!-- Warranties Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الضمانات</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الجهاز</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">نوع الضمان</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تاريخ البداية</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تاريخ الانتهاء</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المدة المتبقية</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="warranty in filteredWarranties" :key="warranty.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="warranty.device_brand + ' ' + warranty.device_model"></div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400" x-text="warranty.device_serial"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-gray-100" x-text="warranty.customer_name"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="warranty.customer_phone"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="getWarrantyTypeText(warranty.type)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="warranty.start_date"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="warranty.end_date"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="calculateDaysRemaining(warranty.end_date)"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="{
                                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': warranty.status === 'active',
                                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': warranty.status === 'expiring_soon',
                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': warranty.status === 'expired',
                                          'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': warranty.status === 'claimed'
                                      }"
                                      x-text="getStatusText(warranty.status)">
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button @click="viewWarranty(warranty.id)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400">عرض</button>
                                    <button @click="extendWarranty(warranty.id)" class="text-green-600 hover:text-green-900 dark:text-green-400">تمديد</button>
                                    <button @click="claimWarranty(warranty.id)" class="text-purple-600 hover:text-purple-900 dark:text-purple-400">مطالبة</button>
                                    <button @click="printCertificate(warranty.id)" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400">طباعة</button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
function warrantyManager() {
    return {
        warranties: [
            {
                id: 1,
                device_brand: 'Samsung',
                device_model: 'Galaxy S21',
                device_serial: '123456789',
                customer_name: 'أحمد محمد علي',
                customer_phone: '0599123456',
                type: 'repair',
                start_date: '2024-07-01',
                end_date: '2025-01-01',
                status: 'active',
                repair_id: 'REP-001'
            },
            {
                id: 2,
                device_brand: 'iPhone',
                device_model: '13 Pro',
                device_serial: '987654321',
                customer_name: 'سارة أحمد خالد',
                customer_phone: '0598765432',
                type: 'manufacturer',
                start_date: '2023-12-15',
                end_date: '2024-12-15',
                status: 'expiring_soon',
                repair_id: null
            },
            {
                id: 3,
                device_brand: 'Huawei',
                device_model: 'P30 Pro',
                device_serial: '456789123',
                customer_name: 'محمد عبدالله حسن',
                customer_phone: '0597654321',
                type: 'extended',
                start_date: '2023-06-01',
                end_date: '2024-06-01',
                status: 'expired',
                repair_id: 'REP-003'
            }
        ],
        filteredWarranties: [],
        filters: {
            search: '',
            type: '',
            status: '',
            dateFrom: '',
            dateTo: ''
        },
        warrantyCheck: {
            deviceId: '',
            repairId: '',
            customerPhone: ''
        },
        warrantyResult: null,
        stats: {
            activeWarranties: 89,
            expiringSoon: 12,
            expiredWarranties: 156,
            monthlyClaims: 23
        },

        init() {
            this.filteredWarranties = [...this.warranties];
        },

        filterWarranties() {
            this.filteredWarranties = this.warranties.filter(warranty => {
                const matchesSearch = !this.filters.search || 
                    warranty.device_serial.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    warranty.customer_name.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    warranty.customer_phone.includes(this.filters.search);
                
                const matchesType = !this.filters.type || warranty.type === this.filters.type;
                const matchesStatus = !this.filters.status || warranty.status === this.filters.status;
                const matchesDateFrom = !this.filters.dateFrom || warranty.start_date >= this.filters.dateFrom;
                const matchesDateTo = !this.filters.dateTo || warranty.end_date <= this.filters.dateTo;

                return matchesSearch && matchesType && matchesStatus && matchesDateFrom && matchesDateTo;
            });
        },

        getWarrantyTypeText(type) {
            const types = {
                'manufacturer': 'ضمان الشركة',
                'repair': 'ضمان الإصلاح',
                'extended': 'ضمان ممدد'
            };
            return types[type] || type;
        },

        getStatusText(status) {
            const statuses = {
                'active': 'نشط',
                'expiring_soon': 'ينتهي قريباً',
                'expired': 'منتهي',
                'claimed': 'تم استخدامه'
            };
            return statuses[status] || status;
        },

        calculateDaysRemaining(endDate) {
            const today = new Date();
            const end = new Date(endDate);
            const diffTime = end - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays < 0) return 'منتهي';
            if (diffDays === 0) return 'ينتهي اليوم';
            if (diffDays === 1) return 'ينتهي غداً';
            return `${diffDays} يوم`;
        },

        performWarrantyCheck() {
            // Simulate warranty check
            if (this.warrantyCheck.deviceId || this.warrantyCheck.repairId) {
                // Mock result
                this.warrantyResult = {
                    valid: true,
                    message: 'الجهاز تحت الضمان',
                    details: {
                        type: 'ضمان الإصلاح',
                        expiryDate: '2025-01-01',
                        daysRemaining: '175 يوم'
                    }
                };
            } else {
                this.warrantyResult = {
                    valid: false,
                    message: 'لم يتم العثور على ضمان لهذا الجهاز',
                    details: null
                };
            }
        },

        clearWarrantyCheck() {
            this.warrantyCheck = {
                deviceId: '',
                repairId: '',
                customerPhone: ''
            };
            this.warrantyResult = null;

            if (window.showNotification) {
                window.showNotification('تم مسح بيانات فحص الضمان', 'info');
            }
        },

        checkWarranty() {
            if (window.showNotification) {
                window.showNotification('سيتم إضافة نافذة فحص ضمان متقدمة قريباً', 'info');
            }
        },

        viewWarranty(id) {
            if (window.showNotification) {
                window.showNotification(`سيتم عرض تفاصيل الضمان ${id}`, 'info');
            }
        },

        extendWarranty(id) {
            if (confirm('هل تريد تمديد هذا الضمان؟')) {
                if (window.showNotification) {
                    window.showNotification(`تم تمديد الضمان ${id} بنجاح`, 'success');
                }
            }
        },

        claimWarranty(id) {
            if (confirm('هل تريد تقديم مطالبة لهذا الضمان؟')) {
                if (window.showNotification) {
                    window.showNotification(`تم تقديم مطالبة الضمان ${id} بنجاح`, 'success');
                }
            }
        },

        printCertificate(id) {
            if (window.showNotification) {
                window.showNotification(`سيتم طباعة شهادة الضمان ${id}`, 'info');
            }
            // window.open(`/warranty/${id}/certificate`, '_blank');
        },

        exportWarranties() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري التصدير...
            `;
            button.disabled = true;

            // محاكاة عملية التصدير
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                if (window.showNotification) {
                    window.showNotification('تم تصدير بيانات الضمانات إلى Excel بنجاح!', 'success');
                }
            }, 2000);
        }
    }
}
</script>
@endpush
@endsection
