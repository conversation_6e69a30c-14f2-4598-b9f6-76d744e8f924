@extends('layouts.main')

@section('title', 'مركز الإشعارات والتنبيهات')

@section('content')
<div class="space-y-6" x-data="notificationsManager()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">مركز الإشعارات والتنبيهات</h1>
            <p class="text-gray-600 dark:text-gray-400">إدارة جميع الإشعارات والتنبيهات الخاصة بالصيانة</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="markAllAsRead()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                تحديد الكل كمقروء
            </button>
            <button @click="createNotification()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                إشعار جديد
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">تنبيهات عاجلة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.urgentAlerts">5</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">غير مقروءة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.unreadNotifications">12</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مواعيد متأخرة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.overdueAppointments">3</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مكتملة اليوم</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.completedToday">8</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الإشعار</label>
                <select x-model="filters.type" @change="filterNotifications()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الأنواع</option>
                    <option value="repair_due">موعد إصلاح</option>
                    <option value="parts_arrived">وصول قطع غيار</option>
                    <option value="customer_reminder">تذكير عميل</option>
                    <option value="technician_assignment">تعيين فني</option>
                    <option value="system_alert">تنبيه نظام</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الأولوية</label>
                <select x-model="filters.priority" @change="filterNotifications()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الأولويات</option>
                    <option value="low">منخفضة</option>
                    <option value="medium">متوسطة</option>
                    <option value="high">عالية</option>
                    <option value="urgent">عاجلة</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
                <select x-model="filters.status" @change="filterNotifications()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="unread">غير مقروءة</option>
                    <option value="read">مقروءة</option>
                    <option value="archived">مؤرشفة</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" x-model="filters.dateFrom" @change="filterNotifications()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" x-model="filters.dateTo" @change="filterNotifications()" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الإشعارات والتنبيهات</h3>
        </div>
        
        <div class="divide-y divide-gray-200 dark:divide-gray-700">
            <template x-for="notification in filteredNotifications" :key="notification.id">
                <div class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                     :class="{'bg-blue-50 dark:bg-blue-900/20': !notification.read}"
                     @click="markAsRead(notification.id)">
                    <div class="flex items-start">
                        <!-- Icon -->
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center"
                                 :class="{
                                     'bg-red-100 dark:bg-red-900': notification.priority === 'urgent',
                                     'bg-orange-100 dark:bg-orange-900': notification.priority === 'high',
                                     'bg-yellow-100 dark:bg-yellow-900': notification.priority === 'medium',
                                     'bg-blue-100 dark:bg-blue-900': notification.priority === 'low'
                                 }">
                                <svg class="w-5 h-5" :class="{
                                         'text-red-600 dark:text-red-400': notification.priority === 'urgent',
                                         'text-orange-600 dark:text-orange-400': notification.priority === 'high',
                                         'text-yellow-600 dark:text-yellow-400': notification.priority === 'medium',
                                         'text-blue-600 dark:text-blue-400': notification.priority === 'low'
                                     }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getNotificationIcon(notification.type)"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="mr-4 flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="notification.title"></h4>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                          :class="{
                                              'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': notification.priority === 'urgent',
                                              'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200': notification.priority === 'high',
                                              'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': notification.priority === 'medium',
                                              'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200': notification.priority === 'low'
                                          }"
                                          x-text="getPriorityText(notification.priority)">
                                    </span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400" x-text="notification.created_at"></span>
                                </div>
                            </div>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-300" x-text="notification.message"></p>
                            
                            <!-- Actions -->
                            <div class="mt-3 flex space-x-2 space-x-reverse" x-show="notification.actions && notification.actions.length > 0">
                                <template x-for="action in notification.actions" :key="action.id">
                                    <button @click.stop="performAction(action)" 
                                            class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md"
                                            :class="action.primary ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-blue-700 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-200'"
                                            x-text="action.label">
                                    </button>
                                </template>
                            </div>
                        </div>

                        <!-- Status Indicator -->
                        <div class="flex-shrink-0">
                            <div x-show="!notification.read" class="w-2 h-2 bg-blue-600 rounded-full"></div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Empty State -->
        <div x-show="filteredNotifications.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد إشعارات</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لا توجد إشعارات تطابق المعايير المحددة</p>
        </div>
    </div>

    <!-- Notification Settings -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إعدادات الإشعارات</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">إشعارات الصيانة</h4>
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="checkbox" x-model="settings.repairDue" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">تنبيه عند اقتراب موعد الإصلاح</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" x-model="settings.partsArrived" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">تنبيه عند وصول قطع الغيار</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" x-model="settings.repairCompleted" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">تنبيه عند اكتمال الإصلاح</span>
                    </label>
                </div>
            </div>

            <div>
                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">إشعارات العملاء</h4>
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="checkbox" x-model="settings.customerReminders" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">تذكير العملاء بالمواعيد</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" x-model="settings.deliveryReady" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">تنبيه عند جاهزية التسليم</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" x-model="settings.warrantyExpiry" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">تنبيه انتهاء الضمان</span>
                    </label>
                </div>
            </div>
        </div>

        <div class="mt-6">
            <button @click="saveSettings()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                حفظ الإعدادات
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
function notificationsManager() {
    return {
        notifications: [
            {
                id: 1,
                type: 'repair_due',
                title: 'موعد إصلاح قريب',
                message: 'موعد إصلاح جهاز أحمد محمد علي (REP-001) خلال ساعة واحدة',
                priority: 'high',
                read: false,
                created_at: '2024-07-09 08:30',
                actions: [
                    { id: 1, label: 'عرض التفاصيل', primary: true },
                    { id: 2, label: 'تأجيل', primary: false }
                ]
            },
            {
                id: 2,
                type: 'parts_arrived',
                title: 'وصول قطع غيار',
                message: 'وصلت قطع الغيار المطلوبة لإصلاح Samsung Galaxy S21',
                priority: 'medium',
                read: false,
                created_at: '2024-07-09 07:15',
                actions: [
                    { id: 3, label: 'تحديث الحالة', primary: true }
                ]
            },
            {
                id: 3,
                type: 'customer_reminder',
                title: 'تذكير عميل',
                message: 'حان وقت تذكير سارة أحمد خالد بموعد استلام الجهاز',
                priority: 'low',
                read: true,
                created_at: '2024-07-08 16:45',
                actions: []
            }
        ],
        filteredNotifications: [],
        filters: {
            type: '',
            priority: '',
            status: '',
            dateFrom: '',
            dateTo: ''
        },
        settings: {
            repairDue: true,
            partsArrived: true,
            repairCompleted: true,
            customerReminders: true,
            deliveryReady: true,
            warrantyExpiry: false
        },
        stats: {
            urgentAlerts: 5,
            unreadNotifications: 12,
            overdueAppointments: 3,
            completedToday: 8
        },

        init() {
            this.filteredNotifications = [...this.notifications];
        },

        filterNotifications() {
            this.filteredNotifications = this.notifications.filter(notification => {
                const matchesType = !this.filters.type || notification.type === this.filters.type;
                const matchesPriority = !this.filters.priority || notification.priority === this.filters.priority;
                
                let matchesStatus = true;
                if (this.filters.status === 'unread') matchesStatus = !notification.read;
                else if (this.filters.status === 'read') matchesStatus = notification.read;
                
                const matchesDateFrom = !this.filters.dateFrom || notification.created_at >= this.filters.dateFrom;
                const matchesDateTo = !this.filters.dateTo || notification.created_at <= this.filters.dateTo;

                return matchesType && matchesPriority && matchesStatus && matchesDateFrom && matchesDateTo;
            });
        },

        getNotificationIcon(type) {
            const icons = {
                'repair_due': 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',
                'parts_arrived': 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
                'customer_reminder': 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
                'technician_assignment': 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
                'system_alert': 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z'
            };
            return icons[type] || icons['system_alert'];
        },

        getPriorityText(priority) {
            const priorities = {
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية',
                'urgent': 'عاجلة'
            };
            return priorities[priority] || priority;
        },

        markAsRead(id) {
            const notification = this.notifications.find(n => n.id === id);
            if (notification) {
                notification.read = true;
                this.stats.unreadNotifications = Math.max(0, this.stats.unreadNotifications - 1);
            }
        },

        markAllAsRead() {
            this.notifications.forEach(notification => {
                notification.read = true;
            });
            this.stats.unreadNotifications = 0;
            alert('تم تحديد جميع الإشعارات كمقروءة');
        },

        performAction(action) {
            alert(`تنفيذ الإجراء: ${action.label}`);
        },

        createNotification() {
            alert('سيتم إضافة نافذة إنشاء إشعار جديد');
        },

        saveSettings() {
            alert('تم حفظ إعدادات الإشعارات');
        }
    }
}
</script>
@endpush
@endsection
