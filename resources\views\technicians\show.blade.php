@extends('layouts.main')

@section('title', 'تفاصيل الفني')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تفاصيل الفني</h1>
            <p class="text-gray-600 dark:text-gray-400">عرض تفاصيل وأداء الفني</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('technicians.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة للقائمة
            </a>
            <a href="{{ route('technicians.edit', $technician) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                تعديل
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Personal Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <div class="flex items-start space-x-4 space-x-reverse">
                    @if($technician->profile_image)
                        <img src="{{ Storage::url($technician->profile_image) }}" alt="{{ $technician->first_name }}" 
                             class="w-20 h-20 rounded-full object-cover">
                    @else
                        <div class="w-20 h-20 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                            <svg class="w-10 h-10 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    @endif
                    
                    <div class="flex-1">
                        <h3 class="text-xl font-medium text-gray-900 dark:text-gray-100">
                            {{ $technician->first_name }} {{ $technician->last_name }}
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400">{{ $technician->position }} - {{ $technician->department }}</p>
                        <div class="flex items-center space-x-4 space-x-reverse mt-2">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $technician->status_badge['class'] }}">
                                {{ $technician->status_badge['text'] }}
                            </span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $technician->availability_badge['class'] }}">
                                {{ $technician->availability_badge['text'] }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">معلومات الاتصال</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">البريد الإلكتروني:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $technician->email }}</span>
                            </div>
                            @if($technician->phone)
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">الهاتف:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $technician->phone }}</span>
                            </div>
                            @endif
                            @if($technician->mobile)
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">الجوال:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $technician->mobile }}</span>
                            </div>
                            @endif
                        </div>
                    </div>

                    <div>
                        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">معلومات العمل</h4>
                        <div class="space-y-2">
                            @if($technician->employee_id)
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">رقم الموظف:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $technician->employee_id }}</span>
                            </div>
                            @endif
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">تاريخ التوظيف:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $technician->hire_date->format('Y-m-d') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">مستوى المهارة:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $technician->skill_level_text }}</span>
                            </div>
                            @if($technician->experience_years)
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">سنوات الخبرة:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">{{ $technician->experience_years }} سنة</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Specializations -->
            @if($technician->specializations && count($technician->specializations) > 0)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">التخصصات</h4>
                <div class="flex flex-wrap gap-2">
                    @foreach($technician->specializations as $specialization)
                        <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            {{ $specialization }}
                        </span>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Performance Metrics -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">مقاييس الأداء</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $metrics['total_repairs'] }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $metrics['completed_repairs'] }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">طلبات مكتملة</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ $metrics['pending_repairs'] }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">طلبات معلقة</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ number_format($metrics['average_rating'], 1) }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">متوسط التقييم</p>
                    </div>
                </div>
            </div>

            <!-- Recent Repairs -->
            @if($recentRepairs->count() > 0)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الطلبات الأخيرة</h4>
                <div class="space-y-3">
                    @foreach($recentRepairs as $repair)
                    <div class="flex justify-between items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->repair_number }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $repair->customer ? $repair->customer->first_name . ' ' . $repair->customer->last_name : 'غير محدد' }}
                            </p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $repair->status_badge['class'] }}">
                                {{ $repair->status_badge['text'] }}
                            </span>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ $repair->created_at->format('Y-m-d') }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
                <div class="mt-4">
                    <a href="{{ route('repairs.index', ['technician_id' => $technician->id]) }}" 
                       class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm font-medium">
                        عرض جميع الطلبات →
                    </a>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات سريعة</h4>
                <div class="space-y-3">
                    <form action="{{ route('technicians.update-availability', $technician) }}" method="POST">
                        @csrf
                        <div>
                            <label for="availability" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تحديث التوفر</label>
                            <select name="availability" id="availability" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="available" {{ $technician->availability == 'available' ? 'selected' : '' }}>متاح</option>
                                <option value="busy" {{ $technician->availability == 'busy' ? 'selected' : '' }}>مشغول</option>
                                <option value="unavailable" {{ $technician->availability == 'unavailable' ? 'selected' : '' }}>غير متاح</option>
                                <option value="on_break" {{ $technician->availability == 'on_break' ? 'selected' : '' }}>في استراحة</option>
                            </select>
                        </div>
                        <button type="submit" class="w-full mt-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm">
                            تحديث التوفر
                        </button>
                    </form>
                </div>
            </div>

            <!-- Current Workload -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">عبء العمل الحالي</h4>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الطلبات النشطة:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">{{ $metrics['current_workload'] }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">مكتمل هذا الشهر:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">{{ $metrics['completed_this_month'] }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">متوسط وقت الإصلاح:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">{{ $metrics['average_repair_time'] }} يوم</span>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            @if($technician->emergency_contact_name || $technician->emergency_contact_phone)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">جهة الاتصال الطارئ</h4>
                <div class="space-y-2">
                    @if($technician->emergency_contact_name)
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الاسم:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">{{ $technician->emergency_contact_name }}</span>
                    </div>
                    @endif
                    @if($technician->emergency_contact_phone)
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الهاتف:</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">{{ $technician->emergency_contact_phone }}</span>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Notes -->
            @if($technician->notes)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">ملاحظات</h4>
                <p class="text-gray-700 dark:text-gray-300 text-sm">{{ $technician->notes }}</p>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
