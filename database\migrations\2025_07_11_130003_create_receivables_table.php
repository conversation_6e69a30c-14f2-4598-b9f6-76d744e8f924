<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('receivables', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id')->comment('معرف العميل');
            $table->unsignedBigInteger('sale_id')->nullable()->comment('معرف المبيعة');
            $table->unsignedBigInteger('repair_id')->nullable()->comment('معرف الإصلاح');
            $table->string('invoice_number', 50)->comment('رقم الفاتورة');
            $table->date('invoice_date')->comment('تاريخ الفاتورة');
            $table->date('due_date')->comment('تاريخ الاستحقاق');
            $table->decimal('original_amount', 15, 2)->comment('المبلغ الأصلي');
            $table->decimal('paid_amount', 15, 2)->default(0)->comment('المبلغ المدفوع');
            $table->decimal('remaining_amount', 15, 2)->comment('المبلغ المتبقي');
            $table->enum('status', ['pending', 'partial', 'paid', 'overdue', 'cancelled'])->default('pending')->comment('الحالة');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->json('payment_history')->nullable()->comment('تاريخ المدفوعات');
            $table->timestamps();

            // Indexes
            $table->index(['customer_id', 'status']);
            $table->index(['due_date', 'status']);
            $table->index(['invoice_number']);
            $table->index(['sale_id']);
            $table->index(['repair_id']);
            
            // Foreign Keys
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->foreign('sale_id')->references('id')->on('sales')->onDelete('set null');
            $table->foreign('repair_id')->references('id')->on('repairs')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('receivables');
    }
};
