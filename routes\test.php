<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use App\Models\Customer;

// Test route for database connectivity and customer table structure
Route::get('/test/customer-db', function () {
    try {
        // Test database connection
        DB::connection()->getPdo();
        echo "✅ Database connection: OK<br>";
        
        // Test customers table exists
        $tableExists = DB::getSchemaBuilder()->hasTable('customers');
        echo "✅ Customers table exists: " . ($tableExists ? 'YES' : 'NO') . "<br>";
        
        if ($tableExists) {
            // Get table columns
            $columns = DB::getSchemaBuilder()->getColumnListing('customers');
            echo "✅ Table columns: " . implode(', ', $columns) . "<br><br>";
            
            // Test enum values for type column
            $result = DB::select("SHOW COLUMNS FROM customers LIKE 'type'");
            if (!empty($result)) {
                echo "✅ Type column definition: " . $result[0]->Type . "<br>";
            }
            
            // Test customer count
            $count = Customer::count();
            echo "✅ Current customer count: " . $count . "<br>";
            
            // Test customer number generation
            $controller = new \App\Http\Controllers\CustomerController();
            $reflection = new ReflectionClass($controller);
            $method = $reflection->getMethod('generateCustomerNumber');
            $method->setAccessible(true);
            $customerNumber = $method->invoke($controller);
            echo "✅ Generated customer number: " . $customerNumber . "<br>";
        }
        
        echo "<br>🎉 All database tests passed!";
        
    } catch (Exception $e) {
        echo "❌ Database test failed: " . $e->getMessage();
    }
})->middleware('auth');

// Test route for form submission simulation
Route::post('/test/customer-create', function (\Illuminate\Http\Request $request) {
    try {
        echo "<h3>Form Submission Test</h3>";
        echo "Received data:<br>";
        foreach ($request->all() as $key => $value) {
            if ($key !== '_token') {
                echo "- {$key}: " . (is_array($value) ? json_encode($value) : $value) . "<br>";
            }
        }
        
        // Test validation
        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'type' => 'required|in:individual,business',
            'first_name' => 'required_if:type,individual|string|max:255',
            'last_name' => 'required_if:type,individual|string|max:255',
            'company_name' => 'required_if:type,business|string|max:255',
            'mobile' => 'required|string|max:20',
        ]);
        
        if ($validator->fails()) {
            echo "<br>❌ Validation failed:<br>";
            foreach ($validator->errors()->all() as $error) {
                echo "- " . $error . "<br>";
            }
        } else {
            echo "<br>✅ Validation passed!";
        }
        
    } catch (Exception $e) {
        echo "❌ Test failed: " . $e->getMessage();
    }
})->middleware('auth');

// Test form for customer creation
Route::get('/test/customer-form', function () {
    return view('test.customer-form-test');
})->middleware('auth');

// Debug route for customer create view
Route::get('/test/customer-create-debug', function () {
    try {
        // Simulate the exact same call as CustomerController@create
        $customerTypes = ['individual', 'business'];
        $genders = ['male', 'female'];
        $contactMethods = ['phone', 'email', 'sms', 'whatsapp'];
        $languages = ['ar', 'en'];
        $sources = ['website', 'referral', 'social_media', 'advertisement', 'walk_in', 'other'];
        $customer = null;

        echo "<h2>Debug: Customer Create View Variables</h2>";
        echo "<pre>";
        echo "customerTypes: " . json_encode($customerTypes) . "\n";
        echo "genders: " . json_encode($genders) . "\n";
        echo "contactMethods: " . json_encode($contactMethods) . "\n";
        echo "languages: " . json_encode($languages) . "\n";
        echo "sources: " . json_encode($sources) . "\n";
        echo "customer: " . json_encode($customer) . "\n";
        echo "</pre>";

        echo "<h3>Attempting to render view...</h3>";
        return view('customers.create', compact(
            'customer', 'customerTypes', 'genders', 'contactMethods', 'languages', 'sources'
        ));

    } catch (\Exception $e) {
        echo "<h3>Error occurred:</h3>";
        echo "<pre>" . $e->getMessage() . "</pre>";
        echo "<h4>Stack trace:</h4>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
})->middleware('auth');
?>
