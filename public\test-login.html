<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار واجهة تسجيل الدخول</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            padding: 2rem;
            direction: rtl;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-item {
            margin-bottom: 1rem;
            padding: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .test-info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            margin: 0.25rem;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار واجهة تسجيل الدخول</h1>
        
        <div class="test-item test-info">
            <h3>✅ الإصلاحات المطبقة:</h3>
            <ul>
                <li><strong>موضع نص الحقوق:</strong> تم نقله خارج auth-container ليظهر في أسفل الصفحة</li>
                <li><strong>مشكلة زر تسجيل الدخول:</strong> تم إصلاح منع الإرسال المتعدد</li>
                <li><strong>زر Theme Toggle:</strong> تم تحويله من anchor إلى button</li>
                <li><strong>تحسينات JavaScript:</strong> إضافة حماية من الإرسال المتعدد</li>
            </ul>
        </div>

        <div class="test-item test-success">
            <h3>🔧 التحسينات المضافة:</h3>
            <ul>
                <li><strong>منع الإرسال المتعدد:</strong> متغير isSubmitting لمنع الضغط المتعدد</li>
                <li><strong>إعادة تعيين الحالة:</strong> عند مغادرة الصفحة أو إعادة التحميل</li>
                <li><strong>تحسين CSS:</strong> موضع ثابت لنص الحقوق في أسفل الصفحة</li>
                <li><strong>تحسين إمكانية الوصول:</strong> استخدام button بدلاً من anchor</li>
            </ul>
        </div>

        <div class="test-item test-info">
            <h3>🧪 خطوات الاختبار:</h3>
            <ol>
                <li>افتح صفحة تسجيل الدخول: <code>/login</code></li>
                <li>تحقق من موضع نص الحقوق في أسفل الصفحة</li>
                <li>أدخل بيانات صحيحة واضغط زر تسجيل الدخول مرة واحدة</li>
                <li>تأكد من عدم إمكانية الضغط مرة أخرى أثناء التحميل</li>
                <li>اختبر زر تبديل الوضع الليلي/النهاري</li>
                <li>اختبر الحسابات التجريبية بالضغط عليها</li>
            </ol>
        </div>

        <div class="test-item">
            <h3>🔗 روابط سريعة:</h3>
            <button class="btn" onclick="window.open('/login', '_blank')">فتح صفحة تسجيل الدخول</button>
            <button class="btn" onclick="testResponsive()">اختبار التصميم المتجاوب</button>
            <button class="btn" onclick="testAccessibility()">اختبار إمكانية الوصول</button>
        </div>

        <div class="test-item test-success">
            <h3>📱 الحسابات التجريبية:</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div>
                    <strong>مدير النظام:</strong><br>
                    <EMAIL><br>
                    password123
                </div>
                <div>
                    <strong>مدير:</strong><br>
                    <EMAIL><br>
                    password123
                </div>
                <div>
                    <strong>فني:</strong><br>
                    <EMAIL><br>
                    password123
                </div>
                <div>
                    <strong>كاشير:</strong><br>
                    <EMAIL><br>
                    password123
                </div>
            </div>
        </div>

        <div class="test-item">
            <h3>⌨️ اختصارات لوحة المفاتيح:</h3>
            <ul>
                <li><kbd>Alt + T</kbd>: تبديل الوضع الليلي/النهاري</li>
                <li><kbd>Alt + P</kbd>: إظهار/إخفاء كلمة المرور</li>
                <li><kbd>Enter</kbd>: إرسال النموذج</li>
            </ul>
        </div>

        <div class="test-item test-error">
            <h3>⚠️ نقاط مهمة للاختبار:</h3>
            <ul>
                <li>تأكد من أن النموذج يرسل من الضغطة الأولى</li>
                <li>تحقق من عدم إمكانية الضغط المتعدد</li>
                <li>اختبر على أجهزة مختلفة (موبايل، تابلت، ديسكتوب)</li>
                <li>اختبر في متصفحات مختلفة</li>
                <li>تأكد من عمل الوضع الليلي/النهاري</li>
            </ul>
        </div>
    </div>

    <script>
        function testResponsive() {
            const sizes = [
                { width: 375, height: 667, name: 'iPhone' },
                { width: 768, height: 1024, name: 'iPad' },
                { width: 1920, height: 1080, name: 'Desktop' }
            ];
            
            sizes.forEach(size => {
                window.open(
                    '/login',
                    `test_${size.name}`,
                    `width=${size.width},height=${size.height},scrollbars=yes,resizable=yes`
                );
            });
        }

        function testAccessibility() {
            alert('اختبر النقاط التالية:\n\n' +
                  '1. التنقل بـ Tab\n' +
                  '2. استخدام قارئ الشاشة\n' +
                  '3. تباين الألوان\n' +
                  '4. حجم النصوص\n' +
                  '5. وضوح التركيز');
        }

        // إضافة معلومات المتصفح
        document.addEventListener('DOMContentLoaded', function() {
            const browserInfo = document.createElement('div');
            browserInfo.className = 'test-item test-info';
            browserInfo.innerHTML = `
                <h3>🌐 معلومات المتصفح:</h3>
                <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
                <p><strong>الشاشة:</strong> ${screen.width}x${screen.height}</p>
                <p><strong>النافذة:</strong> ${window.innerWidth}x${window.innerHeight}</p>
                <p><strong>اللغة:</strong> ${navigator.language}</p>
            `;
            document.querySelector('.test-container').appendChild(browserInfo);
        });
    </script>
</body>
</html>
