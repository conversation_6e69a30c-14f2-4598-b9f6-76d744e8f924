<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\JournalEntry;
use App\Models\Account;
use App\Models\User;

class JournalEntriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user or create one
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'مدير النظام',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'admin',
            ]);
        }

        // Get accounts
        $cashAccount = Account::where('account_code', '11101')->first(); // الصندوق
        $bankAccount = Account::where('account_code', '11102')->first(); // البنك الأهلي
        $salesAccount = Account::where('account_code', '41100')->first(); // مبيعات قطع الغيار
        $receivableAccount = Account::where('account_code', '11201')->first(); // ذمم العملاء
        $inventoryAccount = Account::where('account_code', '11301')->first(); // مخزون قطع الغيار
        $cogsAccount = Account::where('account_code', '51000')->first(); // تكلفة البضاعة المباعة
        $expenseAccount = Account::where('account_code', '52200')->first(); // الإيجار

        if (!$cashAccount || !$bankAccount || !$salesAccount || !$receivableAccount) {
            $this->command->error('بعض الحسابات المطلوبة غير موجودة. يرجى تشغيل ChartOfAccountsSeeder أولاً.');
            return;
        }

        // Sample journal entries
        $entries = [
            // 1. Cash sale
            [
                'entry_date' => now()->subDays(10),
                'description' => 'مبيعة نقدية - فاتورة رقم 001',
                'reference_type' => 'sale',
                'reference_id' => 1,
                'transactions' => [
                    [
                        'account_id' => $cashAccount->id,
                        'debit_amount' => 1150.00,
                        'credit_amount' => 0,
                        'description' => 'تحصيل نقدي',
                    ],
                    [
                        'account_id' => $salesAccount->id,
                        'debit_amount' => 0,
                        'credit_amount' => 1000.00,
                        'description' => 'مبيعات قطع غيار',
                    ],
                    [
                        'account_id' => Account::where('account_code', '21300')->first()->id, // ضريبة القيمة المضافة
                        'debit_amount' => 0,
                        'credit_amount' => 150.00,
                        'description' => 'ضريبة قيمة مضافة 15%',
                    ],
                ]
            ],

            // 2. Credit sale
            [
                'entry_date' => now()->subDays(8),
                'description' => 'مبيعة آجلة - فاتورة رقم 002',
                'reference_type' => 'sale',
                'reference_id' => 2,
                'transactions' => [
                    [
                        'account_id' => $receivableAccount->id,
                        'debit_amount' => 2300.00,
                        'credit_amount' => 0,
                        'description' => 'ذمة عميل',
                    ],
                    [
                        'account_id' => $salesAccount->id,
                        'debit_amount' => 0,
                        'credit_amount' => 2000.00,
                        'description' => 'مبيعات قطع غيار',
                    ],
                    [
                        'account_id' => Account::where('account_code', '21300')->first()->id,
                        'debit_amount' => 0,
                        'credit_amount' => 300.00,
                        'description' => 'ضريبة قيمة مضافة 15%',
                    ],
                ]
            ],

            // 3. Payment received
            [
                'entry_date' => now()->subDays(5),
                'description' => 'تحصيل من عميل - دفعة جزئية',
                'reference_type' => 'payment',
                'reference_id' => 1,
                'transactions' => [
                    [
                        'account_id' => $cashAccount->id,
                        'debit_amount' => 1000.00,
                        'credit_amount' => 0,
                        'description' => 'تحصيل نقدي',
                    ],
                    [
                        'account_id' => $receivableAccount->id,
                        'debit_amount' => 0,
                        'credit_amount' => 1000.00,
                        'description' => 'تسديد جزئي لذمة عميل',
                    ],
                ]
            ],

            // 4. Inventory purchase
            [
                'entry_date' => now()->subDays(7),
                'description' => 'شراء مخزون قطع غيار',
                'reference_type' => 'purchase',
                'reference_id' => 1,
                'transactions' => [
                    [
                        'account_id' => $inventoryAccount->id,
                        'debit_amount' => 5000.00,
                        'credit_amount' => 0,
                        'description' => 'شراء قطع غيار',
                    ],
                    [
                        'account_id' => Account::where('account_code', '21101')->first()->id, // ذمم الموردين
                        'debit_amount' => 0,
                        'credit_amount' => 5000.00,
                        'description' => 'ذمة مورد',
                    ],
                ]
            ],

            // 5. Expense payment
            [
                'entry_date' => now()->subDays(3),
                'description' => 'دفع إيجار الشهر الحالي',
                'reference_type' => 'expense',
                'reference_id' => 1,
                'transactions' => [
                    [
                        'account_id' => $expenseAccount->id,
                        'debit_amount' => 3000.00,
                        'credit_amount' => 0,
                        'description' => 'إيجار شهر ' . now()->format('m/Y'),
                    ],
                    [
                        'account_id' => $cashAccount->id,
                        'debit_amount' => 0,
                        'credit_amount' => 3000.00,
                        'description' => 'دفع نقدي',
                    ],
                ]
            ],

            // 6. Bank transfer
            [
                'entry_date' => now()->subDays(2),
                'description' => 'تحويل من الصندوق إلى البنك',
                'reference_type' => 'transfer',
                'reference_id' => 1,
                'transactions' => [
                    [
                        'account_id' => $bankAccount->id,
                        'debit_amount' => 10000.00,
                        'credit_amount' => 0,
                        'description' => 'إيداع في البنك',
                    ],
                    [
                        'account_id' => $cashAccount->id,
                        'debit_amount' => 0,
                        'credit_amount' => 10000.00,
                        'description' => 'سحب من الصندوق',
                    ],
                ]
            ],
        ];

        foreach ($entries as $entryData) {
            $transactions = $entryData['transactions'];
            unset($entryData['transactions']);

            // Calculate totals
            $totalDebit = collect($transactions)->sum('debit_amount');
            $totalCredit = collect($transactions)->sum('credit_amount');

            // Create journal entry
            $entry = JournalEntry::create([
                'entry_number' => JournalEntry::generateEntryNumber(),
                'entry_date' => $entryData['entry_date'],
                'description' => $entryData['description'],
                'reference_type' => $entryData['reference_type'] ?? null,
                'reference_id' => $entryData['reference_id'] ?? null,
                'total_debit' => $totalDebit,
                'total_credit' => $totalCredit,
                'status' => 'posted', // Post immediately for demo
                'created_by' => $user->id,
                'posted_by' => $user->id,
                'posted_at' => now(),
            ]);

            // Create transactions
            foreach ($transactions as $transaction) {
                $entry->transactions()->create($transaction);
            }

            // Update account balances
            foreach ($entry->transactions as $transaction) {
                $account = $transaction->account;
                if ($account->balance_type === 'debit') {
                    $account->current_balance += $transaction->debit_amount - $transaction->credit_amount;
                } else {
                    $account->current_balance += $transaction->credit_amount - $transaction->debit_amount;
                }
                $account->save();
            }
        }

        $this->command->info('تم إنشاء ' . count($entries) . ' قيد محاسبي تجريبي');
    }
}
