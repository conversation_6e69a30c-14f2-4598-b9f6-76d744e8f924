@extends('layouts.main')

@section('title', 'تحليلات الأداء - ' . $technician->full_name)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تحليلات الأداء</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $technician->full_name }} - {{ $technician->department }}</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('technicians.show', $technician) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة للملف الشخصي
            </a>
            <a href="{{ route('technicians.performance-report', $technician) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-file-alt mr-2"></i>
                تقرير مفصل
            </a>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <form method="GET" action="{{ route('technicians.analytics', $technician) }}" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" name="start_date" value="{{ $startDate->format('Y-m-d') }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" name="end_date" value="{{ $endDate->format('Y-m-d') }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-search mr-2"></i>
                تطبيق
            </button>
        </form>
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي الطلبات</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-gray-100">{{ $metrics['total_repairs'] }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">في الفترة المحددة</p>
                </div>
                <div class="text-blue-600">
                    <i class="fas fa-tools text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">الطلبات المكتملة</p>
                    <p class="text-3xl font-bold text-green-600">{{ $metrics['completed_repairs'] }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        {{ $metrics['total_repairs'] > 0 ? round(($metrics['completed_repairs'] / $metrics['total_repairs']) * 100, 1) : 0 }}% معدل الإنجاز
                    </p>
                </div>
                <div class="text-green-600">
                    <i class="fas fa-check-circle text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">متوسط وقت الإنجاز</p>
                    <p class="text-3xl font-bold text-orange-600">{{ $metrics['average_completion_time'] ?? 'N/A' }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">ساعة</p>
                </div>
                <div class="text-orange-600">
                    <i class="fas fa-clock text-2xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">تقييم العملاء</p>
                    <p class="text-3xl font-bold text-yellow-600">{{ number_format($metrics['customer_satisfaction'] ?? 0, 1) }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">من 5 نجوم</p>
                </div>
                <div class="text-yellow-600">
                    <i class="fas fa-star text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue and Efficiency -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الإيرادات المحققة</h3>
            <div class="text-center">
                <p class="text-4xl font-bold text-green-600">{{ number_format($metrics['revenue_generated'] ?? 0, 2) }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">ريال سعودي</p>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">نقاط الكفاءة</h3>
            <div class="text-center">
                <p class="text-4xl font-bold text-purple-600">{{ number_format($metrics['efficiency_score'] ?? 0, 1) }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">من 100 نقطة</p>
            </div>
        </div>
    </div>

    <!-- Daily Performance Chart -->
    @if(!empty($dailyPerformance))
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الأداء اليومي</h3>
        </div>
        <div class="p-6">
            <canvas id="dailyPerformanceChart" width="400" height="200"></canvas>
        </div>
    </div>
    @endif

    <!-- Repair Status Breakdown -->
    @if(!empty($statusBreakdown))
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">توزيع حالات الطلبات</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                @foreach($statusBreakdown as $status => $count)
                    <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $count }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            @switch($status)
                                @case('pending') في الانتظار @break
                                @case('in_progress') قيد التنفيذ @break
                                @case('completed') مكتمل @break
                                @case('delivered') تم التسليم @break
                                @default {{ $status }}
                            @endswitch
                        </p>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Skills Assessment -->
    @if(!empty($skillsAssessment))
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تقييم المهارات</h3>
                <a href="{{ route('technicians.skills', $technician) }}" class="text-blue-600 hover:text-blue-700 text-sm">
                    إدارة المهارات
                </a>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                @foreach($skillsAssessment as $skill)
                    <div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ $skill['name'] }}</span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ $skill['proficiency'] }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $skill['proficiency'] }}%"></div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Performance Trends -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">اتجاهات الأداء</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600 mb-2">↗️</div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">تحسن في معدل الإنجاز</p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-gray-100">+15%</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600 mb-2">⚡</div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">تحسن في السرعة</p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-gray-100">-2 ساعة</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-yellow-600 mb-2">⭐</div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">تحسن في التقييم</p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-gray-100">+0.3</p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
@if(!empty($dailyPerformance))
// Daily Performance Chart
const ctx = document.getElementById('dailyPerformanceChart').getContext('2d');
const dailyPerformanceChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {!! json_encode(array_column($dailyPerformance, 'date')) !!},
        datasets: [{
            label: 'الطلبات المكتملة',
            data: {!! json_encode(array_column($dailyPerformance, 'completed')) !!},
            borderColor: 'rgb(34, 197, 94)',
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            tension: 0.1
        }, {
            label: 'إجمالي الطلبات',
            data: {!! json_encode(array_column($dailyPerformance, 'total')) !!},
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: 'الأداء اليومي'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
@endif
</script>
@endpush
@endsection
