@extends('layouts.main')

@section('page-title', 'إدارة المصروفات')
@section('page-description', 'تتبع وإدارة جميع المصروفات')

@section('content')
<div class="space-y-6" x-data="expensesManager()">
    <!-- Header Actions -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إدارة المصروفات</h1>
            <p class="text-gray-600 dark:text-gray-400">تتبع وإدارة جميع المصروفات والنفقات</p>
        </div>
        <div class="flex space-x-3 space-x-reverse">
            <button @click="showAddModal = true" class="btn-primary">
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                إضافة مصروف جديد
            </button>
            <button @click="exportExpenses()" class="btn-secondary">
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير Excel
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="card p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مصروفات اليوم</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="formatCurrency(todayExpenses)">125,000 د.ع</p>
                </div>
            </div>
        </div>

        <div class="card p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مصروفات الشهر</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="formatCurrency(monthExpenses)">2,850,000 د.ع</p>
                </div>
            </div>
        </div>

        <div class="card p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">عدد المصروفات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="expenses.length">48</p>
                </div>
            </div>
        </div>

        <div class="card p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوسط المصروف</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="formatCurrency(averageExpense)">59,375 د.ع</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart -->
    <div class="card p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">المصروفات حسب التصنيف</h3>
            <div class="flex space-x-2 space-x-reverse">
                <button @click="chartPeriod = 'week'" 
                        :class="chartPeriod === 'week' ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'"
                        class="px-3 py-1 text-sm rounded-md">أسبوع</button>
                <button @click="chartPeriod = 'month'" 
                        :class="chartPeriod === 'month' ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'"
                        class="px-3 py-1 text-sm rounded-md">شهر</button>
            </div>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <template x-for="category in expensesByCategory" :key="category.name">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-2 rounded-full flex items-center justify-center"
                         :style="'background-color: ' + category.color + '20; color: ' + category.color">
                        <span class="text-2xl font-bold" x-text="Math.round((category.amount / totalExpenses) * 100) + '%'"></span>
                    </div>
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="category.name"></p>
                    <p class="text-xs text-gray-500 dark:text-gray-400" x-text="formatCurrency(category.amount)"></p>
                </div>
            </template>
        </div>
    </div>

    <!-- Filters -->
    <div class="card p-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <!-- Search -->
            <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    <input type="text" 
                           x-model="searchQuery"
                           placeholder="البحث في الوصف..." 
                           class="input-field pl-10">
                </div>
            </div>

            <!-- Category Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التصنيف</label>
                <select x-model="selectedCategory" class="input-field">
                    <option value="">جميع التصنيفات</option>
                    <option value="rent">إيجار</option>
                    <option value="utilities">فواتير</option>
                    <option value="supplies">مستلزمات</option>
                    <option value="maintenance">صيانة</option>
                    <option value="marketing">تسويق</option>
                    <option value="salaries">رواتب</option>
                    <option value="other">أخرى</option>
                </select>
            </div>

            <!-- Date From -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
                <input type="date" x-model="dateFrom" class="input-field">
            </div>

            <!-- Date To -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
                <input type="date" x-model="dateTo" class="input-field">
            </div>
        </div>
    </div>

    <!-- Expenses Table -->
    <div class="card overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            التاريخ
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الوصف
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            التصنيف
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            المبلغ
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            طريقة الدفع
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="expense in filteredExpenses" :key="expense.id">
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" 
                                x-text="formatDate(expense.date)"></td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="expense.description"></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400" x-text="expense.notes"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                      :class="getCategoryColor(expense.category)"
                                      x-text="getCategoryName(expense.category)"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600 dark:text-red-400" 
                                x-text="formatCurrency(expense.amount)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400" 
                                x-text="getPaymentMethodName(expense.payment_method)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button @click="editExpense(expense)" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                                        تعديل
                                    </button>
                                    <button @click="deleteExpense(expense.id)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                        حذف
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <!-- Empty State -->
        <div x-show="filteredExpenses.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد مصروفات</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">ابدأ بإضافة مصروف جديد</p>
            <div class="mt-6">
                <button @click="showAddModal = true" class="btn-primary">
                    إضافة مصروف جديد
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function expensesManager() {
    return {
        searchQuery: '',
        selectedCategory: '',
        dateFrom: '',
        dateTo: '',
        chartPeriod: 'month',
        showAddModal: false,
        
        expenses: [
            {
                id: 1,
                date: '2024-01-15',
                description: 'إيجار المحل',
                category: 'rent',
                amount: 500000,
                payment_method: 'cash',
                notes: 'إيجار شهر يناير'
            },
            {
                id: 2,
                date: '2024-01-14',
                description: 'فاتورة الكهرباء',
                category: 'utilities',
                amount: 125000,
                payment_method: 'bank_transfer',
                notes: 'فاتورة شهر ديسمبر'
            },
            {
                id: 3,
                date: '2024-01-13',
                description: 'مستلزمات مكتبية',
                category: 'supplies',
                amount: 75000,
                payment_method: 'cash',
                notes: 'أوراق وأقلام'
            },
            {
                id: 4,
                date: '2024-01-12',
                description: 'صيانة أجهزة',
                category: 'maintenance',
                amount: 180000,
                payment_method: 'cash',
                notes: 'صيانة أجهزة الكمبيوتر'
            }
        ],
        
        get filteredExpenses() {
            return this.expenses.filter(expense => {
                const matchesSearch = expense.description.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                                    expense.notes.toLowerCase().includes(this.searchQuery.toLowerCase());
                const matchesCategory = !this.selectedCategory || expense.category === this.selectedCategory;
                const matchesDateFrom = !this.dateFrom || expense.date >= this.dateFrom;
                const matchesDateTo = !this.dateTo || expense.date <= this.dateTo;
                
                return matchesSearch && matchesCategory && matchesDateFrom && matchesDateTo;
            });
        },
        
        get todayExpenses() {
            const today = new Date().toISOString().split('T')[0];
            return this.expenses
                .filter(expense => expense.date === today)
                .reduce((sum, expense) => sum + expense.amount, 0);
        },
        
        get monthExpenses() {
            const thisMonth = new Date().getMonth();
            const thisYear = new Date().getFullYear();
            return this.expenses
                .filter(expense => {
                    const expenseDate = new Date(expense.date);
                    return expenseDate.getMonth() === thisMonth && expenseDate.getFullYear() === thisYear;
                })
                .reduce((sum, expense) => sum + expense.amount, 0);
        },
        
        get totalExpenses() {
            return this.expenses.reduce((sum, expense) => sum + expense.amount, 0);
        },
        
        get averageExpense() {
            return this.expenses.length > 0 ? this.totalExpenses / this.expenses.length : 0;
        },
        
        get expensesByCategory() {
            const categories = {};
            const colors = {
                'rent': '#ef4444',
                'utilities': '#f59e0b',
                'supplies': '#10b981',
                'maintenance': '#3b82f6',
                'marketing': '#8b5cf6',
                'salaries': '#06b6d4',
                'other': '#6b7280'
            };
            
            this.expenses.forEach(expense => {
                if (!categories[expense.category]) {
                    categories[expense.category] = {
                        name: this.getCategoryName(expense.category),
                        amount: 0,
                        color: colors[expense.category] || '#6b7280'
                    };
                }
                categories[expense.category].amount += expense.amount;
            });
            
            return Object.values(categories);
        },
        
        getCategoryName(category) {
            const categories = {
                'rent': 'إيجار',
                'utilities': 'فواتير',
                'supplies': 'مستلزمات',
                'maintenance': 'صيانة',
                'marketing': 'تسويق',
                'salaries': 'رواتب',
                'other': 'أخرى'
            };
            return categories[category] || category;
        },
        
        getCategoryColor(category) {
            const colors = {
                'rent': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                'utilities': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                'supplies': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                'maintenance': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                'marketing': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
                'salaries': 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-300',
                'other': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
            };
            return colors[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        },
        
        getPaymentMethodName(method) {
            const methods = {
                'cash': 'نقدي',
                'bank_transfer': 'تحويل بنكي',
                'card': 'بطاقة',
                'check': 'شيك'
            };
            return methods[method] || method;
        },
        
        editExpense(expense) {
            alert(`تعديل المصروف: ${expense.description}`);
        },
        
        deleteExpense(expenseId) {
            if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
                this.expenses = this.expenses.filter(e => e.id !== expenseId);
                alert('تم حذف المصروف بنجاح');
            }
        },
        
        exportExpenses() {
            alert('سيتم تصدير المصروفات إلى Excel');
        },
        
        formatCurrency(amount) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'currency',
                currency: 'IQD',
                minimumFractionDigits: 0
            }).format(amount);
        },
        
        formatDate(date) {
            return new Intl.DateTimeFormat('ar-IQ', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            }).format(new Date(date));
        }
    }
}
</script>
@endpush
@endsection
