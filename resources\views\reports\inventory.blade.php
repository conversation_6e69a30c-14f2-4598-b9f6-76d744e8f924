@extends('layouts.main')

@section('title', 'تقارير المخزون')

@section('content')
<div class="space-y-6" x-data="inventoryReports()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تقارير المخزون</h1>
            <p class="text-gray-600 dark:text-gray-400">تحليل شامل لحالة المخزون والحركة</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير التقرير
            </button>
            <button @click="printReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                طباعة
            </button>
        </div>
    </div>

    <!-- Inventory Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الأصناف</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.totalItems">245</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">متوفر</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.inStock">198</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مخزون منخفض</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.lowStock">23</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">نفد المخزون</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="summary.outOfStock">24</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفئة</label>
                <select x-model="filters.category" @change="filterItems()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الفئات</option>
                    <option value="parts">قطع غيار</option>
                    <option value="accessories">إكسسوارات</option>
                    <option value="tools">أدوات</option>
                    <option value="supplies">مستلزمات</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة المخزون</label>
                <select x-model="filters.stockStatus" @change="filterItems()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الحالات</option>
                    <option value="in_stock">متوفر</option>
                    <option value="low_stock">مخزون منخفض</option>
                    <option value="out_of_stock">نفد المخزون</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المورد</label>
                <select x-model="filters.supplier" @change="filterItems()" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                    <option value="">جميع الموردين</option>
                    <option value="supplier1">مورد الأجهزة الذكية</option>
                    <option value="supplier2">شركة قطع الغيار</option>
                    <option value="supplier3">مورد الإكسسوارات</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البحث</label>
                <input type="text" x-model="filters.search" @input="filterItems()" placeholder="البحث في المنتجات..." class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
        </div>
    </div>

    <!-- Inventory Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المنتج</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفئة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية الحالية</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحد الأدنى</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">السعر</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">القيمة الإجمالية</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="item in filteredItems" :key="item.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                                        <span class="text-xs font-medium text-gray-600 dark:text-gray-300" x-text="item.name.charAt(0)"></span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="item.name"></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400" x-text="item.sku"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="getCategoryText(item.category)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="item.currentStock"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100" x-text="item.minStock"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">₪<span x-text="item.price"></span></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">₪<span x-text="(item.currentStock * item.price).toFixed(2)"></span></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusColor(item)" x-text="getStatusText(item)"></span>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Stock Value by Category -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">قيمة المخزون حسب الفئة</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">رسم دائري لقيمة المخزون</p>
                </div>
            </div>
        </div>

        <!-- Stock Movement -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">حركة المخزون</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">رسم بياني لحركة المخزون</p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function inventoryReports() {
    return {
        summary: {
            totalItems: 245,
            inStock: 198,
            lowStock: 23,
            outOfStock: 24
        },
        filters: {
            category: '',
            stockStatus: '',
            supplier: '',
            search: ''
        },
        items: [
            {
                id: 1,
                name: 'شاشة iPhone 13',
                sku: 'IP13-SCR-001',
                category: 'parts',
                currentStock: 15,
                minStock: 5,
                price: 250,
                supplier: 'supplier1'
            },
            {
                id: 2,
                name: 'بطارية Samsung S21',
                sku: 'SAM-BAT-021',
                category: 'parts',
                currentStock: 3,
                minStock: 10,
                price: 80,
                supplier: 'supplier2'
            },
            {
                id: 3,
                name: 'كفر حماية شفاف',
                sku: 'ACC-CVR-001',
                category: 'accessories',
                currentStock: 0,
                minStock: 20,
                price: 15,
                supplier: 'supplier3'
            }
        ],
        filteredItems: [],

        init() {
            this.filteredItems = [...this.items];
        },

        filterItems() {
            this.filteredItems = this.items.filter(item => {
                const matchesCategory = !this.filters.category || item.category === this.filters.category;
                const matchesSupplier = !this.filters.supplier || item.supplier === this.filters.supplier;
                const matchesSearch = !this.filters.search || 
                    item.name.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    item.sku.toLowerCase().includes(this.filters.search.toLowerCase());
                
                let matchesStatus = true;
                if (this.filters.stockStatus) {
                    if (this.filters.stockStatus === 'out_of_stock') {
                        matchesStatus = item.currentStock === 0;
                    } else if (this.filters.stockStatus === 'low_stock') {
                        matchesStatus = item.currentStock > 0 && item.currentStock <= item.minStock;
                    } else if (this.filters.stockStatus === 'in_stock') {
                        matchesStatus = item.currentStock > item.minStock;
                    }
                }

                return matchesCategory && matchesSupplier && matchesSearch && matchesStatus;
            });
        },

        getCategoryText(category) {
            const categories = {
                'parts': 'قطع غيار',
                'accessories': 'إكسسوارات',
                'tools': 'أدوات',
                'supplies': 'مستلزمات'
            };
            return categories[category] || category;
        },

        getStatusText(item) {
            if (item.currentStock === 0) return 'نفد المخزون';
            if (item.currentStock <= item.minStock) return 'مخزون منخفض';
            return 'متوفر';
        },

        getStatusColor(item) {
            if (item.currentStock === 0) {
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            }
            if (item.currentStock <= item.minStock) {
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
            }
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
        },

        exportReport() {
            alert('سيتم تصدير تقرير المخزون');
        },

        printReport() {
            window.print();
        }
    }
}
</script>
@endpush
@endsection
