<?php

namespace App\Http\Controllers;

use App\Models\JournalEntry;
use App\Models\Account;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class JournalEntryController extends Controller
{
    /**
     * Display journal entries
     */
    public function index(Request $request)
    {
        $query = JournalEntry::with(['creator', 'poster', 'transactions.account']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->where('entry_date', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->where('entry_date', '<=', $request->end_date);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('entry_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $entries = $query->orderBy('entry_date', 'desc')
                        ->orderBy('entry_number', 'desc')
                        ->paginate(20);

        return view('accounting.journal-entries.index', compact('entries'));
    }

    /**
     * Show create form
     */
    public function create()
    {
        $accounts = Account::where('is_active', true)
                          ->orderBy('account_code')
                          ->get();

        return view('accounting.journal-entries.create', compact('accounts'));
    }

    /**
     * Store new journal entry
     */
    public function store(Request $request)
    {
        $request->validate([
            'entry_date' => 'required|date',
            'description' => 'required|string|max:255',
            'transactions' => 'required|array|min:2',
            'transactions.*.account_id' => 'required|exists:accounts,id',
            'transactions.*.debit_amount' => 'nullable|numeric|min:0',
            'transactions.*.credit_amount' => 'nullable|numeric|min:0',
            'transactions.*.description' => 'nullable|string|max:255',
        ]);

        // Validate that each transaction has either debit or credit (not both)
        foreach ($request->transactions as $index => $transaction) {
            $debit = $transaction['debit_amount'] ?? 0;
            $credit = $transaction['credit_amount'] ?? 0;
            
            if (($debit > 0 && $credit > 0) || ($debit == 0 && $credit == 0)) {
                return back()->withErrors([
                    "transactions.{$index}" => 'كل معاملة يجب أن تحتوي على مبلغ مدين أو دائن فقط'
                ])->withInput();
            }
        }

        // Calculate totals
        $totalDebit = collect($request->transactions)->sum('debit_amount');
        $totalCredit = collect($request->transactions)->sum('credit_amount');

        if (abs($totalDebit - $totalCredit) > 0.01) {
            return back()->withErrors([
                'transactions' => 'إجمالي المدين يجب أن يساوي إجمالي الدائن'
            ])->withInput();
        }

        DB::beginTransaction();
        
        try {
            $entry = JournalEntry::create([
                'entry_number' => JournalEntry::generateEntryNumber(),
                'entry_date' => $request->entry_date,
                'description' => $request->description,
                'total_debit' => $totalDebit,
                'total_credit' => $totalCredit,
                'status' => 'draft',
                'created_by' => auth()->id(),
            ]);

            foreach ($request->transactions as $transaction) {
                $entry->transactions()->create([
                    'account_id' => $transaction['account_id'],
                    'debit_amount' => $transaction['debit_amount'] ?? 0,
                    'credit_amount' => $transaction['credit_amount'] ?? 0,
                    'description' => $transaction['description'] ?? '',
                ]);
            }

            DB::commit();

            return redirect()->route('journal-entries.show', $entry)
                           ->with('success', 'تم إنشاء القيد بنجاح');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'حدث خطأ أثناء إنشاء القيد'])
                        ->withInput();
        }
    }

    /**
     * Show journal entry details
     */
    public function show(JournalEntry $journalEntry)
    {
        $journalEntry->load(['creator', 'poster', 'reverser', 'transactions.account']);
        
        return view('accounting.journal-entries.show', compact('journalEntry'));
    }

    /**
     * Show edit form
     */
    public function edit(JournalEntry $journalEntry)
    {
        if (!$journalEntry->canBeEdited()) {
            return redirect()->route('journal-entries.index')
                           ->with('error', 'لا يمكن تعديل هذا القيد');
        }

        $accounts = Account::where('is_active', true)
                          ->orderBy('account_code')
                          ->get();

        $journalEntry->load('transactions.account');

        return view('accounting.journal-entries.edit', compact('journalEntry', 'accounts'));
    }

    /**
     * Update journal entry
     */
    public function update(Request $request, JournalEntry $journalEntry)
    {
        if (!$journalEntry->canBeEdited()) {
            return redirect()->route('journal-entries.index')
                           ->with('error', 'لا يمكن تعديل هذا القيد');
        }

        $request->validate([
            'entry_date' => 'required|date',
            'description' => 'required|string|max:255',
            'transactions' => 'required|array|min:2',
            'transactions.*.account_id' => 'required|exists:accounts,id',
            'transactions.*.debit_amount' => 'nullable|numeric|min:0',
            'transactions.*.credit_amount' => 'nullable|numeric|min:0',
            'transactions.*.description' => 'nullable|string|max:255',
        ]);

        // Calculate totals
        $totalDebit = collect($request->transactions)->sum('debit_amount');
        $totalCredit = collect($request->transactions)->sum('credit_amount');

        if (abs($totalDebit - $totalCredit) > 0.01) {
            return back()->withErrors([
                'transactions' => 'إجمالي المدين يجب أن يساوي إجمالي الدائن'
            ])->withInput();
        }

        DB::beginTransaction();
        
        try {
            $journalEntry->update([
                'entry_date' => $request->entry_date,
                'description' => $request->description,
                'total_debit' => $totalDebit,
                'total_credit' => $totalCredit,
            ]);

            // Delete existing transactions
            $journalEntry->transactions()->delete();

            // Create new transactions
            foreach ($request->transactions as $transaction) {
                $journalEntry->transactions()->create([
                    'account_id' => $transaction['account_id'],
                    'debit_amount' => $transaction['debit_amount'] ?? 0,
                    'credit_amount' => $transaction['credit_amount'] ?? 0,
                    'description' => $transaction['description'] ?? '',
                ]);
            }

            DB::commit();

            return redirect()->route('journal-entries.show', $journalEntry)
                           ->with('success', 'تم تحديث القيد بنجاح');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'حدث خطأ أثناء تحديث القيد'])
                        ->withInput();
        }
    }

    /**
     * Delete journal entry
     */
    public function destroy(JournalEntry $journalEntry)
    {
        if (!$journalEntry->canBeEdited()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف هذا القيد'
            ]);
        }

        try {
            $journalEntry->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف القيد بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف القيد'
            ]);
        }
    }

    /**
     * Post journal entry
     */
    public function post(JournalEntry $journalEntry)
    {
        if (!$journalEntry->canBePosted()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن ترحيل هذا القيد'
            ]);
        }

        try {
            $journalEntry->post(auth()->id());

            return response()->json([
                'success' => true,
                'message' => 'تم ترحيل القيد بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Reverse journal entry
     */
    public function reverse(Request $request, JournalEntry $journalEntry)
    {
        if (!$journalEntry->canBeReversed()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن عكس هذا القيد'
            ]);
        }

        $request->validate([
            'reason' => 'required|string|max:255'
        ]);

        try {
            $reversalEntry = $journalEntry->reverse(auth()->id(), $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'تم عكس القيد بنجاح',
                'reversal_entry_id' => $reversalEntry->id
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
