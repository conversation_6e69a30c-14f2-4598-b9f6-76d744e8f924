# تصميم النظام المحاسبي المتكامل

## 1. هيكل قاعدة البيانات

### الجداول الأساسية:

#### 1.1 جدول الحسابات (accounts)
```sql
- id (Primary Key)
- account_code (رقم الحساب - فريد)
- account_name (اسم الحساب)
- account_name_en (اسم الحساب بالإنجليزية)
- parent_id (الحساب الأب - للهيكل الشجري)
- account_type (نوع الحساب: asset, liability, equity, revenue, expense)
- account_category (فئة الحساب: current_asset, fixed_asset, etc.)
- is_active (نشط/غير نشط)
- is_system (حساب نظام لا يمكن حذفه)
- balance_type (طبيعة الرصيد: debit, credit)
- opening_balance (الرصيد الافتتاحي)
- current_balance (الرصيد الحالي)
- description (وصف الحساب)
- created_at, updated_at
```

#### 1.2 جدول القيود المحاسبية (journal_entries)
```sql
- id (Primary Key)
- entry_number (رقم القيد - فريد)
- entry_date (تاريخ القيد)
- reference_type (نوع المرجع: sale, purchase, payment, etc.)
- reference_id (معرف المرجع)
- description (وصف القيد)
- total_debit (إجمالي المدين)
- total_credit (إجمالي الدائن)
- status (حالة القيد: draft, posted, reversed)
- created_by (المستخدم المنشئ)
- posted_by (المستخدم المرحل)
- posted_at (تاريخ الترحيل)
- created_at, updated_at
```

#### 1.3 جدول تفاصيل القيود (account_transactions)
```sql
- id (Primary Key)
- journal_entry_id (معرف القيد)
- account_id (معرف الحساب)
- debit_amount (المبلغ المدين)
- credit_amount (المبلغ الدائن)
- description (وصف العملية)
- created_at, updated_at
```

#### 1.4 جدول الذمم المدينة (receivables)
```sql
- id (Primary Key)
- customer_id (معرف العميل)
- sale_id (معرف المبيعة)
- invoice_number (رقم الفاتورة)
- invoice_date (تاريخ الفاتورة)
- due_date (تاريخ الاستحقاق)
- original_amount (المبلغ الأصلي)
- paid_amount (المبلغ المدفوع)
- remaining_amount (المبلغ المتبقي)
- status (الحالة: pending, partial, paid, overdue)
- created_at, updated_at
```

#### 1.5 جدول الذمم الدائنة (payables)
```sql
- id (Primary Key)
- supplier_id (معرف المورد)
- purchase_id (معرف المشتريات)
- invoice_number (رقم فاتورة المورد)
- invoice_date (تاريخ الفاتورة)
- due_date (تاريخ الاستحقاق)
- original_amount (المبلغ الأصلي)
- paid_amount (المبلغ المدفوع)
- remaining_amount (المبلغ المتبقي)
- status (الحالة: pending, partial, paid, overdue)
- created_at, updated_at
```

## 2. دليل الحسابات المقترح

### 2.1 الأصول (Assets) - 1xxxx
- **الأصول المتداولة (Current Assets) - 11xxx**
  - النقدية والبنوك - 11100
    - الصندوق - 11101
    - البنك الأهلي - 11102
    - البنك الراجحي - 11103
  - الذمم المدينة - 11200
    - ذمم العملاء - 11201
    - أوراق القبض - 11202
  - المخزون - 11300
    - مخزون قطع الغيار - 11301
    - مخزون الإكسسوارات - 11302

- **الأصول الثابتة (Fixed Assets) - 12xxx**
  - المعدات والأجهزة - 12100
  - الأثاث والمفروشات - 12200
  - مجمع الاستهلاك - 12900

### 2.2 الخصوم (Liabilities) - 2xxxx
- **الخصوم المتداولة (Current Liabilities) - 21xxx**
  - الذمم الدائنة - 21100
    - ذمم الموردين - 21101
    - أوراق الدفع - 21102
  - المصروفات المستحقة - 21200
    - رواتب مستحقة - 21201
    - إيجار مستحق - 21202

- **الخصوم طويلة الأجل (Long-term Liabilities) - 22xxx**
  - القروض طويلة الأجل - 22100

### 2.3 حقوق الملكية (Equity) - 3xxxx
- رأس المال - 31000
- الأرباح المحتجزة - 32000
- أرباح العام الجاري - 33000

### 2.4 الإيرادات (Revenues) - 4xxxx
- إيرادات المبيعات - 41000
  - مبيعات قطع الغيار - 41100
  - مبيعات الإكسسوارات - 41200
- إيرادات الخدمات - 42000
  - خدمات الإصلاح - 42100
  - خدمات الصيانة - 42200

### 2.5 المصروفات (Expenses) - 5xxxx
- تكلفة البضاعة المباعة - 51000
- مصروفات التشغيل - 52000
  - الرواتب والأجور - 52100
  - الإيجار - 52200
  - الكهرباء والماء - 52300
  - الاتصالات - 52400

## 3. القيود المحاسبية التلقائية

### 3.1 قيود المبيعات
```
عند إتمام مبيعة نقدية:
من حـ/ الصندوق                    XXX
    إلى حـ/ مبيعات قطع الغيار           XXX

عند إتمام مبيعة آجلة:
من حـ/ ذمم العملاء                XXX
    إلى حـ/ مبيعات قطع الغيار           XXX
```

### 3.2 قيود المشتريات
```
عند شراء قطع غيار نقداً:
من حـ/ مخزون قطع الغيار            XXX
    إلى حـ/ الصندوق                    XXX

عند شراء قطع غيار آجلاً:
من حـ/ مخزون قطع الغيار            XXX
    إلى حـ/ ذمم الموردين                XXX
```

### 3.3 قيود المدفوعات والمقبوضات
```
عند تحصيل من عميل:
من حـ/ الصندوق                    XXX
    إلى حـ/ ذمم العملاء                 XXX

عند دفع لمورد:
من حـ/ ذمم الموردين                XXX
    إلى حـ/ الصندوق                    XXX
```

## 4. التقارير المحاسبية

### 4.1 الميزانية العمومية
- الأصول (متداولة + ثابتة)
- الخصوم (متداولة + طويلة الأجل)
- حقوق الملكية

### 4.2 قائمة الدخل
- الإيرادات
- تكلفة البضاعة المباعة
- إجمالي الربح
- المصروفات التشغيلية
- صافي الربح

### 4.3 قائمة التدفقات النقدية
- التدفقات من الأنشطة التشغيلية
- التدفقات من الأنشطة الاستثمارية
- التدفقات من الأنشطة التمويلية

### 4.4 ميزان المراجعة
- جميع الحسابات مع أرصدتها المدينة والدائنة

## 5. التكامل مع النظام الحالي

### 5.1 ربط مع نقطة البيع
- إنشاء قيود تلقائية عند إتمام المبيعات
- تحديث أرصدة الحسابات فوراً

### 5.2 ربط مع إدارة المخزون
- تسجيل حركات المخزون محاسبياً
- تقييم المخزون بطرق مختلفة

### 5.3 ربط مع إدارة العملاء
- تتبع الذمم المدينة تلقائياً
- إنشاء كشوف حساب العملاء

## 6. الأمان والصلاحيات

### 6.1 مستويات الصلاحيات
- محاسب: عرض التقارير وإدخال القيود
- مدير مالي: جميع العمليات المحاسبية
- مدير عام: جميع الصلاحيات

### 6.2 حماية البيانات
- تشفير البيانات الحساسة
- تسجيل جميع العمليات (Audit Trail)
- نسخ احتياطية دورية
