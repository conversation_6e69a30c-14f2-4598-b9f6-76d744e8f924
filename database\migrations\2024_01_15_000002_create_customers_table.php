<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('customer_number')->unique();
            $table->enum('type', ['individual', 'business'])->default('individual');
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('company_name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->string('whatsapp')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->string('national_id')->nullable();
            $table->string('tax_number')->nullable();
            $table->decimal('credit_limit', 10, 2)->default(0);
            $table->integer('payment_terms')->default(30); // days
            $table->decimal('discount_percentage', 5, 2)->default(0);
            $table->enum('preferred_contact_method', ['email', 'phone', 'sms', 'whatsapp'])->default('phone');
            $table->string('language_preference', 10)->default('ar');
            $table->text('notes')->nullable();
            $table->json('tags')->nullable();
            $table->string('source')->nullable();
            $table->string('referral_source')->nullable();
            $table->boolean('marketing_consent')->default(false);
            $table->boolean('sms_consent')->default(false);
            $table->boolean('email_consent')->default(false);
            $table->boolean('is_vip')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_contact_date')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['type']);
            $table->index(['is_vip']);
            $table->index(['is_active']);
            $table->index(['city']);
            $table->index(['source']);
            $table->index(['email']);
            $table->index(['phone']);
            $table->index(['mobile']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('customers');
    }
};
