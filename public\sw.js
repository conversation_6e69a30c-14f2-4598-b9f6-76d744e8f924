const CACHE_NAME = 'repair-center-v1.0.0';
const OFFLINE_URL = '/offline.html';

// Files to cache for offline functionality
const urlsToCache = [
  '/',
  '/mobile/app',
  '/offline.html',
  '/manifest.json',
  // CSS and JS files
  'https://cdn.tailwindcss.com',
  'https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js',
  // Icons
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  // Essential pages
  '/dashboard',
  '/repairs',
  '/customers',
  // API endpoints for offline data
  '/api/repairs/recent',
  '/api/stats/dashboard'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching files');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        console.log('Service Worker: Cached all files successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Cache failed', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker: Activated');
      return self.clients.claim();
    })
  );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  // Handle navigation requests
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          return caches.open(CACHE_NAME)
            .then((cache) => {
              return cache.match(OFFLINE_URL);
            });
        })
    );
    return;
  }

  // Handle other requests with cache-first strategy
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request)
          .then((fetchResponse) => {
            // Don't cache non-successful responses
            if (!fetchResponse || fetchResponse.status !== 200 || fetchResponse.type !== 'basic') {
              return fetchResponse;
            }

            // Clone the response
            const responseToCache = fetchResponse.clone();

            // Add to cache
            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(event.request, responseToCache);
              });

            return fetchResponse;
          })
          .catch(() => {
            // Return offline page for navigation requests
            if (event.request.destination === 'document') {
              return caches.match(OFFLINE_URL);
            }
            
            // Return cached version if available
            return caches.match(event.request);
          });
      })
  );
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync', event.tag);
  
  if (event.tag === 'background-sync-repairs') {
    event.waitUntil(syncRepairs());
  }
  
  if (event.tag === 'background-sync-notifications') {
    event.waitUntil(syncNotifications());
  }
});

// Push notifications
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push received', event);
  
  const options = {
    body: event.data ? event.data.text() : 'إشعار جديد من مركز الصيانة',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'عرض التفاصيل',
        icon: '/icons/action-explore.png'
      },
      {
        action: 'close',
        title: 'إغلاق',
        icon: '/icons/action-close.png'
      }
    ],
    requireInteraction: true,
    silent: false,
    dir: 'rtl',
    lang: 'ar'
  };

  event.waitUntil(
    self.registration.showNotification('مركز الصيانة', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification click', event);
  
  event.notification.close();

  if (event.action === 'explore') {
    // Open the app
    event.waitUntil(
      clients.openWindow('/mobile/app')
    );
  } else if (event.action === 'close') {
    // Just close the notification
    return;
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/mobile/app')
    );
  }
});

// Periodic background sync (requires permission)
self.addEventListener('periodicsync', (event) => {
  console.log('Service Worker: Periodic sync', event.tag);
  
  if (event.tag === 'update-repairs') {
    event.waitUntil(updateRepairsData());
  }
});

// Helper functions
async function syncRepairs() {
  try {
    // Get pending repairs from IndexedDB
    const pendingRepairs = await getPendingRepairs();
    
    for (const repair of pendingRepairs) {
      try {
        const response = await fetch('/api/repairs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          },
          body: JSON.stringify(repair)
        });
        
        if (response.ok) {
          // Remove from pending list
          await removePendingRepair(repair.id);
          console.log('Service Worker: Synced repair', repair.id);
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync repair', repair.id, error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Sync repairs failed', error);
  }
}

async function syncNotifications() {
  try {
    const response = await fetch('/api/notifications/unread');
    if (response.ok) {
      const notifications = await response.json();
      
      // Store notifications in cache for offline access
      const cache = await caches.open(CACHE_NAME);
      await cache.put('/api/notifications/cached', new Response(JSON.stringify(notifications)));
      
      console.log('Service Worker: Synced notifications');
    }
  } catch (error) {
    console.error('Service Worker: Sync notifications failed', error);
  }
}

async function updateRepairsData() {
  try {
    const response = await fetch('/api/repairs/recent');
    if (response.ok) {
      const repairs = await response.json();
      
      // Update cache with fresh data
      const cache = await caches.open(CACHE_NAME);
      await cache.put('/api/repairs/cached', new Response(JSON.stringify(repairs)));
      
      console.log('Service Worker: Updated repairs data');
    }
  } catch (error) {
    console.error('Service Worker: Update repairs data failed', error);
  }
}

// IndexedDB helpers (simplified)
async function getPendingRepairs() {
  // Implementation would use IndexedDB to get pending repairs
  return [];
}

async function removePendingRepair(id) {
  // Implementation would remove repair from IndexedDB
  console.log('Removing pending repair:', id);
}

// Message handling from main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_REPAIR') {
    // Cache repair data for offline access
    caches.open(CACHE_NAME).then((cache) => {
      cache.put(`/api/repairs/${event.data.repairId}`, 
        new Response(JSON.stringify(event.data.repairData))
      );
    });
  }
});

// Error handling
self.addEventListener('error', (event) => {
  console.error('Service Worker: Error', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker: Unhandled promise rejection', event.reason);
});

console.log('Service Worker: Loaded successfully');
