<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Customer;

class QuickCustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample customers if none exist
        if (Customer::count() === 0) {
            $customers = [
                [
                    'customer_number' => 'CUST-001',
                    'type' => 'individual',
                    'first_name' => 'أحمد',
                    'last_name' => 'محمد علي',
                    'email' => '<EMAIL>',
                    'phone' => '0599123456',
                    'mobile' => '0599123456',
                    'address' => 'شارع الملك فهد، الرياض',
                    'city' => 'الرياض',
                    'country' => 'المملكة العربية السعودية',
                    'is_active' => true,
                    'created_by' => 1,
                ],
                [
                    'customer_number' => 'CUST-002',
                    'type' => 'individual',
                    'first_name' => 'سارة',
                    'last_name' => 'أحمد خالد',
                    'email' => '<EMAIL>',
                    'phone' => '0598765432',
                    'mobile' => '0598765432',
                    'address' => 'شارع العليا، الرياض',
                    'city' => 'الرياض',
                    'country' => 'المملكة العربية السعودية',
                    'is_active' => true,
                    'created_by' => 1,
                ],
                [
                    'customer_number' => 'CUST-003',
                    'type' => 'individual',
                    'first_name' => 'محمد',
                    'last_name' => 'عبدالله حسن',
                    'email' => '<EMAIL>',
                    'phone' => '0597654321',
                    'mobile' => '0597654321',
                    'address' => 'شارع التحلية، جدة',
                    'city' => 'جدة',
                    'country' => 'المملكة العربية السعودية',
                    'is_active' => true,
                    'created_by' => 1,
                ],
                [
                    'customer_number' => 'CUST-004',
                    'type' => 'company',
                    'company_name' => 'شركة التقنية المتقدمة',
                    'email' => '<EMAIL>',
                    'phone' => '0596543210',
                    'mobile' => '0596543210',
                    'address' => 'طريق الملك عبدالعزيز، الدمام',
                    'city' => 'الدمام',
                    'country' => 'المملكة العربية السعودية',
                    'tax_number' => '123456789012345',
                    'is_active' => true,
                    'created_by' => 1,
                ],
                [
                    'customer_number' => 'CUST-005',
                    'type' => 'individual',
                    'first_name' => 'فاطمة',
                    'last_name' => 'حسين محمد',
                    'email' => '<EMAIL>',
                    'phone' => '0595432109',
                    'mobile' => '0595432109',
                    'address' => 'شارع الأمير سلطان، الرياض',
                    'city' => 'الرياض',
                    'country' => 'المملكة العربية السعودية',
                    'is_active' => true,
                    'created_by' => 1,
                ],
            ];

            foreach ($customers as $customerData) {
                Customer::create($customerData);
            }

            $this->command->info('تم إنشاء ' . count($customers) . ' عميل تجريبي');
        } else {
            $this->command->info('العملاء موجودون بالفعل');
        }
    }
}
