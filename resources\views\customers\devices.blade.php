@extends('layouts.main')

@section('title', 'أجهزة العميل')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">أجهزة العميل</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $customer->display_name }} - {{ $customer->customer_number }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('customers.show', $customer) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة لتفاصيل العميل
            </a>
            <button onclick="showAddDeviceModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                إضافة جهاز
            </button>
        </div>
    </div>

    <!-- Devices Summary -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $devices->total() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">إجمالي الأجهزة</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $devices->where('is_active', true)->count() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">أجهزة نشطة</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ $devices->where('warranty_expires_at', '>', now())->count() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">تحت الضمان</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $devices->sum(function($device) { return $device->repairs->count(); }) }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">طلبات الصيانة</div>
            </div>
        </div>
    </div>

    <!-- Devices Grid -->
    @if($devices->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($devices as $device)
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <div class="flex items-start justify-between">
                        <div class="flex items-center">
                            <div class="p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                <svg class="w-8 h-8 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    @switch($device->device_type)
                                        @case('laptop')
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                            @break
                                        @case('desktop')
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                            @break
                                        @case('mobile')
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"></path>
                                            @break
                                        @case('tablet')
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                            @break
                                        @default
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                    @endswitch
                                </svg>
                            </div>
                            <div class="mr-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{ $device->device_type }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $device->brand }} {{ $device->model }}</p>
                            </div>
                        </div>
                        <div class="flex space-x-1 space-x-reverse">
                            @if($device->is_active)
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    نشط
                                </span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    غير نشط
                                </span>
                            @endif
                            
                            @if($device->warranty_expires_at && $device->warranty_expires_at > now())
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    ضمان
                                </span>
                            @endif
                        </div>
                    </div>

                    <div class="mt-4 space-y-2">
                        @if($device->serial_number)
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">الرقم التسلسلي:</span>
                                <span class="text-gray-900 dark:text-gray-100 font-mono">{{ $device->serial_number }}</span>
                            </div>
                        @endif
                        
                        @if($device->imei)
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">IMEI:</span>
                                <span class="text-gray-900 dark:text-gray-100 font-mono">{{ $device->imei }}</span>
                            </div>
                        @endif
                        
                        @if($device->purchase_date)
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">تاريخ الشراء:</span>
                                <span class="text-gray-900 dark:text-gray-100">{{ $device->purchase_date->format('Y-m-d') }}</span>
                            </div>
                        @endif
                        
                        @if($device->warranty_expires_at)
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">انتهاء الضمان:</span>
                                <span class="text-gray-900 dark:text-gray-100 {{ $device->warranty_expires_at < now() ? 'text-red-600' : 'text-green-600' }}">
                                    {{ $device->warranty_expires_at->format('Y-m-d') }}
                                </span>
                            </div>
                        @endif
                    </div>

                    @if($device->notes)
                        <div class="mt-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $device->notes }}</p>
                        </div>
                    @endif

                    <!-- Recent Repairs -->
                    @if($device->repairs && $device->repairs->count() > 0)
                        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">طلبات الصيانة الأخيرة</h4>
                            <div class="space-y-2">
                                @foreach($device->repairs->take(3) as $repair)
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-600 dark:text-gray-400">{{ $repair->created_at->format('Y-m-d') }}</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            @switch($repair->status)
                                                @case('pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 @break
                                                @case('in_progress') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 @break
                                                @case('completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @break
                                                @case('cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @break
                                                @default bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                            @endswitch
                                        ">
                                            @switch($repair->status)
                                                @case('pending') معلق @break
                                                @case('in_progress') قيد التنفيذ @break
                                                @case('completed') مكتمل @break
                                                @case('cancelled') ملغي @break
                                                @default {{ $repair->status }}
                                            @endswitch
                                        </span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Actions -->
                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex space-x-2 space-x-reverse">
                            <a href="{{ route('repairs.create', ['customer_id' => $customer->id, 'device_id' => $device->id]) }}" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-3 rounded text-sm">
                                طلب صيانة
                            </a>
                            <button onclick="editDevice({{ $device->id }})" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white text-center py-2 px-3 rounded text-sm">
                                تعديل
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="flex justify-center">
            {{ $devices->links() }}
        </div>
    @else
        <div class="bg-white dark:bg-gray-800 rounded-lg p-12 shadow-sm text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد أجهزة</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لم يتم تسجيل أي أجهزة لهذا العميل بعد.</p>
            <div class="mt-6">
                <button onclick="showAddDeviceModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <svg class="-mr-1 ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    إضافة جهاز
                </button>
            </div>
        </div>
    @endif
</div>

<!-- Add Device Modal -->
<div id="addDeviceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إضافة جهاز جديد</h3>
            
            <form action="{{ route('customers.add-device', $customer) }}" method="POST" class="space-y-4">
                @csrf
                
                <div>
                    <label for="device_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الجهاز *</label>
                    <select name="device_type" id="device_type" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                        <option value="">اختر نوع الجهاز</option>
                        <option value="laptop">لابتوب</option>
                        <option value="desktop">كمبيوتر مكتبي</option>
                        <option value="mobile">جوال</option>
                        <option value="tablet">تابلت</option>
                        <option value="printer">طابعة</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>

                <div>
                    <label for="brand" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الماركة *</label>
                    <input type="text" name="brand" id="brand" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label for="model" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموديل *</label>
                    <input type="text" name="model" id="model" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label for="serial_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الرقم التسلسلي</label>
                    <input type="text" name="serial_number" id="serial_number" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                </div>

                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات</label>
                    <textarea name="notes" id="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
                </div>

                <div class="flex justify-end space-x-2 space-x-reverse pt-4">
                    <button type="button" onclick="hideAddDeviceModal()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                        إلغاء
                    </button>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        حفظ الجهاز
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showAddDeviceModal() {
    document.getElementById('addDeviceModal').classList.remove('hidden');
}

function hideAddDeviceModal() {
    document.getElementById('addDeviceModal').classList.add('hidden');
}

function editDevice(deviceId) {
    // Implementation for editing device
    alert('تعديل الجهاز #' + deviceId);
}

// Close modal when clicking outside
document.getElementById('addDeviceModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideAddDeviceModal();
    }
});
</script>
@endsection
