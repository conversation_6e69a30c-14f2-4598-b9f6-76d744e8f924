@extends('layouts.main')

@section('title', 'ميزان المراجعة')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">ميزان المراجعة</h1>
            <p class="text-gray-600 dark:text-gray-400">جميع الحسابات وأرصدتها كما في {{ $asOfDate }}</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <button onclick="printReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-print mr-2"></i>
                طباعة
            </button>
            
            <button onclick="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-download mr-2"></i>
                تصدير
            </button>
            
            <a href="{{ route('accounting-reports.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-arrow-right mr-2"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كما في تاريخ</label>
                <input type="date" name="as_of_date" value="{{ $asOfDate }}" 
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                <i class="fas fa-search mr-2"></i>
                تحديث
            </button>
        </form>
    </div>

    <!-- Trial Balance Summary -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-plus text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المدين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($trialBalance['total_debits'], 2) }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">ريال سعودي</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-minus text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الدائن</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {{ number_format($trialBalance['total_credits'], 2) }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">ريال سعودي</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 {{ $trialBalance['is_balanced'] ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900' }} rounded-lg flex items-center justify-center">
                        <i class="fas {{ $trialBalance['is_balanced'] ? 'fa-check text-green-600 dark:text-green-400' : 'fa-times text-red-600 dark:text-red-400' }}"></i>
                    </div>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">حالة التوازن</p>
                    <p class="text-lg font-bold {{ $trialBalance['is_balanced'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        {{ $trialBalance['is_balanced'] ? 'متوازن' : 'غير متوازن' }}
                    </p>
                    @if(!$trialBalance['is_balanced'])
                        <p class="text-xs text-red-500 dark:text-red-400">
                            الفرق: {{ number_format(abs($trialBalance['total_debits'] - $trialBalance['total_credits']), 2) }} ريال
                        </p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Trial Balance Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden" id="trial-balance-report">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="text-center">
                <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100">ميزان المراجعة</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-1">كما في {{ $asOfDate }}</p>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            @if(count($trialBalance['accounts']) > 0)
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                رقم الحساب
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                اسم الحساب
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                نوع الحساب
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                الرصيد المدين
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                الرصيد الدائن
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($trialBalance['accounts'] as $accountData)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-mono text-gray-900 dark:text-gray-100">
                                        {{ $accountData['account']->account_code }}
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        <a href="{{ route('accounts.show', $accountData['account']) }}" 
                                           class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                                            {{ $accountData['account']->account_name }}
                                        </a>
                                    </div>
                                    @if($accountData['account']->account_name_en)
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ $accountData['account']->account_name_en }}
                                        </div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @switch($accountData['account']->account_type)
                                            @case('asset') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 @break
                                            @case('liability') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 @break
                                            @case('equity') bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300 @break
                                            @case('revenue') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 @break
                                            @case('expense') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300 @break
                                            @default bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300
                                        @endswitch">
                                        {{ $accountData['account']->getAccountTypeLabel() }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-left">
                                    @if($accountData['debit_balance'] > 0)
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                            {{ number_format($accountData['debit_balance'], 2) }}
                                        </div>
                                    @else
                                        <div class="text-sm text-gray-400">-</div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-left">
                                    @if($accountData['credit_balance'] > 0)
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                            {{ number_format($accountData['credit_balance'], 2) }}
                                        </div>
                                    @else
                                        <div class="text-sm text-gray-400">-</div>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot class="bg-gray-50 dark:bg-gray-700">
                        <tr class="font-bold">
                            <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                                الإجمالي
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-bold text-gray-900 dark:text-gray-100">
                                {{ number_format($trialBalance['total_debits'], 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-left text-sm font-bold text-gray-900 dark:text-gray-100">
                                {{ number_format($trialBalance['total_credits'], 2) }}
                            </td>
                        </tr>
                    </tfoot>
                </table>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-balance-scale text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد حسابات بأرصدة</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Balance Verification -->
    @if(!$trialBalance['is_balanced'])
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-red-400"></i>
            </div>
            <div class="mr-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                    تحذير: ميزان المراجعة غير متوازن
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                    <p>إجمالي المدين لا يساوي إجمالي الدائن. يرجى مراجعة القيود المحاسبية للتأكد من صحتها.</p>
                    <p class="mt-1">الفرق: {{ number_format(abs($trialBalance['total_debits'] - $trialBalance['total_credits']), 2) }} ريال</p>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
function printReport() {
    window.print();
}

function exportReport() {
    // This would typically export to Excel or PDF
    alert('ميزة التصدير قيد التطوير');
}

// Print styles
const printStyles = `
    @media print {
        body * {
            visibility: hidden;
        }
        #trial-balance-report, #trial-balance-report * {
            visibility: visible;
        }
        #trial-balance-report {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none !important;
        }
    }
`;

// Add print styles to head
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
@endpush
@endsection
