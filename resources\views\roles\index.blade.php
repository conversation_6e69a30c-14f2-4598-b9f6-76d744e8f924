@extends('layouts.main')

@section('title', 'إدارة الأدوار والصلاحيات')

@section('content')
<div class="space-y-6" x-data="rolesManager()">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">إدارة الأدوار والصلاحيات</h1>
            <p class="text-gray-600 dark:text-gray-400">تحديد الأدوار وتخصيص الصلاحيات للمستخدمين</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button @click="exportRoles()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                تصدير
            </button>
            <button @click="addRole()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                إضافة دور جديد
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الأدوار</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.totalRoles">6</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الصلاحيات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.totalPermissions">24</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">المستخدمين المعينين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.assignedUsers">15</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">أدوار مخصصة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" x-text="stats.customRoles">3</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Roles Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <template x-for="role in roles" :key="role.id">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" x-text="role.name"></h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400" x-text="role.description"></p>
                        <div class="mt-2 flex items-center space-x-4 space-x-reverse">
                            <span class="text-sm text-gray-600 dark:text-gray-300">
                                <span x-text="role.users_count"></span> مستخدم
                            </span>
                            <span class="text-sm text-gray-600 dark:text-gray-300">
                                <span x-text="role.permissions_count"></span> صلاحية
                            </span>
                        </div>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <button @click="editRole(role.id)" class="text-blue-600 hover:text-blue-700 dark:text-blue-400">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                        <button @click="deleteRole(role.id)" x-show="!role.is_system" class="text-red-600 hover:text-red-700 dark:text-red-400">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Permissions Preview -->
                <div class="space-y-3">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">الصلاحيات الرئيسية:</h4>
                    <div class="flex flex-wrap gap-2">
                        <template x-for="permission in role.key_permissions" :key="permission">
                            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                  x-text="permission">
                            </span>
                        </template>
                        <span x-show="role.permissions_count > role.key_permissions.length" 
                              class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400">
                            +<span x-text="role.permissions_count - role.key_permissions.length"></span> أخرى
                        </span>
                    </div>
                </div>

                <!-- Actions -->
                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <button @click="managePermissions(role.id)" class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400">
                            إدارة الصلاحيات
                        </button>
                        <button @click="viewUsers(role.id)" class="text-sm text-green-600 hover:text-green-700 dark:text-green-400">
                            عرض المستخدمين
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- Permissions Matrix -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">مصفوفة الصلاحيات</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الصلاحية</th>
                        <template x-for="role in roles" :key="role.id">
                            <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" x-text="role.name"></th>
                        </template>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <template x-for="permission in allPermissions" :key="permission.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100" x-text="permission.name"></td>
                            <template x-for="role in roles" :key="role.id">
                                <td class="px-3 py-4 whitespace-nowrap text-center">
                                    <input type="checkbox" 
                                           :checked="hasPermission(role.id, permission.id)"
                                           @change="togglePermission(role.id, permission.id)"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                </td>
                            </template>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
function rolesManager() {
    return {
        roles: [
            {
                id: 1,
                name: 'مدير النظام',
                description: 'صلاحيات كاملة لإدارة النظام',
                users_count: 2,
                permissions_count: 24,
                is_system: true,
                key_permissions: ['إدارة المستخدمين', 'إدارة النظام', 'التقارير', 'النسخ الاحتياطي']
            },
            {
                id: 2,
                name: 'مدير المبيعات',
                description: 'إدارة المبيعات والعملاء',
                users_count: 3,
                permissions_count: 12,
                is_system: false,
                key_permissions: ['المبيعات', 'العملاء', 'التقارير المالية']
            },
            {
                id: 3,
                name: 'محاسب',
                description: 'إدارة الحسابات والمدفوعات',
                users_count: 2,
                permissions_count: 8,
                is_system: false,
                key_permissions: ['المدفوعات', 'التقارير المالية', 'المشتريات']
            },
            {
                id: 4,
                name: 'فني صيانة',
                description: 'إدارة أوامر الصيانة',
                users_count: 5,
                permissions_count: 6,
                is_system: false,
                key_permissions: ['الصيانة', 'المنتجات', 'العملاء']
            },
            {
                id: 5,
                name: 'موظف مبيعات',
                description: 'إجراء المبيعات وخدمة العملاء',
                users_count: 3,
                permissions_count: 5,
                is_system: false,
                key_permissions: ['POS', 'العملاء', 'المنتجات']
            }
        ],
        allPermissions: [
            { id: 1, name: 'إدارة المستخدمين' },
            { id: 2, name: 'إدارة الأدوار' },
            { id: 3, name: 'المبيعات' },
            { id: 4, name: 'المشتريات' },
            { id: 5, name: 'المدفوعات' },
            { id: 6, name: 'العملاء' },
            { id: 7, name: 'الموردين' },
            { id: 8, name: 'المنتجات' },
            { id: 9, name: 'الصيانة' },
            { id: 10, name: 'التقارير' },
            { id: 11, name: 'POS' },
            { id: 12, name: 'النسخ الاحتياطي' }
        ],
        rolePermissions: {
            1: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], // مدير النظام
            2: [3, 6, 10, 11], // مدير المبيعات
            3: [4, 5, 10], // محاسب
            4: [6, 8, 9], // فني صيانة
            5: [3, 6, 8, 11] // موظف مبيعات
        },
        stats: {
            totalRoles: 5,
            totalPermissions: 12,
            assignedUsers: 15,
            customRoles: 4
        },

        init() {
            // Initialize component
        },

        addRole() {
            const name = prompt('اسم الدور الجديد:');
            if (name) {
                const newRole = {
                    id: this.roles.length + 1,
                    name: name,
                    description: 'دور مخصص',
                    users_count: 0,
                    permissions_count: 0,
                    is_system: false,
                    key_permissions: []
                };
                this.roles.push(newRole);
                this.rolePermissions[newRole.id] = [];
                alert('تم إضافة الدور بنجاح');
            }
        },

        editRole(id) {
            const role = this.roles.find(r => r.id === id);
            if (role) {
                const newName = prompt('اسم الدور:', role.name);
                if (newName) {
                    role.name = newName;
                    alert('تم تحديث الدور بنجاح');
                }
            }
        },

        deleteRole(id) {
            if (confirm('هل أنت متأكد من حذف هذا الدور؟')) {
                this.roles = this.roles.filter(role => role.id !== id);
                delete this.rolePermissions[id];
                alert('تم حذف الدور بنجاح');
            }
        },

        managePermissions(id) {
            alert(`إدارة صلاحيات الدور رقم ${id}`);
        },

        viewUsers(id) {
            alert(`عرض مستخدمي الدور رقم ${id}`);
        },

        hasPermission(roleId, permissionId) {
            return this.rolePermissions[roleId] && this.rolePermissions[roleId].includes(permissionId);
        },

        togglePermission(roleId, permissionId) {
            if (!this.rolePermissions[roleId]) {
                this.rolePermissions[roleId] = [];
            }
            
            const index = this.rolePermissions[roleId].indexOf(permissionId);
            if (index > -1) {
                this.rolePermissions[roleId].splice(index, 1);
            } else {
                this.rolePermissions[roleId].push(permissionId);
            }
            
            // Update permissions count
            const role = this.roles.find(r => r.id === roleId);
            if (role) {
                role.permissions_count = this.rolePermissions[roleId].length;
            }
        },

        exportRoles() {
            alert('سيتم تصدير بيانات الأدوار والصلاحيات');
        }
    }
}
</script>
@endpush
@endsection
