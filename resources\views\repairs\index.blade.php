@extends('layouts.main')

@section('title', 'إدارة طلبات الصيانة')

@section('content')
<div class="content-wrapper animate-fade-in-up" x-data="repairsManager()"
     x-init="loadRepairs()"
     data-repairs="{{ json_encode($repairs->items()) }}"
     data-technicians="{{ json_encode($technicians) }}"
     data-device-types="{{ json_encode($deviceTypes) }}">

    <!-- Page Header -->
    <div class="page-header animate-fade-in-down">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="animate-fade-in-left">
                    <h1 class="page-title">إدارة طلبات الصيانة</h1>
                    <p class="page-subtitle">متابعة وإدارة جميع طلبات الصيانة والإصلاح بكفاءة عالية</p>
                </div>
                <div class="action-group animate-fade-in-right">
                    <button @click="exportRepairs()" class="btn btn-success hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        تصدير التقرير
                    </button>
                    <a href="{{ route('repairs.create') }}" class="btn btn-primary hover-lift ripple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        طلب صيانة جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card hover-lift animate-scale-in animate-delay-100">
            <div class="stat-value" style="color: var(--accent-color);" x-text="stats.totalRepairs">156</div>
            <div class="stat-label">إجمالي الطلبات</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                +12 هذا الأسبوع
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-200">
            <div class="stat-value" style="color: var(--warning-color);" x-text="stats.pendingRepairs">23</div>
            <div class="stat-label">قيد الانتظار</div>
            <div class="stat-change negative">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                يحتاج متابعة
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-300">
            <div class="stat-value" style="color: var(--info-color);" x-text="stats.inProgressRepairs">45</div>
            <div class="stat-label">قيد الإصلاح</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                </svg>
                جاري العمل
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-500">
            <div class="stat-value" style="color: var(--success-color);" x-text="stats.completedRepairs">78</div>
            <div class="stat-label">مكتملة</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                معدل ممتاز
            </div>
        </div>

        <div class="stat-card hover-lift animate-scale-in animate-delay-700">
            <div class="stat-value" x-text="stats.avgRepairTime">3.2 يوم</div>
            <div class="stat-label">متوسط وقت الإصلاح</div>
            <div class="stat-change positive">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                تحسن 15%
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card animate-fade-in-up animate-delay-300">
        <div class="card-header">
            <h3 class="card-title">فلترة وبحث طلبات الصيانة</h3>
            <button @click="clearFilters()" class="btn btn-ghost btn-sm hover-scale">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                مسح الفلاتر
            </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div class="form-group">
                <label class="form-label">البحث</label>
                <div class="search-box">
                    <input type="text" x-model="filters.search" @input="filterRepairs()"
                           placeholder="رقم الطلب أو العميل..."
                           class="search-input focus-glow">
                    <svg class="search-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">الحالة</label>
                <select x-model="filters.status" @change="filterRepairs()" class="form-select focus-glow">
                    <option value="">جميع الحالات</option>
                    <option value="pending">قيد الانتظار</option>
                    <option value="diagnosed">تم التشخيص</option>
                    <option value="in_progress">قيد الإصلاح</option>
                    <option value="completed">مكتملة</option>
                    <option value="delivered">تم التسليم</option>
                    <option value="cancelled">ملغية</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">نوع الجهاز</label>
                <select x-model="filters.deviceType" @change="filterRepairs()" class="form-select focus-glow">
                    <option value="">جميع الأجهزة</option>
                    <option value="mobile">هاتف محمول</option>
                    <option value="laptop">لابتوب</option>
                    <option value="desktop">حاسوب مكتبي</option>
                    <option value="tablet">تابلت</option>
                    <option value="other">أخرى</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">الفني المسؤول</label>
                <select x-model="filters.technician" @change="filterRepairs()" class="form-select focus-glow">
                    <option value="">جميع الفنيين</option>
                    <template x-for="tech in technicians" :key="tech.id">
                        <option :value="tech.id" x-text="tech.name"></option>
                    </template>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">من تاريخ</label>
                <input type="date" x-model="filters.dateFrom" @change="filterRepairs()"
                       class="form-input focus-glow">
            </div>
            <div class="form-group">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" x-model="filters.dateTo" @change="filterRepairs()"
                       class="form-input focus-glow">
            </div>
        </div>
    </div>

    <!-- Repairs Table -->
    <div class="table-container animate-fade-in-up animate-delay-500">
        <div class="card-header">
            <h3 class="card-title">جدول طلبات الصيانة</h3>
            <div class="action-group">
                <button @click="refreshRepairs()" class="btn btn-ghost btn-sm hover-rotate">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    تحديث
                </button>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>العميل</th>
                        <th>الجهاز</th>
                        <th>المشكلة</th>
                        <th>الفني</th>
                        <th>التاريخ</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="(repair, index) in filteredRepairs" :key="repair.id">
                        <tr class="hover-lift" :class="'animate-fade-in-up animate-delay-' + (index * 100)">
                            <td>
                                <div class="text-sm font-medium text-accent-color" x-text="repair.repair_number"></div>
                                <div class="text-sm text-muted-light dark:text-muted-dark">
                                    <span class="badge" :class="getPriorityBadgeClass(repair.priority)" x-text="'أولوية: ' + getPriorityText(repair.priority)"></span>
                                </div>
                            </td>
                            <td>
                                <div class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="repair.customer ? repair.customer.first_name + ' ' + repair.customer.last_name : 'غير محدد'"></div>
                                <div class="text-sm text-muted-light dark:text-muted-dark" x-text="repair.customer ? repair.customer.phone : ''"></div>
                            </td>
                            <td>
                                <div class="text-sm font-medium text-primary-light dark:text-primary-dark" x-text="repair.device_brand + ' ' + repair.device_model"></div>
                                <div class="text-sm text-muted-light dark:text-muted-dark" x-text="getDeviceTypeText(repair.device_type)"></div>
                            </td>
                            <td>
                                <div class="text-sm text-primary-light dark:text-primary-dark" x-text="repair.problem_description ? repair.problem_description.substring(0, 30) + '...' : ''"></div>
                            </td>
                            <td class="text-sm text-primary-light dark:text-primary-dark" x-text="repair.technician ? repair.technician.first_name + ' ' + repair.technician.last_name : 'غير محدد'"></td>
                            <td class="text-sm text-muted-light dark:text-muted-dark" x-text="new Date(repair.created_at).toLocaleDateString('ar-SA')"></td>
                            <td>
                                <span class="badge" :class="getStatusBadgeClass(repair.status)" x-text="getStatusText(repair.status)"></span>
                            </td>
                            <td>
                                <div class="action-group">
                                    <a :href="`{{ route('repairs.show', '') }}/${repair.id}`" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        عرض
                                    </a>
                                    <a :href="`{{ route('repairs.edit', '') }}/${repair.id}`" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        تعديل
                                    </a>
                                    <button @click="updateStatus(repair.id)" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        تحديث الحالة
                                    </button>
                                    <a :href="`{{ route('repairs.print', '') }}/${repair.id}`" target="_blank" class="btn btn-ghost btn-sm hover-scale">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                        </svg>
                                        طباعة
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
function repairsManager() {
    return {
        repairs: [],
        technicians: [],
        deviceTypes: [],
        filteredRepairs: [],
        filters: {
            search: '',
            status: '',
            deviceType: '',
            technician: '',
            dateFrom: '',
            dateTo: ''
        },
        stats: {
            totalRepairs: 0,
            pendingRepairs: 0,
            inProgressRepairs: 0,
            completedRepairs: 0,
            avgRepairTime: '0 يوم'
        },

        loadRepairs() {
            // تحميل البيانات من data attributes
            const repairsData = this.$el.dataset.repairs;
            const techniciansData = this.$el.dataset.technicians;
            const deviceTypesData = this.$el.dataset.deviceTypes;

            if (repairsData) {
                this.repairs = JSON.parse(repairsData);
                this.filteredRepairs = [...this.repairs];
                this.calculateStats();
            }

            if (techniciansData) {
                this.technicians = JSON.parse(techniciansData);
            }

            if (deviceTypesData) {
                this.deviceTypes = JSON.parse(deviceTypesData);
            }

            // إضافة تأثيرات الرسوم المتحركة للعناصر
            this.$nextTick(() => {
                this.animateElements();
            });
        },

        calculateStats() {
            this.stats.totalRepairs = this.repairs.length;
            this.stats.pendingRepairs = this.repairs.filter(r => r.status === 'pending').length;
            this.stats.inProgressRepairs = this.repairs.filter(r => r.status === 'in_progress').length;
            this.stats.completedRepairs = this.repairs.filter(r => ['completed', 'delivered'].includes(r.status)).length;
        },

        animateElements() {
            // إضافة تأثيرات متدرجة للصفوف
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                setTimeout(() => {
                    row.classList.add('animate-fade-in-up');
                }, index * 100);
            });
        },

        filterRepairs() {
            this.filteredRepairs = this.repairs.filter(repair => {
                const matchesSearch = !this.filters.search ||
                    repair.repair_number.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                    (repair.customer && (repair.customer.first_name + ' ' + repair.customer.last_name).toLowerCase().includes(this.filters.search.toLowerCase())) ||
                    (repair.customer && repair.customer.phone.includes(this.filters.search));
                
                const matchesStatus = !this.filters.status || repair.status === this.filters.status;
                const matchesDeviceType = !this.filters.deviceType || repair.device_type === this.filters.deviceType;
                const matchesTechnician = !this.filters.technician || repair.technician_id === parseInt(this.filters.technician);
                
                const matchesDateFrom = !this.filters.dateFrom || repair.created_at >= this.filters.dateFrom;
                const matchesDateTo = !this.filters.dateTo || repair.created_at <= this.filters.dateTo;

                return matchesSearch && matchesStatus && matchesDeviceType && matchesTechnician && matchesDateFrom && matchesDateTo;
            });
        },

        getStatusText(status) {
            const statuses = {
                'pending': 'قيد الانتظار',
                'diagnosed': 'تم التشخيص',
                'in_progress': 'قيد الإصلاح',
                'completed': 'مكتملة',
                'delivered': 'تم التسليم',
                'cancelled': 'ملغية'
            };
            return statuses[status] || status;
        },

        getDeviceTypeText(type) {
            const types = {
                'mobile': 'هاتف محمول',
                'laptop': 'لابتوب',
                'desktop': 'حاسوب مكتبي',
                'tablet': 'تابلت',
                'other': 'أخرى'
            };
            return types[type] || type;
        },

        getPriorityText(priority) {
            const priorities = {
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية',
                'urgent': 'عاجلة'
            };
            return priorities[priority] || priority;
        },

        createRepair() {
            window.location.href = '/repairs/create';
        },

        viewRepair(id) {
            window.location.href = `/repairs/${id}`;
        },

        editRepair(id) {
            window.location.href = `/repairs/${id}/edit`;
        },

        updateStatus(id) {
            alert(`تحديث حالة الطلب ${id}`);
        },

        printTicket(id) {
            window.open(`/repairs/${id}/print`, '_blank');
        },

        exportRepairs() {
            // إضافة تأثير التحميل
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                جاري التصدير...
            `;
            button.disabled = true;

            // محاكاة عملية التصدير
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                if (window.showNotification) {
                    window.showNotification('تم تصدير بيانات طلبات الصيانة بنجاح!', 'success');
                }
            }, 2000);
        },

        refreshRepairs() {
            // إضافة تأثير التحديث
            const button = event.target;
            button.classList.add('animate-spin');

            // محاكاة تحديث البيانات
            setTimeout(() => {
                button.classList.remove('animate-spin');
                if (window.showNotification) {
                    window.showNotification('تم تحديث البيانات', 'info');
                }
            }, 1000);
        },

        clearFilters() {
            this.filters.search = '';
            this.filters.status = '';
            this.filters.deviceType = '';
            this.filters.technician = '';
            this.filters.dateFrom = '';
            this.filters.dateTo = '';
            this.filterRepairs();

            if (window.showNotification) {
                window.showNotification('تم مسح جميع الفلاتر', 'info');
            }
        },

        getStatusBadgeClass(status) {
            const classes = {
                'pending': 'badge-warning',
                'diagnosed': 'badge-info',
                'in_progress': 'badge-secondary',
                'completed': 'badge-success',
                'delivered': 'badge-success',
                'cancelled': 'badge-danger'
            };
            return classes[status] || 'badge-secondary';
        },

        getPriorityBadgeClass(priority) {
            const classes = {
                'low': 'badge-info',
                'medium': 'badge-warning',
                'high': 'badge-danger',
                'urgent': 'badge-danger'
            };
            return classes[priority] || 'badge-secondary';
        }
    }
}
</script>
@endpush
@endsection
