@extends('layouts.main')

@section('title', 'لوحة تحكم نقطة البيع')

@section('content')
<div class="space-y-6" x-data="posReportsDashboard()">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">لوحة تحكم نقطة البيع</h1>
            <p class="text-gray-600 dark:text-gray-400">تقارير ومؤشرات الأداء</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
            <!-- Period Filter -->
            <select x-model="selectedPeriod" @change="updateData" 
                    class="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 text-gray-900 dark:text-gray-100">
                <option value="today">اليوم</option>
                <option value="week">هذا الأسبوع</option>
                <option value="month">هذا الشهر</option>
                <option value="year">هذا العام</option>
            </select>
            
            <!-- Location Filter -->
            <select x-model="selectedLocation" @change="updateData"
                    class="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 text-gray-900 dark:text-gray-100">
                <option value="">جميع المواقع</option>
                @foreach($locations as $location)
                    <option value="{{ $location->id }}">{{ $location->name }}</option>
                @endforeach
            </select>
            
            <button @click="refreshData" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-sync-alt mr-2"></i>
                تحديث
            </button>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Sales -->
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100">إجمالي المبيعات</p>
                    <p class="text-3xl font-bold" x-text="metrics.sales_overview?.total_sales || 0"></p>
                    <p class="text-blue-100 text-sm">عملية بيع</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100">إجمالي الإيرادات</p>
                    <p class="text-3xl font-bold" x-text="formatCurrency(metrics.sales_overview?.total_revenue || 0)"></p>
                    <p class="text-green-100 text-sm">ريال سعودي</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
        </div>

        <!-- Average Sale Value -->
        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100">متوسط قيمة المبيعة</p>
                    <p class="text-3xl font-bold" x-text="formatCurrency(metrics.sales_overview?.average_sale_value || 0)"></p>
                    <p class="text-purple-100 text-sm">ريال سعودي</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>

        <!-- Items Sold -->
        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100">العناصر المباعة</p>
                    <p class="text-3xl font-bold" x-text="metrics.sales_overview?.items_sold || 0"></p>
                    <p class="text-orange-100 text-sm">قطعة</p>
                </div>
                <div class="text-4xl opacity-80">
                    <i class="fas fa-boxes"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Hourly Sales Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">المبيعات بالساعة</h3>
            <div class="h-64">
                <canvas id="hourlySalesChart"></canvas>
            </div>
        </div>

        <!-- Payment Methods Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">طرق الدفع</h3>
            <div class="h-64">
                <canvas id="paymentMethodsChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Top Products and Recent Sales -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top Selling Products -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">أكثر المنتجات مبيعاً</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <template x-for="(product, index) in metrics.top_products" :key="index">
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                    <span class="text-blue-600 dark:text-blue-400 font-bold text-sm" x-text="index + 1"></span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900 dark:text-gray-100" x-text="product.name"></p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400" x-text="product.part_number"></p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-gray-900 dark:text-gray-100" x-text="product.total_sold + ' قطعة'"></p>
                                <p class="text-sm text-gray-500 dark:text-gray-400" x-text="formatCurrency(product.total_revenue)"></p>
                            </div>
                        </div>
                    </template>
                </div>
                
                <div x-show="!metrics.top_products || metrics.top_products.length === 0" class="text-center py-8">
                    <i class="fas fa-box-open text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد بيانات منتجات</p>
                </div>
            </div>
        </div>

        <!-- Recent Sales -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">المبيعات الأخيرة</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <template x-for="sale in metrics.recent_sales" :key="sale.id">
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div>
                                <p class="font-medium text-gray-900 dark:text-gray-100" x-text="sale.sale_number"></p>
                                <p class="text-sm text-gray-500 dark:text-gray-400" x-text="sale.customer?.full_name || 'عميل مجهول'"></p>
                                <p class="text-xs text-gray-400 dark:text-gray-500" x-text="formatDate(sale.sale_date)"></p>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-gray-900 dark:text-gray-100" x-text="formatCurrency(sale.total_amount)"></p>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      :class="getStatusColor(sale.status)"
                                      x-text="sale.status_label"></span>
                            </div>
                        </div>
                    </template>
                </div>
                
                <div x-show="!metrics.recent_sales || metrics.recent_sales.length === 0" class="text-center py-8">
                    <i class="fas fa-receipt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد مبيعات حديثة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Alerts -->
    <div x-show="metrics.inventory_alerts && metrics.inventory_alerts.length > 0" 
         class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">تنبيهات المخزون</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <template x-for="alert in metrics.inventory_alerts" :key="alert.part.id">
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                        <div class="flex items-start justify-between">
                            <div>
                                <p class="font-medium text-red-800 dark:text-red-200" x-text="alert.part.name"></p>
                                <p class="text-sm text-red-600 dark:text-red-400" x-text="alert.part.part_number"></p>
                            </div>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                  x-text="alert.severity"></span>
                        </div>
                        <div class="mt-2">
                            <p class="text-sm text-red-600 dark:text-red-400">
                                المخزون الحالي: <span x-text="alert.current_stock"></span>
                            </p>
                            <p class="text-sm text-red-600 dark:text-red-400">
                                الحد الأدنى: <span x-text="alert.min_level"></span>
                            </p>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div x-show="loading" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-3 space-x-reverse">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span class="text-gray-900 dark:text-gray-100">جاري تحميل البيانات...</span>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function posReportsDashboard() {
    return {
        selectedPeriod: '{{ $period }}',
        selectedLocation: '{{ $locationId }}',
        loading: false,
        metrics: @json($metrics),
        hourlySalesChart: null,
        paymentMethodsChart: null,

        init() {
            this.$nextTick(() => {
                this.initCharts();
            });
        },

        async updateData() {
            this.loading = true;
            try {
                const response = await fetch(`{{ route('pos.reports.dashboard') }}?period=${this.selectedPeriod}&location_id=${this.selectedLocation}`, {
                    headers: {
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    this.metrics = await response.json();
                    this.updateCharts();
                }
            } catch (error) {
                console.error('Error updating data:', error);
            } finally {
                this.loading = false;
            }
        },

        async refreshData() {
            await this.updateData();
        },

        initCharts() {
            this.initHourlySalesChart();
            this.initPaymentMethodsChart();
        },

        initHourlySalesChart() {
            const ctx = document.getElementById('hourlySalesChart');
            if (!ctx) return;

            const data = this.metrics.hourly_sales || [];
            
            this.hourlySalesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(item => item.hour),
                    datasets: [{
                        label: 'المبيعات',
                        data: data.map(item => item.sales_count),
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },

        initPaymentMethodsChart() {
            const ctx = document.getElementById('paymentMethodsChart');
            if (!ctx) return;

            const data = this.metrics.payment_methods || [];
            
            this.paymentMethodsChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.map(item => item.method_label),
                    datasets: [{
                        data: data.map(item => item.total),
                        backgroundColor: [
                            'rgb(59, 130, 246)',
                            'rgb(16, 185, 129)',
                            'rgb(245, 158, 11)',
                            'rgb(239, 68, 68)',
                            'rgb(139, 92, 246)',
                            'rgb(236, 72, 153)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        },

        updateCharts() {
            if (this.hourlySalesChart) {
                const data = this.metrics.hourly_sales || [];
                this.hourlySalesChart.data.labels = data.map(item => item.hour);
                this.hourlySalesChart.data.datasets[0].data = data.map(item => item.sales_count);
                this.hourlySalesChart.update();
            }

            if (this.paymentMethodsChart) {
                const data = this.metrics.payment_methods || [];
                this.paymentMethodsChart.data.labels = data.map(item => item.method_label);
                this.paymentMethodsChart.data.datasets[0].data = data.map(item => item.total);
                this.paymentMethodsChart.update();
            }
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR',
                minimumFractionDigits: 2
            }).format(amount || 0);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        getStatusColor(status) {
            const colors = {
                'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                'completed': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                'refunded': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
            };
            return colors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
        }
    }
}
</script>
@endpush
@endsection
