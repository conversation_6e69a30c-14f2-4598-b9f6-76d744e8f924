@extends('layouts.main')

@section('title', 'تفاصيل العميل')

@section('content')
@php
    $customer = $customer ?? new \App\Models\Customer();
    $stats = $stats ?? [
        'total_spent' => 0,
        'outstanding_balance' => 0,
        'average_repair_cost' => 0,
        'total_repairs' => 0,
        'completed_repairs' => 0,
        'pending_repairs' => 0,
        'total_devices' => 0,
        'active_devices' => 0
    ];
    $recentRepairs = $recentRepairs ?? collect();
@endphp

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تفاصيل العميل</h1>
            <p class="text-gray-600 dark:text-gray-400">
                @if(isset($customer) && $customer->exists)
                    {{ $customer->display_name ?? ($customer->first_name . ' ' . $customer->last_name) }} - {{ $customer->customer_number }}
                @else
                    عميل غير موجود
                @endif
            </p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('customers.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                العودة للقائمة
            </a>
            @if(isset($customer) && $customer->exists)
                <a href="{{ route('customers.edit', $customer) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                تعديل
                </a>
                <a href="{{ route('customers.analytics', $customer) }}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    التحليلات
                </a>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Customer Info -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Basic Info Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <div class="text-center">
                    <div class="mx-auto h-24 w-24 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700">
                        <img src="https://ui-avatars.com/api/?name={{ urlencode($customer->display_name ?? 'عميل') }}&background=3B82F6&color=fff&size=96"
                             alt="{{ $customer->display_name ?? 'عميل' }}" class="h-full w-full object-cover">
                    </div>
                    <h3 class="mt-4 text-xl font-medium text-gray-900 dark:text-gray-100">{{ $customer->display_name ?? 'عميل غير محدد' }}</h3>
                    @if($customer->email ?? false)
                        <p class="text-gray-500 dark:text-gray-400">{{ $customer->email }}</p>
                    @endif
                    @if($customer->mobile ?? false)
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ $customer->mobile }}</p>
                    @endif

                    <div class="mt-4 flex justify-center space-x-2 space-x-reverse">
                        @if($customer->is_vip ?? false)
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                VIP
                            </span>
                        @endif
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ ($customer->is_active ?? true) ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                            {{ ($customer->is_active ?? true) ? 'نشط' : 'غير نشط' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Financial Summary -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الملخص المالي</h4>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">إجمالي الإنفاق:</span>
                        <span class="font-medium text-green-600 dark:text-green-400">{{ number_format($stats['total_spent'], 2) }} ر.س</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الرصيد المستحق:</span>
                        <span class="font-medium {{ $stats['outstanding_balance'] > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400' }}">
                            {{ number_format($stats['outstanding_balance'], 2) }} ر.س
                        </span>
                    </div>
                    @if($customer->credit_limit ?? false)
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">حد الائتمان:</span>
                            <span class="font-medium">{{ number_format($customer->credit_limit ?? 0, 2) }} ر.س</span>
                        </div>
                    @endif
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">متوسط تكلفة الطلب:</span>
                        <span class="font-medium">{{ number_format($stats['average_repair_cost'], 2) }} ر.س</span>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات الاتصال</h4>
                <div class="space-y-3">
                    @if($customer->email ?? false)
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-sm text-gray-900 dark:text-gray-100">{{ $customer->email }}</span>
                        </div>
                    @endif

                    @if($customer->mobile ?? false)
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <span class="text-sm text-gray-900 dark:text-gray-100">{{ $customer->mobile }}</span>
                        </div>
                    @endif

                    @if($customer->phone ?? false)
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <span class="text-sm text-gray-900 dark:text-gray-100">{{ $customer->phone }}</span>
                        </div>
                    @endif

                    @if($customer->whatsapp ?? false)
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-400 ml-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097"/>
                            </svg>
                            <span class="text-sm text-gray-900 dark:text-gray-100">{{ $customer->whatsapp }}</span>
                        </div>
                    @endif

                    @if($customer->address)
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-gray-400 ml-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                {{ $customer->address }}
                                @if($customer->city || $customer->state)
                                    <br>
                                    {{ $customer->city }}{{ $customer->state ? ', ' . $customer->state : '' }}
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $stats['total_repairs'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">طلبات مكتملة</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $stats['completed_repairs'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                            <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">طلبات معلقة</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $stats['pending_repairs'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">الأجهزة</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $stats['total_devices'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs Navigation -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="border-b border-gray-200 dark:border-gray-700">
                    <nav class="flex space-x-8 space-x-reverse px-6" aria-label="Tabs">
                        <a href="#repairs" onclick="showTab('repairs')" id="repairs-tab" class="tab-link border-blue-500 text-blue-600 dark:text-blue-400 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            طلبات الصيانة
                        </a>
                        <a href="#devices" onclick="showTab('devices')" id="devices-tab" class="tab-link border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            الأجهزة
                        </a>
                        <a href="#payments" onclick="showTab('payments')" id="payments-tab" class="tab-link border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            المدفوعات
                        </a>
                        <a href="#communications" onclick="showTab('communications')" id="communications-tab" class="tab-link border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            التواصل
                        </a>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Repairs Tab -->
                    <div id="repairs-content" class="tab-content">
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100">طلبات الصيانة الأخيرة</h4>
                            @if(isset($customer) && $customer->exists)
                                <a href="{{ route('customers.repairs', $customer) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm">
                                    عرض الكل
                                </a>
                            @endif
                        </div>

                        @if($recentRepairs->count() > 0)
                            <div class="space-y-4">
                                @foreach($recentRepairs as $repair)
                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h5 class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->title ?? 'طلب صيانة #' . $repair->id }}</h5>
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $repair->description }}</p>
                                                <div class="flex items-center mt-2 space-x-4 space-x-reverse">
                                                    <span class="text-xs text-gray-500">{{ $repair->created_at->format('Y-m-d') }}</span>
                                                    @if($repair->technician)
                                                        <span class="text-xs text-gray-500">الفني: {{ $repair->technician->name }}</span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="text-left">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                    @switch($repair->status)
                                                        @case('pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 @break
                                                        @case('in_progress') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 @break
                                                        @case('completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @break
                                                        @case('cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @break
                                                        @default bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                                    @endswitch
                                                ">
                                                    @switch($repair->status)
                                                        @case('pending') معلق @break
                                                        @case('in_progress') قيد التنفيذ @break
                                                        @case('completed') مكتمل @break
                                                        @case('cancelled') ملغي @break
                                                        @default {{ $repair->status }}
                                                    @endswitch
                                                </span>
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100 mt-1">
                                                    {{ number_format($repair->total_cost ?? 0, 2) }} ر.س
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد طلبات صيانة</h3>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لم يقم هذا العميل بأي طلبات صيانة بعد.</p>
                            </div>
                        @endif
                    </div>

                    <!-- Devices Tab -->
                    <div id="devices-content" class="tab-content hidden">
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100">أجهزة العميل</h4>
                            <a href="{{ route('customers.devices', $customer) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm">
                                عرض الكل
                            </a>
                        </div>

                        @if($customer->devices->count() > 0)
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($customer->devices->take(4) as $device)
                                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                        <div class="flex items-center">
                                            <div class="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                                <svg class="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                                </svg>
                                            </div>
                                            <div class="mr-3">
                                                <h5 class="font-medium text-gray-900 dark:text-gray-100">{{ $device->device_type }}</h5>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $device->brand }} {{ $device->model }}</p>
                                                @if($device->serial_number)
                                                    <p class="text-xs text-gray-500">S/N: {{ $device->serial_number }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد أجهزة</h3>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لم يتم تسجيل أي أجهزة لهذا العميل.</p>
                            </div>
                        @endif
                    </div>

                    <!-- Payments Tab -->
                    <div id="payments-content" class="tab-content hidden">
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100">المدفوعات الأخيرة</h4>
                            <a href="{{ route('customers.payments', $customer) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm">
                                عرض الكل
                            </a>
                        </div>

                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد مدفوعات</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لم يتم تسجيل أي مدفوعات لهذا العميل.</p>
                        </div>
                    </div>

                    <!-- Communications Tab -->
                    <div id="communications-content" class="tab-content hidden">
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100">سجل التواصل</h4>
                            <a href="{{ route('customers.communications', $customer) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm">
                                عرض الكل
                            </a>
                        </div>

                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا يوجد تواصل</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">لم يتم تسجيل أي تواصل مع هذا العميل.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات سريعة</h4>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <a href="{{ route('repairs.create', ['customer_id' => $customer->id]) }}" class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">طلب صيانة جديد</span>
            </a>

            <a href="{{ route('customers.devices', $customer) }}" class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                <svg class="w-6 h-6 text-green-600 dark:text-green-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">إدارة الأجهزة</span>
            </a>

            <a href="{{ route('customers.communications', $customer) }}" class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                <svg class="w-6 h-6 text-purple-600 dark:text-purple-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">إضافة تواصل</span>
            </a>

            <a href="{{ route('customers.analytics', $customer) }}" class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                <svg class="w-6 h-6 text-orange-600 dark:text-orange-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">عرض التحليلات</span>
            </a>
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tabs
    const tabLinks = document.querySelectorAll('.tab-link');
    tabLinks.forEach(link => {
        link.classList.remove('border-blue-500', 'text-blue-600', 'dark:text-blue-400');
        link.classList.add('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    });

    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');

    // Add active class to selected tab
    const activeTab = document.getElementById(tabName + '-tab');
    activeTab.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    activeTab.classList.add('border-blue-500', 'text-blue-600', 'dark:text-blue-400');
}
</script>
@endsection