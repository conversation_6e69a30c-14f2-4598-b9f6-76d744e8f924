<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Location;
use App\Models\Promotion;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\SalePayment;
use App\Models\Part;
use App\Models\Customer;
use App\Models\User;

class POSSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default location if not exists
        $location = Location::firstOrCreate(
            ['code' => 'MAIN'],
            [
                'name' => 'الفرع الرئيسي',
                'description' => 'الفرع الرئيسي لمركز الصيانة',
                'city' => 'الرياض',
                'country' => 'المملكة العربية السعودية',
                'is_active' => true,
                'is_main_branch' => true,
                'business_hours' => [
                    'sunday' => ['open' => '09:00', 'close' => '18:00'],
                    'monday' => ['open' => '09:00', 'close' => '18:00'],
                    'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                    'thursday' => ['open' => '09:00', 'close' => '18:00'],
                    'friday' => ['open' => '14:00', 'close' => '18:00'],
                    'saturday' => ['open' => '09:00', 'close' => '18:00'],
                ],
                'services_offered' => [
                    'mobile_repair',
                    'computer_repair',
                    'tablet_repair',
                    'parts_sales',
                    'accessories_sales'
                ],
                'pos_settings' => [
                    'allow_sales' => true,
                    'tax_rate' => 15,
                    'payment_methods' => ['cash', 'card', 'bank_transfer'],
                    'auto_print_receipt' => true,
                    'require_customer_info' => false,
                ],
                'receipt_header' => 'مركز الصيانة المتقدم',
                'receipt_footer' => 'شكراً لثقتكم - نتطلع لخدمتكم مرة أخرى',
            ]
        );

        // Create sample promotions
        $promotions = [
            [
                'name' => 'خصم 10% على جميع الإكسسوارات',
                'code' => 'ACC10',
                'description' => 'خصم 10% على جميع الإكسسوارات',
                'type' => 'percentage',
                'value' => 10,
                'max_discount' => 100,
                'min_purchase' => 50,
                'is_active' => true,
                'start_date' => now()->subDays(30),
                'end_date' => now()->addDays(30),
                'usage_limit' => 100,
                'usage_limit_per_customer' => 5,
                'used_count' => 0,
                'applicable_locations' => [$location->id],
                'created_by' => 1,
            ],
            [
                'name' => 'خصم 50 ريال على المشتريات فوق 200 ريال',
                'code' => 'SAVE50',
                'description' => 'خصم 50 ريال على المشتريات التي تزيد عن 200 ريال',
                'type' => 'fixed_amount',
                'value' => 50,
                'min_purchase' => 200,
                'is_active' => true,
                'start_date' => now()->subDays(15),
                'end_date' => now()->addDays(45),
                'usage_limit' => 50,
                'usage_limit_per_customer' => 2,
                'used_count' => 0,
                'applicable_locations' => [$location->id],
                'created_by' => 1,
            ],
            [
                'name' => 'اشتري 2 واحصل على 1 مجاناً - شواحن',
                'code' => 'CHARGER21',
                'description' => 'اشتري شاحنين واحصل على الثالث مجاناً',
                'type' => 'buy_x_get_y',
                'buy_quantity' => 2,
                'get_quantity' => 1,
                'is_active' => true,
                'start_date' => now()->subDays(7),
                'end_date' => now()->addDays(23),
                'usage_limit' => 30,
                'usage_limit_per_customer' => 1,
                'used_count' => 0,
                'applicable_items' => ['chargers'],
                'applicable_locations' => [$location->id],
                'created_by' => 1,
            ]
        ];

        foreach ($promotions as $promotionData) {
            Promotion::firstOrCreate(
                ['code' => $promotionData['code']],
                $promotionData
            );
        }

        // Create sample sales if we have parts and customers
        if (Part::count() > 0 && Customer::count() > 0 && User::count() > 0) {
            $this->createSampleSales($location);
        }
    }

    private function createSampleSales($location)
    {
        $parts = Part::limit(10)->get();
        $customers = Customer::limit(5)->get();
        $users = User::limit(3)->get();

        // Create 10 sample sales
        for ($i = 1; $i <= 10; $i++) {
            $customer = $customers->random();
            $user = $users->random();
            
            $sale = Sale::create([
                'sale_number' => Sale::generateSaleNumber($location->code),
                'customer_id' => rand(0, 1) ? $customer->id : null, // 50% chance of having customer
                'user_id' => $user->id,
                'location_id' => $location->id,
                'sale_type' => collect(['parts_only', 'accessories', 'mixed'])->random(),
                'status' => collect(['pending', 'completed', 'completed', 'completed'])->random(), // 75% completed
                'payment_status' => 'pending',
                'sale_date' => now()->subDays(rand(0, 30)),
                'subtotal' => 0,
                'tax_amount' => 0,
                'discount_amount' => 0,
                'total_amount' => 0,
                'paid_amount' => 0,
                'remaining_amount' => 0,
            ]);

            // Add 1-5 items to each sale
            $itemCount = rand(1, 5);
            $subtotal = 0;

            for ($j = 1; $j <= $itemCount; $j++) {
                $part = $parts->random();
                $quantity = rand(1, 3);
                $unitPrice = $part->price * (1 + (rand(-20, 20) / 100)); // ±20% price variation
                $lineTotal = $quantity * $unitPrice;
                $subtotal += $lineTotal;

                SaleItem::create([
                    'sale_id' => $sale->id,
                    'item_type' => 'part',
                    'part_id' => $part->id,
                    'item_name' => $part->name,
                    'item_code' => $part->part_number,
                    'item_description' => $part->description,
                    'item_category' => $part->category,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'original_price' => $part->price,
                    'discount_amount' => 0,
                    'line_total' => $lineTotal,
                    'tax_rate' => 15,
                    'tax_amount' => $lineTotal * 0.15,
                    'affects_inventory' => true,
                ]);
            }

            // Calculate totals
            $discountAmount = rand(0, 1) ? ($subtotal * rand(5, 15) / 100) : 0; // Random discount
            $taxableAmount = $subtotal - $discountAmount;
            $taxAmount = $taxableAmount * 0.15;
            $totalAmount = $subtotal - $discountAmount + $taxAmount;

            $sale->update([
                'subtotal' => $subtotal,
                'discount_amount' => $discountAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'remaining_amount' => $totalAmount,
            ]);

            // Add payments for completed sales
            if ($sale->status === 'completed') {
                $paymentMethods = ['cash', 'card', 'bank_transfer'];
                $remainingAmount = $totalAmount;

                // Add 1-2 payments
                $paymentCount = rand(1, 2);
                for ($k = 1; $k <= $paymentCount && $remainingAmount > 0; $k++) {
                    $paymentAmount = $k === $paymentCount ? $remainingAmount : rand(50, min(200, $remainingAmount));
                    $paymentMethod = collect($paymentMethods)->random();

                    $payment = SalePayment::create([
                        'sale_id' => $sale->id,
                        'payment_number' => SalePayment::generatePaymentNumber(),
                        'payment_method' => $paymentMethod,
                        'amount' => $paymentAmount,
                        'status' => 'completed',
                        'processed_by' => $user->id,
                        'processed_at' => $sale->sale_date->addMinutes(rand(5, 60)),
                        'reference_number' => $paymentMethod !== 'cash' ? 'REF' . rand(100000, 999999) : null,
                        'card_last_four' => $paymentMethod === 'card' ? rand(1000, 9999) : null,
                        'card_type' => $paymentMethod === 'card' ? collect(['visa', 'mastercard', 'mada'])->random() : null,
                    ]);

                    $remainingAmount -= $paymentAmount;
                }

                $sale->update([
                    'paid_amount' => $totalAmount - $remainingAmount,
                    'remaining_amount' => $remainingAmount,
                    'payment_status' => $remainingAmount > 0 ? 'partial' : 'paid',
                    'completed_at' => $sale->sale_date->addHours(rand(1, 24)),
                ]);
            }
        }

        $this->command->info('تم إنشاء ' . Sale::count() . ' مبيعة تجريبية');
        $this->command->info('تم إنشاء ' . SaleItem::count() . ' عنصر مبيعة');
        $this->command->info('تم إنشاء ' . SalePayment::count() . ' دفعة');
    }
}
