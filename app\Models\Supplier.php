<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Supplier extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'supplier_number',
        'name',
        'company_name',
        'contact_person',
        'email',
        'phone',
        'mobile',
        'fax',
        'website',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'tax_number',
        'registration_number',
        'bank_name',
        'bank_account',
        'iban',
        'swift_code',
        'payment_terms',
        'credit_limit',
        'discount_percentage',
        'delivery_time',
        'minimum_order',
        'currency',
        'rating',
        'status',
        'category',
        'specialization',
        'certifications',
        'notes',
        'contract_start_date',
        'contract_end_date',
        'is_preferred',
        'is_active',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'minimum_order' => 'decimal:2',
        'rating' => 'decimal:1',
        'contract_start_date' => 'date',
        'contract_end_date' => 'date',
        'specialization' => 'array',
        'certifications' => 'array',
        'is_preferred' => 'boolean',
        'is_active' => 'boolean'
    ];

    protected $dates = [
        'contract_start_date',
        'contract_end_date',
        'deleted_at'
    ];

    // Relationships
    public function parts()
    {
        return $this->hasMany(Part::class);
    }

    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    public function contacts()
    {
        return $this->hasMany(SupplierContact::class);
    }

    public function evaluations()
    {
        return $this->hasMany(SupplierEvaluation::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get supplier payables
     */
    public function payables()
    {
        return $this->hasMany(Payable::class);
    }

    /**
     * Get outstanding balance
     */
    public function getOutstandingBalance()
    {
        return $this->payables()->whereIn('status', ['pending', 'partial'])->sum('remaining_amount');
    }

    /**
     * Check if credit limit is exceeded
     */
    public function isCreditLimitExceeded()
    {
        return $this->credit_limit > 0 && $this->getOutstandingBalance() > $this->credit_limit;
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    public function notifications()
    {
        return $this->morphMany(Notification::class, 'related');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'supplier_categories');
    }

    // Accessors
    public function getDisplayNameAttribute()
    {
        return $this->company_name ?: $this->name;
    }

    public function getStatusBadgeAttribute()
    {
        $statuses = [
            'active' => ['class' => 'badge-success', 'text' => 'نشط'],
            'inactive' => ['class' => 'badge-secondary', 'text' => 'غير نشط'],
            'suspended' => ['class' => 'badge-warning', 'text' => 'معلق'],
            'blacklisted' => ['class' => 'badge-danger', 'text' => 'محظور']
        ];

        return $statuses[$this->status] ?? ['class' => 'badge-secondary', 'text' => 'غير محدد'];
    }

    public function getRatingBadgeAttribute()
    {
        if ($this->rating >= 4.5) {
            return ['class' => 'badge-success', 'text' => 'ممتاز'];
        } elseif ($this->rating >= 3.5) {
            return ['class' => 'badge-info', 'text' => 'جيد'];
        } elseif ($this->rating >= 2.5) {
            return ['class' => 'badge-warning', 'text' => 'متوسط'];
        } else {
            return ['class' => 'badge-danger', 'text' => 'ضعيف'];
        }
    }

    public function getContractStatusAttribute()
    {
        if (!$this->contract_start_date || !$this->contract_end_date) {
            return ['class' => 'badge-secondary', 'text' => 'بدون عقد'];
        }

        $now = now();
        
        if ($now->lt($this->contract_start_date)) {
            return ['class' => 'badge-info', 'text' => 'لم يبدأ'];
        } elseif ($now->gt($this->contract_end_date)) {
            return ['class' => 'badge-danger', 'text' => 'منتهي'];
        } elseif ($now->diffInDays($this->contract_end_date) <= 30) {
            return ['class' => 'badge-warning', 'text' => 'ينتهي قريباً'];
        } else {
            return ['class' => 'badge-success', 'text' => 'ساري'];
        }
    }

    public function getTotalPurchasesAttribute()
    {
        return $this->purchases()->sum('total_amount');
    }

    public function getAverageDeliveryTimeAttribute()
    {
        return $this->purchases()
            ->whereNotNull('delivered_at')
            ->get()
            ->avg(function ($purchase) {
                return $purchase->created_at->diffInDays($purchase->delivered_at);
            }) ?? 0;
    }

    public function getOnTimeDeliveryRateAttribute()
    {
        $totalDeliveries = $this->purchases()->whereNotNull('delivered_at')->count();
        
        if ($totalDeliveries === 0) {
            return 0;
        }
        
        $onTimeDeliveries = $this->purchases()
            ->whereNotNull('delivered_at')
            ->whereColumn('delivered_at', '<=', 'expected_delivery_date')
            ->count();
            
        return ($onTimeDeliveries / $totalDeliveries) * 100;
    }

    public function getLastPurchaseDateAttribute()
    {
        $lastPurchase = $this->purchases()->latest()->first();
        return $lastPurchase ? $lastPurchase->created_at : null;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    public function scopePreferred($query)
    {
        return $query->where('is_preferred', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeBySpecialization($query, $specialization)
    {
        return $query->whereJsonContains('specialization', $specialization);
    }

    public function scopeByRating($query, $minRating)
    {
        return $query->where('rating', '>=', $minRating);
    }

    public function scopeWithExpiredContracts($query)
    {
        return $query->where('contract_end_date', '<', now());
    }

    public function scopeWithExpiringContracts($query, $days = 30)
    {
        return $query->whereBetween('contract_end_date', [now(), now()->addDays($days)]);
    }

    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('company_name', 'like', "%{$term}%")
              ->orWhere('contact_person', 'like', "%{$term}%")
              ->orWhere('email', 'like', "%{$term}%")
              ->orWhere('phone', 'like', "%{$term}%")
              ->orWhere('supplier_number', 'like', "%{$term}%");
        });
    }

    // Methods
    public function generateSupplierNumber()
    {
        $prefix = 'SUP';
        $year = now()->year;
        
        $lastSupplier = static::whereYear('created_at', $year)
                             ->orderBy('id', 'desc')
                             ->first();
        
        $sequence = $lastSupplier ? (int)substr($lastSupplier->supplier_number, -5) + 1 : 1;
        
        return $prefix . $year . str_pad($sequence, 5, '0', STR_PAD_LEFT);
    }

    public function addContact($name, $position, $email = null, $phone = null)
    {
        return $this->contacts()->create([
            'name' => $name,
            'position' => $position,
            'email' => $email,
            'phone' => $phone,
            'is_primary' => $this->contacts()->count() === 0
        ]);
    }

    public function updateRating()
    {
        $evaluations = $this->evaluations()->where('created_at', '>=', now()->subYear())->get();
        
        if ($evaluations->isEmpty()) {
            return $this->rating;
        }
        
        $averageRating = $evaluations->avg('overall_rating');
        $this->update(['rating' => round($averageRating, 1)]);
        
        return $this->rating;
    }

    public function evaluate($criteria)
    {
        $evaluation = $this->evaluations()->create([
            'quality_rating' => $criteria['quality'] ?? 0,
            'delivery_rating' => $criteria['delivery'] ?? 0,
            'service_rating' => $criteria['service'] ?? 0,
            'price_rating' => $criteria['price'] ?? 0,
            'overall_rating' => ($criteria['quality'] + $criteria['delivery'] + $criteria['service'] + $criteria['price']) / 4,
            'comments' => $criteria['comments'] ?? null,
            'evaluated_by' => auth()->id(),
            'evaluation_date' => now()
        ]);
        
        $this->updateRating();
        
        return $evaluation;
    }

    public function hasSpecialization($specialization)
    {
        return in_array($specialization, $this->specialization ?? []);
    }

    public function addSpecialization($specialization)
    {
        $specializations = $this->specialization ?? [];
        
        if (!in_array($specialization, $specializations)) {
            $specializations[] = $specialization;
            $this->update(['specialization' => $specializations]);
        }
    }

    public function removeSpecialization($specialization)
    {
        $specializations = $this->specialization ?? [];
        $specializations = array_filter($specializations, function($s) use ($specialization) {
            return $s !== $specialization;
        });
        
        $this->update(['specialization' => array_values($specializations)]);
    }

    public function hasCertification($certification)
    {
        return in_array($certification, $this->certifications ?? []);
    }

    public function addCertification($certification, $expiryDate = null)
    {
        $certifications = $this->certifications ?? [];
        
        $certifications[] = [
            'name' => $certification,
            'issued_date' => now()->toDateString(),
            'expiry_date' => $expiryDate
        ];
        
        $this->update(['certifications' => $certifications]);
    }

    public function isContractActive()
    {
        if (!$this->contract_start_date || !$this->contract_end_date) {
            return false;
        }
        
        $now = now();
        return $now->gte($this->contract_start_date) && $now->lte($this->contract_end_date);
    }

    public function isContractExpiring($days = 30)
    {
        if (!$this->contract_end_date) {
            return false;
        }
        
        return $this->contract_end_date->diffInDays(now()) <= $days && $this->contract_end_date->isFuture();
    }

    public function renewContract($startDate, $endDate)
    {
        $this->update([
            'contract_start_date' => $startDate,
            'contract_end_date' => $endDate
        ]);
        
        return true;
    }

    public function markAsPreferred($reason = null)
    {
        $this->update(['is_preferred' => true]);
        
        if ($reason) {
            $this->update(['notes' => $this->notes . "\n\nMarked as preferred: {$reason}"]);
        }
    }

    public function removePreferredStatus($reason = null)
    {
        $this->update(['is_preferred' => false]);
        
        if ($reason) {
            $this->update(['notes' => $this->notes . "\n\nPreferred status removed: {$reason}"]);
        }
    }

    public function suspend($reason)
    {
        $this->update([
            'status' => 'suspended',
            'notes' => $this->notes . "\n\nSuspended: {$reason}"
        ]);
    }

    public function reactivate($reason = null)
    {
        $this->update([
            'status' => 'active',
            'notes' => $this->notes . "\n\nReactivated: {$reason}"
        ]);
    }

    public function blacklist($reason)
    {
        $this->update([
            'status' => 'blacklisted',
            'is_active' => false,
            'notes' => $this->notes . "\n\nBlacklisted: {$reason}"
        ]);
    }

    public function getPerformanceMetrics($months = 12)
    {
        $purchases = $this->purchases()
            ->where('created_at', '>=', now()->subMonths($months))
            ->get();
            
        return [
            'total_orders' => $purchases->count(),
            'total_value' => $purchases->sum('total_amount'),
            'average_order_value' => $purchases->avg('total_amount') ?? 0,
            'on_time_delivery_rate' => $this->on_time_delivery_rate,
            'average_delivery_time' => $this->average_delivery_time,
            'quality_rating' => $this->rating,
            'defect_rate' => $this->calculateDefectRate($months),
            'return_rate' => $this->calculateReturnRate($months)
        ];
    }

    private function calculateDefectRate($months)
    {
        // This would need to be implemented based on your defect tracking system
        return 0;
    }

    private function calculateReturnRate($months)
    {
        // This would need to be implemented based on your return tracking system
        return 0;
    }

    public function canPlaceOrder($amount)
    {
        if (!$this->is_active || $this->status !== 'active') {
            return false;
        }
        
        if ($amount < $this->minimum_order) {
            return false;
        }
        
        if ($this->credit_limit > 0) {
            $outstandingAmount = $this->purchases()
                ->whereIn('payment_status', ['pending', 'partial'])
                ->sum('total_amount');
                
            return ($outstandingAmount + $amount) <= $this->credit_limit;
        }
        
        return true;
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($supplier) {
            if (!$supplier->supplier_number) {
                $supplier->supplier_number = $supplier->generateSupplierNumber();
            }
            $supplier->created_by = auth()->id();
        });
        
        static::updating(function ($supplier) {
            $supplier->updated_by = auth()->id();
        });
    }
}
