<div class="flex flex-col h-full">
    <!-- Logo -->
    <div class="flex items-center justify-center h-16 px-4 bg-primary-600 dark:bg-primary-700">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
            </div>
            <div class="ml-3">
                <h1 class="text-xl font-bold text-white">الطارق</h1>
                <p class="text-xs text-primary-100">إدارة مراكز الصيانة</p>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        <!-- Dashboard -->
        <a href="{{ route('dashboard') }}" 
           class="sidebar-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
            </svg>
            <span>لوحة التحكم</span>
        </a>

        <!-- POS -->
        <a href="{{ route('pos.index') }}" 
           class="sidebar-link {{ request()->routeIs('pos.*') ? 'active' : '' }}">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <span>نقاط البيع</span>
        </a>

        <!-- Maintenance -->
        <div x-data="{ open: {{ request()->routeIs('maintenance.*') ? 'true' : 'false' }} }">
            <button @click="open = !open" 
                    class="sidebar-link w-full justify-between {{ request()->routeIs('maintenance.*') ? 'active' : '' }}">
                <div class="flex items-center">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>إدارة الصيانة</span>
                </div>
                <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
            <div x-show="open" x-transition class="mr-8 mt-1 space-y-1">
                <a href="{{ route('maintenance.index') }}" class="sidebar-link text-sm">جميع الصيانات</a>
                <a href="{{ route('maintenance.create') }}" class="sidebar-link text-sm">صيانة جديدة</a>
                <a href="{{ route('maintenance.pending') }}" class="sidebar-link text-sm">قيد الانتظار</a>
                <a href="{{ route('maintenance.completed') }}" class="sidebar-link text-sm">مكتملة</a>
            </div>
        </div>

        <!-- Products -->
        <div x-data="{ open: {{ request()->routeIs('products.*') ? 'true' : 'false' }} }">
            <button @click="open = !open" 
                    class="sidebar-link w-full justify-between {{ request()->routeIs('products.*') ? 'active' : '' }}">
                <div class="flex items-center">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <span>إدارة المنتجات</span>
                </div>
                <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
            <div x-show="open" x-transition class="mr-8 mt-1 space-y-1">
                <a href="{{ route('products.index') }}" class="sidebar-link text-sm">جميع المنتجات</a>
                <a href="{{ route('products.create') }}" class="sidebar-link text-sm">منتج جديد</a>
                <a href="{{ route('products.categories') }}" class="sidebar-link text-sm">التصنيفات</a>
                <a href="{{ route('products.low-stock') }}" class="sidebar-link text-sm">مخزون منخفض</a>
            </div>
        </div>

        <!-- Inventory Management -->
        <div x-data="{ open: {{ request()->routeIs('inventory.*') ? 'true' : 'false' }} }">
            <button @click="open = !open"
                    class="sidebar-link w-full justify-between {{ request()->routeIs('inventory.*') ? 'active' : '' }}">
                <div class="flex items-center">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <span>إدارة المخزون</span>
                </div>
                <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
            <div x-show="open" x-transition class="mr-8 mt-1 space-y-1">
                <a href="{{ route('inventory.index') }}" class="sidebar-link text-sm">لوحة المخزون</a>
                <a href="{{ route('inventory.movements') }}" class="sidebar-link text-sm">حركات المخزون</a>
                <a href="{{ route('inventory.adjust-stock') }}" class="sidebar-link text-sm">تعديل المخزون</a>
                <a href="{{ route('inventory.low-stock') }}" class="sidebar-link text-sm">تنبيهات المخزون</a>
                <a href="{{ route('inventory.reports') }}" class="sidebar-link text-sm">تقارير المخزون</a>
            </div>
        </div>

        <!-- Customers -->
        <a href="{{ route('customers.index') }}"
           class="sidebar-link {{ request()->routeIs('customers.*') ? 'active' : '' }}">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <span>العملاء</span>
        </a>

        <!-- Suppliers -->
        <a href="{{ route('suppliers.index') }}" 
           class="sidebar-link {{ request()->routeIs('suppliers.*') ? 'active' : '' }}">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <span>الموردين</span>
        </a>

        <!-- Expenses -->
        <a href="{{ route('expenses.index') }}"
           class="sidebar-link {{ request()->routeIs('expenses.*') ? 'active' : '' }}">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <span>المصروفات</span>
        </a>

        <!-- Accounting System -->
        <div x-data="{ open: {{ request()->routeIs('accounting.*') || request()->routeIs('accounts.*') || request()->routeIs('journal-entries.*') ? 'true' : 'false' }} }">
            <button @click="open = !open"
                    class="sidebar-link w-full justify-between {{ request()->routeIs('accounting.*') || request()->routeIs('accounts.*') || request()->routeIs('journal-entries.*') ? 'active' : '' }}">
                <div class="flex items-center">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    <span>النظام المحاسبي</span>
                </div>
                <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
            <div x-show="open" x-transition class="mr-8 mt-1 space-y-1">
                <a href="{{ route('accounts.index') }}" class="sidebar-link text-sm {{ request()->routeIs('accounts.*') ? 'active' : '' }}">دليل الحسابات</a>
                <a href="{{ route('journal-entries.index') }}" class="sidebar-link text-sm {{ request()->routeIs('journal-entries.*') ? 'active' : '' }}">القيود المحاسبية</a>
                <a href="{{ route('accounting.receivables.index') }}" class="sidebar-link text-sm {{ request()->routeIs('accounting.receivables.*') ? 'active' : '' }}">الذمم المدينة</a>
                <a href="{{ route('accounting.payables.index') }}" class="sidebar-link text-sm {{ request()->routeIs('accounting.payables.*') ? 'active' : '' }}">الذمم الدائنة</a>
                <a href="{{ route('accounting-reports.index') }}" class="sidebar-link text-sm {{ request()->routeIs('accounting.reports.*') ? 'active' : '' }}">التقارير المحاسبية</a>
            </div>
        </div>

        <!-- Reports -->
        <div x-data="{ open: {{ request()->routeIs('reports.*') ? 'true' : 'false' }} }">
            <button @click="open = !open" 
                    class="sidebar-link w-full justify-between {{ request()->routeIs('reports.*') ? 'active' : '' }}">
                <div class="flex items-center">
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span>التقارير</span>
                </div>
                <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
            <div x-show="open" x-transition class="mr-8 mt-1 space-y-1">
                <a href="{{ route('reports.sales') }}" class="sidebar-link text-sm">تقرير المبيعات</a>
                <a href="{{ route('reports.maintenance') }}" class="sidebar-link text-sm">تقرير الصيانة</a>
                <a href="{{ route('reports.inventory') }}" class="sidebar-link text-sm">تقرير المخزون</a>
                <a href="{{ route('reports.financial') }}" class="sidebar-link text-sm">التقرير المالي</a>
            </div>
        </div>

        <!-- Settings -->
        <a href="{{ route('settings.index') }}" 
           class="sidebar-link {{ request()->routeIs('settings.*') ? 'active' : '' }}">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span>الإعدادات</span>
        </a>
    </nav>

    <!-- User info -->
    <div class="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name={{ auth()->user()->name ?? 'المستخدم' }}&background=3b82f6&color=fff" alt="صورة المستخدم">
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ auth()->user()->name ?? 'المستخدم' }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ auth()->user()->email ?? '<EMAIL>' }}</p>
            </div>
        </div>
    </div>
</div>
