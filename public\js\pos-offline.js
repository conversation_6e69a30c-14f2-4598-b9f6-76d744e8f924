/**
 * POS Offline Mode Manager
 * Handles offline functionality for the POS system
 */

class POSOfflineManager {
    constructor(config = {}) {
        this.config = {
            autoSync: true,
            syncInterval: 300000, // 5 minutes
            maxOfflineSales: 100,
            storagePrefix: 'pos_offline_',
            ...config
        };
        
        this.isOnline = navigator.onLine;
        this.syncTimer = null;
        this.offlineData = null;
        this.pendingSales = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadOfflineData();
        this.loadPendingSales();
        
        if (this.config.autoSync && this.isOnline) {
            this.startAutoSync();
        }
    }

    setupEventListeners() {
        // Network status events
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.onNetworkStatusChange('online');
            if (this.config.autoSync) {
                this.startAutoSync();
            }
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.onNetworkStatusChange('offline');
            this.stopAutoSync();
        });

        // Page visibility events
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isOnline && this.config.autoSync) {
                this.syncData();
            }
        });

        // Before unload - save any pending data
        window.addEventListener('beforeunload', () => {
            this.savePendingData();
        });
    }

    async prepareOfflineData(locationId = null) {
        try {
            const response = await fetch(`/pos/offline/prepare-data?location_id=${locationId || ''}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.success) {
                this.offlineData = result.data;
                this.saveToStorage('offline_data', this.offlineData);
                this.showNotification('تم تحضير البيانات للوضع غير المتصل', 'success');
                return result;
            } else {
                throw new Error(result.message || 'فشل في تحضير البيانات');
            }
        } catch (error) {
            console.error('Failed to prepare offline data:', error);
            this.showNotification('فشل في تحضير البيانات للوضع غير المتصل', 'error');
            throw error;
        }
    }

    async storeOfflineSale(saleData) {
        try {
            // Add offline metadata
            const offlineSale = {
                ...saleData,
                offline_id: this.generateOfflineId(),
                created_offline: true,
                offline_timestamp: Date.now(),
                sync_status: 'pending'
            };

            // Store locally
            this.pendingSales.push(offlineSale);
            this.saveToStorage('pending_sales', this.pendingSales);

            // Try to sync immediately if online
            if (this.isOnline) {
                await this.syncSale(offlineSale);
            }

            this.showNotification('تم حفظ المبيعة في الوضع غير المتصل', 'success');
            return { success: true, offline_id: offlineSale.offline_id };

        } catch (error) {
            console.error('Failed to store offline sale:', error);
            this.showNotification('فشل في حفظ المبيعة', 'error');
            throw error;
        }
    }

    async syncData() {
        if (!this.isOnline || this.pendingSales.length === 0) {
            return { success: true, synced_count: 0 };
        }

        try {
            const response = await fetch('/pos/offline/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.success) {
                // Clear synced sales
                this.pendingSales = this.pendingSales.filter(sale => sale.sync_status !== 'synced');
                this.saveToStorage('pending_sales', this.pendingSales);
                
                if (result.synced_count > 0) {
                    this.showNotification(`تم مزامنة ${result.synced_count} مبيعة`, 'success');
                }
            }

            return result;

        } catch (error) {
            console.error('Failed to sync data:', error);
            this.showNotification('فشل في مزامنة البيانات', 'error');
            throw error;
        }
    }

    async syncSale(saleData) {
        try {
            const response = await fetch('/pos/offline/store-sale', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(saleData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.success) {
                // Mark as synced
                const saleIndex = this.pendingSales.findIndex(sale => sale.offline_id === saleData.offline_id);
                if (saleIndex !== -1) {
                    this.pendingSales[saleIndex].sync_status = 'synced';
                    this.saveToStorage('pending_sales', this.pendingSales);
                }
            }

            return result;

        } catch (error) {
            console.error('Failed to sync sale:', error);
            throw error;
        }
    }

    async getSyncStatus() {
        try {
            const response = await fetch('/pos/offline/status', {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            return result.status;

        } catch (error) {
            console.error('Failed to get sync status:', error);
            return null;
        }
    }

    startAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
        }

        this.syncTimer = setInterval(() => {
            if (this.isOnline) {
                this.syncData();
            }
        }, this.config.syncInterval);
    }

    stopAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
        }
    }

    loadOfflineData() {
        this.offlineData = this.getFromStorage('offline_data');
    }

    loadPendingSales() {
        this.pendingSales = this.getFromStorage('pending_sales') || [];
    }

    savePendingData() {
        this.saveToStorage('pending_sales', this.pendingSales);
    }

    generateOfflineId() {
        return `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    saveToStorage(key, data) {
        try {
            localStorage.setItem(this.config.storagePrefix + key, JSON.stringify(data));
        } catch (error) {
            console.error('Failed to save to storage:', error);
        }
    }

    getFromStorage(key) {
        try {
            const data = localStorage.getItem(this.config.storagePrefix + key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Failed to get from storage:', error);
            return null;
        }
    }

    clearStorage() {
        try {
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith(this.config.storagePrefix)) {
                    localStorage.removeItem(key);
                }
            });
        } catch (error) {
            console.error('Failed to clear storage:', error);
        }
    }

    onNetworkStatusChange(status) {
        const statusElement = document.getElementById('network-status');
        if (statusElement) {
            statusElement.textContent = status === 'online' ? 'متصل' : 'غير متصل';
            statusElement.className = status === 'online' ? 'text-green-600' : 'text-red-600';
        }

        this.showNotification(
            status === 'online' ? 'تم الاتصال بالإنترنت' : 'انقطع الاتصال بالإنترنت',
            status === 'online' ? 'success' : 'warning'
        );

        // Trigger custom event
        window.dispatchEvent(new CustomEvent('pos-network-status-change', {
            detail: { status, isOnline: this.isOnline }
        }));
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${this.getNotificationClasses(type)}`;
        notification.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    ${this.getNotificationIcon(type)}
                </div>
                <div class="mr-3">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <div class="mr-auto pl-3">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                        <span class="sr-only">إغلاق</span>
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    getNotificationClasses(type) {
        const classes = {
            success: 'bg-green-50 border border-green-200 text-green-800',
            error: 'bg-red-50 border border-red-200 text-red-800',
            warning: 'bg-yellow-50 border border-yellow-200 text-yellow-800',
            info: 'bg-blue-50 border border-blue-200 text-blue-800'
        };
        return classes[type] || classes.info;
    }

    getNotificationIcon(type) {
        const icons = {
            success: '<svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
            error: '<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
            warning: '<svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
            info: '<svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
        };
        return icons[type] || icons.info;
    }

    // Public API
    getPendingSalesCount() {
        return this.pendingSales.filter(sale => sale.sync_status === 'pending').length;
    }

    getOfflineData() {
        return this.offlineData;
    }

    isOfflineMode() {
        return !this.isOnline;
    }

    async forceSync() {
        return await this.syncData();
    }

    clearOfflineData() {
        this.clearStorage();
        this.offlineData = null;
        this.pendingSales = [];
    }
}

// Global instance
window.POSOffline = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Get configuration from meta tags or default
    const config = {
        autoSync: document.querySelector('meta[name="pos-auto-sync"]')?.content === 'true',
        syncInterval: parseInt(document.querySelector('meta[name="pos-sync-interval"]')?.content) || 300000,
    };

    window.POSOffline = new POSOfflineManager(config);
});
