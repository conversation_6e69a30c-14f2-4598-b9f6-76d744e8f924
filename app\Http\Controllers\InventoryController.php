<?php

namespace App\Http\Controllers;

use App\Models\Inventory;
use App\Models\InventoryMovement;
use App\Models\Part;
use App\Models\Location;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InventoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display inventory dashboard
     */
    public function index()
    {
        // Get inventory statistics
        $stats = [
            'total_items' => Inventory::sum('quantity'),
            'total_value' => Inventory::sum(DB::raw('quantity * cost_price')),
            'low_stock_items' => Inventory::lowStock()->count(),
            'out_of_stock_items' => Inventory::outOfStock()->count(),
            'expired_items' => Inventory::expired()->count(),
            'near_expiry_items' => Inventory::nearExpiry()->count(),
        ];

        // Recent movements
        $recentMovements = InventoryMovement::with(['inventory.product', 'user'])
            ->latest()
            ->limit(10)
            ->get();

        // Low stock alerts
        $lowStockItems = Inventory::with(['product', 'location'])
            ->lowStock()
            ->limit(10)
            ->get();

        // Items near expiry
        $nearExpiryItems = Inventory::with(['product', 'location'])
            ->nearExpiry()
            ->limit(10)
            ->get();

        return view('inventory.index', compact(
            'stats',
            'recentMovements', 
            'lowStockItems',
            'nearExpiryItems'
        ));
    }

    /**
     * Display inventory movements
     */
    public function movements(Request $request)
    {
        $query = InventoryMovement::with(['inventory.product', 'user']);

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $query->whereHas('inventory.product', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%');
            });
        }

        $movements = $query->latest()->paginate(20);

        // Movement statistics
        $movementStats = InventoryMovement::getMovementStats();

        return view('inventory.movements', compact('movements', 'movementStats'));
    }

    /**
     * Show stock adjustment form
     */
    public function adjustStock(Request $request)
    {
        $inventory = null;
        
        if ($request->filled('inventory_id')) {
            $inventory = Inventory::with(['product', 'location'])->find($request->inventory_id);
        }

        $products = Product::active()->get();
        $locations = Location::active()->get();

        return view('inventory.adjust-stock', compact('inventory', 'products', 'locations'));
    }

    /**
     * Process stock adjustment
     */
    public function processAdjustment(Request $request)
    {
        $request->validate([
            'inventory_id' => 'required|exists:inventories,id',
            'new_quantity' => 'required|integer|min:0',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string|max:1000'
        ]);

        $inventory = Inventory::findOrFail($request->inventory_id);
        $oldQuantity = $inventory->quantity;
        
        DB::transaction(function () use ($inventory, $request, $oldQuantity) {
            // Update inventory quantity
            $inventory->update(['quantity' => $request->new_quantity]);

            // Log the movement
            InventoryMovement::create([
                'inventory_id' => $inventory->id,
                'type' => 'adjustment',
                'quantity' => abs($request->new_quantity - $oldQuantity),
                'reason' => $request->reason,
                'notes' => $request->notes,
                'user_id' => auth()->id()
            ]);
        });

        return redirect()->route('inventory.index')
            ->with('success', 'تم تعديل المخزون بنجاح');
    }

    /**
     * Show transfer form
     */
    public function transferStock(Request $request)
    {
        $inventory = null;
        
        if ($request->filled('inventory_id')) {
            $inventory = Inventory::with(['product', 'location'])->find($request->inventory_id);
        }

        $locations = Location::active()->get();

        return view('inventory.transfer-stock', compact('inventory', 'locations'));
    }

    /**
     * Process stock transfer
     */
    public function processTransfer(Request $request)
    {
        $request->validate([
            'from_inventory_id' => 'required|exists:inventories,id',
            'to_location_id' => 'required|exists:locations,id',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string|max:1000'
        ]);

        $fromInventory = Inventory::findOrFail($request->from_inventory_id);
        $toLocation = Location::findOrFail($request->to_location_id);

        if ($request->quantity > $fromInventory->quantity) {
            return back()->withErrors(['quantity' => 'الكمية المطلوب نقلها أكبر من المتاح']);
        }

        DB::transaction(function () use ($fromInventory, $toLocation, $request) {
            // Transfer stock
            $fromInventory->transferTo($toLocation, $request->quantity, $request->reason);
        });

        return redirect()->route('inventory.index')
            ->with('success', 'تم نقل المخزون بنجاح');
    }

    /**
     * Generate inventory reports
     */
    public function reports(Request $request)
    {
        $reportType = $request->get('type', 'summary');
        
        switch ($reportType) {
            case 'valuation':
                return $this->valuationReport($request);
            case 'movements':
                return $this->movementsReport($request);
            case 'expiry':
                return $this->expiryReport($request);
            default:
                return $this->summaryReport($request);
        }
    }

    /**
     * Summary report
     */
    private function summaryReport(Request $request)
    {
        $inventoryData = Inventory::with(['product', 'location'])
            ->selectRaw('
                COUNT(*) as total_items,
                SUM(quantity) as total_quantity,
                SUM(quantity * cost_price) as total_cost_value,
                SUM(quantity * selling_price) as total_selling_value
            ')
            ->first();

        $categoryBreakdown = Inventory::join('products', 'inventories.product_id', '=', 'products.id')
            ->join('categories', 'products.category_id', '=', 'categories.id')
            ->selectRaw('
                categories.name as category_name,
                COUNT(*) as item_count,
                SUM(inventories.quantity) as total_quantity,
                SUM(inventories.quantity * inventories.cost_price) as total_value
            ')
            ->groupBy('categories.id', 'categories.name')
            ->get();

        return view('inventory.reports.summary', compact('inventoryData', 'categoryBreakdown'));
    }

    /**
     * Valuation report
     */
    private function valuationReport(Request $request)
    {
        $inventoryItems = Inventory::with(['product.category', 'location'])
            ->selectRaw('
                *,
                (quantity * cost_price) as cost_value,
                (quantity * selling_price) as selling_value
            ')
            ->orderByDesc('cost_value')
            ->get();

        return view('inventory.reports.valuation', compact('inventoryItems'));
    }

    /**
     * Movements report
     */
    private function movementsReport(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $movements = InventoryMovement::with(['inventory.product', 'user'])
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->orderBy('created_at', 'desc')
            ->get();

        $movementStats = InventoryMovement::getMovementStats();

        return view('inventory.reports.movements', compact('movements', 'movementStats', 'dateFrom', 'dateTo'));
    }

    /**
     * Expiry report
     */
    private function expiryReport(Request $request)
    {
        $expiredItems = Inventory::with(['product', 'location'])
            ->expired()
            ->get();

        $nearExpiryItems = Inventory::with(['product', 'location'])
            ->nearExpiry()
            ->get();

        return view('inventory.reports.expiry', compact('expiredItems', 'nearExpiryItems'));
    }

    /**
     * Low stock alerts
     */
    public function lowStockAlerts()
    {
        $lowStockItems = Inventory::with(['product', 'location'])
            ->lowStock()
            ->get();

        $outOfStockItems = Inventory::with(['product', 'location'])
            ->outOfStock()
            ->get();

        return view('inventory.low-stock', compact('lowStockItems', 'outOfStockItems'));
    }
}
