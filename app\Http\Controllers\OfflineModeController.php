<?php

namespace App\Http\Controllers;

use App\Services\OfflineModeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class OfflineModeController extends Controller
{
    protected $offlineService;

    public function __construct(OfflineModeService $offlineService)
    {
        $this->offlineService = $offlineService;
    }

    /**
     * Prepare offline data for POS
     */
    public function prepareData(Request $request)
    {
        $request->validate([
            'location_id' => 'nullable|exists:locations,id',
        ]);

        $locationId = $request->get('location_id');
        
        try {
            $result = $this->offlineService->prepareOfflineData($locationId);
            
            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Failed to prepare offline data', [
                'location_id' => $locationId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تحضير البيانات للوضع غير المتصل',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store offline sale
     */
    public function storeSale(Request $request)
    {
        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'sale_type' => 'required|in:repair_service,parts_only,accessories,mixed',
            'items' => 'required|array|min:1',
            'items.*.type' => 'required|in:repair_service,part,accessory,labor,other',
            'items.*.name' => 'required|string',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.line_total' => 'required|numeric|min:0',
            'subtotal' => 'required|numeric|min:0',
            'tax_amount' => 'required|numeric|min:0',
            'total_amount' => 'required|numeric|min:0',
            'payments' => 'required|array|min:1',
            'payments.*.method' => 'required|in:cash,card,bank_transfer',
            'payments.*.amount' => 'required|numeric|min:0.01',
            'location_id' => 'nullable|exists:locations,id',
            'user_id' => 'required|exists:users,id',
            'sale_date' => 'required|date',
        ]);

        try {
            $saleData = $request->all();
            $result = $this->offlineService->storeOfflineSale($saleData);
            
            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Failed to store offline sale', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حفظ المبيعة في الوضع غير المتصل',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync offline data to database
     */
    public function sync(Request $request)
    {
        try {
            $result = $this->offlineService->syncOfflineData();
            
            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Failed to sync offline data', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في مزامنة البيانات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get offline sync status
     */
    public function status(Request $request)
    {
        try {
            $status = $this->offlineService->getSyncStatus();
            
            return response()->json([
                'success' => true,
                'status' => $status
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get offline status', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في الحصول على حالة المزامنة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear offline data
     */
    public function clear(Request $request)
    {
        $request->validate([
            'location_id' => 'nullable|exists:locations,id',
        ]);

        try {
            $locationId = $request->get('location_id');
            $result = $this->offlineService->clearOfflineData($locationId);
            
            return response()->json([
                'success' => $result,
                'message' => $result ? 'تم مسح البيانات غير المتصلة بنجاح' : 'فشل في مسح البيانات'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to clear offline data', [
                'location_id' => $request->get('location_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في مسح البيانات غير المتصلة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check network connectivity
     */
    public function checkConnectivity(Request $request)
    {
        try {
            // Simple connectivity check
            $startTime = microtime(true);
            
            // Try to connect to database
            \DB::connection()->getPdo();
            
            $responseTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds
            
            return response()->json([
                'success' => true,
                'online' => true,
                'response_time' => round($responseTime, 2),
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'online' => false,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ]);
        }
    }

    /**
     * Get offline configuration
     */
    public function getConfig(Request $request)
    {
        $request->validate([
            'location_id' => 'nullable|exists:locations,id',
        ]);

        try {
            $locationId = $request->get('location_id');
            $location = $locationId ? \App\Models\Location::find($locationId) : \App\Models\Location::getMainBranch();
            
            $config = [
                'enabled' => true,
                'auto_sync' => true,
                'sync_interval' => 300, // 5 minutes
                'max_offline_sales' => 100,
                'data_retention_hours' => 24,
                'location' => [
                    'id' => $location?->id,
                    'name' => $location?->name,
                    'code' => $location?->code,
                ],
                'settings' => [
                    'tax_rate' => $location?->getTaxRate() ?? 15,
                    'payment_methods' => $location?->getDefaultPaymentMethods() ?? ['cash'],
                    'require_customer' => $location?->pos_settings['require_customer_info'] ?? false,
                    'auto_print_receipt' => $location?->pos_settings['auto_print_receipt'] ?? true,
                ]
            ];
            
            return response()->json([
                'success' => true,
                'config' => $config
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get offline config', [
                'location_id' => $request->get('location_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في الحصول على إعدادات الوضع غير المتصل',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update offline configuration
     */
    public function updateConfig(Request $request)
    {
        $request->validate([
            'location_id' => 'nullable|exists:locations,id',
            'auto_sync' => 'boolean',
            'sync_interval' => 'integer|min:60|max:3600', // 1 minute to 1 hour
            'max_offline_sales' => 'integer|min:10|max:1000',
        ]);

        try {
            $locationId = $request->get('location_id');
            $location = $locationId ? \App\Models\Location::find($locationId) : \App\Models\Location::getMainBranch();
            
            if (!$location) {
                return response()->json([
                    'success' => false,
                    'message' => 'الموقع غير موجود'
                ], 404);
            }

            $posSettings = $location->pos_settings ?? [];
            
            if ($request->has('auto_sync')) {
                $posSettings['offline_auto_sync'] = $request->boolean('auto_sync');
            }
            
            if ($request->has('sync_interval')) {
                $posSettings['offline_sync_interval'] = $request->get('sync_interval');
            }
            
            if ($request->has('max_offline_sales')) {
                $posSettings['offline_max_sales'] = $request->get('max_offline_sales');
            }

            $location->update(['pos_settings' => $posSettings]);
            
            return response()->json([
                'success' => true,
                'message' => 'تم تحديث إعدادات الوضع غير المتصل بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update offline config', [
                'location_id' => $request->get('location_id'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تحديث إعدادات الوضع غير المتصل',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
