<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'name_en',
        'logo',
        'email',
        'phone',
        'address',
        'city',
        'country',
        'postal_code',
        'tax_number',
        'registration_number',
        'website',
        'currency',
        'timezone',
        'fiscal_year_start',
        'default_profit_margin',
        'is_active',
        'settings'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'settings' => 'array',
        'fiscal_year_start' => 'date'
    ];

    /**
     * العلاقة مع المواقع
     */
    public function locations()
    {
        return $this->hasMany(Location::class);
    }

    /**
     * العلاقة مع المستخدمين
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * العلاقة مع العملاء
     */
    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * العلاقة مع الموردين
     */
    public function suppliers()
    {
        return $this->hasMany(Supplier::class);
    }

    /**
     * العلاقة مع المنتجات
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * الحصول على الإعدادات
     */
    public function getSetting($key, $default = null)
    {
        return data_get($this->settings, $key, $default);
    }

    /**
     * تحديث إعداد معين
     */
    public function updateSetting($key, $value)
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);
        $this->update(['settings' => $settings]);
    }

    /**
     * الحصول على العملة المنسقة
     */
    public function formatCurrency($amount)
    {
        $currency = $this->currency ?? 'USD';
        $locale = $this->getSetting('locale', 'en_US');

        $formatter = new \NumberFormatter($locale, \NumberFormatter::CURRENCY);
        return $formatter->formatCurrency($amount, $currency);
    }
}
