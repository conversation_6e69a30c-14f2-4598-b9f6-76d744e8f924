<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        // For now, we'll allow all authenticated users
        // In a real application, you would check user permissions here
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        // TODO: Implement actual permission checking
        // Example:
        // if (!auth()->user()->hasPermission($permission)) {
        //     abort(403, 'Unauthorized action.');
        // }

        return $next($request);
    }
}
