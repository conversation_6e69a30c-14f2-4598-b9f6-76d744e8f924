<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('account_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('journal_entry_id')->comment('معرف القيد');
            $table->unsignedBigInteger('account_id')->comment('معرف الحساب');
            $table->decimal('debit_amount', 15, 2)->default(0)->comment('المبلغ المدين');
            $table->decimal('credit_amount', 15, 2)->default(0)->comment('المبلغ الدائن');
            $table->text('description')->nullable()->comment('وصف العملية');
            $table->string('reference_number', 50)->nullable()->comment('رقم المرجع');
            $table->json('metadata')->nullable()->comment('بيانات إضافية');
            $table->timestamps();

            // Indexes
            $table->index(['journal_entry_id']);
            $table->index(['account_id']);
            $table->index(['journal_entry_id', 'account_id']);
            
            // Foreign Keys
            $table->foreign('journal_entry_id')->references('id')->on('journal_entries')->onDelete('cascade');
            $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('account_transactions');
    }
};
