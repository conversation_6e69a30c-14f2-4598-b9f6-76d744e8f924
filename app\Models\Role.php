<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Role extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'name',
        'name_en',
        'description',
        'is_system_role',
        'permissions'
    ];

    protected $casts = [
        'is_system_role' => 'boolean',
        'permissions' => 'array'
    ];

    /**
     * العلاقة مع الشركة
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * العلاقة مع المستخدمين
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * التحقق من وجود صلاحية معينة
     */
    public function hasPermission($permission)
    {
        $permissions = $this->permissions ?? [];
        return in_array($permission, $permissions);
    }

    /**
     * إضافة صلاحية
     */
    public function addPermission($permission)
    {
        $permissions = $this->permissions ?? [];
        if (!in_array($permission, $permissions)) {
            $permissions[] = $permission;
            $this->update(['permissions' => $permissions]);
        }
    }

    /**
     * إزالة صلاحية
     */
    public function removePermission($permission)
    {
        $permissions = $this->permissions ?? [];
        $permissions = array_diff($permissions, [$permission]);
        $this->update(['permissions' => array_values($permissions)]);
    }

    /**
     * الحصول على جميع الصلاحيات المتاحة
     */
    public static function getAllPermissions()
    {
        return [
            // إدارة النظام
            'system.settings' => 'إعدادات النظام',
            'system.backup' => 'النسخ الاحتياطي',
            'system.logs' => 'سجلات النظام',
            
            // إدارة الشركة
            'company.view' => 'عرض بيانات الشركة',
            'company.edit' => 'تعديل بيانات الشركة',
            
            // إدارة المواقع
            'locations.view' => 'عرض المواقع',
            'locations.create' => 'إنشاء موقع',
            'locations.edit' => 'تعديل المواقع',
            'locations.delete' => 'حذف المواقع',
            
            // إدارة المستخدمين
            'users.view' => 'عرض المستخدمين',
            'users.create' => 'إنشاء مستخدم',
            'users.edit' => 'تعديل المستخدمين',
            'users.delete' => 'حذف المستخدمين',
            
            // إدارة الأدوار
            'roles.view' => 'عرض الأدوار',
            'roles.create' => 'إنشاء دور',
            'roles.edit' => 'تعديل الأدوار',
            'roles.delete' => 'حذف الأدوار',
            
            // إدارة العملاء
            'customers.view' => 'عرض العملاء',
            'customers.create' => 'إنشاء عميل',
            'customers.edit' => 'تعديل العملاء',
            'customers.delete' => 'حذف العملاء',
            
            // إدارة الموردين
            'suppliers.view' => 'عرض الموردين',
            'suppliers.create' => 'إنشاء مورد',
            'suppliers.edit' => 'تعديل الموردين',
            'suppliers.delete' => 'حذف الموردين',
            
            // إدارة المنتجات
            'products.view' => 'عرض المنتجات',
            'products.create' => 'إنشاء منتج',
            'products.edit' => 'تعديل المنتجات',
            'products.delete' => 'حذف المنتجات',
            'products.import' => 'استيراد المنتجات',
            'products.export' => 'تصدير المنتجات',
            
            // إدارة المخزون
            'inventory.view' => 'عرض المخزون',
            'inventory.adjust' => 'تعديل المخزون',
            'inventory.transfer' => 'نقل المخزون',
            
            // المبيعات
            'sales.view' => 'عرض المبيعات',
            'sales.create' => 'إنشاء مبيعة',
            'sales.edit' => 'تعديل المبيعات',
            'sales.delete' => 'حذف المبيعات',
            'sales.pos' => 'نقاط البيع',
            
            // المشتريات
            'purchases.view' => 'عرض المشتريات',
            'purchases.create' => 'إنشاء مشترى',
            'purchases.edit' => 'تعديل المشتريات',
            'purchases.delete' => 'حذف المشتريات',
            
            // المصروفات
            'expenses.view' => 'عرض المصروفات',
            'expenses.create' => 'إنشاء مصروف',
            'expenses.edit' => 'تعديل المصروفات',
            'expenses.delete' => 'حذف المصروفات',
            
            // التقارير
            'reports.sales' => 'تقارير المبيعات',
            'reports.purchases' => 'تقارير المشتريات',
            'reports.inventory' => 'تقارير المخزون',
            'reports.financial' => 'التقارير المالية',
            'reports.customers' => 'تقارير العملاء',
            'reports.suppliers' => 'تقارير الموردين',
            'reports.expenses' => 'تقارير المصروفات',
            
            // الصيانة
            'maintenance.view' => 'عرض طلبات الصيانة',
            'maintenance.create' => 'إنشاء طلب صيانة',
            'maintenance.edit' => 'تعديل طلبات الصيانة',
            'maintenance.delete' => 'حذف طلبات الصيانة',
            'maintenance.assign' => 'تعيين الفنيين',
        ];
    }

    /**
     * إنشاء الأدوار الافتراضية
     */
    public static function createDefaultRoles($companyId)
    {
        $roles = [
            [
                'name' => 'مدير النظام',
                'name_en' => 'System Administrator',
                'description' => 'مدير النظام مع جميع الصلاحيات',
                'is_system_role' => true,
                'permissions' => array_keys(self::getAllPermissions())
            ],
            [
                'name' => 'مدير',
                'name_en' => 'Manager',
                'description' => 'مدير مع صلاحيات إدارية محدودة',
                'is_system_role' => true,
                'permissions' => [
                    'locations.view', 'users.view', 'customers.view', 'customers.create', 'customers.edit',
                    'suppliers.view', 'suppliers.create', 'suppliers.edit', 'products.view', 'products.create', 'products.edit',
                    'inventory.view', 'sales.view', 'sales.create', 'sales.edit', 'sales.pos',
                    'purchases.view', 'purchases.create', 'purchases.edit', 'expenses.view', 'expenses.create', 'expenses.edit',
                    'reports.sales', 'reports.purchases', 'reports.inventory', 'reports.financial',
                    'maintenance.view', 'maintenance.create', 'maintenance.edit', 'maintenance.assign'
                ]
            ],
            [
                'name' => 'كاشير',
                'name_en' => 'Cashier',
                'description' => 'كاشير مع صلاحيات المبيعات',
                'is_system_role' => true,
                'permissions' => [
                    'customers.view', 'customers.create', 'products.view', 'inventory.view',
                    'sales.view', 'sales.create', 'sales.pos', 'reports.sales'
                ]
            ],
            [
                'name' => 'فني',
                'name_en' => 'Technician',
                'description' => 'فني صيانة',
                'is_system_role' => true,
                'permissions' => [
                    'customers.view', 'products.view', 'inventory.view',
                    'maintenance.view', 'maintenance.edit'
                ]
            ]
        ];

        foreach ($roles as $roleData) {
            $roleData['company_id'] = $companyId;
            self::create($roleData);
        }
    }
}
