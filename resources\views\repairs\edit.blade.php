@extends('layouts.main')

@section('title', 'تعديل طلب الصيانة')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">تعديل طلب الصيانة</h1>
            <p class="text-gray-600 dark:text-gray-400">تعديل تفاصيل طلب الصيانة رقم {{ $repair->repair_number }}</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('repairs.show', $repair) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
                إلغاء
            </a>
            <button type="submit" form="repair-form" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                حفظ التغييرات
            </button>
        </div>
    </div>

    <form id="repair-form" action="{{ route('repairs.update', $repair) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Form -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Customer Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات العميل</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="md:col-span-2">
                            <label for="customer_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العميل</label>
                            <select name="customer_id" id="customer_id" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="">اختر العميل</option>
                                @foreach($customers as $customer)
                                    <option value="{{ $customer->id }}" {{ $repair->customer_id == $customer->id ? 'selected' : '' }}>
                                        {{ $customer->first_name }} {{ $customer->last_name }}
                                        @if($customer->company_name) - {{ $customer->company_name }} @endif
                                        ({{ $customer->phone }})
                                    </option>
                                @endforeach
                            </select>
                            @error('customer_id')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Device Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات الجهاز</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="device_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الجهاز</label>
                            <select name="device_type" id="device_type" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                <option value="">اختر نوع الجهاز</option>
                                @foreach($deviceTypes as $type)
                                    <option value="{{ $type }}" {{ $repair->device_type == $type ? 'selected' : '' }}>{{ $type }}</option>
                                @endforeach
                            </select>
                            @error('device_type')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="device_brand" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الماركة</label>
                            <input type="text" name="device_brand" id="device_brand" required placeholder="مثل: Samsung, Apple, HP"
                                   value="{{ old('device_brand', $repair->device_brand) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('device_brand')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="device_model" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموديل</label>
                            <input type="text" name="device_model" id="device_model" required placeholder="مثل: Galaxy S21, iPhone 13"
                                   value="{{ old('device_model', $repair->device_model) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('device_model')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="device_serial" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الرقم التسلسلي</label>
                            <input type="text" name="device_serial" id="device_serial"
                                   value="{{ old('device_serial', $repair->device_serial) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('device_serial')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="device_imei" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IMEI (للهواتف)</label>
                            <input type="text" name="device_imei" id="device_imei"
                                   value="{{ old('device_imei', $repair->device_imei) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('device_imei')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Problem Description -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">وصف المشكلة</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="problem_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">وصف المشكلة</label>
                            <textarea name="problem_description" id="problem_description" rows="4" required placeholder="اشرح المشكلة بالتفصيل..."
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">{{ old('problem_description', $repair->problem_description) }}</textarea>
                            @error('problem_description')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="repair_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الإصلاح</label>
                                <select name="repair_type" id="repair_type" required
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                    <option value="">اختر نوع الإصلاح</option>
                                    <option value="warranty" {{ $repair->repair_type == 'warranty' ? 'selected' : '' }}>ضمان</option>
                                    <option value="paid" {{ $repair->repair_type == 'paid' ? 'selected' : '' }}>مدفوع</option>
                                    <option value="internal" {{ $repair->repair_type == 'internal' ? 'selected' : '' }}>داخلي</option>
                                </select>
                                @error('repair_type')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الأولوية</label>
                                <select name="priority" id="priority" required
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                    <option value="low" {{ $repair->priority == 'low' ? 'selected' : '' }}>منخفضة</option>
                                    <option value="normal" {{ $repair->priority == 'normal' ? 'selected' : '' }}>عادية</option>
                                    <option value="high" {{ $repair->priority == 'high' ? 'selected' : '' }}>عالية</option>
                                    <option value="urgent" {{ $repair->priority == 'urgent' ? 'selected' : '' }}>عاجلة</option>
                                </select>
                                @error('priority')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="estimated_cost" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التكلفة المقدرة</label>
                                <input type="number" name="estimated_cost" id="estimated_cost" step="0.01" placeholder="0.00"
                                       value="{{ old('estimated_cost', $repair->estimated_cost) }}"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                @error('estimated_cost')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="technician_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الفني المسؤول</label>
                                <select name="technician_id" id="technician_id"
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                    <option value="">تعيين تلقائي</option>
                                    @foreach($technicians as $technician)
                                        <option value="{{ $technician->id }}" {{ $repair->technician_id == $technician->id ? 'selected' : '' }}>
                                            {{ $technician->first_name }} {{ $technician->last_name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('technician_id')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="estimated_completion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الإنجاز المتوقع</label>
                                <input type="date" name="estimated_completion" id="estimated_completion"
                                       value="{{ old('estimated_completion', $repair->estimated_completion ? $repair->estimated_completion->format('Y-m-d') : '') }}"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                                @error('estimated_completion')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات إضافية</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="accessories" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الملحقات المرفقة</label>
                            <input type="text" name="accessories[]" id="accessories" placeholder="مثل: شاحن، سماعات، كفر..."
                                   value="{{ old('accessories.0', is_array($repair->accessories) ? implode(', ', $repair->accessories) : $repair->accessories) }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">
                            @error('accessories')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="condition_on_arrival" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة الجهاز عند الوصول</label>
                            <textarea name="condition_on_arrival" id="condition_on_arrival" rows="3" placeholder="وصف حالة الجهاز عند الاستلام..."
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">{{ old('condition_on_arrival', $repair->condition_on_arrival) }}</textarea>
                            @error('condition_on_arrival')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="customer_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات العميل</label>
                            <textarea name="customer_notes" id="customer_notes" rows="3" placeholder="أي ملاحظات من العميل..."
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100">{{ old('customer_notes', $repair->customer_notes) }}</textarea>
                            @error('customer_notes')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Current Status -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">الحالة الحالية</h4>
                    <div class="text-center">
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full {{ $repair->status_badge['class'] }}">
                            {{ $repair->status_badge['text'] }}
                        </span>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                            آخر تحديث: {{ $repair->updated_at->format('Y-m-d H:i') }}
                        </p>
                    </div>
                </div>

                <!-- Repair Summary -->
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">ملخص الطلب</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">رقم الطلب:</span>
                            <span class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->repair_number }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">تاريخ الإنشاء:</span>
                            <span class="font-medium text-gray-900 dark:text-gray-100">{{ $repair->created_at->format('Y-m-d') }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">العميل:</span>
                            <span class="font-medium text-gray-900 dark:text-gray-100">
                                {{ $repair->customer ? $repair->customer->first_name . ' ' . $repair->customer->last_name : 'غير محدد' }}
                            </span>
                        </div>
                        
                        <hr class="border-gray-200 dark:border-gray-700">
                        
                        <div class="flex justify-between text-lg font-bold">
                            <span class="text-gray-900 dark:text-gray-100">التكلفة المقدرة:</span>
                            <span class="text-blue-600 dark:text-blue-400">
                                {{ $repair->estimated_cost ? number_format($repair->estimated_cost, 2) . ' ₪' : 'غير محدد' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection
